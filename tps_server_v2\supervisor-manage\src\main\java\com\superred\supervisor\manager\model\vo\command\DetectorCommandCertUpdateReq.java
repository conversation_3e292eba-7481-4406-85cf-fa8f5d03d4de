package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/03/24
 */
@Data
public class DetectorCommandCertUpdateReq {

    @Schema(description = "通信证书ID")
    @NotNull(message = "通信证书ID不能为空")
    private Integer id;

    @Schema(description = "监测器ID数组")
    @NotEmpty(message = "设备编号不能为空")
    private List<String> deviceIdList;
}