package com.superred.supervisor.gateway.model.app.register;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * 组件唯一编码查询请求
 *
 * <AUTHOR>
 * @since 2025/06/26
 */
@Data
public class AppAgentIdReq {

    /**
     * MAC地址.
     **/
    @Schema(description = "MAC地址")
    @NotBlank(message = "MAC地址不能为空")
    private String mac;
    /**
     * 终端主硬盘序列号.
     **/
    @Schema(description = "终端主硬盘序列号")
    @JsonProperty("hdcode")
    @NotBlank(message = "组件主硬盘序列号不能为空")
    private String hdCode;

    /**
     * 厂商序列号，有国家局统一编号.
     **/
    @Schema(description = "厂商序列号，有国家局统一编号")
    @NotBlank(message = "厂商序列号不能为空")
    private String vendor;


}
