package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 指令响应结果统计请求参数
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data
public class CommandResultStatReq {
    /**
     * 设备id
     */
    @Schema(description = "设备id", example = "device123")
    private String deviceId;

    /**
     * 命令
     */
    @Schema(description = "命令")
    private String cmd;
}