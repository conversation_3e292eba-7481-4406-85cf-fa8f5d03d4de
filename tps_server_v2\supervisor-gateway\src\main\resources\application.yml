server:
  tomcat:
    max-http-form-post-size: 100MB
  port: 50070
spring:
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
  redis:
    database: 0
    host: ************
    password: 123456
    port: 6379
    timeout: 5000ms
  datasource:
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************
    username: superred
    password: 123456
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      connection-timeout: 30000       # 等待连接池分配连接的最大时长（毫秒），超过这个时长还没可用的连接则发生SQLException， 默认:30秒
      minimum-idle: 5                 # 最小连接数
      maximum-pool-size: 50           # 最大连接数
      auto-commit: true               # 自动提交
      idle-timeout: 600000            # 连接超时的最大时长（毫秒），超时则被释放（retired），默认:10分钟
      pool-name: DataSourceHikariCP_A     # 连接池名字
      max-lifetime: 1800000           # 连接的生命时长（毫秒），超时而且没被使用则被释放（retired），默认:30分钟 1800000ms
      connection-test-query: SELECT 1
  kafka:
    bootstrap-servers: ************:9092
    producer:
      # 发生错误后，消息重发的次数。
      retries: 3
      #当有多个消息需要被发送到同一个分区时，生产者会把它们放在同一个批次里。该参数指定了一个批次可以使用的内存大小，按照字节数计算。
      batch-size: 10384
      # 设置生产者内存缓冲区的大小。
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
    consumer:
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

    cmd-result:
      topic: cmd-result-request-msg
    agent-collection:
      topic: agent-collection-request-msg
    agent-detection:
      topic: agent-detection-request-msg

logging:
  config: classpath:logback-spring.xml
  file:
    path: /opt/wlh/supervisor/logs
  level:
    root: info
    com.superred: debug

superred:
  common:
    sm4-db:
      key: b778099a3e7b23415c78ddd7491b35c0
      iv: 57d31d0ef5394c443425c3984fe668bf
    starter:
      enable-default-exception-handler: false
      enable-security-header-filter: false
      enable-logging-filter: true
    minio:
      endpoint: http://************:9000
      accessKey: admin
      secretKey: admin123
      bucket: supervisor

knife4j:
  enable: true
springdoc:
  swagger-ui:
    tags-sorter: alpha
    operations-sorter: order

management:
  endpoint:
    health:
      show-components: always  # 默认就是按组件显示
      show-details: always
  health:
    defaults:
      enabled: false         # 全部关闭
    db:
      enabled: true          # 只启用 DB 检查
    diskspace:
      enabled: true
