package com.superred.supervisor.manager.model.vo.policy.terminal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 终端策略下发记录分页查询请求
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Data
@Schema(description = "终端策略下发记录分页查询请求")
public class TerminalPolicyIssueRecordPageReq {

    @Schema(description = "策略编码")
    private String policyCode;

    @Schema(description = "策略名称（模糊查询）")
    private String policyName;

    @Schema(description = "模块类型：alarm file_detect device_info")
    private String module;

    @Schema(description = "策略类型：add-增加,del-删除,reset-全量,inc_del-增量删除")
    private String cmd;

    @Schema(description = "创建人")
    private String creatorId;

    @Schema(description = "当前页", example = "1")
    private Integer start;

    @Schema(description = "每页显示条数", example = "10")
    private Integer limit;
}
