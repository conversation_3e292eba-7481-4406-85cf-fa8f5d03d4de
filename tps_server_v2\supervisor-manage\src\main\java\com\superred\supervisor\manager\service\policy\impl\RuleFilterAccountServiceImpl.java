package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleFilterAccount;
import com.superred.supervisor.common.repository.policy.RuleFilterAccountRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.constant.RuleSourceTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.config.FilterAccountPolicyConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterAccountPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterAccountReq;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterAccountResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleFilterAccountService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-12 11:16
 */
@Slf4j
@Service("ruleFilterAccountService")
@AllArgsConstructor
public class RuleFilterAccountServiceImpl implements RuleFilterAccountService, RuleService {

    @Resource
    private RuleFilterAccountRepository ruleFilterAccountRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    private static final Pattern PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9!\"#$%&'()*+,\\-./:;<=>?@\\[\\\\\\]^_`{|}~]+$");

    @Override
    public IPage<RuleFilterAccountResp> page(RuleFilterAccountPageReq ruleFilterAccountPageReq) {
        // 查询
        List<String> ruleIdList = this.getRuleIdList(ruleFilterAccountPageReq);
        LambdaQueryWrapper<RuleFilterAccount> queryWrapper = new LambdaQueryWrapper<RuleFilterAccount>()
                .like(!StrUtil.isEmpty(ruleFilterAccountPageReq.getRuleId()), RuleFilterAccount::getRuleId, ruleFilterAccountPageReq.getRuleId())
                .like(!StrUtil.isEmpty(ruleFilterAccountPageReq.getRuleContent()), RuleFilterAccount::getRuleContent, ruleFilterAccountPageReq.getRuleContent())
                .like(!StrUtil.isEmpty(ruleFilterAccountPageReq.getFilterFileType()), RuleFilterAccount::getFilterFileType, ruleFilterAccountPageReq.getFilterFileType())
                .eq(!StrUtil.isEmpty(ruleFilterAccountPageReq.getStatus()), RuleFilterAccount::getStatus, ruleFilterAccountPageReq.getStatus())
                .eq(!StrUtil.isEmpty(ruleFilterAccountPageReq.getIsShare()), RuleFilterAccount::getIsShare, ruleFilterAccountPageReq.getIsShare())
                .eq(!StrUtil.isEmpty(ruleFilterAccountPageReq.getRuleSource()), RuleFilterAccount::getRuleSource, ruleFilterAccountPageReq.getRuleSource())
                .le(CharSequenceUtil.isNotEmpty(ruleFilterAccountPageReq.getEndDate()), RuleFilterAccount::getUpdateTime, ruleFilterAccountPageReq.getEndDate())
                .ge(CharSequenceUtil.isNotEmpty(ruleFilterAccountPageReq.getStartDate()), RuleFilterAccount::getUpdateTime, ruleFilterAccountPageReq.getStartDate())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleFilterAccount::getRuleId, ruleIdList)
                .ne(RuleFilterAccount::getRuleSource, RuleSourceTypeEnum.SUPER_POLICY_SHARE.getKey())
                .orderByDesc(RuleFilterAccount::getUpdateTime);
        Page<RuleFilterAccount> page = new Page<>(ruleFilterAccountPageReq.getStart(), ruleFilterAccountPageReq.getLimit());
        IPage<RuleFilterAccount> page1 = this.ruleFilterAccountRepository.page(page, queryWrapper);
        return page1.convert(RuleFilterAccountResp::fromRuleFilterAccount);
    }

    @Override
    public RuleFilterAccountResp getById(Long ruleId) {
        RuleFilterAccount ruleFilterAccount = this.ruleFilterAccountRepository.getOne(Wrappers.<RuleFilterAccount>lambdaQuery()
                .eq(RuleFilterAccount::getRuleId, ruleId)
                .last("limit 1"));
        return RuleFilterAccountResp.fromRuleFilterAccount(ruleFilterAccount);
    }

    @Override
    public void save(RuleFilterAccountReq ruleFilterAccountReq) {
        // 验证参数
        this.validateParam(ruleFilterAccountReq);
        // 验重复
        this.validateSaveRepeat(ruleFilterAccountReq);
        RuleFilterAccount ruleFilterAccount = fromRuleFilterAccountReq(ruleFilterAccountReq);
        // 赋值策略ID
        ruleFilterAccount.setRuleId(this.ruleIdBuilder.buildRuleId().toString());
        this.ruleFilterAccountRepository.save(ruleFilterAccount);
    }

    @Override
    public void edit(RuleFilterAccountReq ruleFilterAccountReq) {
        // 验证ID
        this.validateId(ruleFilterAccountReq);
        // 验证参数
        this.validateParam(ruleFilterAccountReq);
        // 验重复
        this.validateEditRepeat(ruleFilterAccountReq);
        RuleFilterAccount ruleFilterAccount = fromRuleFilterAccountReq(ruleFilterAccountReq);
        this.ruleFilterAccountRepository.update(ruleFilterAccount, Wrappers.<RuleFilterAccount>lambdaUpdate().eq(RuleFilterAccount::getRuleId, ruleFilterAccountReq.getRuleId()));
    }

    public static RuleFilterAccount fromRuleFilterAccountReq(RuleFilterAccountReq ruleFilterAccountReq) {
        return RuleFilterAccount.builder()
                .ruleContent(ruleFilterAccountReq.getRuleContent())
                .ruleDesc(ruleFilterAccountReq.getRuleDesc())
                .filterFileType(ruleFilterAccountReq.getFilterFileType())
                .filterFileSizeMin(ruleFilterAccountReq.getFilterFileSizeMin())
                .filterFileSizeMax(ruleFilterAccountReq.getFilterFileSizeMax())
                .status(ruleFilterAccountReq.getStatus())
                .isShare(ruleFilterAccountReq.getIsShare())
                .ruleSource(ruleFilterAccountReq.getRuleSource())
                .level(ruleFilterAccountReq.getLevel())
                .updateTime(ruleFilterAccountReq.getUpdateTime())
                .createTime(ruleFilterAccountReq.getCreateTime())
                .ext1(ruleFilterAccountReq.getExt1())
                .ext2(ruleFilterAccountReq.getExt2())
                .ext3(ruleFilterAccountReq.getExt3())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleFilterAccountRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 验证是否在使用
     *
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleFilterAccount> list = this.ruleFilterAccountRepository.list(Wrappers.<RuleFilterAccount>lambdaQuery()
                .in(RuleFilterAccount::getRuleId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.equals(PolicyApplyStatusEnum.APPLY.getKey().toString(), item.getStatus())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
                Integer ruleSource = Integer.valueOf(item.getRuleSource());
                if (ruleSource != RuleSourceTypeEnum.LOCAL_POLICY.getKey()) {
                    throw new BaseBusinessException("存在上级策略，上级策略不允许被删除");
                }
            });
        }
    }


    /**
     * 验证重复
     *
     * @param ruleFilterAccountReq
     */
    private void validateSaveRepeat(RuleFilterAccountReq ruleFilterAccountReq) {
        LambdaQueryWrapper<RuleFilterAccount> queryWrapper = new LambdaQueryWrapper<RuleFilterAccount>()
                .eq(CharSequenceUtil.isNotEmpty(ruleFilterAccountReq.getRuleContent()), RuleFilterAccount::getRuleContent, ruleFilterAccountReq.getRuleContent());
        List<RuleFilterAccount> list = this.ruleFilterAccountRepository.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("策略内容 存在重复数据，请检查");
        }
    }

    /**
     * 验证重复
     *
     * @param ruleFilterAccountReq
     */
    private void validateEditRepeat(RuleFilterAccountReq ruleFilterAccountReq) {
        LambdaQueryWrapper<RuleFilterAccount> queryWrapper = new LambdaQueryWrapper<RuleFilterAccount>()
                .eq(StrUtil.isNotEmpty(ruleFilterAccountReq.getRuleContent()), RuleFilterAccount::getRuleContent, ruleFilterAccountReq.getRuleContent())
                .ne(RuleFilterAccount::getRuleId, ruleFilterAccountReq.getRuleId());
        List<RuleFilterAccount> list = this.ruleFilterAccountRepository.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("策略内容 存在重复数据，请检查");
        }
    }

    /**
     * 验证参数
     *
     * @param ruleFilterAccountReq
     */
    private void validateParam(RuleFilterAccountReq ruleFilterAccountReq) {
        Matcher matcher = PATTERN.matcher(ruleFilterAccountReq.getRuleContent());
        if (!matcher.matches()) {
            throw new BaseBusinessException("策略内容 格式错误");
        }
        if (ruleFilterAccountReq.getRuleContent().getBytes().length > 512) {
            throw new BaseBusinessException("策略内容 长度不可超过512字节");
        }
    }

    /**
     * 验证ID
     *
     * @param ruleFilterAccountReq
     */
    private void validateId(RuleFilterAccountReq ruleFilterAccountReq) {
        if (StrUtil.isBlank(ruleFilterAccountReq.getRuleId())) {
            throw new BaseBusinessException("策略ID为空错误");
        }
    }

    /**
     * 获取规则ID列表
     * @param ruleFilterAccountPageReq
     * @return
     */
    private List<String> getRuleIdList(RuleFilterAccountPageReq ruleFilterAccountPageReq) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleFilterAccountPageReq.getIssueDeviceType())
                || StrUtil.isBlank(ruleFilterAccountPageReq.getPolicyId())
                || StrUtil.isBlank(ruleFilterAccountPageReq.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleFilterAccountPageReq.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleFilterAccountPageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleFilterAccountPageReq.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleFilterAccountPageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 文件筛选 - 账号筛选策略
        return StrUtil.equals(module, PolicyModuleTwoEnum.ACCOUNT_FILTER.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 文件筛选 - 账号筛选策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleTwoEnum.ACCOUNT_FILTER.getKey())
                .moduleStr(PolicyModuleTwoEnum.ACCOUNT_FILTER.getValue())
                .moduleParentStr(PolicyModuleOneEnum.FILE_FILTER.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 文件筛选 - 账号筛选策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleFilterAccount> list = this.ruleFilterAccountRepository.list(Wrappers.<RuleFilterAccount>lambdaQuery()
                .in(RuleFilterAccount::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<FilterAccountPolicyConfigDTO> configDTOS = list.stream().map(item -> {
            return FilterAccountPolicyConfigDTO.getPolicyConfig(item);
        }).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 文件筛选 - 账号筛选策略
        this.ruleFilterAccountRepository.update(Wrappers.<RuleFilterAccount>lambdaUpdate()
                .in(RuleFilterAccount::getRuleId, ruleIds)
                .set(RuleFilterAccount::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 文件筛选 - 账号筛选策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleFilterAccount> list = this.ruleFilterAccountRepository.list(Wrappers.<RuleFilterAccount>lambdaQuery()
                .in(RuleFilterAccount::getRuleId, ruleIdList));
        List<RuleFilterAccountResp> respList = new ArrayList<>();
        list.forEach(item -> {
            RuleFilterAccountResp resp = RuleFilterAccountResp.fromRuleFilterAccount(item);
            respList.add(resp);
        });
        policyDetail.setFilterAccountList(respList);
        return policyDetail;
    }
}
