package com.superred.supervisor.manager.controller.policy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackTrojanPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackTrojanReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackTrojanResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.service.policy.RuleAttackTrojanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-12 16:16
 */
@Tag(name = "4.7. 攻击窃密策略 - 木马活动检测策略")
@RestController
@RequestMapping("/rule/attack/trojan")
@Slf4j
@Validated
public class RuleAttackTrojanController {

    @Resource
    private RuleAttackTrojanService ruleAttackTrojanService;

    @Operation(summary = "1 分页")
    @GetMapping("/page")
    public RPage<RuleAttackTrojanResp> page(RuleAttackTrojanPageReq ruleAttackTrojanPageReq) {
        IPage<RuleAttackTrojanResp> page = this.ruleAttackTrojanService.page(ruleAttackTrojanPageReq);
        return new RPage<>(page);
    }

    @Operation(summary = "2 查询详情")
    @GetMapping("/{ruleId}")
    public R<RuleAttackTrojanResp> getById(@PathVariable("ruleId") Long ruleId) {
        RuleAttackTrojanResp resp = this.ruleAttackTrojanService.getById(ruleId);
        return R.success(resp);
    }

    @Operation(summary = "3 新增")
    @SysLogAnn(module = LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY, operateType = OperateTypeConstants.ADD, desc = OperateTypeConstants.ADD + LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY)
    @PostMapping("/save")
    public R save(@Valid @RequestBody RuleAttackTrojanReq ruleAttackTrojanReq) {
        this.ruleAttackTrojanService.save(ruleAttackTrojanReq);
        return R.success();
    }

    @Operation(summary = "4 编辑")
    @SysLogAnn(module = LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY, operateType = OperateTypeConstants.MODIFY, desc = OperateTypeConstants.MODIFY + LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY)
    @PostMapping("/edit")
    public R edit(@Valid @RequestBody RuleAttackTrojanReq ruleAttackTrojanReq) {
        this.ruleAttackTrojanService.edit(ruleAttackTrojanReq);
        return R.success();
    }

    @Operation(summary = "5 删除")
    @PostMapping("/del")
    @SysLogAnn(module = LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY, operateType = OperateTypeConstants.DELETE, desc = OperateTypeConstants.DELETE + LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY)
    public R del(@Valid @RequestBody PolicyBatchIdsReq batchIdsReq) {
        this.ruleAttackTrojanService.del(batchIdsReq);
        return R.success();
    }

    @Operation(summary = "6 查看策略应用策略情况")
    @PostMapping("/policy/{ruleId}")
    public R<List<RulePolicyApplyResp>> policyApply(@PathVariable("ruleId") Long ruleId) {
        List<RulePolicyApplyResp> result = this.ruleAttackTrojanService.policyApply(ruleId);
        return R.success(result);
    }

    @Operation(summary = "7 导出")
    @PostMapping("/export")
    @SysLogAnn(module = LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY, operateType = OperateTypeConstants.EXPORT, desc = OperateTypeConstants.EXPORT + LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY)
    public void export(HttpServletResponse response, @RequestBody RuleAttackTrojanPageReq ruleAttackTrojanPageReq) throws IOException {

        // do something
    }

    @Operation(summary = "8 导入")
    @PostMapping("/import")
    @SysLogAnn(module = LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY, operateType = OperateTypeConstants.IMPORT, desc = OperateTypeConstants.IMPORT + LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY)
    public R importFile(@RequestParam("file") MultipartFile file) throws IOException {
        return null;
    }

    @Operation(summary = "9 下载模版")
    @PostMapping("/download")
    @SysLogAnn(module = LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY, operateType = OperateTypeConstants.DOWNLOAD, desc = LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY + "下载模板")
    public void download(HttpServletResponse response) throws IOException {
        //        String filePath = "template/木马活动检测策略模版.xlsx";
        //        String fileName = "木马活动检测策略模版.xlsx";
        //        ClassPathResource classpathResource = new ClassPathResource(filePath);
        //        InputStream inputStream = classpathResource.getInputStream();
        //        FileUtils.downloadFileExcel(response, inputStream, fileName);

    }
}
