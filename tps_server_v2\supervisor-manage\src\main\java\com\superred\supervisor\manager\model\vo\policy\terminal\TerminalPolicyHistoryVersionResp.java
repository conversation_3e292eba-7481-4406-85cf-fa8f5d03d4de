package com.superred.supervisor.manager.model.vo.policy.terminal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 终端策略历史版本响应（兼容前端PolicyHistoryVersionResp格式）
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "终端策略历史版本响应")
public class TerminalPolicyHistoryVersionResp {

    @Schema(description = "模块")
    private String module;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "策略ID")
    private Long policyId;
}
