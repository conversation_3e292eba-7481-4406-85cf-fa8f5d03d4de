package com.superred.supervisor.gateway.controller.terminal;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.aop.IgnoreLogin;
import com.superred.supervisor.gateway.service.terminal.TerminalRegisterService;
import com.superred.supervisor.standard.v202505.terminal.register.RegInfoResp;
import com.superred.supervisor.standard.v202505.terminal.register.TerminalIdReq;
import com.superred.supervisor.standard.v202505.terminal.register.TerminalIdResp;
import com.superred.supervisor.standard.v202505.terminal.register.TerminalRegisterReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 终端注册、认证接口
 *
 * <AUTHOR>
 * @since 2025/5/16 16:03
 */
@Tag(name = "1.1 终端注册、认证接口(2025-02)")
@RestController
@Slf4j
@RequestMapping("/C2/")
public class TerminalRegisterController {

    @Resource
    private TerminalRegisterService terminalRegisterService;

    /**
     *  C.3.3.1 组织机构/人员信息查询接口.
     *
     **/
    @PostMapping("/register/reg_info")
    @Operation(summary = "C.3.3.1 组织机构/人员信息查询接口")
    @ApiOperationSupport(order = 1)
    @IgnoreLogin
    public RegInfoResp regInfoRequest() {
        return terminalRegisterService.regInfo();
    }


    /**
     * C.3.3.2 主机唯一编码查询接口.
     *
     **/
    @PostMapping("/register/get_computer_client_id")
    @Operation(summary = "C.3.3.2 主机唯一编码查询接口")
    @ApiOperationSupport(order = 2)
    @IgnoreLogin
    public ApiResponse<TerminalIdResp> getComputerClientIdRequest(@RequestBody TerminalIdReq req) {
        TerminalIdResp resp = terminalRegisterService.getComputerClientId(req);
        return ApiResponse.success(resp);
    }


    /**
     * C.3.3.3 终端保密组件注册接口
     **/
    @PostMapping("/register/reg_request")
    @Operation(summary = "C.3.3.3 终端保密组件注册接口")
    @ApiOperationSupport(order = 3)
    @IgnoreLogin
    public ApiResponse<String> regRequest(@RequestBody TerminalRegisterReq req) {

        terminalRegisterService.regRequest(req);
        return ApiResponse.success();
    }

    /**
     * C.3.3.4 认证接口.
     *
     **/
    @PostMapping("/auth/login")
    @Operation(summary = "C.3.3.4 认证接口")
    @ApiOperationSupport(order = 4)
    @IgnoreLogin
    public ApiResponse<String> authLoginRequest(@RequestBody TerminalRegisterReq req) {

        terminalRegisterService.authLogin(req);
        return ApiResponse.success();
    }

    /**
     * C.3.3.5 终端保密组件注销接口
     **/
    @PostMapping("/register/reg_cancel")
    @Operation(summary = "C.3.3.5 终端检测组件注销接口")
    @ApiOperationSupport(order = 5)
    public ApiResponse<String> registerRegCancelRequest() {

        terminalRegisterService.regCancel();
        return ApiResponse.success();
    }

}
