#!/bin/bash

if [ $# -lt 1 ]; then
    echo "缺少参数" >&2
    echo ""
    usage;
    exit 1
fi

db_name=$2



result=$(mysql -usuperred -p123456 ${db_name} -e "SELECT * FROM local_device_info")

number=$1

if [ -z "$result" ]; then
	mysql -usuperred -p123456 ${db_name} -e "INSERT INTO local_device_info(device_id,device_type,soft_version,platform_type,address_type,register_status,register_message,vendor_name) VALUES(CONCAT(YEAR(CURDATE()) % 100,LPAD(MONTH(CURDATE()),2,'0'),'12','02','$number'),'02',CONCAT(DATE_FORMAT(CURDATE(),'%Y%m%d'),'_','001'),'011','1','-1','未注册','Superred')"
else
	mysql -usuperred -p123456 ${db_name} -e "UPDATE local_device_info SET register_status = -1, register_message = '未注册', vendor_name = 'Superred', device_id = CONCAT(YEAR(CURDATE()) % 100,LPAD(MONTH(CURDATE()),2,'0'),'12','02','$number')"
fi






