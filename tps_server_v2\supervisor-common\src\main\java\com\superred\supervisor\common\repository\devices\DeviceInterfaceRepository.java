package com.superred.supervisor.common.repository.devices;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.devices.DeviceInterface;
import com.superred.supervisor.common.mapper.devices.DeviceInterfaceMapper;
import org.springframework.stereotype.Repository;

/**
 * 检测器网卡流量 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-18 20:06:46
 */
@Repository
public class DeviceInterfaceRepository extends ServiceImpl<DeviceInterfaceMapper, DeviceInterface> {

}

