package com.superred.supervisor.common.entity.operation.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 0-待执行(未下发)，1-执行中(已下发)，2-执行成功，3-执行失败
 *
 * <AUTHOR>
 * @since 2025/7/24 14:33
 */
@Getter
@AllArgsConstructor
public enum OperationExecStatus implements IEnum<Integer> {

    NOT_ISSUED(0, "未下发"),
    ISSUED(1, "已下发"),
    SUCCESS(2, "执行成功"),
    FAILED(3, "执行失败");

    private final Integer value;


    private final String desc;

    /**
     * 执行结果.0:成功；1：失败
     * @param value 执行结果.0:成功；1：失败
     * @return 对应的枚举值
     */
    public static OperationExecStatus getByResultValue(Integer value) {
        if (value == null) {
            return FAILED;
        }
        if (value == 0) {
            return SUCCESS;
        } else if (value == 1) {
            return FAILED;
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }
}
