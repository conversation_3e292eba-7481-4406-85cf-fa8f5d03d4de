package com.superred.supervision.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervision.db.entity.CheckItemResult;
import com.superred.supervision.db.mapper.CheckItemResultMapper;
import com.superred.supervision.db.service.CheckItemResultService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 终端任务检查项违规情况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-29
 */
@Service
public class CheckItemResultServiceImpl extends ServiceImpl<CheckItemResultMapper, CheckItemResult> implements CheckItemResultService {

    @Override
    public List<CheckItemResult> findByTaskIdAndDeviceId(String deviceId, String taskId) {
        LambdaQueryWrapper<CheckItemResult> qw = new LambdaQueryWrapper<>();
        qw.eq(CheckItemResult::getDeviceId, deviceId);
        qw.eq(CheckItemResult::getTaskId, taskId);
        return baseMapper.selectList(qw);
    }
}
