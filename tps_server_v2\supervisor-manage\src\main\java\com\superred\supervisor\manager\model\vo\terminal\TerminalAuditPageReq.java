package com.superred.supervisor.manager.model.vo.terminal;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * agent审核分页查询请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TerminalAuditPageReq extends PageReqDTO {


    @Schema(description = "注册时间start")
    private Date regTimeStart;


    @Schema(description = "注册时间end")
    private Date regTimeEnd;

    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "终端责任人姓名")
    private String userName;

    @Schema(description = "软件版本号")
    private String softVersion;

    @Schema(description = "终端状态 0 审核通过 1  审核不通过 2 待审核 3 禁用 4 注销")
    private Integer status;

    @Schema(description = "终端部门id")
    private String orgId;

}
