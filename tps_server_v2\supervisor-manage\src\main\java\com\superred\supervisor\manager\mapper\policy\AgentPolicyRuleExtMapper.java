package com.superred.supervisor.manager.mapper.policy;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:31
 */
@Mapper
public interface AgentPolicyRuleExtMapper extends BaseMapper<AgentPolicyRule> {

    List<RulePolicyApplyResp> selectPolicyApply(@Param("query") Long ruleId);
}

