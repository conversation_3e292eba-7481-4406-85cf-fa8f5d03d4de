package com.superred.supervisor.gateway.service.app;

import com.superred.supervisor.gateway.model.app.register.AppAgentIdReq;
import com.superred.supervisor.gateway.model.app.register.AppAgentIdResp;
import com.superred.supervisor.gateway.model.app.register.AppRegStatusResp;
import com.superred.supervisor.gateway.model.app.register.AppRegisterReq;

/**
 * 终端注册、认证接口
 *
 * <AUTHOR>
 * @since 2025/5/16 16:30
 */
public interface AppRegisterService {


    /**
     * 主机唯一编码查询接口
     *
     * @param req 注册请求
     * @return 注册结果
     */
    AppAgentIdResp getAppClientIdRequest(AppAgentIdReq req);

    /**
     * 主机注册接口
     *
     * @param req 注册请求
     */
    void regRequest(AppRegisterReq req);

    /**
     * 组件重新注册接口
     *
     * @param req 注册请求
     */
    void reRegRequest(AppRegisterReq req);

    /**
     * 认证接口
     *
     * @param req 注册请求
     */
    void authLogin(AppRegisterReq req);

    /**
     * 注册状态查询接口
     *
     * @return 注册信息
     */
    AppRegStatusResp registerStatus();

}
