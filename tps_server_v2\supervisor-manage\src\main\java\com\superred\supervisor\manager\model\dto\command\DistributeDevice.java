package com.superred.supervisor.manager.model.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * @since 2025/03/17 16:11
 **/
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Builder
@AllArgsConstructor
public class DistributeDevice {


    /**
     * 设备id
     */
    private String deviceId;


    private Set<Map<String, String>> effectZone;


    public DistributeDevice(String deviceId) {
        this.deviceId = deviceId;
    }
}