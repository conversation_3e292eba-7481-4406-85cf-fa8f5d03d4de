package com.superred.supervisor.manager.config.intercepter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.Ipv4Util;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.WebUtils;
import com.superred.supervisor.common.entity.settings.IpWhitelist;
import com.superred.supervisor.common.entity.settings.LocalDeviceSuspectedLog;
import com.superred.supervisor.common.entity.settings.enums.IpWhiteListType;
import com.superred.supervisor.common.repository.settings.LocalDeviceSuspectedLogRepository;
import com.superred.supervisor.manager.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.util.Date;
import java.util.List;

/**
 *  授权文件校验拦截器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class IpWhiteListInterceptor implements HandlerInterceptor {


    @Resource
    private CacheService cacheService;

    @Resource
    private LocalDeviceSuspectedLogRepository localDeviceSuspectedLogRepository;


    /**
     * 目标方法执行前
     * 该方法在控制器处理请求方法前执行，其返回值表示是否中断后续操作
     * 返回 true 表示继续向下执行，返回 false 表示中断后续操作
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        String clientIp = WebUtils.getClientIp();
        long ipv4ToLong = Ipv4Util.ipv4ToLong(clientIp, 0);
        if (ipv4ToLong == 0) {
            log.warn(" >>>>>>>>>>>>>>>>> Invalid client IP: {}", clientIp);
            return false;
        }
        List<IpWhitelist> ipWhitelists = cacheService.getIpWhiteListByType(IpWhiteListType.WEB_MANAGE);
        if (CollUtil.isEmpty(ipWhitelists)) {
            throw new BaseBusinessException("IP白名单配置未设置，请联系管理员");
        }
        for (IpWhitelist ipWhitelist : ipWhitelists) {
            if (ipv4ToLong <= ipWhitelist.getEndIpLong() && ipv4ToLong >= ipWhitelist.getStartIpLong()) {
                return true;
            }
        }
        //防止日志频率过快，导致日志被刷屏
        if (this.limitLogFrequency(clientIp)) {
            this.saveLogs(clientIp);
        }
        throw new BaseBusinessException("当前IP[" + clientIp + "]不在白名单，请联系管理员添加管理白名单");

    }

    /**
     * 限制日志频率
     * 五分钟内只允许记录一次日志
     *
     * @param clientIp
     * @return boolean
     */
    private boolean limitLogFrequency(String clientIp) {

        RRateLimiter rateLimiter = cacheService.getIpRateLimiter(clientIp);

        // 初始化限流器（只需初始化一次即可，Redisson 会自动维护）
        if (!rateLimiter.isExists()) {
            rateLimiter.trySetRate(
                    RateType.OVERALL,
                    1,                              // 允许最多x次
                    Duration.ofMinutes(5),         // 每x分钟为一个周期
                    Duration.ofMinutes(10)          // 如果x分钟内没有再请求，该限流器自动清除
            );

        }

        return rateLimiter.tryAcquire();
    }

    private void saveLogs(String clientIp) {
        LocalDeviceSuspectedLog suspectedLog = new LocalDeviceSuspectedLog();
        suspectedLog.setDeviceId(cacheService.cacheLocalDeviceId());
        suspectedLog.setRisk(2);
        suspectedLog.setEventType(3);
        suspectedLog.setMsg(clientIp + "异常访问管理端");
        suspectedLog.setTime(new Date());
        localDeviceSuspectedLogRepository.save(suspectedLog);
    }


}
