package com.superred.supervisor.manager.model.vo.policy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.superred.common.core.model.PageReqDTO;
import com.superred.supervisor.manager.utils.PolicyUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-04-07 10:28
 */
@Data

public class RuleAuditAppBehaviorPageReq extends PageReqDTO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "策略下发版本")
    private String version;

    @Schema(description = "策略下发ID")
    private String policyId;

    @Schema(description = "策略下发设备类型 05 终端；01 检测器")
    private String issueDeviceType;

    @TableId(value = "rule_id", type = IdType.AUTO)
    @Schema(description = "策略ID")
    private String ruleId;

    @Schema(description = "策略名称")
    private String ruleName;

    @Schema(description = "策略描述")
    private String ruleDesc;

    @Schema(description = "IP地址 每个数组元素类型为: 单个 IP,IP 子网，IP 范围类型。0.0.0.0/0 表示 ANY")
    private String ip;

    @Schema(description = "审计行为类型 1 WEB行为 2 DNS 行为 3 SSL/TLS 行为， 4 数据库操作行为 5 文件传输行为 6 控制行为 7 登录行为 8 邮件行为 9 风险服务和软件行为 99 其它")
    private String auditType;

    @Schema(description = "策略失效时间")
    private String expireTime = PolicyUtils.handleExpireTime(24);

    @Schema(description = "JSON字符串 过滤规则参数")
    private String param;

    @Schema(description = "规则应用状态，0未应用，1已应用")
    private String status;

    @Schema(description = "是否共享")
    private String isShare;

    @Schema(description = "策略来源 1 本级 2上级")
    private String ruleSource;

    @Schema(description = "平台级别")
    private String level;

    @Schema(description = "规则更新时间")
    private String updateTime;

    @Schema(description = "策略创建时间")
    private String createTime;

    @Schema(description = "扩展字段1")
    private Long ext1;

    @Schema(description = "扩展字段2")
    private String ext2;

    @Schema(description = "扩展字段3")
    private String ext3;

    @Schema(description = "上级共享策略ID")
    private String upRuleId;

    @Schema(description = "开始时间")
    private String startDate;

    @Schema(description = "结束时间")
    private String endDate;

}
