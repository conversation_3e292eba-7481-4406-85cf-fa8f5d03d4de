package com.superred.supervisor.common.repository.agent;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.agent.AgentSystemAudit;
import com.superred.supervisor.common.mapper.agent.AgentSystemAuditMapper;
import org.springframework.stereotype.Repository;

/**
 * 系统审计日志 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-24 11:31:49
 */
@Repository
public class AgentSystemAuditRepository extends ServiceImpl<AgentSystemAuditMapper, AgentSystemAudit> {

}

