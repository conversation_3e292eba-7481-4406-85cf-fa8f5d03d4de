package com.superred.supervisor.manager.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.manager.model.vo.system.log.SyslogReq;
import com.superred.supervisor.manager.model.vo.system.log.SyslogResp;
import com.superred.supervisor.manager.service.system.SyslogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 *  系统日志
 * @since 2025年06月30日
 */
@Slf4j
@Tag(name = "2.2 系统日志")
@RestController
@AllArgsConstructor
@RequestMapping("/syslog")
public class SyslogController {

    @Resource
    private SyslogService syslogService;


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1 分页查询")
    @PostMapping("/list")
    public RPage<SyslogResp> getSyslogByPage(@Valid @RequestBody SyslogReq req) {
        IPage<SyslogResp> syslogByPage = this.syslogService.getSyslogByPage(req);
        return new RPage<>(syslogByPage);

    }

    @ApiOperationSupport(order = 1)
    @Operation(summary = "2 获取日志类型")
    @PostMapping("/log_type")
    public R<List<String>> getLogType() {
        List<String> logType = this.syslogService.getLogType();
        return R.success(logType);
    }
}
