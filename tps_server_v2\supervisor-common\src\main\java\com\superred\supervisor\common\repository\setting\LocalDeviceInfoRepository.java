package com.superred.supervisor.common.repository.setting;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.setting.LocalDeviceInfo;
import com.superred.supervisor.common.mapper.setting.LocalDeviceInfoMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class LocalDeviceInfoRepository extends ServiceImpl<LocalDeviceInfoMapper, LocalDeviceInfo> {


    public LocalDeviceInfo getLocalDeviceInfo() {
        return this.getOne(Wrappers.<LocalDeviceInfo>lambdaQuery().last("limit 1"));
    }
}
