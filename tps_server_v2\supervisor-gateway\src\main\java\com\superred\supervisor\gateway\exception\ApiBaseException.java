package com.superred.supervisor.gateway.exception;

import com.superred.supervisor.common.model.resp.ApiResponse;
import lombok.Getter;

/**
 * 网关接口统一异常
 *
 * <AUTHOR>
 * @since 2025/5/19 15:26
 */
@Getter
public class ApiBaseException extends RuntimeException {

    private final Integer code;
    private final String msg;

    public ApiBaseException(String message) {
        super(message);
        this.msg = message;
        this.code = ApiResponse.FAIL_CODE;
    }

    public ApiBaseException(String message, Throwable throwable) {
        super(message, throwable);
        this.msg = message;
        this.code = ApiResponse.FAIL_CODE;
    }
}
