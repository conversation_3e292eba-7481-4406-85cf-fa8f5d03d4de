package com.superred.supervisor.common.entity.agent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.superred.supervisor.common.entity.agent.enums.AgentAuditType;
import com.superred.supervisor.common.entity.agent.enums.AgentStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端设备信息 实体
 *
 * <AUTHOR>
 * @since 2025-03-18 13:38:01
 */
@Data
@TableName("agent_device_info")
public class AgentDeviceInfo {


    /**
     * 设备编号
     */
    @TableId(value = "device_id", type = IdType.AUTO)
    private String deviceId;

    /**
     * 终端计算机名
     */
    @TableField(value = "device_name")
    private String deviceName;

    /**
     * 全数字组成的最长为2个字节的字符串，检测器为“01”
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 前八位为年月日，下划线后自定义
     */
    @TableField("soft_version")
    private String softVersion;

    /**
     * 设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值
     */
    @TableField("interface")
    private String interfaces;

    /**
     * 内存总数，表示整个设备的内存大小，单位MB。
     */
    @TableField("mem_total")
    private Long memTotal;

    /**
     * CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。
     physical_id：CPU ID，数值类型
     core：CPU核心数，数值类型；
     clock：CPU主频，数值类型精确到小数点后1位

     */
    @TableField("cpu_info")
    private String cpuInfo;

    /**
     * 磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。
     size为数值类型，表示磁盘大小，单位GB；
     serial为字符串类型，最长64个字节，表示磁盘序列号
     */
    @TableField("disk_info")
    private String diskInfo;

    /**
     * 检测器部署的地理位置
     */
    @TableField("address")
    private String address;

    /**
     * 行政区域编码类型，表示检测器部署所在地的区域编码。
     */
    @TableField("address_code")
    private String addressCode;

    /**
     * 注册时间
     */
    @TableField("register_time")
    private LocalDateTime registerTime;

    /**
     * 终端状态 0 在线，1 审核失败，2 待审核，3 禁用 4 离线 5 注销 6已卸载
     */
    @TableField("register_status")
    private AgentStatus registerStatus;

    /**
     * 注册状态描述
     */
    @TableField("register_message")
    private String registerMessage;

    /**
     * 审核时间
     */
    @TableField("verify_time")
    private LocalDateTime verifyTime;

    /**
     * 备注信息
     */
    @TableField("memo")
    private String memo;

    /**
     * 心跳时间
     */
    @TableField("heartbeat_time")
    private LocalDateTime heartbeatTime;

    /**
     * 上报类型
     */
    @TableField("report_type")
    private String reportType;

    /**
     * ip地址
     */
    @TableField("ip")
    private String ip;

    /**
     * mac地址
     */
    @TableField("mac")
    private String mac;

    /**
     * 使用人编号
     */
    @TableField("user_id")
    private String userId;

    /**
     * 使用人姓名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 主机名称
     */
    @TableField("host_name")
    private String hostName;

    /**
     * 主机操作系统
     */
    @TableField("os")
    private String os;

    /**
     * 主机CPU架构
     */
    @TableField("arch")
    private String arch;

    /**
     * 最后升级时间
     */
    @TableField("last_upgrade_time")
    private LocalDateTime lastUpgradeTime;

    /**
     * 升级包使用的操作系统字段
     */
    @TableField("update_os")
    private String updateOs;

    /**
     * 升级包使用的CPU架构
     */
    @TableField("update_arch")
    private String updateArch;

    /**
     * 密级
     */
    @TableField("secret")
    private String secret;

    /**
     * 拓展字段
     */
    @TableField("extended_fields")
    private String extendedFields;

    /**
     * 1 自动审核 2人工审核
     */
    @TableField("audit_type")
    private AgentAuditType auditType;

    /**
     * 区域路径
     */
    @TableField("region_path")
    private String regionPath;

    /**
     * 机构id 来自用户
     */
    @TableField("org_id")
    private Integer orgId;

}

