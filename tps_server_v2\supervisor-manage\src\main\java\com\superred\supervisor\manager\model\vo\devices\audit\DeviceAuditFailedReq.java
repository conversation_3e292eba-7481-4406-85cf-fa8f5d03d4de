package com.superred.supervisor.manager.model.vo.devices.audit;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 审核请求
 *
 * <AUTHOR>
 * @since 2025/3/12 14:34
 */
@Data

public class DeviceAuditFailedReq {


    /**
     * 注册状态描述，0（成功），1（失败），2（审核中）
     */
    @Schema(description = "注册状态描述，0（成功），1 失败（需从页面录入），2（审核中）")
    @NotBlank(message = "驳回原因不能为空")
    private String regMessage;

    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */
    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    @NotBlank(message = "设备编号不能为空")
    private String deviceId;
}
