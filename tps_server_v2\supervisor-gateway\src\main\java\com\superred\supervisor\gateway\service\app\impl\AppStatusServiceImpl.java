package com.superred.supervisor.gateway.service.app.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.entity.app.AppAgentInfo;
import com.superred.supervisor.common.entity.app.AppSystemStatus;
import com.superred.supervisor.common.repository.app.AppAgentInfoRepository;
import com.superred.supervisor.common.repository.app.AppSystemStatusRepository;
import com.superred.supervisor.gateway.exception.ApiBaseException;
import com.superred.supervisor.gateway.model.app.status.AppBusinessStatusReq;
import com.superred.supervisor.gateway.model.app.status.AppSystemAuditReq;
import com.superred.supervisor.gateway.model.app.status.AppSystemStatusReq;
import com.superred.supervisor.gateway.service.app.AppStatusService;
import com.superred.supervisor.gateway.utils.AgentAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/6/30 16:01
 */
@Service
@Slf4j
public class AppStatusServiceImpl implements AppStatusService {

    @Resource
    private AppSystemStatusRepository appSystemStatusRepository;

    @Resource
    private AppAgentInfoRepository appAgentInfoRepository;


    /**
     * 上报APP系统状态
     * 运行状态上报 认证成功上报一次 之后每小时一次
     *
     * @param req 请求参数
     */
    @Override
    public void reportSystemStatus(AppSystemStatusReq req) {

        String deviceId = AgentAuthUtils.getDeviceIdFromRequest();
        AppAgentInfo appAgentInfo = appAgentInfoRepository.getById(deviceId);
        if (appAgentInfo == null) {
            throw new ApiBaseException("设备不存在");
        }

        appSystemStatusRepository.remove(Wrappers.<AppSystemStatus>lambdaQuery().eq(AppSystemStatus::getDeviceId, deviceId));

        AppSystemStatus agentSystemStatus = new AppSystemStatus();
        agentSystemStatus.setDeviceId(deviceId);
        agentSystemStatus.setCpu(JsonUtil.toJson(req.getCpu()));
        agentSystemStatus.setMem(req.getMem());
        agentSystemStatus.setDisk(req.getDisk());
        agentSystemStatus.setDisk(req.getDisk());
        agentSystemStatus.setTime(LocalDateTime.now());

        appSystemStatusRepository.save(agentSystemStatus);

    }

    /**
     * 上报APP业务状态
     *
     * @param req 请求参数列表
     */
    @Override
    public void reportBusinessStatus(AppBusinessStatusReq req) {
        throw new UnsupportedOperationException("业务状态上报功能未实现");
    }

    /**
     * 上报APP系统审计信息
     *
     * @param req 请求参数
     * @return ApiRes 返回结果
     */
    @Override
    public void reportSystemAudit(List<AppSystemAuditReq> req) {
        throw new UnsupportedOperationException("系统审计上报功能未实现");

    }
}
