package com.superred.common.core.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;

/**
 * 缓存请求体打印 请求日志
 *
 * <AUTHOR>
 * @since 2025/7/15 13:50
 */
@Slf4j
public class RequestLoggingFilter extends OncePerRequestFilter {
    private static final String UA_PATTERN = "^\\d+/[a-zA-Z0-9._-]+[\\\\(（][a-zA-Z0-9_-]+[\\\\)）]$";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        // 判断是否是 multipart（文件上传）请求，跳过日志打印

        boolean isMultipart = this.isFileUploadRequest(request);

        HttpServletRequest requestToUse = request;
        if (!isMultipart) {
            requestToUse = new ContentCachingRequestWrapper(request);
        }

        Instant start = Instant.now();

        try {
            filterChain.doFilter(requestToUse, response);
        } finally {
            Duration duration = Duration.between(start, Instant.now());

            String method = request.getMethod();
            String uri = request.getRequestURI();
            String query = request.getQueryString();

            int status = response.getStatus();

            StringBuilder logBuilder = new StringBuilder();
            logBuilder.append(String.format("[%-6s %s%s] Status: %d, Cost: %dms",
                    method,
                    uri,
                    query != null ? "?" + query : "",
                    status,
                    duration.toMillis())).append("\n");

            String agent = request.getHeader(HttpHeaders.USER_AGENT);
            if (agent != null && agent.matches(UA_PATTERN)) {
                logBuilder.append("User-Agent: ").append(agent).append("\n");
            }
            String fileDesc = request.getHeader("Content-Filedesc");
            if (fileDesc != null) {
                logBuilder.append("Content-Filedesc: ").append(fileDesc).append("\n");
            }

            if (requestToUse instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper cachingRequest = (ContentCachingRequestWrapper) requestToUse;

                byte[] content = cachingRequest.getContentAsByteArray();
                if (content.length > 0) {
                    String body = new String(content, request.getCharacterEncoding() != null
                            ? Charset.forName(cachingRequest.getCharacterEncoding())
                            : StandardCharsets.UTF_8);
                    logBuilder.append("Request Body: ").append(body).append("\n");
                }
            }
            log.debug("\n{}", logBuilder);
        }
    }

    private boolean isFileUploadRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        if (contentType != null && contentType.startsWith(MediaType.MULTIPART_FORM_DATA_VALUE)) {
            return true;
        }
        return request instanceof MultipartHttpServletRequest;

    }
}
