#!/bin/bash

systemctl restart firewalld.service
firewall-cmd --set-log-denied=all

firewall-cmd --set-default-zone=public

# 重新加载配置
sudo firewall-cmd --reload

sudo tee /etc/rsyslog.d/10-firewalld.conf <<EOF
kern.* /opt/wlh/supervisor/log/firewall_all.log
EOF

# 重启 rsyslog
sudo systemctl restart rsyslog


sudo tee /etc/logrotate.d/firewall <<EOF
/opt/wlh/supervisor/log/firewall_*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 root root
}
EOF

