#!/bin/bash

set -e

# 检查参数
if [ $# -lt 2 ]; then
  echo "用法: $0 <签名输出目录> <文件夹1> [文件夹2] ..."
  exit 1
fi

SIGN_DIR=$(realpath "$1")
shift
INPUT_DIRS=("$@")
DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
JSON_FILE="${SIGN_DIR}/signature_map.json"
JSON_SIG_FILE="${SIGN_DIR}/signature_map.json.sig"
PRIVATE_KEY="private_key.pem"

# 创建签名输出目录
mkdir -p "$SIGN_DIR"

# 初始化 JSON 内容
echo "{" > "$JSON_FILE"
echo "  \"created_at\": \"${DATE}\"," >> "$JSON_FILE"
echo "  \"files\": [" >> "$JSON_FILE"
FIRST_ENTRY=true

for DIR in "${INPUT_DIRS[@]}"; do
  find "$DIR" -type f -regex '.*\.\(sh\|jar\|sql\|so\)$' | while read -r FILE; do
    RELATIVE_HASH=$(echo "$FILE" | sha256sum | awk '{print $1}')
    SIG_NAME="${RELATIVE_HASH}.sig"
    SIG_PATH="${SIGN_DIR}/.${SIG_NAME}"

    echo "[INFO] 正在签名: $FILE → $SIG_PATH"
    openssl dgst -sha256 -sign "$PRIVATE_KEY" -out "$SIG_PATH" "$FILE"

    # 添加 JSON 条目
    if [ "$FIRST_ENTRY" = true ]; then
      FIRST_ENTRY=false
    else
      echo "," >> "$JSON_FILE"
    fi

    echo "    {" >> "$JSON_FILE"
    echo "      \"file\": \"$(echo "$(realpath "$FILE")" | awk -F'/opt' '{print "/opt" $NF}')\"," >> "$JSON_FILE"
    echo "      \"signature\": \"$(echo "$(realpath "$SIG_PATH")" | awk -F'/opt' '{print "/opt" $NF}')\"" >> "$JSON_FILE"
    echo -n "    }" >> "$JSON_FILE"
  done
done

echo "" >> "$JSON_FILE"
echo "  ]" >> "$JSON_FILE"
echo "}" >> "$JSON_FILE"

# 对 JSON 文件本身进行签名
echo "[INFO] 正在签名 manifest JSON: $JSON_FILE → $JSON_SIG_FILE"
openssl dgst -sha256 -sign "$PRIVATE_KEY" -out "$JSON_SIG_FILE" "$JSON_FILE"

echo "[✔] 所有文件签名完成，JSON 文件签名完成："
echo "    - 文件列表: $JSON_FILE"
echo "    - JSON 签名: $JSON_SIG_FILE"

