package com.superred.supervisor.manager.model.dto.policy.config;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleAttackBlacklistIp;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-28 11:15
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AgentBlackIpPolicyConfigDTO {

    /**
     * 策略ID
     */
    private Long ruleId;

    /**
     * 对外访问的IP地址
     */
    private String ip;

    /**
     * 端口范围
     */
    private String port;

    /**
     * 通信协议  6表示TCP、17表示UDP、0表示无限制。
     */
    private Integer protocol;

    /**
     * 告警级别，0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）。
     */
    private Integer risk;

    /**
     * 攻击组织
     */
    private String attackGroup;

    /**
     * 攻击阶段 ID 1 侦查扫描 2 攻击渗透 3 样本投递 4 持续控制 5 横向移动 6 数据窃取 99 其它
     */
    private Integer attackStage;

    /**
     * 策略名称
     */
    private String ruleName;

    /**
     * 攻击设施类型ID 1 密码爆破 2 漏洞扫描 3 样本分发 4 恶意发邮 5 钓鱼网站 6 信息搜集 7 数据窃取 8 命令控制 99 其它
     */
    private List<Integer> facilityType;


    /**
     * 描述信息
     */
    private String desc;

    /**
     * 攻击分类 1. 窃密木马 2. 远控木马 3. 电脑病毒 4. 僵尸网络 5. 网络蠕虫 6. 间谍软件 7. 挖矿木马 8. 黑客工具 9. 勒索软件 10. 恶意文档 11. 后门程序 99. 其它
     */
    private Integer attackClass;

    public static AgentBlackIpPolicyConfigDTO getPolicyConfig(RuleAttackBlacklistIp blacklistIp) {
        if (blacklistIp == null) {
            return null;
        }
        return AgentBlackIpPolicyConfigDTO.builder()
                .ruleId(Long.parseLong(blacklistIp.getRuleId()))
                .ip(PolicyUtils.handleStrNull(blacklistIp.getSip()))
                .port(PolicyUtils.handleStrNull(blacklistIp.getSport()))
                .protocol(PolicyUtils.strToInt(blacklistIp.getProtocol()))
                .risk(PolicyUtils.strToInt(blacklistIp.getRisk()))
                .attackGroup(PolicyUtils.handleStrNull(blacklistIp.getAttackGroup()))
                .attackStage(PolicyUtils.strToInt(blacklistIp.getAttackStage()))
                .ruleName(PolicyUtils.handleStrNull(blacklistIp.getRuleName()))
                .facilityType(PolicyUtils.strToIntList(blacklistIp.getFacilityType()))
                .desc(PolicyUtils.handleStrNull(blacklistIp.getDesc()))
                .attackClass(PolicyUtils.strToInt(blacklistIp.getAttackClass()))
                .build();
    }
}
