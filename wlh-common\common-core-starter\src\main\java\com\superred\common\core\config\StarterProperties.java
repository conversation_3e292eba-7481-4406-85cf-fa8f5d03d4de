package com.superred.common.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import java.util.List;


/**
 *  starter配置类
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Data
@EnableConfigurationProperties(StarterProperties.class)
@ConfigurationProperties(prefix = "superred.common.starter")
public class StarterProperties {


    /**
     * 是否启用默认异常处理器
     */
    private Boolean enableDefaultExceptionHandler;

    /**
     * 是否启用安全header过滤器
     */
    private Boolean enableSecurityHeaderFilter;

    /**
     * 是否启用请求日志打印功能
     */
    private Boolean enableLoggingFilter;

    /**
     *  可以将application.yaml 中的配置注入到JVM环境变量，供静态类，非bean模式类中使用
     *
     *  需要注入到系统属性的键列表
     *  注意：仅支持 String 类型property
     *  System.setProperty
     */
    private List<String> injectKeys;

}
