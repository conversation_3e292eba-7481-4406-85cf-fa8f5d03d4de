package com.superred.supervision.base.vo.device;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/6/17 13:37
 **/
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SubModuleVo implements Serializable {

    private String name;

    private String status;

    private String innerPolicy;

    private Map<String, String> version;

    private Integer recordDelayednum;

    private Integer fileDelayednum;

    /**
     * 24小时产生告警数
     */
    @JsonProperty("record_24h_num")
    private Integer record24hNum;
}
