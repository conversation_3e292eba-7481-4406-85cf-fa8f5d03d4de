package com.superred.supervision.base.vo.command;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.ToString;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

/**
 * <AUTHOR>
 * @since 2022/6/20 10:26
 **/
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(NON_NULL)
@ToString
public class CommandResultVo {

    private String time;

    private String type;

    private String cmd;
    @JsonProperty(value = "cmd_id")
    private String cmdId;

    /**
     * 数值类型，取值为：0（成功）、1（失败
     */
    private Integer result;

    private String message;

    private Object detail;
}
