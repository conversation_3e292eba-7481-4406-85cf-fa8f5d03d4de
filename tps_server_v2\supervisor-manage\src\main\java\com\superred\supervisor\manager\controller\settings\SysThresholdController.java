package com.superred.supervisor.manager.controller.settings;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.settings.SysCacheDataThresholdReq;
import com.superred.supervisor.manager.model.vo.settings.SysDataStorageTimeThresholdReq;
import com.superred.supervisor.manager.model.vo.settings.SysDetectorOfflineThresholdReq;
import com.superred.supervisor.manager.model.vo.settings.SysExceptionThresholdReq;
import com.superred.supervisor.manager.model.vo.settings.SysFlowExceptionThresholdReq;
import com.superred.supervisor.manager.model.vo.settings.SysThresholdResp;
import com.superred.supervisor.manager.model.vo.settings.SysVerifyJarSignerThresholdReq;
import com.superred.supervisor.manager.service.system.SystemSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * 系统阈值
 * @since 2025年03月14日
 */

@Tag(name = "5.3 系统阈值设置")
@RestController
@RequestMapping("/sys/config")
public class SysThresholdController {


    @Resource
    private SystemSettingService systemSettingService;

    @ApiOperationSupport(order = 1)
    @Operation(summary = "1.获取系统阈值")
    @GetMapping("/sys_threshold_value/get")
    public R<SysThresholdResp> getSysThresholdValue() {
        SysThresholdResp sysThresholdValue = systemSettingService.getSysThresholdValue();
        return R.success(sysThresholdValue);
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2.系统异常阈值设置")
    @PostMapping("/sys_threshold_value/set")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "系统异常阈值设置")
    public R<Boolean> setSysThresholdValue(@RequestBody SysExceptionThresholdReq req) {
        Boolean b = systemSettingService.setSysThresholdValue(req);
        return R.success(b);
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "3.缓存数据阈值设置")
    @PostMapping("/cache_threshold_value/set")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "缓存数据阈值设置")
    public R<Boolean> setCacheResholdValue(@RequestBody SysCacheDataThresholdReq req) {
        Boolean b = systemSettingService.setCacheResholdValue(req);
        return R.success(b);
    }


    @ApiOperationSupport(order = 4)
    @Operation(summary = "4.流量异常阈值设置")
    @PostMapping("/flow_threshold_value/set")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "流量异常阈值设置")
    public R<Boolean> setFlowrateResholdValue(@RequestBody SysFlowExceptionThresholdReq req) {
        Boolean b = systemSettingService.setFlowrateResholdValue(req);
        return R.success(b);
    }

    @ApiOperationSupport(order = 5)
    @Operation(summary = "5.数据存储时长设置")
    @PostMapping("/datastorage/set")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "设置数据存储设置")
    public R<Boolean> setDataStorage(@RequestBody SysDataStorageTimeThresholdReq req) {
        Boolean b = systemSettingService.setDataStorage(req);
        return R.success(b);
    }

    @ApiOperationSupport(order = 6)
    @Operation(summary = "6.签名校验周期设置")
    @PostMapping("/jar_verify_singer/set")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "签名校验周期设置")
    public R<Boolean> setJarVerifySinger(@RequestBody SysVerifyJarSignerThresholdReq req) {
        Boolean b = systemSettingService.setJarVerifySinger(req);
        return R.success(b);
    }


    @ApiOperationSupport(order = 7)
    @Operation(summary = "7.监测器离线超时设置")
    @PostMapping("/detector_offline_duration/set")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "监测器离线超时设置")
    public R<Boolean> setDetectorOffline(@RequestBody SysDetectorOfflineThresholdReq req) {
        Boolean b = systemSettingService.setDetectorOffline(req);
        return R.success(b);
    }


}
