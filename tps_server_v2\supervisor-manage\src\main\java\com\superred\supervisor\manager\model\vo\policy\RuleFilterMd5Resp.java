package com.superred.supervisor.manager.model.vo.policy;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.common.entity.policy.RuleFilterMd5;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025-03-11 15:55
 */
@Data

@Builder
public class RuleFilterMd5Resp {

    private static final long serialVersionUID = 1L;

    private Long id;

    @Schema(description = "策略ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ruleId;

    @Schema(description = "策略内容")
    @NotBlank(message = "策略内容 不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9]+$", message = "策略内容 格式错误")
    private String ruleContent;

    @Schema(description = "策略描述")
    private String ruleDesc;

    @Schema(description = "告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级")
    private Integer risk;

    @Schema(description = "策略应用状态，0未应用，1已应用")
    private Integer status;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "监测器策略数量")
    private Integer detectorPolicyCount;

    public static RuleFilterMd5Resp fromRuleFilterMd5(RuleFilterMd5 ruleFilterMd5) {
        return RuleFilterMd5Resp.builder()
                .id(ruleFilterMd5.getId())
                .ruleId(ruleFilterMd5.getRuleId())
                .ruleContent(ruleFilterMd5.getRuleContent())
                .ruleDesc(ruleFilterMd5.getRuleDesc())
                .risk(ruleFilterMd5.getRisk())
                .status(ruleFilterMd5.getStatus())
                .updateTime(ruleFilterMd5.getUpdateTime())
                .build();
    }
}
