package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.db.entity.CountStatisticsError;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
public interface CountStatisticsErrorService extends IService<CountStatisticsError> {

    List<CountStatisticsError> findByTime(String startTime, String endTime);

    void deleteByTime(String startTime, String endTime);
}
