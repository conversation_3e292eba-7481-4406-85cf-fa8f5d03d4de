package com.superred.supervisor.common.entity.system.enums;

import com.superred.supervisor.common.constant.log.LogTypeConstants;
import lombok.Getter;

/**
 * <AUTHOR>
 * 日志类型枚举
 * @since 2025年07月03日
 */
@Getter
public enum LogTypeEnum {


    USER_MANAGEMENT("userManagement", LogTypeConstants.USER_MANAGEMENT),
    DEPARTMENT_MANAGEMENT("departmentManagement", LogTypeConstants.DEPARTMENT_MANAGEMENT),
    ROLES("roles", LogTypeConstants.ROLES),
    TERMINAL_PERSONNEL_MANAGEMENT("terminalPersonnelManagement", LogTypeConstants.TERMINAL_PERSONNEL_MANAGEMENT),
    SYSTEM_AUTHORIZATION("systemAuthorization", LogTypeConstants.SYSTEM_AUTHORIZATION),
    SYSTEM_CONFIGURATION("systemConfiguration", LogTypeConstants.SYSTEM_CONFIGURATION),
    SYSTEM_MAINTENANCE("systemMaintenance", LogTypeConstants.SYSTEM_MAINTENANCE),
    UKEY_MANAGEMENT("ukeyManagement", LogTypeConstants.UKEY_MANAGEMENT),
    SYSTEM_UPGRADE_FILE_MANAGEMENT("systemUpgradeFileManagement", LogTypeConstants.SYSTEM_UPGRADE_FILE_MANAGEMENT),
    TERMINAL_CONFIDENTIALITY_COMPONENT_DOWNLOAD_MANAGEMENT("terminalConfidentialityComponentDownloadManagement", LogTypeConstants.TERMINAL_CONFIDENTIALITY_COMPONENT_DOWNLOAD_MANAGEMENT),
    ACCESS_DEVICE_MANAGEMENT("accessDeviceManagement", LogTypeConstants.ACCESS_DEVICE_MANAGEMENT),
    TERMINAL_DEVICE_REPORTING_MANAGEMENT("terminalDeviceReportingManagement", LogTypeConstants.TERMINAL_DEVICE_REPORTING_MANAGEMENT),
    TERMINAL_SOFTWARE("terminalSoftware", LogTypeConstants.TERMINAL_SOFTWARE),
    TERMINAL_STRATEGY_ISSUANCE("terminalStrategyIssuance", LogTypeConstants.TERMINAL_STRATEGY_ISSUANCE),
    MONITOR_STRATEGY_ISSUANCE("monitorStrategyIssuance", LogTypeConstants.MONITOR_STRATEGY_ISSUANCE),
    TERMINAL_COMMAND_ISSUANCE("terminalCommandIssuance", LogTypeConstants.TERMINAL_COMMAND_ISSUANCE),
    DETECTOR_INSTRUCTION_ISSUANCE("detectorInstructionIssuance", LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE),
    DOMAIN_BLACKLIST_DETECTION_STRATEGY("domainBlacklistDetectionStrategy", LogTypeConstants.DOMAIN_BLACKLIST_DETECTION_STRATEGY),
    IP_BLACKLIST_DETECTION_STRATEGY("ipBlacklistDetectionStrategy", LogTypeConstants.IP_BLACKLIST_DETECTION_STRATEGY),
    URL_BLACKLIST_DETECTION_STRATEGY("urlBlacklistDetectionStrategy", LogTypeConstants.URL_BLACKLIST_DETECTION_STRATEGY),
    MALICIOUS_FILE_DETECTION_STRATEGY("maliciousFileDetectionStrategy", LogTypeConstants.MALICIOUS_FILE_DETECTION_STRATEGY),
    PENETRATION_ATTACK_DETECTION_STRATEGY("penetrationAttackDetectionStrategy", LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY),
    TROJAN_ACTIVITY_DETECTION_STRATEGY("trojanActivityDetectionStrategy", LogTypeConstants.TROJAN_ACTIVITY_DETECTION_STRATEGY),
    APPLICATION_BEHAVIOR_AUDIT_STRATEGY("applicationBehaviorAuditStrategy", LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY),
    EQUIPMENT_INFORMATION_COLLECTION_STRATEGY("equipmentInformationCollectionStrategy", LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY),
    ACCOUNT_FILTERING_STRATEGY("accountFilteringStrategy", LogTypeConstants.ACCOUNT_FILTERING_STRATEGY),
    KEYWORD_STRATEGY("keywordStrategy", LogTypeConstants.KEYWORD_STRATEGY),
    FILE_MD5_STRATEGY("fileMd5Strategy", LogTypeConstants.FILE_MD5_STRATEGY),
    RISK_SOFTWARE_USAGE_STRATEGY("riskSoftwareUsageStrategy", LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY);

    private final String code;    // 英文标识
    private final String desc;    // 中文描述

    LogTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // 根据code获取枚举对象
    public static LogTypeEnum fromCode(String code) {
        for (LogTypeEnum type : LogTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}