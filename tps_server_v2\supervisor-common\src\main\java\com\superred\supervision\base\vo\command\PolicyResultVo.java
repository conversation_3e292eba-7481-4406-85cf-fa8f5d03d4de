package com.superred.supervision.base.vo.command;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 策略响应vo
 * <AUTHOR>
 * @since 2022/6/20 13:45
 **/
@Data
@ToString
public class PolicyResultVo {
    private String time;

    private String type;

    private String module;

    private String cmd;

    private String version;

    private List<String> success;

    private List<Object> fail;


}
