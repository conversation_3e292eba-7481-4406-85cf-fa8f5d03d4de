package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleThreeEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleAttackBlacklistIp;
import com.superred.supervisor.common.repository.policy.RuleAttackBlacklistIpRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.constant.RuleSourceTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.config.BlackIpPolicyConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackBlacklistIpPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackBlacklistIpReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackBlacklistIpResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleAttackBlacklistIpService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-12 16:28
 */
@Slf4j
@Service("ruleAttackBlacklistIpService")
@AllArgsConstructor
public class RuleAttackBlacklistIpServiceImpl implements RuleAttackBlacklistIpService, RuleService {

    @Resource
    private RuleAttackBlacklistIpRepository ruleAttackBlacklistIpRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    @Override
    public IPage<RuleAttackBlacklistIpResp> page(RuleAttackBlacklistIpPageReq ruleAttackBlacklistIp) {
        List<String> ruleIdList = this.getRuleIdList(ruleAttackBlacklistIp);
        LambdaQueryWrapper<RuleAttackBlacklistIp> queryWrapper = new LambdaQueryWrapper<RuleAttackBlacklistIp>()
                .le(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getEndDate()), RuleAttackBlacklistIp::getUpdateTime, ruleAttackBlacklistIp.getEndDate())
                .ge(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getStartDate()), RuleAttackBlacklistIp::getUpdateTime, ruleAttackBlacklistIp.getStartDate())
                .like(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getRuleId()), RuleAttackBlacklistIp::getRuleId, ruleAttackBlacklistIp.getRuleId())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getSip()), RuleAttackBlacklistIp::getSip, ruleAttackBlacklistIp.getSip())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getSport()), RuleAttackBlacklistIp::getSport, ruleAttackBlacklistIp.getSport())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getDip()), RuleAttackBlacklistIp::getDip, ruleAttackBlacklistIp.getDip())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getDport()), RuleAttackBlacklistIp::getDport, ruleAttackBlacklistIp.getDport())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getProtocol()), RuleAttackBlacklistIp::getProtocol, ruleAttackBlacklistIp.getProtocol())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getRuleName()), RuleAttackBlacklistIp::getRuleName, ruleAttackBlacklistIp.getRuleName())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getAttackClass()), RuleAttackBlacklistIp::getAttackClass, ruleAttackBlacklistIp.getAttackClass())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getAttackGroup()), RuleAttackBlacklistIp::getAttackGroup, ruleAttackBlacklistIp.getAttackGroup())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getAttackStage()), RuleAttackBlacklistIp::getAttackStage, ruleAttackBlacklistIp.getAttackStage())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getFacilityType()), RuleAttackBlacklistIp::getFacilityType, ruleAttackBlacklistIp.getFacilityType())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getDesc()), RuleAttackBlacklistIp::getDesc, ruleAttackBlacklistIp.getDesc())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getRisk()), RuleAttackBlacklistIp::getRisk, ruleAttackBlacklistIp.getRisk())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getStatus()), RuleAttackBlacklistIp::getStatus, ruleAttackBlacklistIp.getStatus())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getIsShare()), RuleAttackBlacklistIp::getIsShare, ruleAttackBlacklistIp.getIsShare())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistIp.getRuleSource()), RuleAttackBlacklistIp::getRuleSource, ruleAttackBlacklistIp.getRuleSource())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleAttackBlacklistIp::getRuleId, ruleIdList)
                .ne(RuleAttackBlacklistIp::getRuleSource, RuleSourceTypeEnum.SUPER_POLICY_SHARE.getKey())
                .orderByDesc(RuleAttackBlacklistIp::getUpdateTime);
        Page<RuleAttackBlacklistIp> page = new Page<>(ruleAttackBlacklistIp.getStart(), ruleAttackBlacklistIp.getLimit());
        IPage<RuleAttackBlacklistIp> page1 = this.ruleAttackBlacklistIpRepository.page(page, queryWrapper);
        return page1.convert(RuleAttackBlacklistIpResp::fromRuleAttackBlacklistIp);
    }

    @Override
    public RuleAttackBlacklistIpResp getById(Long ruleId) {
        RuleAttackBlacklistIp ruleAttackBlacklistIp = this.ruleAttackBlacklistIpRepository.getById(ruleId);
        return RuleAttackBlacklistIpResp.fromRuleAttackBlacklistIp(ruleAttackBlacklistIp);
    }

    @Override
    public void save(RuleAttackBlacklistIpReq ruleAttackBlacklistIpReq) {
        RuleAttackBlacklistIp ruleAttackBlacklistIp = fromRuleAttackBlacklistIpReq(ruleAttackBlacklistIpReq);
        // 赋值ruleId
        ruleAttackBlacklistIp.setRuleId(this.ruleIdBuilder.buildRuleId().toString());
        ruleAttackBlacklistIp.setSport(this.handlePortRange(ruleAttackBlacklistIp.getSport()));
        ruleAttackBlacklistIp.setDport(this.handlePortRange(ruleAttackBlacklistIp.getDport()));
        this.ruleAttackBlacklistIpRepository.save(ruleAttackBlacklistIp);
    }


    @Override
    public void edit(RuleAttackBlacklistIpReq ruleAttackBlacklistIpReq) {
        RuleAttackBlacklistIp ruleAttackBlacklistIp = fromRuleAttackBlacklistIpReq(ruleAttackBlacklistIpReq);
        ruleAttackBlacklistIp.setSport(this.handlePortRange(ruleAttackBlacklistIp.getSport()));
        ruleAttackBlacklistIp.setDport(this.handlePortRange(ruleAttackBlacklistIp.getDport()));
        this.ruleAttackBlacklistIpRepository.update(ruleAttackBlacklistIp, Wrappers.<RuleAttackBlacklistIp>lambdaUpdate().eq(RuleAttackBlacklistIp::getRuleId, ruleAttackBlacklistIpReq.getRuleId()));
    }

    public static RuleAttackBlacklistIp fromRuleAttackBlacklistIpReq(RuleAttackBlacklistIpReq ruleAttackBlacklistIpReq) {
        return RuleAttackBlacklistIp.builder()
                .ruleId(ruleAttackBlacklistIpReq.getRuleId())
                .sip(ruleAttackBlacklistIpReq.getSip())
                .sport(ruleAttackBlacklistIpReq.getSport())
                .dip(ruleAttackBlacklistIpReq.getDip())
                .dport(ruleAttackBlacklistIpReq.getDport())
                .protocol(ruleAttackBlacklistIpReq.getProtocol())
                .ruleName(ruleAttackBlacklistIpReq.getRuleName())
                .attackClass(ruleAttackBlacklistIpReq.getAttackClass())
                .attackGroup(ruleAttackBlacklistIpReq.getAttackGroup())
                .attackStage(ruleAttackBlacklistIpReq.getAttackStage())
                .facilityType(ruleAttackBlacklistIpReq.getFacilityType())
                .desc(ruleAttackBlacklistIpReq.getDesc())
                .risk(ruleAttackBlacklistIpReq.getRisk())
                .status(ruleAttackBlacklistIpReq.getStatus())
                .isShare(ruleAttackBlacklistIpReq.getIsShare())
                .ruleSource(ruleAttackBlacklistIpReq.getRuleSource())
                .level(ruleAttackBlacklistIpReq.getLevel())
                .updateTime(ruleAttackBlacklistIpReq.getUpdateTime())
                .createTime(ruleAttackBlacklistIpReq.getCreateTime())
                .ext1(ruleAttackBlacklistIpReq.getExt1())
                .ext2(ruleAttackBlacklistIpReq.getExt2())
                .ext3(ruleAttackBlacklistIpReq.getExt3())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleAttackBlacklistIpRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 验证是否在使用
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleAttackBlacklistIp> list = this.ruleAttackBlacklistIpRepository.list(Wrappers.<RuleAttackBlacklistIp>lambdaQuery()
                .in(RuleAttackBlacklistIp::getRuleId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.equals(PolicyApplyStatusEnum.APPLY.getKey().toString(), item.getStatus())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
                Integer ruleSource = Integer.valueOf(item.getRuleSource());
                if (ruleSource != RuleSourceTypeEnum.LOCAL_POLICY.getKey()) {
                    throw new BaseBusinessException("存在上级策略，上级策略不允许被删除");
                }
            });
        }
    }

    /**
     * 这是处理端口前端保存的 端口范围，0 或空表示所有端口
     * 2-2 保存成 2
     * 2-23 保存成2-23
     *
     * @param portRange 2-2 或者 2-23 或者 空
     * @return 2或者2-23 或者空
     */
    public String handlePortRange(String portRange) {
        if (CharSequenceUtil.isBlank(portRange)) {
            return null;
        }
        List<String> ranges = CharSequenceUtil.split(portRange, "-", true, true);
        if (Objects.equals(ranges.get(0), ranges.get(1))) {
            return ranges.get(0);
        }
        return portRange;
    }

    /**
     * 获取规则ID列表
     * @param ruleAttackBlacklistIp
     * @return
     */
    private List<String> getRuleIdList(RuleAttackBlacklistIpPageReq ruleAttackBlacklistIp) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleAttackBlacklistIp.getIssueDeviceType())
                || StrUtil.isBlank(ruleAttackBlacklistIp.getPolicyId())
                || StrUtil.isBlank(ruleAttackBlacklistIp.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleAttackBlacklistIp.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleAttackBlacklistIp.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleAttackBlacklistIp.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleAttackBlacklistIp.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 攻击窃密检测 - 黑名单检测策略 - IP黑名单检测策略
        return StrUtil.equals(module, PolicyModuleThreeEnum.IP_BLACKLIST.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 攻击窃密检测 - 黑名单检测策略 - IP黑名单检测策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleThreeEnum.IP_BLACKLIST.getKey())
                .moduleStr(PolicyModuleThreeEnum.IP_BLACKLIST.getValue())
                .moduleParentStr(PolicyModuleOneEnum.ALARM.getValue() + " - " + PolicyModuleTwoEnum.BLACKLIST.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 攻击窃密检测 - 黑名单检测策略 - IP黑名单检测策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleAttackBlacklistIp> list = this.ruleAttackBlacklistIpRepository.list(Wrappers.<RuleAttackBlacklistIp>lambdaQuery()
                .in(RuleAttackBlacklistIp::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<BlackIpPolicyConfigDTO> configDTOS = list.stream().map(item -> {
            return BlackIpPolicyConfigDTO.getPolicyConfig(item);
        }).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 攻击窃密检测 - 黑名单检测策略 - IP黑名单检测策略
        this.ruleAttackBlacklistIpRepository.update(Wrappers.<RuleAttackBlacklistIp>lambdaUpdate()
                .in(RuleAttackBlacklistIp::getRuleId, ruleIds)
                .set(RuleAttackBlacklistIp::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 攻击窃密检测 - 黑名单检测策略 - IP黑名单检测策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleAttackBlacklistIp> list = this.ruleAttackBlacklistIpRepository.list(Wrappers.<RuleAttackBlacklistIp>lambdaQuery()
                .in(RuleAttackBlacklistIp::getRuleId, ruleIdList));
        List<RuleAttackBlacklistIpResp> respList = new ArrayList<>();
        list.forEach(item -> {
            RuleAttackBlacklistIpResp resp = RuleAttackBlacklistIpResp.fromRuleAttackBlacklistIp(item);
            respList.add(resp);
        });
        policyDetail.setBlacklistIpList(respList);
        return policyDetail;
    }
}
