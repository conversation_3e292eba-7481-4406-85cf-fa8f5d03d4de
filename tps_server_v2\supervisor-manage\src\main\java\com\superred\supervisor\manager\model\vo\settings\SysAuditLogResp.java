package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
@Data
public class SysAuditLogResp {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID")
    private String id;

    /**
     * 日志编号
     */
    @Schema(description = "日志编号")
    private String logId;

    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */
    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    private String deviceId;

    /**
     * 操作用户名，如“admin”
     */
    @Schema(description = "操作用户名，如“admin”")
    private String user;

    /**
     * 登录人IP
     */
    @Schema(description = "登录人IP")
    private String loginIp;

    /**
     * 审计日志类型，manage操作行为审计，platform响应管理平台，detector涉密处理事件，system系统事件审计，monitor本地策略事件
     */
    @Schema(description = "审计日志类型")
    private String eventType;


    @Schema(description = "操作类型")
    private String optType;

    /**
     * 事件产生时间
     */
    @Schema(description = "事件产生时间")
    private String time;

    /**
     * 时间内容，如"用户登录"
     */
    @Schema(description = "时间内容，如'用户登录'")
    private String message;

    /**
     * 是否已经上报，0-否，1-是
     */
    @Schema(description = "是否已经上报，0-否，1-是")
    private Integer isUpload;

    /**
     * 上传时间
     */
    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

    /**
     * 是否已读，0-未读，1-已读，默认是未读
     */
    @Schema(description = "是否已读，0-未读，1-已读，默认是未读")
    private Integer ifRead;

    /**
     * 角色
     */
    @Schema(description = "角色")
    private String roleDataType;


}
