package com.superred.supervisor.common.repository.policy;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.RuleAttackBlacklistIp;
import com.superred.supervisor.common.mapper.policy.RuleAttackBlacklistIpMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2025-03-12 16:27
 */
@Slf4j
@Repository
@AllArgsConstructor
public class RuleAttackBlacklistIpRepository extends ServiceImpl<RuleAttackBlacklistIpMapper, RuleAttackBlacklistIp> {
}
