package com.superred.supervisor.common.entity.operation.terminal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.superred.supervisor.common.entity.operation.enums.OperationExecStatus;
import com.superred.supervisor.common.entity.operation.enums.OperationType;
import lombok.Data;

/**
 * 终端操作详情表 实体
 *
 * <AUTHOR>
 * @since 2025-07-24 14:22:59
 */
@Data
@TableName("op_terminal_operation_exec")
public class TerminalOperationExec {


    /**
     * 详情ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作ID
     */
    @TableField("operation_id")
    private Long operationId;

    /**
     * 关联ID：当type=policy时为policy_code，当type=command时为cmd_code
     */
    @TableField("ref_id")
    private String refId;

    /**
     * 操作类型：policy-策略,command-指令
     */
    @TableField("operation_type")
    private OperationType operationType;

    /**
     * 策略版本号（快照数据）
     */
    @TableField("version")
    private String version;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 执行状态：0-待执行(未下发)，1-执行中(已下发)，2-执行成功，3-执行失败
     */
    @TableField("exec_status")
    private OperationExecStatus execStatus;

    /**
     * 执行结果（来自设备响应上报接口）：0-成功，1-失败
     */
    @TableField("exec_result")
    private Integer execResult;

    /**
     * 执行结果描述，失败时说明失败原因
     */
    @TableField("exec_message")
    private String execMessage;

    /**
     * 获取指令时间
     */
    @TableField("fetch_time")
    private LocalDateTime fetchTime;

    /**
     * 结果上报时间（对应原time字段）
     */
    @TableField("result_time")
    private LocalDateTime resultTime;

    /**
     * 成功执行的规则ID列表
     */
    @TableField("success_items")
    private String successItems;

    /**
     * 未成功执行的规则对象列表
     */
    @TableField("failed_items")
    private String failedItems;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

}

