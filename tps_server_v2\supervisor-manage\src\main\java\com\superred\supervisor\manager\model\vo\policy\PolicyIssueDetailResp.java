package com.superred.supervisor.manager.model.vo.policy;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-04-02 17:29
 */
@Data

public class PolicyIssueDetailResp {

    @Schema(description = "规则ID")
    private String ruleId;

    @Schema(description = "规则内容")
    private String ruleConfig;

    @Schema(description = "失败设备数量")
    private int failDeviceAmount;

    @Schema(description = "成功设备数量")
    private int successDeviceAmount;

    @Schema(description = "下发数量")
    private int issueDeviceAmount;

    @Schema(description = "成功设备列表")
    private List<PolicyIssueDeviceResp> successDeviceList;

    @Schema(description = "失败设备列表")
    private List<PolicyIssueDeviceResp> failDeviceList;

    @Schema(description = "下发详情")
    private List<PolicyIssueDeviceResp> issueDeviceList;
}
