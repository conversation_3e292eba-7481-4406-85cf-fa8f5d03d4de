<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.superred.supervisor.manager.mapper.command.IssueCommandExtMapper">

    <resultMap id="CmdStatisticsVOMap"
               type="com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsResp">
        <result column="cmd_id" property="cmdId"/>
        <result column="cmd" property="cmd"/>
        <result column="result" property="result"/>
        <result column="message" property="message"/>
        <result column="detail" property="detail"/>

        <result column="create_time" property="createTime"/>

        <result column="fail_count" property="failCount"/>
        <result column="success_count" property="successCount"/>
        <result column="issue_count" property="issueCount"/>
        <result column="un_issue_count" property="unIssueCount"/>
        <result column="device_count" property="deviceCount"/>
    </resultMap>


    <select id="statisticsPage" resultMap="CmdStatisticsVOMap">
        select
        cmd_id,
        cmd,
        a.create_time,
        (select count(1) from sys_issue_command as b WHERE b.cmd_id = a.cmd_id and b.`result` = 0 ) as success_count,
        (select count(1) from sys_issue_command as b WHERE b.cmd_id = a.cmd_id and b.`result` = 1 ) as fail_count,
        (select count(1) from sys_issue_command as b WHERE b.cmd_id = a.cmd_id and b.status= 1 ) as issue_count,
        (select count(1) from sys_issue_command as b WHERE b.cmd_id = a.cmd_id and b.status= 0 ) as un_issue_count,
        COUNT(a.device_id) as device_count
        from
        sys_issue_command as a

        <where>
            <if test="query.deviceId != null and query.deviceId != ''">
                and device_id = #{query.deviceId}
            </if>
            <if test="query.cmdId != null and query.cmdId != ''">
                and cmd_id like CONCAT('%',#{query.cmdId},'%')
            </if>
            <if test="query.cmd != null and query.cmd != ''">
                and cmd = #{query.cmd}
            </if>
        </where>

        GROUP BY a.cmd_id,a.cmd

        HAVING
        1 = 1

        <if test="query.result != null and query.result == 0">
            and success_count > 0
        </if>
        <if test="query.result != null and query.result == 1">
            and fail_count > 0
        </if>
        order by a.create_time desc

    </select>

</mapper>
