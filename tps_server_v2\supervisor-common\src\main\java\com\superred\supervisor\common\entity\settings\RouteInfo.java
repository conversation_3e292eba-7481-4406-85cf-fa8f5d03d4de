package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 路由信息(LocalRouteInfo) 实体
 *
 * <AUTHOR>
 * @since 2025-03-19 17:01:19
 */
@Data
@TableName("local_route_info")
public class RouteInfo {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * IP地址
     */
    @TableField("ip")
    private String ip;

    /**
     * 网关
     */
    @TableField("gateway")
    private String gateway;

    /**
     * 网卡编号
     */
    @TableField("network")
    private String network;

}

