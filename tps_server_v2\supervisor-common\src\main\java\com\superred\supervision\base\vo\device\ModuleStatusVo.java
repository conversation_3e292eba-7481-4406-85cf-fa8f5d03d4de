package com.superred.supervision.base.vo.device;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/17 12:38
 **/
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ModuleStatusVo implements Serializable {
    /**
     * 模块名称
     */
    private String name;
    /**
     * 模块状态
     */
    private String status;

    private List<SubModuleVo> submodule;
}
