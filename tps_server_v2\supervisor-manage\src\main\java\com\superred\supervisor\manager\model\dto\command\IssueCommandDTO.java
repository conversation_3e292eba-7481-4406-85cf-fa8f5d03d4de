package com.superred.supervisor.manager.model.dto.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 监测器指令下发dto
 *
 * <AUTHOR> Leixm
 * @since: 2025/03/19 18:12
 */
@Data
public class IssueCommandDTO {

    @Schema(description = "设备编号")
    private List<String> deviceIdList;

    @Schema(description = "命令类型")
    private String type;

    @Schema(description = "命令具体类型")
    private String cmd;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "升级文件id/内置规则更新包id")
    private Integer updateFileId;

    @Schema(description = "取值方法")
    private String method;

    @Schema(description = "检查文件名")
    private String filename;

    @Schema(description = "读取的开始偏移")
    private String offset;

    @Schema(description = "读取长度")
    private Integer length;

    @Schema(description = "检查文件完整路径")
    private String path;

    @Schema(description = "本地上传文件计算值")
    private String upValue;

    @Schema(description = "上报模块")
    private List<String> submodules;

    @Schema(description = "是否全部")
    private Integer isAll;

    @Schema(description = "规则列表")
    private List<String> blacklistRuleIds;

    @Schema(description = "规则列表")
    private List<String> keywordsRuleIds;

    @Schema(description = "规则列表")
    private List<String> md5RuleIds;

}