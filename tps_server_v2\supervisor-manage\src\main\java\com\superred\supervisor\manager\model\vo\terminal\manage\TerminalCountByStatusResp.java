package com.superred.supervisor.manager.model.vo.terminal.manage;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 终端状态统计
 *
 * <AUTHOR>
 * @since 2025/4/16 14:16
 */

@Data
public class TerminalCountByStatusResp {

    @Schema(description = "在线终端数量")
    private Integer countOnline;

    @Schema(description = "离线终端数量")
    private Integer countOffline;

    @Schema(description = "禁用终端数量")
    private Integer countDisable;

    @Schema(description = "卸载终端数量")
    private Integer countUninstall;
}
