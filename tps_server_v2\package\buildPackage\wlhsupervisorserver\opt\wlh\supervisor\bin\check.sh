#!/bin/bash

# 颜色
RED='\E[1;31m'
GREEN='\E[1;32m'
RES='\E[0m'
SERVER_SUM=0
# $1 进程名
# $2 软件名
do_check() {
  local server_pid=$( ps -ef|grep $1 | grep -v 'grep' | awk '{print $2}')
  local server_name="$2"
  if [ "$server_pid" == "" ]; then
    echo -e "${RED}[×]    $server_name${RES}"
  else
    echo -e "${GREEN}[√]    $server_name${RES}"
  fi
}


OS_ID=$(grep -oP '(?<=^ID=).+' /etc/os-release | tr -d '"')
OS_NAME=$(grep -oP '(?<=^NAME=).+' /etc/os-release | tr -d '"')
OS_VERSION=$(grep -oP '(?<=^VERSION=).+' /etc/os-release | tr -d '"')
ARCH=$(arch)
SERVER_VERSION=$(grep -oP '(?<=^BUILD=).+' /opt/wlh/supervisor/BUILD_INFO/VERSION.ini)
CPU_INFO=$(grep "model name" /proc/cpuinfo | uniq | cut -f2 -d: | sed 's/^[ ]*//g')
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4}')
MEM_TOTAL=$(free -g | grep Mem | awk '{print $2}')
MEM_USED=$(free -g | grep Mem | awk '{print $3}')
MEMORY_USAGE=$(free | grep Mem | awk '{print $3/$2 * 100}')
DISK_TOTAL=$(df -h / | awk '/\// {print $(NF-4)}')
DISK_USED=$(df -h / | awk '/\// {print $(NF-3)}')
DISK_USAGE=$(df -h / | awk '/\// {print $(NF-1)}')
JAVA_VERSION=$(/opt/wlh/platform/lib/jdk/bin/java -version 2>&1 | sed '1!d')

echo -e "${GREEN}自监管检测平台版本   ：$SERVER_VERSION${RES}"
echo -e "${GREEN}架构              ：$ARCH${RES}"
echo -e "${GREEN}操作系统           ：$OS_NAME $OS_VERSION ($OS_ID)${RES}"
echo -e "${GREEN}CPU信息           ：$CPU_INFO${RES}"
echo -e "${GREEN}CPU使用率          ：$CPU_USAGE%${RES}"
echo -e "${GREEN}内存使用率  ：$MEMORY_USAGE%($MEM_USED""G/$MEM_TOTAL""G)${RES}"
echo -e "${GREEN}磁盘使用率  ：$DISK_USAGE($DISK_USED/$DISK_TOTAL)${RES}"
echo -e "${GREEN}JAVA版本   ：$JAVA_VERSION${RES}"

echo
if ! command -v /opt/wlh/platform/lib/jdk/bin/java > /dev/null 2>&1; then
  echo -e "${RED}[×]    java${RES}"
else
  echo -e "${GREEN}[√]    java${RES}"
fi

do_check supervisor-manage '检测平台管理系统'
do_check supervisor-gateway '接口服务'
do_check supervisor-storage '数据存储服务'
do_check supervisor-report '数据上报服务'
do_check supervisor-monitor '状态监控服务'

echo -e "${GREEN}若存在${RES}${RED}[×]${RES}${GREEN}的进程可参照 /opt/wlh/supervisor/目录下文档中的步骤重启服务或查看日志${RES}"

