package com.superred.supervisor.common.repository.settings;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.settings.DeviceRunLog;
import com.superred.supervisor.common.mapper.settings.DeviceRunLogMapper;
import org.springframework.stereotype.Repository;

/**
 * 监测器离线异常告警(DDeviceRunLog) Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-27 15:31:59
 */
@Repository
public class DeviceRunLogRepository extends ServiceImpl<DeviceRunLogMapper, DeviceRunLog> {

}

