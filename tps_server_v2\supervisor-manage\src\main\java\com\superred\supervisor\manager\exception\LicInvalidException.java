package com.superred.supervisor.manager.exception;

import com.superred.supervisor.manager.model.auth.LicenseInvalidDTO;
import lombok.Getter;

/**
 * LicInvalidException.
 * 授权无效.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/8/23 14:07
 */
@Getter
public class LicInvalidException extends RuntimeException {

    private final LicenseInvalidDTO licenseInvalidDTO;

    public LicInvalidException(LicenseInvalidDTO licenseInvalidDTO, String message) {
        super(message);
        this.licenseInvalidDTO = licenseInvalidDTO;
        this.licenseInvalidDTO.setLicenseMsg(message);

    }
}
