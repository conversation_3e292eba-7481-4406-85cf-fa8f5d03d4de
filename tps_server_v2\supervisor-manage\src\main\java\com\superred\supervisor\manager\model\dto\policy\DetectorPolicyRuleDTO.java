package com.superred.supervisor.manager.model.dto.policy;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-26 16:11
 */
@Data
public class DetectorPolicyRuleDTO {

    @Schema(description = "规则ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ruleId;

    @Schema(description = "策略ID")
    private Long policyId;

    @Schema(description = "模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单")
    private String module;

    public static List<DetectorPolicyRuleDTO> fromDetectorPolicyRule(List<DetectorPolicyRule> detectorPolicyRuleList) {
        List<DetectorPolicyRuleDTO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
            detectorPolicyRuleList.forEach(item -> {
                DetectorPolicyRuleDTO detectorPolicyRuleDTO = new DetectorPolicyRuleDTO();
                detectorPolicyRuleDTO.setPolicyId(item.getPolicyId());
                detectorPolicyRuleDTO.setModule(item.getModule());
                detectorPolicyRuleDTO.setRuleId(item.getRuleId());
                list.add(detectorPolicyRuleDTO);
            });
        }
        return list;
    }
}
