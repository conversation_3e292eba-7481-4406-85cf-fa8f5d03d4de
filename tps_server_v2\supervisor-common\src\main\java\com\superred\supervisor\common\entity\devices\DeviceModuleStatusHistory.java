package com.superred.supervisor.common.entity.devices;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 检测器模块状态历史 实体
 *
 * <AUTHOR>
 * @since 2025-03-18 20:06:46
 */
@Data
@TableName("d_device_module_status_history")
public class DeviceModuleStatusHistory {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("device_id")
    private String deviceId;

    /**
     * 模块
     */
    @TableField("module")
    private String module;

    /**
     * 子模块
     */
    @TableField("submodule")
    private String submodule;

    /**
     * 模块状态
     */
    @TableField("status")
    private String status;

    /**
     * 内置策略版本
     */
    @TableField("inner_policy")
    private String innerPolicy;

    /**
     * 消息积压数
     */
    @TableField("record_delayednum")
    private Integer recordDelayednum;

    /**
     * 24小时产生告警数
     */
    @TableField("record24h_num")
    private Integer record24hNum;

    /**
     * 文件积压数
     */
    @TableField("file_delayednum")
    private Integer fileDelayednum;

    /**
     * 模块版本号
     */
    @TableField("version")
    private String version;

    /**
     * 时间
     */
    @TableField("time")
    private LocalDateTime time;

}

