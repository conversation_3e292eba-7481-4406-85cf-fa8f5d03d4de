package com.superred.supervisor.manager.model.vo.policy.terminal;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端策略分页查询响应
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Data
@Schema(description = "终端策略分页查询响应")
public class TerminalPolicyPageResp {

    @Schema(description = "策略ID")
    private Long id;

    @Schema(description = "策略编码")
    private String policyCode;

    @Schema(description = "策略名称")
    private String policyName;

    @Schema(description = "模块类型：alarm file_detect device_info")
    private String module;

    @Schema(description = "策略类型：add-增加,del-删除,reset-全量,inc_del-增量删除")
    private String policyType;

    @Schema(description = "规则数量")
    private Integer ruleCount;

    @Schema(description = "策略版本")
    private String policyVersion;

    @Schema(description = "策略描述")
    private String description;

    @Schema(description = "策略状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creatorId;

    @Schema(description = "更新人")
    private String updaterId;
}
