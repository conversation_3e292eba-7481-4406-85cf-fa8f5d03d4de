package com.superred.supervisor.common.repository.system;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.system.SysMenu;
import com.superred.supervisor.common.entity.system.SysRoleMenu;
import com.superred.supervisor.common.mapper.system.SysMenuMapper;
import com.superred.supervisor.common.mapper.system.SysRoleMenuMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单表 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:21
 */
@Repository
public class SysMenuRepository extends ServiceImpl<SysMenuMapper, SysMenu> {

    @Resource
    private SysRoleMenuMapper sysRoleMenuMapper;


    public List<SysMenu> selectMenuByRoleId(Integer roleId) {
        List<SysRoleMenu> sysRoleMenus = sysRoleMenuMapper.selectList(Wrappers.<SysRoleMenu>lambdaQuery().eq(SysRoleMenu::getRoleId, roleId));
        List<Integer> menuIds = sysRoleMenus.stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
        return this.list(Wrappers.<SysMenu>lambdaQuery().in(SysMenu::getId, menuIds).ne(SysMenu::getType, 4));
    }

    public List<String> getMenuPermissionByRoleId(Integer roleId) {
        List<SysRoleMenu> sysRoleMenus = sysRoleMenuMapper.selectList(Wrappers.<SysRoleMenu>lambdaQuery().eq(SysRoleMenu::getRoleId, roleId));
        List<Integer> menuIds = sysRoleMenus.stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
        List<SysMenu> sysMenus = this.listByIds(menuIds);
        return sysMenus.stream().map(SysMenu::getPermission).filter(StrUtil::isNotBlank).collect(Collectors.toList());
    }
}

