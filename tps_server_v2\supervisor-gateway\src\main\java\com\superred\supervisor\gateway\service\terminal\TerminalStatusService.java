package com.superred.supervisor.gateway.service.terminal;



import com.superred.supervisor.standard.v202505.terminal.status.TerminalBusinessStatusReq;
import com.superred.supervisor.standard.v202505.terminal.status.TerminalSystemAuditReq;
import com.superred.supervisor.standard.v202505.terminal.status.TerminalSystemStatusReq;

import java.util.List;

/**
 * 终端状态上报
 *
 * <AUTHOR>
 * @since 2025/5/29 13:41
 */
public interface TerminalStatusService {

    /**
     * 上报终端系统状态
     *
     * @param req 请求参数
     */
    void reportSystemStatus(TerminalSystemStatusReq req);

    /**
     * 上报终端业务状态
     *
     * @param req 请求参数列表
     */
    void reportBusinessStatus(List<TerminalBusinessStatusReq> req);

    /**
     * 上报终端系统审计信息
     *
     * @param req 请求参数
     * @return ApiRes 返回结果
     */
    void reportSystemAudit(TerminalSystemAuditReq req);
}
