<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         name="Nano Rules"
         xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.io/ruleset_2_0_0.xsd">

    <description>nano ruleset</description>

    <rule ref="category/java/bestpractices.xml/AbstractClassWithoutAbstractMethod"/>
    <rule ref="category/java/bestpractices.xml/AccessorClassGeneration"/>
    <rule ref="category/java/bestpractices.xml/AccessorMethodGeneration"/>
    <rule ref="category/java/bestpractices.xml/ArrayIsStoredDirectly"/>
    <rule ref="category/java/bestpractices.xml/AvoidPrintStackTrace"/>
    <rule ref="category/java/bestpractices.xml/AvoidReassigningParameters"/>
    <rule ref="category/java/bestpractices.xml/AvoidStringBufferField"/>

    <rule ref="category/java/bestpractices.xml/ConstantsInInterface"/>
    <rule ref="category/java/bestpractices.xml/DefaultLabelNotLastInSwitchStmt"/>
    <rule ref="category/java/bestpractices.xml/MethodReturnsInternalArray"/>
    <rule ref="category/java/bestpractices.xml/PreserveStackTrace"/>
    <rule ref="category/java/bestpractices.xml/LooseCoupling"/>
    <rule ref="category/java/bestpractices.xml/SwitchStmtsShouldHaveDefault"/>
    <rule ref="category/java/bestpractices.xml/SystemPrintln"/>
    <rule ref="category/java/bestpractices.xml/UseCollectionIsEmpty"/>
    <rule ref="category/java/bestpractices.xml/UseVarargs"/>
    <rule ref="category/java/bestpractices.xml/UnusedFormalParameter"/>
    <rule ref="category/java/bestpractices.xml/UnusedLocalVariable"/>
    <rule ref="category/java/bestpractices.xml/UnusedPrivateField"/>
    <rule ref="category/java/bestpractices.xml/UnusedPrivateMethod"/>


    <rule ref="category/java/codestyle.xml/AvoidProtectedFieldInFinalClass"/>
    <rule ref="category/java/codestyle.xml/AvoidProtectedMethodInFinalClassNotExtending"/>
    <rule ref="category/java/codestyle.xml/ConfusingTernary"/>
    <rule ref="category/java/codestyle.xml/EmptyControlStatement"/>
    <rule ref="category/java/codestyle.xml/EmptyMethodInAbstractClassShouldBeAbstract"/>
    <rule ref="category/java/codestyle.xml/ExtendsObject"/>
    <rule ref="category/java/codestyle.xml/ForLoopShouldBeWhileLoop"/>
    <rule ref="category/java/codestyle.xml/TooManyStaticImports"/>
    <rule ref="category/java/codestyle.xml/UnnecessaryLocalBeforeReturn"/>
    <rule ref="category/java/codestyle.xml/UnnecessaryImport"/>


    <rule ref="category/java/design.xml/AbstractClassWithoutAnyMethod"/>
    <rule ref="category/java/design.xml/AvoidThrowingNullPointerException"/>
    <rule ref="category/java/design.xml/AvoidDeeplyNestedIfStmts"/>
    <rule ref="category/java/design.xml/CollapsibleIfStatements"/>
    <rule ref="category/java/design.xml/CouplingBetweenObjects">
        <properties>
            <property name="threshold" value="30"/>
        </properties>
    </rule>
    <rule ref="category/java/design.xml/ClassWithOnlyPrivateConstructorsShouldBeFinal"/>
    <rule ref="category/java/design.xml/ExcessiveMethodLength"/>
    <rule ref="category/java/design.xml/ExcessiveParameterList">
        <properties>
            <property name="minimum" value="7"/>
        </properties>
    </rule>
    <rule ref="category/java/design.xml/FinalFieldCouldBeStatic"/>
    <rule ref="category/java/design.xml/ImmutableField"/>
    <rule ref="category/java/design.xml/LogicInversion"/>
    <rule ref="category/java/design.xml/NPathComplexity">
        <properties>
            <property name="reportLevel" value="900"/>
        </properties>
    </rule>
    <rule ref="category/java/design.xml/SimplifiedTernary"/>
    <rule ref="category/java/design.xml/SimplifyBooleanExpressions"/>
    <rule ref="category/java/design.xml/SimplifyBooleanReturns"/>
    <rule ref="category/java/design.xml/SimplifyConditional"/>
    <rule ref="category/java/design.xml/SwitchDensity"/>

    <rule ref="category/java/documentation.xml/UncommentedEmptyMethodBody"/>
    <rule ref="category/java/documentation.xml/CommentRequired">
        <properties>
            <property name="classCommentRequirement" value="Required"/>
            <property name="fieldCommentRequirement" value="Ignored"/>
            <property name="publicMethodCommentRequirement" value="Ignored"/>
            <property name="protectedMethodCommentRequirement" value="Ignored"/>
            <property name="enumCommentRequirement" value="Required"/>
        </properties>
    </rule>

    <rule ref="category/java/errorprone.xml/AssignmentToNonFinalStatic"/>
    <rule ref="category/java/errorprone.xml/AvoidBranchingStatementAsLastInLoop"/>
    <rule ref="category/java/errorprone.xml/AvoidDecimalLiteralsInBigDecimalConstructor"/>
    <rule ref="category/java/errorprone.xml/AvoidLosingExceptionInformation"/>
    <rule ref="category/java/errorprone.xml/AvoidMultipleUnaryOperators"/>
    <rule ref="category/java/errorprone.xml/AvoidCallingFinalize"/>
    <rule ref="category/java/errorprone.xml/AvoidUsingOctalValues"/>
    <rule ref="category/java/errorprone.xml/AvoidInstanceofChecksInCatchClause"/>
    <rule ref="category/java/errorprone.xml/BrokenNullCheck"/>
    <rule ref="category/java/errorprone.xml/ClassCastExceptionWithToArray"/>
    <rule ref="category/java/errorprone.xml/CloseResource">
    </rule>
    <rule ref="category/java/errorprone.xml/CompareObjectsWithEquals"/>
    <rule ref="category/java/errorprone.xml/ComparisonWithNaN"/>
    <rule ref="category/java/errorprone.xml/ConstructorCallsOverridableMethod"/>
    <rule ref="category/java/errorprone.xml/DontUseFloatTypeForLoopIndices"/>
    <rule ref="category/java/errorprone.xml/EmptyCatchBlock"/>
    <rule ref="category/java/errorprone.xml/EmptyStatementNotInLoop"/>
    <rule ref="category/java/errorprone.xml/EqualsNull"/>
    <rule ref="category/java/errorprone.xml/InstantiationToGetClass"/>
    <rule ref="category/java/errorprone.xml/IdempotentOperations"/>
    <rule ref="category/java/errorprone.xml/InvalidLogMessageFormat"/>
    <rule ref="category/java/errorprone.xml/JumbledIncrementer"/>
    <rule ref="category/java/errorprone.xml/MoreThanOneLogger"/>
    <rule ref="category/java/errorprone.xml/MisplacedNullCheck"/>
    <rule ref="category/java/errorprone.xml/MissingStaticMethodInNonInstantiatableClass"/>
    <rule ref="category/java/errorprone.xml/NonStaticInitializer"/>
    <rule ref="category/java/errorprone.xml/OverrideBothEqualsAndHashcode"/>
    <rule ref="category/java/errorprone.xml/ReturnFromFinallyBlock"/>
    <rule ref="category/java/errorprone.xml/ReturnEmptyCollectionRatherThanNull"/>

    <rule ref="category/java/errorprone.xml/SimpleDateFormatNeedsLocale"/>
    <rule ref="category/java/errorprone.xml/SingleMethodSingleton"/>
    <rule ref="category/java/errorprone.xml/StringBufferInstantiationWithChar"/>
    <rule ref="category/java/errorprone.xml/UseLocaleWithCaseConversions"/>
    <rule ref="category/java/errorprone.xml/UnconditionalIfStatement"/>
    <rule ref="category/java/errorprone.xml/UnnecessaryCaseChange"/>
    <rule ref="category/java/errorprone.xml/UseEqualsToCompareStrings"/>


    <rule ref="category/java/multithreading.xml/AvoidThreadGroup"/>
    <rule ref="category/java/multithreading.xml/AvoidSynchronizedAtMethodLevel"/>
    <rule ref="category/java/multithreading.xml/DontCallThreadRun"/>
    <rule ref="category/java/multithreading.xml/DoubleCheckedLocking"/>
    <rule ref="category/java/multithreading.xml/NonThreadSafeSingleton"/>
    <rule ref="category/java/multithreading.xml/UseNotifyAllInsteadOfNotify"/>

    <rule ref="category/java/performance.xml/AddEmptyString"/>
    <rule ref="category/java/performance.xml/AppendCharacterWithChar"/>
    <rule ref="category/java/performance.xml/AvoidArrayLoops"/>
    <rule ref="category/java/performance.xml/AvoidFileStream"/>
    <rule ref="category/java/performance.xml/BigIntegerInstantiation"/>
    <rule ref="category/java/performance.xml/ConsecutiveAppendsShouldReuse"/>
    <rule ref="category/java/performance.xml/ConsecutiveLiteralAppends"/>
    <rule ref="category/java/performance.xml/InefficientEmptyStringCheck"/>
    <rule ref="category/java/performance.xml/InefficientStringBuffering"/>
    <rule ref="category/java/performance.xml/InsufficientStringBufferDeclaration"/>
    <rule ref="category/java/performance.xml/OptimizableToArrayCall"/>
    <rule ref="category/java/performance.xml/RedundantFieldInitializer"/>
    <rule ref="category/java/performance.xml/StringInstantiation"/>
    <rule ref="category/java/performance.xml/StringToString"/>
    <rule ref="category/java/performance.xml/UseArraysAsList"/>
    <rule ref="category/java/performance.xml/UseIndexOfChar"/>
    <rule ref="category/java/performance.xml/UselessStringValueOf"/>
    <rule ref="category/java/performance.xml/UseStringBufferLength"/>
    <rule ref="category/java/performance.xml/UseStringBufferForStringAppends"/>

</ruleset>