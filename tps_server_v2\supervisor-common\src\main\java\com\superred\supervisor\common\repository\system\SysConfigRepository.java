package com.superred.supervisor.common.repository.system;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.system.SysConfig;
import com.superred.supervisor.common.mapper.system.SysConfigMapper;
import org.springframework.stereotype.Repository;

/**
 * 字典项表 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:20
 */
@Repository
public class SysConfigRepository extends ServiceImpl<SysConfigMapper, SysConfig> {

    public SysConfig getOneByName(String name) {
        return this.getOne(Wrappers.<SysConfig>lambdaQuery().eq(SysConfig::getName, name));
    }

}

