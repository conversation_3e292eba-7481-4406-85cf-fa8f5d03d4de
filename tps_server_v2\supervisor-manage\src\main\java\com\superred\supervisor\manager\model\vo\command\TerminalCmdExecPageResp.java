package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 终端指令统计详情Resp
 */
@Data
public class TerminalCmdExecPageResp {


    @Schema(description = "设备编号")
    private String deviceId;

    @Schema(description = "设备名称")
    private String hostName;

    @Schema(description = "ip地址")
    private String terminalIp;

    @Schema(description = "单位名称")
    private String company;

    @Schema(description = "所属部门名称")
    private String orgName;



    @Schema(description = "执行结果，取值为：0（成功）、1（失败）")
    private Integer result;

    @Schema(description = "执行结果描述，result为1，说明失败原因")
    private String message;

    @Schema(description = "下发状态：0-待执行，1-执行中，2-执行成功，3-执行失败，4-已取消")
    private Integer status;


}