package com.superred.supervisor.manager.model.vo.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-04-02 13:45
 */
@Data
@Builder
public class PolicyModuleResp {

    @Schema(description = "模块所属模块")
    private String module;

    @Schema(description = "策略类型")
    private String moduleParentStr;

    @Schema(description = "策略子类型")
    private String moduleStr;
}
