package com.superred.supervisor.common.repository.policy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.RuleAttackPermeate;
import com.superred.supervisor.common.mapper.policy.RuleAttackPermeateMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2025-03-12 15:59
 */
@Slf4j
@Repository
@AllArgsConstructor
public class RuleAttackPermeateRepository extends ServiceImpl<RuleAttackPermeateMapper, RuleAttackPermeate> {
}
