package com.superred.supervisor.gateway.model.app.cmd;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 策略响应.
 **/
@Data
public class AppPolicyResultReq {


    /**
     * 上报时间.
     **/
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

    /**
     * 指令类型
     **/
    private String type;

    /**
     * 指令名称
     **/
    private String cmd;

    /**
     * 策略类型.
     **/
    private String module;

    /**
     * 版本.
     **/
    private String version;

    /**
     * 成功执行的策略ID列表
     **/
    private List<Long> success;

    /**
     * 未成功执行的策略对象列表.
     **/
    private List<FailItem> fail;

    /**
     * 失败项
     * <AUTHOR>
     * @since 2025/07/28
     */
    @Data
    public static class FailItem {

        @JsonProperty("rule_id")
        private Long ruleId;

        private String msg;

    }
}
