package com.superred.supervisor.manager.model.vo.policy;

import com.superred.supervisor.manager.common.annotation.BlankOrPattern;
import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @create 2025-03-12 16:17
 */
@Data

public class RuleAttackTrojanReq {

    private static final long serialVersionUID = 1L;

    @Schema(description = "策略ID")
    private String ruleId;

    @Schema(description = "策略名称")
    @ByteSize(max = 128, message = "策略名称长度不能超过128个字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]+$", message = "策略名称格式错误,只允许输入中文、英文、数字")
    private String ruleName;

    @Schema(description = "是否保存pcap，默认为1保存，2不保存")
    private String storePcap;

    @Schema(description = "攻击分类 1 窃密木马 2 远控木马 3 电脑病毒 4 僵尸网络 5 网络蠕虫 6 间谍软件 7 挖矿木马 8 黑客工具 11 后门程序 99 其它")
    private String attackClass;

    @Schema(description = "攻击组织")
    @ByteSize(max = 64, message = "攻击组织长度不能超过64个字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]+$", message = "攻击组织格式错误,只允许输入中文、英文、数字")
    private String attackGroup;

    @Schema(description = "策略内容")
    @NotBlank(message = "策略内容不能为空")
    @ByteSize(max = 2048, message = "策略内容长度不能超过2048个字节")
    @Pattern(regexp = "(alert|log|pass|activate|dynamic)\\s+([a-zA-Z0-9]{2,16})\\s+((\\S+)\\s+(\\S+)\\s+(->|<>)\\s+(\\S+)\\s+(\\S+)\\s+)?\\((([\\S\\s]*?msg\\:[\\S\\s]+?sid\\:[\\S\\s]+?)|([\\S\\s]*?sid\\:[\\S\\s]+?msg\\:[\\S\\s]+?))\\)", message = "策略内容格式错误")
    private String rule;

    @Schema(description = "攻击窃密告警描述。简要描述木马性质、木马进行操作（取文件、控制…）或适用系统（windows linux unix…）")
    private String desc;

    @Schema(description = "告警级别;告警级别型，取值为：0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）")
    private String risk;

    @Schema(description = "是否共享状态，0是，1否")
    private String isShare;

    @Schema(description = "策略应用状态，0未应用，1已应用")
    private String status;

    @Schema(description = "策略来源 1 本级 2上级")
    private String ruleSource;

    @Schema(description = "平台级别")
    private String level;

    @Schema(description = "策略更新时间")
    private String updateTime;

    @Schema(description = "策略创建时间")
    private String createTime;

    @Schema(description = "扩展字段1")
    private Long ext1;

    @Schema(description = "扩展字段2")
    private String ext2;

    @Schema(description = "扩展字段3")
    private String ext3;

    @Schema(description = "开始时间")
    private String startDate;

    @Schema(description = "结束时间")
    private String endDate;
}
