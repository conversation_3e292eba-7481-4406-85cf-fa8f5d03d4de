package com.superred.supervisor.manager.model.vo.policy;

import com.superred.common.core.model.PageReqDTO;
import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @create 2025-03-12 16:02
 */
@Data

public class RuleAttackPermeatePageReq extends PageReqDTO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "策略下发版本")
    private String version;

    @Schema(description = "策略下发ID")
    private String policyId;

    @Schema(description = "策略下发设备类型 05 终端；01 检测器")
    private String issueDeviceType;

    @Schema(description = "策略ID")
    private String ruleId;

    @Schema(description = "策略名称")
    private String ruleName;

    @Schema(description = "是否保存pcap，默认为1保存，2不保存")
    private String storePcap;

    @Schema(description = "攻击分类 1. 系统漏洞利用 2. 软件漏洞利用 3. 网络钓鱼 4. 网络扫描 5. 密码猜解 6. SQL注入 7. 溢出攻击 8. 代码执行 9. 非授权访问/权限绕过 10. 跨站脚本攻击（XSS） 11. 跨站请求伪造（CSRF） 12. 目录遍历攻击 13. 文件利用 14. Webshell利用 15. Webshell上传 99. 其它")
    private String attackClass;

    @Schema(description = "攻击组织")
    @ByteSize(max = 128, message = "攻击组织长度不能超过128个字节")
    private String attackGroup;

    @ByteSize(max = 2048, message = "策略内容长度不能超过2048个字节")
    @Pattern(regexp = "(alert|log|pass|activate|dynamic)\\s+([a-zA-Z0-9]{2,16})\\s+((\\S+)\\s+(\\S+)\\s+(->|<>)\\s+(\\S+)\\s+(\\S+)\\s+)?\\((([\\S\\s]*?msg\\:[\\S\\s]+?sid\\:[\\S\\s]+?)|([\\S\\s]*?sid\\:[\\S\\s]+?msg\\:[\\S\\s]+?))\\)", message = "策略内容格式错误")
    @Schema(description = "策略内容")
    private String rule;

    @Schema(description = "渗透行为描述")
    private String desc;

    @Schema(description = "渗透行为利用漏洞编号")
    private String cve;

    @Schema(description = "漏洞名称")
    private String vulnerability;

    @Schema(description = "告警级别，0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）。")
    private String risk;

    @Schema(description = "是否共享状态，0是，1否")
    private String isShare;

    @Schema(description = "策略状态，0：无效，1：有效")
    private String status;

    @Schema(description = "策略来源 1 本级 2上级")
    private String ruleSource;

    @Schema(description = "平台级别")
    private String level;

    @Schema(description = "策略更新时间")
    private String updateTime;

    @Schema(description = "策略创建时间")
    private String createTime;

    @Schema(description = "扩展字段1")
    private Long ext1;

    @Schema(description = "扩展字段2")
    private String ext2;

    @Schema(description = "扩展字段3")
    private String ext3;

    @Schema(description = "开始时间")
    private String startDate;

    @Schema(description = "结束时间")
    private String endDate;
}
