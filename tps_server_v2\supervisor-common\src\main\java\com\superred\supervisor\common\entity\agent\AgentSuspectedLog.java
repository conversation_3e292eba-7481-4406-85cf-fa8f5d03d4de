package com.superred.supervisor.common.entity.agent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统异常日志 实体
 *
 * <AUTHOR>
 * @since 2025-03-24 09:23:56
 */
@Data
@TableName("agent_suspected_log")
public class AgentSuspectedLog {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("device_id")
    private String deviceId;

    /**
     * 异常类型：
     1（系统异常）
     2（软件异常）
     3（流量异常）
     4（策略异常）
     */
    @TableField("event_type")
    private Integer eventType;

    /**
     * 异常产生时间
     */
    @TableField("time")
    private LocalDateTime time;

    /**
     * 告警级别:
     0（无风险）
     1（一般级）
     2（关注级）
     3（严重级）
     4（紧急级）
     */
    @TableField("risk")
    private Integer risk;

    /**
     * 异常事件描述
     */
    @TableField("msg")
    private String msg;

    /**
     * 是否上报
     */
    @TableField("report")
    private Integer report;

    /**
     * 软件版本
     */
    @TableField("soft_version")
    private String softVersion;

}

