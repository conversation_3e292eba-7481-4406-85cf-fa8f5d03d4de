package com.superred.supervisor.manager.repository.setting;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.settings.LocalDeviceSuspectedLog;
import com.superred.supervisor.manager.mapper.setting.LocalDeviceSuspectedLogExtMapper;
import org.springframework.stereotype.Repository;

/**
 * 系统异常日志(LocalDeviceSuspectedLog) Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-27 15:31:59
 */
@Repository
public class LocalDeviceSuspectedLogExtRepository extends ServiceImpl<LocalDeviceSuspectedLogExtMapper, LocalDeviceSuspectedLog> {

}

