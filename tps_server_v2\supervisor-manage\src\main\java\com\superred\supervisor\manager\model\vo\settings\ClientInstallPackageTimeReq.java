package com.superred.supervisor.manager.model.vo.settings;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月17日
 */
@Data
public class ClientInstallPackageTimeReq {


    @Schema(description = "是否开启  0否  1是")
    private Integer agentInstallState;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "开启时间：年月日 如：2025-01-01")
    private Date agentInstallStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(description = "截止时间：年月日 如：2025-01-01")
    private Date agentInstallEndDate;

    @Schema(description = "每周时段: MON  TUE WED  THU  FRI  SAT  SUN")
    private String agentInstallCycleWeek;

    // 接收时间部分（时分秒）
    @DateTimeFormat(pattern = "HH:mm")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    @Schema(description = "每日开始时段： 如：08:00")
    private Date agentInstallStartTime;

    @DateTimeFormat(pattern = "HH:mm")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    @Schema(description = "每日截止时段： 如：08:00")
    private Date agentInstallEndTime;

}
