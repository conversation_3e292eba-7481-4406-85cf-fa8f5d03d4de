package com.superred.supervisor.manager.schedule;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.superred.supervisor.common.entity.devices.DeviceModuleStatusHistory;
import com.superred.supervisor.common.entity.devices.DeviceSuspectedLog;
import com.superred.supervisor.common.entity.devices.DeviceSystemStatus;
import com.superred.supervisor.common.entity.settings.AuditLog;
import com.superred.supervisor.common.entity.settings.DeviceRunLog;
import com.superred.supervisor.common.entity.system.SysConfig;
import com.superred.supervisor.common.repository.devices.DeviceModuleStatusHistoryRepository;
import com.superred.supervisor.common.repository.devices.DeviceSuspectedLogRepository;
import com.superred.supervisor.common.repository.settings.AuditLogRepository;
import com.superred.supervisor.common.repository.settings.DeviceRunLogRepository;
import com.superred.supervisor.manager.repository.system.SysConfigExtRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * MonitorDSDSchedule.
 * 监测器数据存储处理定时任务.
 * 重要数据长期存储，不少于3年
 * jcq和终端上报的状态，审计数据，保存不少于6个月
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-09-06 16:39
 */
@Component
@EnableScheduling
@Slf4j
public class MonitorDSDSchedule {


    @Autowired
    private SysConfigExtRepository sysConfigExtRepository;

    @Autowired
    AuditLogRepository auditLogService;

    @Resource
    DeviceSuspectedLogRepository deviceSuspectedLogService;

    @Resource
    DeviceModuleStatusHistoryRepository deviceModuleStatusHistoryService;

    @Resource
    DeviceRunLogRepository deviceRunLogService;


    /**
     * 1、 监测器状态数据处理.
     * jcq和终端上报的状态，审计数据，保存不少于6个月
     *
     * <AUTHOR>
     * @since 2023-09-06 16:51
     **/
    @Scheduled(cron = "${dsDeal.monitor.statusData}")
    public void sysStatusData() throws Exception {

        QueryWrapper<SysConfig> query = new QueryWrapper<>();
        query.eq("`key`", "status_data_storage_time");
        SysConfig statusData = sysConfigExtRepository.getOne(query);
        LocalDateTime now = LocalDateTime.now();
        if (statusData != null) {
            LocalDateTime localDateTime = now.minusMonths(Integer.valueOf(statusData.getValue()));
            // 删除监测器运行状态d_device_system_status
            LambdaQueryWrapper<DeviceSystemStatus> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.le(DeviceSystemStatus::getTime, localDateTime);
//            List<DeviceSystemStatus> deviceSystemStatusList = statusService.list(queryWrapper);

            // 删除监测器运行日志d_device_run_log
            LambdaQueryWrapper<DeviceRunLog> runLogWrapper = new LambdaQueryWrapper<>();
            runLogWrapper.le(DeviceRunLog::getCreateTime, localDateTime);
            List<DeviceRunLog> deviceRunLogList = deviceRunLogService.list(runLogWrapper);
            if (deviceRunLogList != null) {
                deviceRunLogService.remove(runLogWrapper);

            }

            // 删除监测器异常日志d_device_suspected_log
            LambdaQueryWrapper<DeviceSuspectedLog> logWrapper = new LambdaQueryWrapper<>();
            logWrapper.le(DeviceSuspectedLog::getTime, localDateTime);
            List<DeviceSuspectedLog> deviceSuspectedLogList = this.deviceSuspectedLogService.list(logWrapper);
            if (CollectionUtil.isNotEmpty(deviceSuspectedLogList)) {
                this.deviceSuspectedLogService.remove(logWrapper);
            }
            // 删除监测器模块状态d_device_module_status_history
            LambdaQueryWrapper<DeviceModuleStatusHistory> moduleWrapper = new LambdaQueryWrapper<>();
            moduleWrapper.le(DeviceModuleStatusHistory::getTime, localDateTime);
            List<DeviceModuleStatusHistory> moduleList = this.deviceModuleStatusHistoryService.list(moduleWrapper);
            if (CollectionUtil.isNotEmpty(moduleList)) {
                this.deviceModuleStatusHistoryService.remove(moduleWrapper);
            }
        }
    }

    /**
     * 2、 监测器审计数据处理.
     * jcq和终端上报的状态，审计数据，保存不少于6个月
     *
     * <AUTHOR>
     * @since 2023-09-06 16:51
     **/
    @Scheduled(cron = "${dsDeal.monitor.auditData}")
    public void auditData() throws Exception {

        QueryWrapper<SysConfig> query = new QueryWrapper<>();
        query.eq("`key`", "audit_data_storage_time");
        SysConfig statusData = sysConfigExtRepository.getOne(query);
        LocalDateTime now = LocalDateTime.now();
        if (statusData != null) {
            LocalDateTime localDateTime = now.minusMonths(Integer.valueOf(statusData.getValue()));
            //监测器审计数据
            LambdaQueryWrapper<AuditLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.le(AuditLog::getTime, localDateTime);
            List<AuditLog> deviceSystemStatusList = auditLogService.list(queryWrapper);
            if (deviceSystemStatusList != null) {

                auditLogService.remove(queryWrapper);

            }
        }
    }


}
