package com.superred.supervisor.manager.model.vo.devices.manager;

import com.superred.supervisor.common.entity.devices.DeviceInterface;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 设备接口镜像状态
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data
public class DeviceInterfaceMirrorStatusResp {


    /**
     *
     */
    @Schema(description = "")
    private Integer id;


    /**
     * 网卡序号，1，2
     */
    @Schema(description = "网卡序号，1，2")
    private Integer interfaceSeq;

    /**
     * 网卡编号，“eth0”
     */
    @Schema(description = "网卡编号，“eth0”")
    private String interfaceFlag;

    /**
     * 网卡状态，1启用，2停用，3掉线，4故障，99未知
     */
    @Schema(description = "网卡状态，1启用，2停用，3掉线，4故障，99未知")
    private Integer interfaceStat;

    /**
     * 网卡流量，kbps
     */
    @Schema(description = "网卡流量，kbps")
    private Integer interfaceFlow;

    /**
     * 错误报文数
     */
    @Schema(description = "错误报文数")
    private Integer interfaceError;

    /**
     * 丢包数
     */
    @Schema(description = "丢包数")
    private Integer interfaceDrop;

    /**
     * 数据采集时长，单位秒
     */
    @Schema(description = "数据采集时长，单位秒")
    private Integer durationTime;


    public static DeviceInterfaceMirrorStatusResp from(DeviceInterface deviceInterface) {

        DeviceInterfaceMirrorStatusResp deviceInterfaceMirrorStatusResp = new DeviceInterfaceMirrorStatusResp();
        deviceInterfaceMirrorStatusResp.setId(deviceInterface.getId());
        deviceInterfaceMirrorStatusResp.setInterfaceSeq(deviceInterface.getInterfaceSeq());
        deviceInterfaceMirrorStatusResp.setInterfaceFlag(deviceInterface.getInterfaceFlag());
        deviceInterfaceMirrorStatusResp.setInterfaceStat(deviceInterface.getInterfaceStat());
        deviceInterfaceMirrorStatusResp.setInterfaceFlow(deviceInterface.getInterfaceFlow());
        deviceInterfaceMirrorStatusResp.setInterfaceError(deviceInterface.getInterfaceError());
        deviceInterfaceMirrorStatusResp.setInterfaceDrop(deviceInterface.getInterfaceDrop());
        deviceInterfaceMirrorStatusResp.setDurationTime(Math.toIntExact(deviceInterface.getDurationTime()));
        return deviceInterfaceMirrorStatusResp;

    }
}
