package com.superred.common.core.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * gzip 过滤器
 *
 * <AUTHOR>
 * @since 2025/7/15 14:29
 */
@Slf4j
public class GzipDecompressFilter extends OncePerRequestFilter {

    private static final String CONTENT_ENCODING_TYPE = "gzip";

    @Override
    protected void doFilterInternal(HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {

        // 判断是否 gzip 压缩
        String encoding = request.getHeader(HttpHeaders.CONTENT_ENCODING);
        boolean isGzip = encoding != null && encoding.equalsIgnoreCase(CONTENT_ENCODING_TYPE);

        HttpServletRequest wrappedRequest = request;

        if (isGzip) {
            log.warn(" >>>>>>>>>>>> Request is gzip compressed, decompressing it.");
            wrappedRequest = new GzipHttpServletRequestWrapper(request);
        }

        filterChain.doFilter(wrappedRequest, response);
    }
}
