package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 自监管网卡信息
 * </p>
 *
 * <AUTHOR>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("local_device_network")
public class DeviceNetwork implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 网卡编号，“eth0”
     */
    private String flag;

    /**
     * ip地址，************
     */
    private String ip;

    /**
     * 子网掩码，***********/24
     */
    private String netmask;

    /**
     * 网关地址，*************
     */
    private String gateway;

    /**
     * mac地址，00:0d:48:09:4e:88
     */
    private String mac;

    /**
     * 是否管理口，true 或 false
     */
    private Boolean manage;

    /**
     * 接口类型，1 本地管理接口，2 数据通信接口
     */
    private String type;


}
