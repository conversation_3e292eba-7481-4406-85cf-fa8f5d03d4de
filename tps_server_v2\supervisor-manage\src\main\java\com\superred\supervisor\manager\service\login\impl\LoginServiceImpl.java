package com.superred.supervisor.manager.service.login.impl;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.supervisor.common.entity.settings.IpWhitelist;
import com.superred.supervisor.common.entity.settings.enums.IpWhiteListType;
import com.superred.supervisor.common.entity.system.SysMenu;
import com.superred.supervisor.common.entity.system.SysRole;
import com.superred.supervisor.common.entity.system.SysUkey;
import com.superred.supervisor.common.entity.system.SysUser;
import com.superred.supervisor.common.entity.system.SysUserUkey;
import com.superred.supervisor.common.entity.system.enums.UserLockStatus;
import com.superred.supervisor.common.repository.settings.IpWhitelistRepository;
import com.superred.supervisor.common.repository.system.SysMenuRepository;
import com.superred.supervisor.common.repository.system.SysRoleRepository;
import com.superred.supervisor.common.repository.system.SysUkeyRepository;
import com.superred.supervisor.common.repository.system.SysUserUkeyRepository;
import com.superred.supervisor.manager.constant.CommonConstants;
import com.superred.supervisor.manager.model.auth.CaptchaResp;
import com.superred.supervisor.manager.model.auth.KeyPairResp;
import com.superred.supervisor.manager.model.auth.LoginUser;
import com.superred.supervisor.manager.model.vo.login.LoginReq;
import com.superred.supervisor.manager.model.vo.login.LoginResp;
import com.superred.supervisor.manager.model.vo.login.SysUserResp;
import com.superred.supervisor.manager.model.vo.login.UkeyLoginReq;
import com.superred.supervisor.manager.model.vo.login.UkeyLoginResp;
import com.superred.supervisor.manager.model.vo.login.UserInfoResp;
import com.superred.supervisor.manager.model.vo.system.MenuTreeResp;
import com.superred.supervisor.manager.repository.system.SysUserExtRepository;
import com.superred.supervisor.manager.service.CacheService;
import com.superred.supervisor.manager.service.login.LoginService;
import com.superred.supervisor.manager.utils.SecurityUtils;
import com.wf.captcha.ArithmeticCaptcha;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.security.KeyPair;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.superred.supervisor.manager.constant.CommonConstants.TOKEN_TYPE;


/**
 * 登录服务impl
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Slf4j
@Service
public class LoginServiceImpl implements LoginService {

    @Resource
    private SysUserExtRepository sysUserExtRepository;

    @Resource
    private SysRoleRepository sysRoleRepository;

    @Resource
    private SysUkeyRepository sysUkeyRepository;

    @Resource
    private SysUserUkeyRepository sysUserUkeyRepository;

    @Resource
    private SysMenuRepository sysMenuRepository;

    @Resource
    private CacheService cacheService;

    @Resource
    private IpWhitelistRepository ipWhitelistRepository;


    /**
     * 用户登录
     *
     * @param req 登录请求
     * @return 登录响应
     */
    @Override
    @SuppressWarnings("PMD.NPathComplexity")
    public LoginResp login(LoginReq req) {
        String username = req.getUsername();
        SysUser sysUser = sysUserExtRepository.selectUserByName(username);
        if (sysUser == null) {
            throw new BaseBusinessException("用户名不存在");
        }
        this.validImageCode(req.getImageCode(), req.getImageCodeKey());
        MDC.put(CommonConstants.LOG_USERNAME, sysUser.getUsername());
        String realPassword = cacheService.decodeByPrivateKey(req.getKeyId(), req.getPassword());

        SysRole sysRole = sysRoleRepository.selectRoleByUserId(sysUser.getId());
        // 是否授权
        if (sysRole == null) {
            throw new BaseBusinessException("用户未授权，请使用安全管理员授权后再进行登录");
        }
        // 是否停用
        if (sysUser.getEnable() != null && sysUser.getEnable().equals(UserLockStatus.LOCKED)) {
            throw new BaseBusinessException("该账号已被停用，无法登录");
        }
        // 是否锁定
        if (sysUser.getLockFlag().equals(UserLockStatus.LOCKED)
                && LocalDateTime.now().isBefore(sysUser.getLockTime())
        ) {
            StringBuilder errorMessage = getErrorMessage(sysUser);
            throw new BaseBusinessException(errorMessage.toString());
        }
        // 如果用户被锁定，且锁定时间已过，解锁用户
        if (sysUser.getLockFlag() != null
                && sysUser.getLockFlag().equals(UserLockStatus.LOCKED)
                && LocalDateTime.now().isAfter(sysUser.getLockTime())
        ) {
            sysUser = this.unlockUser(sysUser);
        }
        // 密码错误
        if (!BCrypt.checkpw(realPassword, sysUser.getPassword())) {
            return this.passwordError(sysUser);
        }
        // 登录成功
        SysUser sysUserUpdate = new SysUser();
        sysUserUpdate.setId(sysUser.getId());
        sysUserUpdate.setTryCount(0);
        sysUserUpdate.setLastLoginTime(LocalDateTime.now());
        sysUserExtRepository.updateById(sysUserUpdate);

        cacheService.evictSM2PrivateKey(req.getKeyId());
        return this.doLogin(sysUser, sysRole);
    }


    /**
     * {
     * "access_token": "ad839e63-14dc-4b03-9cb1-06363030659e",
     * "token_type": "bearer",
     * "refresh_token": "2029a6ef-cf67-4b49-ac47-d9d3ebf5e68e",
     * "expires_in": 1799,
     * "scope": "select",
     * "role": 1034,
     * "user_id": 5,
     * "username": "sysadmin"
     * }
     *
     * @param sysUser
     * @param sysRole
     * @return
     */
    private LoginResp doLogin(SysUser sysUser, SysRole sysRole) {
        String token = UUID.randomUUID().toString();
        Integer cacheSysLoginOverTimeSecond = cacheService.cacheSysLoginOverTimeSecond();

        LoginUser userDetails = LoginUser.builder()
                .userId(sysUser.getId())
                .username(sysUser.getUsername())
                .roleId(sysRole.getId())
                .roleCode(sysRole.getCode())
                .orgId(sysUser.getOrgId())
                .expireTimeMin(cacheSysLoginOverTimeSecond)
                .expiredDateTime(LocalDateTime.now().plusSeconds(cacheSysLoginOverTimeSecond))
                .build();

        cacheService.cacheLoginUser(token, userDetails);

        return LoginResp.builder()
                .accessToken(token)
                .tokenType(TOKEN_TYPE)
                .expiresIn(userDetails.getExpireTimeMin())
                .roleId(userDetails.getRoleId())
                .userId(userDetails.getUserId())
                .username(userDetails.getUsername())
                .roleName(sysRole.getName())
                .roleCode(sysRole.getCode())
                .isTipPassUpdate(sysUser.getFirstLogin() == 1)
                .build();

    }

    private void validImageCode(String imageCode, String imageCodeKey) {


        String codeInSession = cacheService.getImageCode(imageCodeKey);

        log.warn("codeInSession:{}, code input: {}", codeInSession, imageCode);
        if (StringUtils.isBlank(imageCode)) {
            throw new BaseBusinessException("验证码不能为空");
        }
        if (codeInSession == null) {
            throw new BaseBusinessException("验证码不存在");
        }
        if (!imageCode.equalsIgnoreCase(codeInSession)) {
            throw new BaseBusinessException("验证码输入错误");
        }
        cacheService.evictImageCode(imageCodeKey);
    }

    private SysUser unlockUser(SysUser userOrg) {
        SysUser sysUser = new SysUser();
        sysUser.setId(userOrg.getId());
        sysUser.setLockFlag(UserLockStatus.NORMAL);
        sysUser.setTryCount(0);
        sysUser.setLockTime(LocalDateTime.now());
        sysUserExtRepository.updateById(sysUser);
        log.warn("用户：{} 被解锁", userOrg.getUsername());
        return sysUserExtRepository.getById(userOrg.getId());
    }

    private StringBuilder getErrorMessage(SysUser userOrg) {
        StringBuilder errorMessage = new StringBuilder();
        long lockMillis = Duration.between(LocalDateTime.now(), userOrg.getLockTime()).toMillis();
        long lockS = lockMillis / 1000;
        long lockMinutes = lockS / 60;
        long lockSeconds = lockS % 60;
        StringBuilder sb = new StringBuilder();
        if (lockMinutes > 0) {
            sb.append(lockMinutes).append('分');
        }
        if (lockSeconds > 0) {
            sb.append(lockSeconds).append('秒');
        }
        errorMessage.append("用户帐号已被锁定，请").append(sb).append("之后重试！");
        return errorMessage;
    }

    private LoginResp passwordError(SysUser sysUser) {
        Integer maxTryCounts = cacheService.cacheLoginMaxTryCount();
        int tryCount = sysUser.getTryCount() == null ? 0 : sysUser.getTryCount();
        tryCount += 1;
        if (tryCount <= maxTryCounts) {
            SysUser sysUserUpdate = new SysUser();
            sysUserUpdate.setId(sysUser.getId());
            sysUserUpdate.setTryCount(tryCount);
            sysUserExtRepository.updateById(sysUserUpdate);
        }

        // 到达尝试次数锁定用户，设置锁定时长
        if (tryCount >= maxTryCounts && UserLockStatus.NORMAL.equals(sysUser.getLockFlag())) {
            Integer maxLockTimeMinute = cacheService.cacheLoginMaxLockTimeMinute();
            SysUser sysUserUpdate = new SysUser();
            sysUserUpdate.setId(sysUser.getId());
            sysUserUpdate.setLockFlag(UserLockStatus.LOCKED);
            sysUserUpdate.setLockTime(LocalDateTime.now().plusMinutes(maxLockTimeMinute));
            sysUserExtRepository.updateById(sysUserUpdate);
            log.warn("用户：{} 被锁定", sysUser.getUsername());
        }

        String errorMessage = "用户名或密码错误,"
                + "你已经输入" + tryCount + "次，最多可以输入"
                + maxTryCounts + "次！";

        throw new BaseBusinessException(errorMessage);
    }


    /**
     * 用户登出
     */
    @Override
    public void logout() {

        LoginUser user = SecurityUtils.getUserNullable();
        String token = SecurityUtils.getToken();

        if (user != null) {
            cacheService.evictUserSession(user.getUserId(), token);
        }

    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @Override
    public UserInfoResp getUserInfo() {
        LoginUser loginUser = SecurityUtils.getUser();
        SysUser sysUser = sysUserExtRepository.getById(loginUser.getUserId());

        SysUserResp sysUserResp = SysUserResp.fromSysUser(sysUser);

        long countIpWhiteList = ipWhitelistRepository.count(
                Wrappers.<IpWhitelist>lambdaQuery()
                        .eq(IpWhitelist::getType, IpWhiteListType.WEB_MANAGE));

        return UserInfoResp.builder()
                .permissions(sysMenuRepository.getMenuPermissionByRoleId(loginUser.getRoleId()))
                .sysUser(sysUserResp)
                .roleId(loginUser.getRoleId())
                .roleCode(loginUser.getRoleCode())
                .isTipPassUpdate(this.isTipUpdatePass(sysUser))
                .needAddIpWhitelist(countIpWhiteList > 0)
                .build();
    }

    /**
     * 获取登录用户菜单
     *
     * @return 菜单树
     */
    @Override
    public List<MenuTreeResp> getLoginUserMenu() {

        Integer roleId = SecurityUtils.getRoleId();
        List<SysMenu> menus = sysMenuRepository.selectMenuByRoleId(roleId);
        if (menus != null && !menus.isEmpty()) {
            return MenuTreeResp.buildTree(menus, -1);
        }

        return Collections.emptyList();
    }

    /**
     * 创建图片验证码
     *
     * @return 图片验证码
     */
    @Override
    public CaptchaResp createImageCode() {
        ArithmeticCaptcha captcha = this.getPositiveCaptcha();
        log.info("V2 本次生成的验证码为：{},已存放到HttpSession中", captcha.text());

        String codeKey = UUID.fastUUID().toString(true);

        cacheService.cacheCaptcha(codeKey, captcha.text());
        return CaptchaResp.builder()
                .imageCodeBase64(captcha.toBase64())
                .imageCodeKey(codeKey)
                .build();

    }

    /**
     * 创建SM2密钥对
     *
     * @return SM2密钥对响应
     */
    @Override
    public KeyPairResp createSM2KeyPair() {
        KeyPair pair = SecureUtil.generateKeyPair("SM2");
        // 获取原始密钥参数
        String privateKey = HexUtil.encodeHexStr(((BCECPrivateKey) pair.getPrivate()).getS().toByteArray());
        String publicKey = HexUtil.encodeHexStr(((BCECPublicKey) pair.getPublic()).getQ().getEncoded(false)); // 04开头未压缩格式

        String keyId = UUID.fastUUID().toString(true);

        cacheService.cacheSM2PrivateKey(keyId, privateKey);

        return KeyPairResp.builder()
                .keyId(keyId)
                .publicKeyHex(publicKey)
                .build();
    }

    /**
     * ukey登录
     *
     * @param req 登录请求
     * @return 登录响应
     */
    @Override
    public UkeyLoginResp getRandomNumber(UkeyLoginReq req) {
        String serialNumber = req.getSerialNumber();
        SysUkey ukey = sysUkeyRepository.getOne(Wrappers.<SysUkey>lambdaQuery().eq(SysUkey::getSerialNumber, serialNumber));

        // 是否绑定 0 未绑定  1 绑定
        if (ukey == null || ukey.getBind() != 1) {
            throw new BaseBusinessException("该设备未绑定用户，请联系管理员绑定用户！");
        }
        SysUserUkey sysUserUkey = sysUserUkeyRepository.getOne(Wrappers.<SysUserUkey>lambdaQuery().eq(SysUserUkey::getUkeyId, ukey.getId()));
        SysUser sysUser = sysUserExtRepository.getById(sysUserUkey.getUserId());
        String randomNumber = RandomUtil.randomNumbers(16);

        UkeyLoginResp ukeyLogin = new UkeyLoginResp();
        ukeyLogin.setSerialNumber(serialNumber);
        ukeyLogin.setRandomNumber(randomNumber);
        ukeyLogin.setUserName(sysUser.getUsername());
        ukeyLogin.setRealName(sysUser.getRealName());

        return ukeyLogin;

    }

    /**
     * 是否提醒密码修改
     *
     * @return
     */
    private boolean isTipUpdatePass(SysUser sysUser) {
        // 得到密码修改时间
        LocalDateTime passUpdateTime = sysUser.getPassUpdateTime();
        // 获取字典项中的密码更换周期
        Integer sysPassChange = cacheService.cacheSysPassChangeDay();

        if (null == passUpdateTime) {
            return false;
        }
        Date localData = Date.from(passUpdateTime.atZone(ZoneId.systemDefault()).toInstant());
        DateTime dateTime = DateUtil.offsetDay(localData, sysPassChange);
        // 与当前时间做比较
        DateTime dateTimeNow = new DateTime();
        // 当前时间大于提醒日期
        return 0 < dateTimeNow.compareTo(dateTime);
    }

    private ArithmeticCaptcha getPositiveCaptcha() {
        int max = 10;
        ArithmeticCaptcha captcha;
        while (true) {
            captcha = new ArithmeticCaptcha();
            captcha.setLen(2);
            if (max <= 0) {
                log.error("生成验证码失败");
                break;
            }
            if (captcha.text().contains("-")) {
                log.warn("生成的验证码包含负数，重新生成: {}", captcha.text());
                max--;
            } else {
                break;
            }
        }
        if (captcha.text().contains("-")) {
            throw new BaseBusinessException("生成验证码失败");
        }
        return captcha;
    }

}
