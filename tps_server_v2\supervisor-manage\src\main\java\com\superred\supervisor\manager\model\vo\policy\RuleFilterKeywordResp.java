package com.superred.supervisor.manager.model.vo.policy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.common.entity.policy.RuleFilterKeyword;
import com.superred.supervisor.manager.constant.FileFilterTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025-03-11 16:55
 */
@Data

@Builder
public class RuleFilterKeywordResp {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "规则ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ruleId;

    @Schema(description = "规则类型，file_keyword 关键词")
    private String module;

    @Schema(description = "规则类型，0 关键词，1正则表达式")
    private Integer ruleType;

    @Schema(description = "最少命中次数，默认为1")
    private Integer minMatchCount;

    @Schema(description = "规则内容")
    private String ruleContent;

    @Schema(description = "告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级")
    private Integer risk;

    @Schema(description = "文件过滤类型，1文档 2图片 3文本/网页 4压缩包 5邮件    1,2")
    private String fileFilterType;

    @Schema(description = "文件过滤类型，1文档 2图片 3文本/网页 4压缩包 5邮件    1,2")
    private String fileFilterTypeStr;

    @Schema(description = "文件最小值")
    private Integer fileFilterMinSize;

    @Schema(description = "文件最大值")
    private Integer fileFilterMaxSize;

    @Schema(description = "监测器策略数量")
    private Integer detectorPolicyCount;

    @Schema(description = "终端策略数量")
    private Integer agentPolicyCount;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "规则应用状态，0未应用，1已应用")
    private Integer status;

    @Schema(description = "包含规则")
    private String ruleContain;

    @Schema(description = "位置规则")
    private String rulePosition;

    @Schema(description = "排除规则")
    private String ruleExclude;

    @Schema(description = "规则描述")
    private String ruleDesc;

    public static RuleFilterKeywordResp fromRuleFilterKeyword(RuleFilterKeyword ruleFilterKeyword) {
        return RuleFilterKeywordResp.builder()
                .id(ruleFilterKeyword.getId())
                .ruleId(ruleFilterKeyword.getRuleId())
                .ruleType(ruleFilterKeyword.getRuleType())
                .minMatchCount(ruleFilterKeyword.getMinMatchCount())
                .ruleContent(ruleFilterKeyword.getRuleContent())
                .risk(ruleFilterKeyword.getRisk())
                .fileFilterType(ruleFilterKeyword.getFileFilterType())
                .fileFilterTypeStr(FileFilterTypeEnum.getValueByKey(ruleFilterKeyword.getFileFilterType()))
                .fileFilterMinSize(ruleFilterKeyword.getFileFilterMinSize())
                .fileFilterMaxSize(ruleFilterKeyword.getFileFilterMaxSize())
                .updateTime(ruleFilterKeyword.getUpdateTime())
                .status(ruleFilterKeyword.getStatus())
                .ruleContain(ruleFilterKeyword.getRuleContain())
                .ruleExclude(ruleFilterKeyword.getRuleExclude())
                .rulePosition(ruleFilterKeyword.getRulePosition())
                .ruleDesc(ruleFilterKeyword.getRuleDesc())
                .build();
    }
}
