package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 运维联系人(PSysOpsPerson) 实体
 *
 * <AUTHOR>
 * @since 2025-03-20 10:02:02
 */
@Data
@TableName("p_sys_ops_person")
public class SysOpsPerson {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("name")
    private String name;

    @TableField("email")
    private String email;

    @TableField("phone")
    private String phone;

    @TableField("position")
    private String position;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("modified_time")
    private LocalDateTime modifiedTime;

}

