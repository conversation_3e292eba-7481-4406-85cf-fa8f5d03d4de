package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * <p> 设备类型 设备id 第7到8位
 *
 * <AUTHOR>
 * @since 2025/03/14 14:06
 **/
@Getter
public enum DeviceType {
    /**
     * 监测器
     */
    JCQ("01", "监测器"),
    /**
     * 自监管
     */
    ZJG("02", "自监管处置系统"),
    /**
     * 管理系统
     */
    SER("03", "管理系统"),
    JCQ_JQ_GLTJ("04", "监测器集群管理套件"),
    ZDBMZJ("05", "终端保密组件");

    private final String index;
    private final String label;

    DeviceType(String index, String label) {
        this.index = index;
        this.label = label;
    }

    public static DeviceType getDeviceType(String index) {
        for (DeviceType deviceType : DeviceType.values()) {
            if (deviceType.getIndex().equals(index)) {
                return deviceType;
            }
        }
        return null;
    }
}