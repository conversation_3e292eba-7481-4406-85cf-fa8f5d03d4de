package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2025-04-07 14:04
 */
@Getter
public enum DnsTypeEnum {
    TYPE1(1, "请求报文"),
    TYPE2(2, "响应报文"),
    TYPE3(3, "双向"),
    TYPE4(4, "未知");

    private final Integer key;
    private final String value;

    DnsTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

}
