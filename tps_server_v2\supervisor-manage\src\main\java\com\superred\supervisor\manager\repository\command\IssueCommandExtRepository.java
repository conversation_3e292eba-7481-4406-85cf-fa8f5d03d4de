package com.superred.supervisor.manager.repository.command;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.command.IssueCommand;
import com.superred.supervisor.manager.mapper.command.IssueCommandExtMapper;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsResp;
import org.springframework.stereotype.Service;

/**
 * 指令
 *
 * <AUTHOR>
 */
@Service
public class IssueCommandExtRepository extends ServiceImpl<IssueCommandExtMapper, IssueCommand> {

    public IPage<DetectorCommandStatisticsResp> statisticsPage(DetectorCommandStatisticsReq req) {
        return this.getBaseMapper().statisticsPage(new Page<>(req.getStart(), req.getLimit()), req);
    }
}
