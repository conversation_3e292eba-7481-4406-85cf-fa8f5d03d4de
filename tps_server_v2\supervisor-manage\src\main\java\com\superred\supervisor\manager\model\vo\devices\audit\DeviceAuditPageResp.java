package com.superred.supervisor.manager.model.vo.devices.audit;

import com.superred.supervisor.common.entity.devices.DeviceInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 设备基本信息页面
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data

public class DeviceAuditPageResp {


    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */
    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    private String deviceId;

    /**
     * 设备类型，最长2个数字 检测器为 01 自监管 02 管理系统 03
     */
    @Schema(description = "设备类型，最长2个数字 检测器为 01 自监管 02 管理系统 03")
    private String deviceType;


    /**
     * 产品软件版本号，最长16个字符。如2012.1.5.12
     */
    @Schema(description = "产品软件版本号，最长16个字符。如2012.1.5.12")
    private String softVersion;


    /**
     * 监测器部署的客户单位名，如“XX信息中心”
     */
    @Schema(description = "监测器部署的客户单位名，如“XX信息中心”")
    private String organs;

    /**
     * 监测器部署的地理位置，如“北京市海淀区复兴路128号区政府接入机房F-12”
     */
    @Schema(description = "监测器部署的地理位置，如“北京市海淀区复兴路128号区政府接入机房F-12”")
    private String address;

    /**
     * 行政区域编码，如“100085”
     */
    @Schema(description = "行政区域编码，如“100085”")
    private String addressCode;

    /**
     * 行政区域
     */
    @Schema(description = "行政区域中文全路径")
    private String addressCodeToStr;

    /**
     * 备注信息，注册原因
     */
    @Schema(description = "备注信息，注册原因")
    private String memo;

    /**
     * 注册状态，0（成功），1（失败），2（审核中） 3离线；4无效；5已删除
     */
    @Schema(description = "注册状态，0（成功），1（失败），2（审核中） 3离线；4无效；5已删除")
    private Integer regStatus;

    /**
     * 注册时间
     */
    @Schema(description = "注册时间")
    private LocalDateTime regTime;


    /**
     * 审核方式0人工审核。1自动审核
     */
    @Schema(description = "审核方式0人工审核。1自动审核")
    private Integer auditWay;


    /**
     * 公网IP
     */
    @Schema(description = "公网IP")
    private String publicIpAddress;


    @Schema(description = "CA序列号")
    private String deviceCa;

    public static DeviceAuditPageResp from(DeviceInfo deviceInfo) {

        DeviceAuditPageResp deviceAuditPageResp = new DeviceAuditPageResp();
        deviceAuditPageResp.setDeviceId(deviceInfo.getDeviceId());
        deviceAuditPageResp.setDeviceType(deviceInfo.getDeviceType());
        deviceAuditPageResp.setSoftVersion(deviceInfo.getSoftVersion());
        deviceAuditPageResp.setOrgans(deviceInfo.getOrgans());
        deviceAuditPageResp.setAddress(deviceInfo.getAddress());
        deviceAuditPageResp.setAddressCode(deviceInfo.getAddressCode());
        deviceAuditPageResp.setMemo(deviceInfo.getMemo());
        deviceAuditPageResp.setRegStatus(deviceInfo.getRegisterStatus().getValue());
        deviceAuditPageResp.setRegTime(deviceInfo.getRegisterTime());
        deviceAuditPageResp.setAuditWay(deviceInfo.getAuditType().getValue());
        deviceAuditPageResp.setPublicIpAddress(deviceInfo.getPublicIpAddress());
        deviceAuditPageResp.setDeviceCa(deviceInfo.getDeviceCa());
        return deviceAuditPageResp;

    }
}
