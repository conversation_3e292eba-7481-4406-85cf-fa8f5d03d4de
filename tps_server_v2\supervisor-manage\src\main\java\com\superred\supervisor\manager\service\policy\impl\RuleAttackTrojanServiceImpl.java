package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleAttackTrojan;
import com.superred.supervisor.common.repository.policy.RuleAttackTrojanRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.constant.RuleSourceTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.config.TrojanPolicyConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackTrojanPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackTrojanReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackTrojanResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleAttackTrojanService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-12 16:14
 */
@Slf4j
@Service("ruleAttackTrojanService")
@AllArgsConstructor
public class RuleAttackTrojanServiceImpl implements RuleAttackTrojanService, RuleService {

    @Resource
    private RuleAttackTrojanRepository ruleAttackTrojanRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    @Override
    public IPage<RuleAttackTrojanResp> page(RuleAttackTrojanPageReq ruleAttackTrojanPageReq) {
        List<String> ruleIdList = this.getRuleIdList(ruleAttackTrojanPageReq);
        LambdaQueryWrapper<RuleAttackTrojan> queryWrapper = new LambdaQueryWrapper<RuleAttackTrojan>()
                .le(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getEndDate()), RuleAttackTrojan::getUpdateTime, ruleAttackTrojanPageReq.getEndDate())
                .ge(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getStartDate()), RuleAttackTrojan::getUpdateTime, ruleAttackTrojanPageReq.getStartDate())
                .like(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getRuleId()), RuleAttackTrojan::getRuleId, ruleAttackTrojanPageReq.getRuleId())
                .eq(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getRuleName()), RuleAttackTrojan::getRuleName, ruleAttackTrojanPageReq.getRuleName())
                .eq(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getStorePcap()), RuleAttackTrojan::getStorePcap, ruleAttackTrojanPageReq.getStorePcap())
                .eq(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getAttackClass()), RuleAttackTrojan::getAttackClass, ruleAttackTrojanPageReq.getAttackClass())
                .eq(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getAttackGroup()), RuleAttackTrojan::getAttackGroup, ruleAttackTrojanPageReq.getAttackGroup())
                .eq(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getRule()), RuleAttackTrojan::getRule, ruleAttackTrojanPageReq.getRule())
                .eq(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getDesc()), RuleAttackTrojan::getDesc, ruleAttackTrojanPageReq.getDesc())
                .eq(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getRisk()), RuleAttackTrojan::getRisk, ruleAttackTrojanPageReq.getRisk())
                .eq(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getStatus()), RuleAttackTrojan::getStatus, ruleAttackTrojanPageReq.getStatus())
                .eq(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getIsShare()), RuleAttackTrojan::getIsShare, ruleAttackTrojanPageReq.getIsShare())
                .eq(StrUtil.isNotEmpty(ruleAttackTrojanPageReq.getRuleSource()), RuleAttackTrojan::getRuleSource, ruleAttackTrojanPageReq.getRuleSource())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleAttackTrojan::getRuleId, ruleIdList)
                .ne(RuleAttackTrojan::getRuleSource, RuleSourceTypeEnum.SUPER_POLICY_SHARE.getKey())
                .orderByDesc(RuleAttackTrojan::getUpdateTime);
        Page<RuleAttackTrojan> page = new Page<>(ruleAttackTrojanPageReq.getStart(), ruleAttackTrojanPageReq.getLimit());
        IPage<RuleAttackTrojan> page1 = this.ruleAttackTrojanRepository.page(page, queryWrapper);
        return page1.convert(RuleAttackTrojanResp::fromRuleAttackTrojan);
    }

    @Override
    public RuleAttackTrojanResp getById(Long ruleId) {
        RuleAttackTrojan ruleAttackTrojan = this.ruleAttackTrojanRepository.getById(ruleId);
        return RuleAttackTrojanResp.fromRuleAttackTrojan(ruleAttackTrojan);
    }

    @Override
    public void save(RuleAttackTrojanReq ruleAttackTrojanReq) {
        RuleAttackTrojan ruleAttackTrojan = fromRuleAttackTrojanReq(ruleAttackTrojanReq);
        // 赋值ruleId
        ruleAttackTrojan.setRuleId(this.ruleIdBuilder.buildRuleId().toString());
        this.ruleAttackTrojanRepository.save(ruleAttackTrojan);
    }

    @Override
    public void edit(RuleAttackTrojanReq ruleAttackTrojanReq) {
        RuleAttackTrojan ruleAttackTrojan = fromRuleAttackTrojanReq(ruleAttackTrojanReq);
        this.ruleAttackTrojanRepository.update(ruleAttackTrojan, Wrappers.<RuleAttackTrojan>lambdaUpdate().eq(RuleAttackTrojan::getRuleId, ruleAttackTrojanReq.getRuleId()));
    }


    public static RuleAttackTrojan fromRuleAttackTrojanReq(RuleAttackTrojanReq ruleAttackTrojanReq) {
        return RuleAttackTrojan.builder()
                .ruleId(ruleAttackTrojanReq.getRuleId())
                .ruleName(ruleAttackTrojanReq.getRuleName())
                .storePcap(ruleAttackTrojanReq.getStorePcap())
                .attackClass(ruleAttackTrojanReq.getAttackClass())
                .attackGroup(ruleAttackTrojanReq.getAttackGroup())
                .rule(ruleAttackTrojanReq.getRule())
                .desc(ruleAttackTrojanReq.getDesc())
                .risk(ruleAttackTrojanReq.getRisk())
                .isShare(ruleAttackTrojanReq.getIsShare())
                .status(ruleAttackTrojanReq.getStatus())
                .ruleSource(ruleAttackTrojanReq.getRuleSource())
                .level(ruleAttackTrojanReq.getLevel())
                .updateTime(ruleAttackTrojanReq.getUpdateTime())
                .createTime(ruleAttackTrojanReq.getCreateTime())
                .ext1(ruleAttackTrojanReq.getExt1())
                .ext2(ruleAttackTrojanReq.getExt2())
                .ext3(ruleAttackTrojanReq.getExt3())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleAttackTrojanRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 验证是否在使用
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleAttackTrojan> list = this.ruleAttackTrojanRepository.list(Wrappers.<RuleAttackTrojan>lambdaQuery()
                .in(RuleAttackTrojan::getRuleId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.equals(PolicyApplyStatusEnum.APPLY.getKey().toString(), item.getStatus())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
                Integer ruleSource = Integer.valueOf(item.getRuleSource());
                if (ruleSource != RuleSourceTypeEnum.LOCAL_POLICY.getKey()) {
                    throw new BaseBusinessException("存在上级策略，上级策略不允许被删除");
                }
            });
        }
    }

    /**
     * 获取规则ID列表
     * @param ruleAttackTrojanPageReq
     * @return
     */
    private List<String> getRuleIdList(RuleAttackTrojanPageReq ruleAttackTrojanPageReq) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleAttackTrojanPageReq.getIssueDeviceType())
                || StrUtil.isBlank(ruleAttackTrojanPageReq.getPolicyId())
                || StrUtil.isBlank(ruleAttackTrojanPageReq.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleAttackTrojanPageReq.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleAttackTrojanPageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleAttackTrojanPageReq.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleAttackTrojanPageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 攻击窃密检测 - 木马活动检测策略
        return StrUtil.equals(module, PolicyModuleTwoEnum.TROJAN.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 攻击窃密检测 - 木马活动检测策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleTwoEnum.TROJAN.getKey())
                .moduleStr(PolicyModuleTwoEnum.TROJAN.getValue())
                .moduleParentStr(PolicyModuleOneEnum.ALARM.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 攻击窃密检测 - 木马活动检测策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleAttackTrojan> list = this.ruleAttackTrojanRepository.list(Wrappers.<RuleAttackTrojan>lambdaQuery()
                .in(RuleAttackTrojan::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<TrojanPolicyConfigDTO> configDTOS = list.stream().map(item -> {
            return TrojanPolicyConfigDTO.getPolicyConfig(item);
        }).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 攻击窃密检测 - 木马活动检测策略
        this.ruleAttackTrojanRepository.update(Wrappers.<RuleAttackTrojan>lambdaUpdate()
                .in(RuleAttackTrojan::getRuleId, ruleIds)
                .set(RuleAttackTrojan::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 攻击窃密检测 - 木马活动检测策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleAttackTrojan> list = this.ruleAttackTrojanRepository.list(Wrappers.<RuleAttackTrojan>lambdaQuery()
                .in(RuleAttackTrojan::getRuleId, ruleIdList));
        List<RuleAttackTrojanResp> respList = new ArrayList<>();
        list.forEach(item -> {
            RuleAttackTrojanResp resp = RuleAttackTrojanResp.fromRuleAttackTrojan(item);
            respList.add(resp);
        });
        policyDetail.setTrojanList(respList);
        return policyDetail;
    }
}
