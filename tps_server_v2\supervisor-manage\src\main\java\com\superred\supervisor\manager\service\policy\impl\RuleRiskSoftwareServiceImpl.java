package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleRiskSoftware;
import com.superred.supervisor.common.repository.policy.RuleRiskSoftwareRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.constant.RiskSoftwareEnum;
import com.superred.supervisor.manager.constant.RuleSourceTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.config.RiskSoftwarePolicyConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.model.vo.policy.RuleRiskSoftwarePageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleRiskSoftwareReq;
import com.superred.supervisor.manager.model.vo.policy.RuleRiskSoftwareResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleRiskSoftwareService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-28 15:26
 */
@Slf4j
@Service("ruleRiskSoftwareService")
public class RuleRiskSoftwareServiceImpl implements RuleRiskSoftwareService, RuleService {

    @Resource
    private RuleRiskSoftwareRepository ruleRiskSoftwareRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    @Override
    public IPage<RuleRiskSoftwareResp> page(RuleRiskSoftwarePageReq ruleRiskSoftwarePageReq) {
        // 查询
        List<String> ruleIdList = this.getRuleIdList(ruleRiskSoftwarePageReq);
        LambdaQueryWrapper<RuleRiskSoftware> queryWrapper = new LambdaQueryWrapper<RuleRiskSoftware>()
                .like(!StrUtil.isEmpty(ruleRiskSoftwarePageReq.getRuleId()), RuleRiskSoftware::getRuleId, ruleRiskSoftwarePageReq.getRuleId())
                .like(!StrUtil.isEmpty(ruleRiskSoftwarePageReq.getRuleContent()), RuleRiskSoftware::getRuleContent, ruleRiskSoftwarePageReq.getRuleContent())
                .eq(!StrUtil.isEmpty(ruleRiskSoftwarePageReq.getStatus()), RuleRiskSoftware::getStatus, ruleRiskSoftwarePageReq.getStatus())
                .eq(!StrUtil.isEmpty(ruleRiskSoftwarePageReq.getIsShare()), RuleRiskSoftware::getIsShare, ruleRiskSoftwarePageReq.getIsShare())
                .eq(!StrUtil.isEmpty(ruleRiskSoftwarePageReq.getRuleSource()), RuleRiskSoftware::getRuleSource, ruleRiskSoftwarePageReq.getRuleSource())
                .le(CharSequenceUtil.isNotEmpty(ruleRiskSoftwarePageReq.getEndDate()), RuleRiskSoftware::getUpdateTime, ruleRiskSoftwarePageReq.getEndDate())
                .ge(CharSequenceUtil.isNotEmpty(ruleRiskSoftwarePageReq.getStartDate()), RuleRiskSoftware::getUpdateTime, ruleRiskSoftwarePageReq.getStartDate())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleRiskSoftware::getRuleId, ruleIdList)
                .ne(RuleRiskSoftware::getRuleSource, RuleSourceTypeEnum.SUPER_POLICY_SHARE.getKey())
                .orderByDesc(RuleRiskSoftware::getUpdateTime);
        Page<RuleRiskSoftware> page = new Page<>(ruleRiskSoftwarePageReq.getStart(), ruleRiskSoftwarePageReq.getLimit());
        IPage<RuleRiskSoftware> page1 = this.ruleRiskSoftwareRepository.page(page, queryWrapper);
        return page1.convert(item -> {
            RuleRiskSoftwareResp ruleRiskSoftwareResp = RuleRiskSoftwareResp.fromRuleRiskSoftware(item);
            List<String> list = JsonUtil.fromJson(ruleRiskSoftwareResp.getRuleContent(), new TypeReference<List<String>>() {
            });
            StrBuilder sb = new StrBuilder();
            for (String s : list) {
                sb.append(RiskSoftwareEnum.getValueByKey(s));
                sb.append(",");
            }
            String ruleContentStr = sb.toString();
            ruleRiskSoftwareResp.setRuleContentStr(ruleContentStr.substring(0, ruleContentStr.length() - 1));
            return ruleRiskSoftwareResp;
        });
    }

    @Override
    public RuleRiskSoftwareResp getById(Long ruleId) {
        RuleRiskSoftware ruleRiskSoftware = this.ruleRiskSoftwareRepository.getOne(Wrappers.<RuleRiskSoftware>lambdaQuery()
                .eq(RuleRiskSoftware::getRuleId, ruleId)
                .last("limit 1"));
        return RuleRiskSoftwareResp.fromRuleRiskSoftware(ruleRiskSoftware);
    }

    @Override
    public void save(RuleRiskSoftwareReq ruleRiskSoftwareReq) {
        RuleRiskSoftware ruleRiskSoftware = fromRuleRiskSoftwareReq(ruleRiskSoftwareReq);
        // 赋值策略ID
        ruleRiskSoftware.setRuleId(this.ruleIdBuilder.buildRuleId().toString());
        this.ruleRiskSoftwareRepository.save(ruleRiskSoftware);
    }

    @Override
    public void edit(RuleRiskSoftwareReq ruleRiskSoftwareReq) {
        RuleRiskSoftware ruleRiskSoftware = fromRuleRiskSoftwareReq(ruleRiskSoftwareReq);
        this.ruleRiskSoftwareRepository.update(ruleRiskSoftware, Wrappers.<RuleRiskSoftware>lambdaUpdate().eq(RuleRiskSoftware::getRuleId, ruleRiskSoftwareReq.getRuleId()));
    }

    public static RuleRiskSoftware fromRuleRiskSoftwareReq(RuleRiskSoftwareReq ruleRiskSoftwareReq) {
        return RuleRiskSoftware.builder()
                .ruleName(ruleRiskSoftwareReq.getRuleName())
                .ruleContent(ruleRiskSoftwareReq.getRuleContent())
                .ruleDesc(ruleRiskSoftwareReq.getRuleDesc())
                .status(ruleRiskSoftwareReq.getStatus())
                .isShare(ruleRiskSoftwareReq.getIsShare())
                .ruleSource(ruleRiskSoftwareReq.getRuleSource())
                .updateTime(ruleRiskSoftwareReq.getUpdateTime())
                .createTime(ruleRiskSoftwareReq.getCreateTime())
                .ext1(ruleRiskSoftwareReq.getExt1())
                .ext2(ruleRiskSoftwareReq.getExt2())
                .ext3(ruleRiskSoftwareReq.getExt3())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleRiskSoftwareRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }


    /**
     * 验证是否在使用
     *
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleRiskSoftware> list = this.ruleRiskSoftwareRepository.list(Wrappers.<RuleRiskSoftware>lambdaQuery()
                .in(RuleRiskSoftware::getRuleId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.equals(PolicyApplyStatusEnum.APPLY.getKey().toString(), item.getStatus())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
                Integer ruleSource = Integer.valueOf(item.getRuleSource());
                if (ruleSource != RuleSourceTypeEnum.LOCAL_POLICY.getKey()) {
                    throw new BaseBusinessException("存在上级策略，上级策略不允许被删除");
                }
            });
        }
    }

    /**
     * 获取规则ID列表
     * @param ruleRiskSoftwarePageReq
     * @return
     */
    private List<String> getRuleIdList(RuleRiskSoftwarePageReq ruleRiskSoftwarePageReq) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleRiskSoftwarePageReq.getIssueDeviceType())
                || StrUtil.isBlank(ruleRiskSoftwarePageReq.getPolicyId())
                || StrUtil.isBlank(ruleRiskSoftwarePageReq.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleRiskSoftwarePageReq.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleRiskSoftwarePageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleRiskSoftwarePageReq.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleRiskSoftwarePageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 风险软件使用行为采集
        return StrUtil.equals(module, PolicyModuleOneEnum.NETWORK_BEHAVIOR.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 风险软件使用行为采集
        return PolicyModuleResp.builder()
                .module(PolicyModuleOneEnum.NETWORK_BEHAVIOR.getKey())
                .moduleStr(PolicyModuleOneEnum.NETWORK_BEHAVIOR.getValue())
                .moduleParentStr(PolicyModuleOneEnum.NETWORK_BEHAVIOR.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 风险软件使用行为采集
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleRiskSoftware> list = this.ruleRiskSoftwareRepository.list(Wrappers.<RuleRiskSoftware>lambdaQuery()
                .in(RuleRiskSoftware::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<RiskSoftwarePolicyConfigDTO> configDTOS = list.stream().map(RiskSoftwarePolicyConfigDTO::getPolicyConfig).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 风险软件使用行为采集
        this.ruleRiskSoftwareRepository.update(Wrappers.<RuleRiskSoftware>lambdaUpdate()
                .in(RuleRiskSoftware::getRuleId, ruleIds)
                .set(RuleRiskSoftware::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 风险软件使用行为采集
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleRiskSoftware> list = this.ruleRiskSoftwareRepository.list(Wrappers.<RuleRiskSoftware>lambdaQuery()
                .in(RuleRiskSoftware::getRuleId, ruleIdList));
        List<RuleRiskSoftwareResp> respList = new ArrayList<>();
        list.forEach(item -> {
            RuleRiskSoftwareResp resp = RuleRiskSoftwareResp.fromRuleRiskSoftware(item);
            respList.add(resp);
        });
        policyDetail.setRiskSoftwareList(respList);
        return policyDetail;
    }
}
