package com.superred.supervisor.common.entity.command;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <p>
 * 终端保密检查组件指令表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("sys_issue_agent")
public class IssueAgent {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 设备编号
     */
    private String deviceId;

    /**
     * 命令类型
     */
    private String type;

    /**
     * 命令ID，主要用于跟踪每次命令的执行情况，每次下发命令时，该值递增或更新。
     */
    private String cmdId;

    /**
     * 命令
     */
    private String cmd;

    /**
     * 模块
     */
    private String module;

    /**
     * 策略版本
     */
    private String version;

    /**
     * 系统命令参数
     */
    private String param;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 指令下发状态，0（未下发）、1（已下发）
     */
    private Integer status;

    /**
     * 下发时间
     */
    private LocalDateTime issueTime;

    /**
     * 是否管理中心下发，0：否，1：是
     */
    private Integer isMgr;

    /**
     * 是否上报到管理中心，0：否，1：是
     */
    private Integer isReport;

    /**
     * 上报到管理中心时间
     */
    private LocalDateTime reportTime;

    /**
     * 执行结果，取值为：0（成功）、1（失败）
     */
    private Integer result;

    /**
     * 执行结果描述，result为1，说明失败原因
     */
    private String message;

    /**
     * 策略规则个数
     */
    private Integer num;

    /**
     * 策略Base64编码后规则内容
     */
    private String config;

    /**
     * 结果上报时间
     */
    private LocalDateTime time;

    /**
     * 在检测器成功执行的规则ID列表
     */
    private String success;

    /**
     * 在检测器未成功执行的规则对象列表
     */
    private String fail;

    /**
     * 策略id
     */
    private Long policyId;

    /**
     * 是否产生过告警；0：否；1：是
     */
    private Integer produceAlarm;
}