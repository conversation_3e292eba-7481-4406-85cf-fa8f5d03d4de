<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.superred.supervisor.manager.mapper.setting.LocalDeviceSuspectedLogExtMapper">


    <select id="pageLocalDeviceSuspectedLog"
            resultType="com.superred.supervisor.manager.model.vo.index.LocalDeviceEventPageResp">
        SELECT
        id,
        event_type as eventType,
        time,
        risk,
        msg,
        status
        FROM
        local_device_suspected_log
        <where>
            <if test="req.status != null">
                and status = #{req.status}
            </if>
            <if test="req.startTime != null">
                and time &gt;= #{req.startTime}
            </if>
            <if test="req.endTime != null">
                and time &lt;= #{req.endTime}
            </if>
        </where>
    </select>
</mapper>