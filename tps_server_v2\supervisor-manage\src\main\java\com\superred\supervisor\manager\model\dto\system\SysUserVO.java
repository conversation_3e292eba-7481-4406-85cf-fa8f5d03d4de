package com.superred.supervisor.manager.model.dto.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SysUserVO.
 * TODO.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-21 15:13
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysUserVO {


    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 用户名.
     **/
    @Schema(description = "用户名")
    private String username;

    /**
     * 真实姓名.
     **/
    @Schema(description = "真实姓名")
    private String realName;

}
