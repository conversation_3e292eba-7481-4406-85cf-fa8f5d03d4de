package com.superred.supervisor.manager.model.vo.devices;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DeviceInterfaceManage
 * <p> 接入口信息
 * @since 2022-08-12 10:05:34
 **/
@Data
public class DeviceInterfaceManageResp {

    private static final long serialVersionUID = 1L;


    /**
     * 网卡编号，“eth0”
     */
    @Schema(description = "网卡编号，“eth0”")
    private String flag;

    /**
     * ip地址,************
     */
    @Schema(description = "ip地址,************")
    private String ip;

    /**
     * 子网掩码，***********/24
     */
    @Schema(description = "子网掩码，***********/24")
    private String netmask;

    /**
     * 网关地址,*************
     */
    @Schema(description = "网关地址,*************")
    private String gateway;

    /**
     * mac地址，00:0d:48:09:4e:88
     */
    @Schema(description = "mac地址，00:0d:48:09:4e:88")
    private String mac;

    /**
     * 是否管理口，true，false
     */
    @Schema(description = "是否管理口，true，false")
    private Boolean manage;


}
