package com.superred.supervisor.gateway.exception;


import cn.hutool.core.util.StrUtil;
import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.constant.auth.AuthActiveEnum;
import com.superred.supervisor.gateway.constant.auth.AuthStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * common exception handler, handle the follow exceptions:
 *
 * <AUTHOR>
 * {@link RuntimeException}
 * {@link Throwable}
 * {@link MissingServletRequestParameterException}
 * {@link HttpMessageNotReadableException}
 * {@link NoHandlerFoundException}
 * {@link MethodArgumentNotValidException}
 * {@link BindException}
 * {@link ConstraintViolationException}
 * @since 2021/8/20 3:06 下午
 */
@Slf4j
@RestControllerAdvice
public class GatewayExceptionHandler {

    @ExceptionHandler({ApiBaseException.class})
    public ApiResponse<String> handleApiBaseException(ApiBaseException e) {
        log.error("ApiBaseException==>{}", e.getMessage(), e);
        return ApiResponse.failure(e.getMessage());
    }


    @ExceptionHandler({ApiUnAuthException.class})
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public void handleApiUnAuthException(ApiUnAuthException e,
            HttpServletResponse response) {
        log.error("ApiUnAuthException==>{}", e.getMessage(), e);

        if (e.getStatus() != null) {
            response.setHeader(AuthStatusEnum.KEY, e.getStatus().getCode());
        }
        if (e.getActive() != null) {
            response.setHeader(AuthActiveEnum.KEY, e.getActive().getCode());
        }
    }
    /*==============================================*/


    @ExceptionHandler({RuntimeException.class})
    public ApiResponse<String> handleRunTimeException(RuntimeException e) {
        log.error("RuntimeException==>{}", e.getMessage(), e);
        return ApiResponse.failure(e.getMessage());
    }


    @ExceptionHandler({Throwable.class})
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<String> handleThrowable(Throwable e) {
        log.error("Throwable==>{}", e.getMessage(), e);
        return ApiResponse.failure(e.getMessage());
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<String> handleMissingRequestParameterException(Exception e) {
        log.error("MissingServletRequestParameterException == >", e);
        return ApiResponse.failure(e.getMessage());
    }


    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<String> handleBadRequest(Exception e) {
        log.error("Bad Request Execption == >", e);
        return ApiResponse.failure("unable to parse request or unacceptable body");
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ApiResponse<String> handleNoHandlerFoundException(NoHandlerFoundException e) {
        log.error("NoHandlerFoundException==>{}", e.getMessage(), e);
        return ApiResponse.failure(e.getHttpMethod() + " " + e.getRequestURL());
    }


    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<String> methodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error("MethodArgumentNotValidException==>{}", e.getMessage(), e);
        List<String> errMsgList = new LinkedList<>();
        e.getBindingResult().getAllErrors().forEach(error -> errMsgList.add(
                ((FieldError) error).getField() + StrUtil.SPACE + error.getDefaultMessage())
        );
        return ApiResponse.failure(errMsgList.toString());
    }


    @ExceptionHandler(BindException.class)
    public ApiResponse<Void> handleBindException(BindException e) {
        log.error("BindException==>{}", e.getMessage(), e);
        List<String> errMsgList = new LinkedList<>();
        e.getBindingResult().getAllErrors().forEach(error -> errMsgList.add(
                ((FieldError) error).getField() + StrUtil.SPACE + error.getDefaultMessage())
        );
        return ApiResponse.failure(errMsgList.toString());
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ApiResponse<Void> handleViolationException(ConstraintViolationException e) {
        log.error("ConstraintViolationException==>{}", e.getMessage(), e);
        List<String> errorList = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessageTemplate).collect(Collectors.toList());
        return ApiResponse.failure(errorList.toString());
    }


}
