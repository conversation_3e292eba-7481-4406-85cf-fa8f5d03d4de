package com.superred.supervisor.manager.model.vo.terminal.manage;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 终端设备信息
 */
@Data

public class TerminalStatusPageResp {


    @Schema(description = "设备编号")
    private String deviceId;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "mac")
    private String mac;


    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "主机操作系统")
    private String os;

    @Schema(description = "区域")
    private String regionPathStr;

    @Schema(description = "区域ID")
    private Integer regionId;

    @Schema(description = "单位名称")
    private String company;

    @Schema(description = "所属部门名称")
    private String orgNamePath;

    @Schema(description = "终端责任人ID")
    private String userId;

    @Schema(description = "终端责任人姓名")
    private String userName;


    @Schema(description = "前八位为年月日，下划线后自定义")
    private String softVersion;


    @Schema(description = "注册时间")
    private Date registerTime;


    @Schema(description = "最后升级时间")
    private Date lastUpgradeTime;


    @Schema(description = "审核时间")
    private Date verifyTime;

    @Schema(description = "备注信息")
    private String memo;

    @Schema(description = "心跳时间")
    private LocalDateTime heartbeatTime;


    @Schema(description = "在线状态， 1 在线 2 离线 3 已卸载")
    private Integer onlineStatus;


    @Schema(description = "激活状态， 1 未激活 2 已激活")
    private Integer activateStatus;

}
