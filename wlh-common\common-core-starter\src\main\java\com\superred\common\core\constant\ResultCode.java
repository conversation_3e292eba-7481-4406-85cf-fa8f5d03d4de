package com.superred.common.core.constant;

/**
 * core cloud services list and error code
 *
 * <AUTHOR>
 * @since 0.0.1
 */
public final class ResultCode {

    private ResultCode() {
    }


    public static final Integer SUCCESS = 10200;

    public static final String SUCCESS_MSG = "请求成功";

    public static final String INVALID_ERROR_MSG = "参数错误: ";

    public static final String NOT_FOUND_ERROR_MSG = "资源未找到: ";

    public static final String BODY_ERROR_MSG = "无法解析或接收的请求体: ";

    public static final String PATH_ERROR_MSG = "请求路径错误: ";

    public static final String UNAUTHORIZED_ERROR_MSG = "未授权错误: ";

    /**
     * http 500 error code
     */
    public static final Integer SERVER_ERROR = 10000;
    /**
     * Database access error
     */
    public static final Integer DATABASE_ERROR = 10001;

    /**
     * Message broker access error
     */
    public static final Integer MIDDLEWARE_ERROR = 10002;

    /**
     * Error on API invocation
     */
    public static final Integer API_INVOCATION_ERROR = 10003;

    /**
     * No handler found
     */
    public static final Integer ERROR_URI = 10404;


    /**
     * http 422 error code
     * Invalid parameter, xxxx is required(Message will be different based on the parrameter)
     */
    public static final Integer INVALID_PARAMETER_ERROR = 10422;

    /**
     * http 404 error code
     * resource not found
     */
    public static final Integer NOT_FOUND_ERROR = 11404;

    /**
     * resource not found
     */
    public static final Integer BAD_REQUEST = 12404;

    /**
     * Unauthorized access error
     */
    public static final Integer UNAUTHORIZED_ERROR = 10401;


}
