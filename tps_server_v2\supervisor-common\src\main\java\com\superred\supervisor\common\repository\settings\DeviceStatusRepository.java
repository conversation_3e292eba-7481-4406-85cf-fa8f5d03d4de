package com.superred.supervisor.common.repository.settings;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.settings.DeviceStatus;
import com.superred.supervisor.common.mapper.settings.DeviceStatusMapper;
import org.springframework.stereotype.Repository;

/**
 * 自监管系统运行状态(LocalDeviceStatus) Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-27 15:31:59
 */
@Repository
public class DeviceStatusRepository extends ServiceImpl<DeviceStatusMapper, DeviceStatus> {

}

