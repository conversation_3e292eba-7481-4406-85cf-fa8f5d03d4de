package com.superred.supervisor.manager.model.vo.devices.bak;

import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * devicebak磁盘信息添加请求
 *
 * <AUTHOR>
 * @since 2025/03/14
 */
@Data

public class DevicebakDiskInfoAddReq {


    /**
     * 磁盘大小，单位GB
     */
    @Schema(description = "磁盘大小，单位GB")
    @NotNull(message = "磁盘大小 不能为空")
    @Min(value = 1, message = "磁盘大小 不能小于1")
    @Max(value = 1000000000, message = "磁盘大小 不能大于1000000000")
    private Integer size;

    /**
     * 磁盘序列号，“ST1000NM0012”
     */
    @Schema(description = "磁盘序列号，“ST1000NM0012”")
    @NotBlank(message = "磁盘序列号 不能为空")
    @ByteSize(max = 64, message = "磁盘序列号 不能超过64个字节")
    private String serial;


}
