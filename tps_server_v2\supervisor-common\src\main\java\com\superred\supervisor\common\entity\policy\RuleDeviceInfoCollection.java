package com.superred.supervisor.common.entity.policy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2025-04-18 14:34
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_rule_device_info_collection", autoResultMap = true)
public class RuleDeviceInfoCollection extends Model<RuleDeviceInfoCollection> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 策略ID
     */
    @TableId(value = "rule_id", type = IdType.AUTO)
    private String ruleId;

    /**
     * 策略名称
     */
    private String ruleName;

    /**
     * 策略内容
     */
    private String ruleContent;

    /**
     * 策略描述
     */
    private String ruleDesc;

    /**
     * 规则应用状态，0未应用，1已应用
     */
    private String status;

    /**
     * 是否共享
     */
    private String isShare;

    /**
     * 策略来源 1 本级 2上级
     */
    private String ruleSource;

    /**
     * 平台级别
     */
    private String level;

    /**
     * 规则更新时间
     */
    private String updateTime;

    /**
     * 策略创建时间
     */
    private String createTime;

    /**
     * 扩展字段1
     */
    private Long ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 上级共享策略ID
     */
    private String upRuleId;

}
