package com.superred.supervisor.manager.constant;

import cn.hutool.core.util.StrUtil;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-04-18 15:39
 */
@Getter
public enum DeviceInfoCollectionEnum {
    TYPE0(0, "终端基本信息"),
    TYPE1(1, "外接信息化设备"),
    TYPE2(2, "外接存储介质"),
    TYPE3(3, "外接智能设备"),
    TYPE4(4, "安全防护与数据保护类软件信息"),
    TYPE5(5, "开启或使用无线热点情况");

    private final Integer key;
    private final String value;

    DeviceInfoCollectionEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(String str) {
        String retrurnStr = "";
        if (StrUtil.isBlank(str)) {
            return retrurnStr;
        }
        StringBuffer sb = new StringBuffer();
        List<Integer> keyList = PolicyUtils.strToIntList(str);
        for (DeviceInfoCollectionEnum deviceInfoCollectionEnum : DeviceInfoCollectionEnum.values()) {
            for (Integer key : keyList) {
                if (deviceInfoCollectionEnum.getKey().equals(key)) {
                    sb.append(deviceInfoCollectionEnum.getValue());
                    sb.append(", ");
                }
            }
        }
        retrurnStr = sb.toString();
        if (StrUtil.isNotBlank(retrurnStr)) {
            return retrurnStr.substring(0, retrurnStr.length() - 2);
        }
        return retrurnStr;
    }
}
