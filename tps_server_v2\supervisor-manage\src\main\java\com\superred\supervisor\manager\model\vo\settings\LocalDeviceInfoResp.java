package com.superred.supervisor.manager.model.vo.settings;

import com.superred.supervisor.manager.common.annotation.BlankOrPattern;
import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月24日
 */
@Data
public class LocalDeviceInfoResp {

    @Schema(description = "设备编号")
    @NotNull(message = "设备编号 不能为空")
    private String deviceId;

    @Schema(description = "全数字组成的最长为2个字节的字符串，检测器为“01”")
    private String deviceType;


    @Schema(description = "厂商英文名")
    private String vendorName;

    @Schema(description = "检测器部署的单位名")
    @NotBlank(message = "部署单位 不能为空")
    @ByteSize(min = 1, max = 64, message = "部署单位 长度限制64字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9!\"#$%&'()*+,\\-./:;<=>?@\\[\\\\\\]^_`{|}~]+$", message = "部署单位 格式错误")
    private String organs;

    @Schema(description = "检测器部署的地理位置")
    @ByteSize(max = 128, message = "详细地址 长度限制128字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9!\"#$%&'()*+,\\-./:;<=>?@\\[\\\\\\]^_`{|}~]+$", message = "详细地址 格式错误")
    private String address;

    @Schema(description = "行政区域编码类型，表示检测器部署所在地的区域编码。")
    @NotNull(message = "行政行业/区划 不能为空")
    private String addressCode;


    @Schema(description = "备注信息")
    @ByteSize(max = 128, message = "备注 长度限制128字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9!\"#$%&'()*+,\\-./:;<=>?@\\[\\\\\\]^_`{|}~]+$", message = "备注 格式错误")
    private String memo;

    @Schema(description = "在线时长 ")
    private Integer uptime;

    @Schema(description = "心跳时间")
    private Date heartbeatTime;

    @Schema(description = "业务状态时间")
    private Date businessTime;

    @Schema(description = "平台类型 001 国家级 010 省级 011地市级 100 区县级 ")
    private String platformType;

    @Schema(description = "1行政监管，2行业监管 ")
    @NotNull(message = "监管方式 不能为空")
    @Min(value = 1, message = "监管方式 格式错误")
    @Max(value = 2, message = "监管方式 格式错误")
    private Integer addressType;

    @Schema(description = "部署单位的公网地址")
    private String publicIpAddress;

    @Schema(description = "部署区域的上级code ")
    private String addressCodeParents;

    @Schema(description = "完整的行政区划，中文")
    private String chinesePath;
}
