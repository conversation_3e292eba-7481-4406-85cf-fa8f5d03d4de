package com.superred.supervisor.manager.model.vo.devices.manager;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 修改流量阈值
 *
 * <AUTHOR>
 * @since 2025/3/12 17:03
 */

@Data
public class DeviceRemarkEditReq {

    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */

    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    @NotBlank(message = "设备编号不能为空")
    private String deviceId;


    /**
     * 本地备注
     */
    @Schema(description = "本地备注")
    @NotBlank(message = "本地备注不能为空")
    private String localRemarks;
}
