package com.superred.supervisor.manager.model.vo.devices;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 终端设备信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Data
public class TerminalDeviceInfoDTO {

    @Schema(description = "设备编号")
    private String deviceId;

    @Schema(description = "全数字组成的最长为2个字节的字符串，检测器为“01”")
    private String deviceType;

    @Schema(description = "终端计算机名")
    private String deviceName;

    @Schema(description = "终端操作系统名")
    private String osName;

    @Schema(description = "终端部门")
    private String department;

    @Schema(description = "终端负责人")
    private String personResponsible;

    @Schema(description = "前八位为年月日，下划线后自定义")
    private String softVersion;

    @Schema(description = "设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值")
    private String interfaces;

    @Schema(description = "内存总数，表示整个设备的内存大小，单位MB。")
    private Long memTotal;

    @Schema(description = "CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。	physical_id：CPU ID，数值类型	core：CPU核心数，数值类型；	clock：CPU主频，数值类型精确到小数点后1位	")
    private String cpuInfo;

    @Schema(description = "磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。	size为数值类型，表示磁盘大小，单位GB；	serial为字符串类型，最长64个字节，表示磁盘序列号")
    private String diskInfo;

    @Schema(description = "检测器部署的地理位置")
    private String address;

    @Schema(description = "行政区域编码类型，表示检测器部署所在地的区域编码。")
    private String addressCode;

    @Schema(description = "注册时间")
    private Date registerTime;

    @Schema(description = "注册状态，0（成功），1（失败），2（审核中），5（注销）6 已卸载")
    private Integer registerStatus;

    @Schema(description = "注册状态描述")
    private String registerMessage;

    @Schema(description = "审核时间")
    private Date verifyTime;

    @Schema(description = "备注信息")
    private String memo;

    @Schema(description = "心跳时间")
    private Date heartbeatTime;

    @Schema(description = "上报类型")
    private String reportType;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "终端责任人ID")
    private String userId;

    @Schema(description = "终端责任人姓名")
    private String userName;

    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "主机操作系统")
    private String os;

    @Schema(description = "主机CPU架构")
    private String arch;

    @Schema(description = "最后升级时间")
    private Date lastUpgradeTime;

    @Schema(description = "升级包使用的操作系统字段")
    private String updateOs;

    @Schema(description = "升级包使用的CPU架构")
    private String updateArch;

    @Schema(description = "密级")
    private String secret;


    @Schema(description = "终端状态 0 在线，1 审核失败，2 待审核，3 禁用 4 离线 5 注销 6已卸载")
    private Integer status;

    @Schema(description = "终端状态 0 在线，1 审核失败，2 待审核，3 禁用 4 离线 5 注销 6已卸载")
    private String statusStr;

    @Schema(description = "在线数量")
    private Integer onlineCount;

    @Schema(description = "离线数量")
    private Integer offlineCount;

    @Schema(description = "单位名称")
    private String company;

    @Schema(description = "所属部门名称")
    private String orgName;

    @Schema(description = "ipMac")
    private String ipMac;

    @Schema(description = "激活状态：0未激活，1激活")
    private Integer activationStatus;

    @Schema(description = "激活状态变更时间")
    private LocalDateTime changeTime;

    @Schema(description = "是否可删除")
    private Boolean canDelete;

}
