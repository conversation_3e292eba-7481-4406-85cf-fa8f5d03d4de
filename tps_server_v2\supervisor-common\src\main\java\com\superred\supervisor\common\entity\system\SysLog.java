package com.superred.supervisor.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 日志表 实体
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:20
 */
@Data
@TableName("p_sys_log")
public class SysLog {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 日志唯一id
     */
    @TableField("uuid")
    private String uuid;

    /**
     * 管理员角色
     */
    @TableField("role")
    private String role;

    /**
     * 操作模块
     */
    @TableField("operate_module")
    private String operateModule;

    /**
     * 操作行为
     */
    @TableField("operate_type")
    private String operateType;

    /**
     * 操作日期
     */
    @TableField("operate_date")
    private LocalDateTime operateDate;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 行为类型
     */
    @TableField("behaviour_type")
    private String behaviourType;

    /**
     * 日志风险级别：1紧急、2重要、3一般、4信息
     */
    @TableField("level")
    private String level;

    /**
     * 操作ip
     */
    @TableField("host_ip")
    private String hostIp;

    /**
     * 操作设备id
     */
    @TableField("host_id")
    private String hostId;

    /**
     * 日志模板表type
     */
    @TableField("module_type")
    private String moduleType;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 操作人员名字
     */
    @TableField("username")
    private String username;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modified_time")
    private LocalDateTime modifiedTime;

    /**
     * 1 表示删除，0 表示未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 结果 1成功 2失败
     */
    @TableField("result")
    private Integer result;

    /**
     * 产品类别
     */
    @TableField("product")
    private String product;

}

