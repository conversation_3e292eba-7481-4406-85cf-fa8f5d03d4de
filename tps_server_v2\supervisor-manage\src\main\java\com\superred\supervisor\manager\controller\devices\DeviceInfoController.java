package com.superred.supervisor.manager.controller.devices;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.manager.model.vo.devices.TerminalDeviceInfoDTO;
import com.superred.supervisor.manager.model.vo.devices.TerminalDeviceInfoPageReq;
import com.superred.supervisor.manager.model.vo.devices.DetectorListReq;
import com.superred.supervisor.manager.model.vo.devices.DetectorListResp;
import com.superred.supervisor.manager.service.terminal.TerminalDeviceInfoService;
import com.superred.supervisor.manager.service.devices.DeviceInfoService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "3.3. 设备信息")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/device_info")
public class DeviceInfoController {
    @Resource
    private DeviceInfoService deviceInfoService;

    @Resource
    private TerminalDeviceInfoService terminalDeviceInfoService;

    /**
     * 检测器列表查询
     */
    @ApiOperationSupport(order = 1)
    @Operation(summary = "1. 检测器列表查询")
    @PostMapping("/detector/list")
    public R<List<DetectorListResp>> detectorList(DetectorListReq req) {
        List<DetectorListResp> list = deviceInfoService.detectorList(req);
        return R.success(list);
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2. 终端列表查询")
    @PostMapping("/agent/page")
    public RPage<TerminalDeviceInfoDTO> agentPage(TerminalDeviceInfoPageReq req) {
        Page<TerminalDeviceInfoDTO> page = terminalDeviceInfoService.agentPage(req);
        return new RPage<>(page);
    }

}
