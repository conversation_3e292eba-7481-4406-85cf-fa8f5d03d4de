package com.superred.supervisor.manager.model.vo.system.org;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 导出部门Excel信息实体
 *
 * @author: hailong.qu
 * @since: 2025/6/16 15:30
 */
@Data
public class ExportOrgVO {

    @ExcelProperty(value = "部门名称", index = 0)
    private String name;
    @ExcelProperty(value = "上级部门名称", index = 1)
    private String parentName;
    @ExcelProperty(value = "行政区域", index = 2)
    private String regionName;
    @ExcelProperty(value = "排序", index = 3)
    private String sort;
    @ExcelProperty(value = "描述", index = 4)
    private String desc;
    @ExcelProperty(value = "创建时间", index = 5)
    private String createTime;
}
