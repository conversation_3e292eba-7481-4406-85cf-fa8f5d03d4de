package com.superred.supervisor.manager.model.vo.devices.manager;

import com.superred.supervisor.common.entity.devices.DeviceSuspectedLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 异常状态处理
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data

public class DeviceSuspectedStatusPageResp {


    @Schema(description = "")
    private Integer id;


    /**
     * 异常类型，1系统异常，2软件异常，3插件异常，4策略异常
     */
    @Schema(description = "异常类型，1系统异常，2软件异常，3插件异常，4策略异常")
    private Integer eventType;

    /**
     * 告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级
     */
    @Schema(description = "告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级")
    private Integer risk;

    /**
     * 异常描述，如“关键词检测服务崩溃”
     */
    @Schema(description = "异常描述，如“关键词检测服务崩溃”")
    private String msg;

    /**
     * 异常产生时间
     */
    @Schema(description = "异常产生时间")
    private LocalDateTime time;


    public static DeviceSuspectedStatusPageResp from(DeviceSuspectedLog deviceSuspectedLog) {

        DeviceSuspectedStatusPageResp deviceSuspectedStatusPageResp = new DeviceSuspectedStatusPageResp();
        deviceSuspectedStatusPageResp.setId(deviceSuspectedLog.getId());
        deviceSuspectedStatusPageResp.setEventType(Integer.valueOf(deviceSuspectedLog.getEventType()));
        deviceSuspectedStatusPageResp.setRisk(Integer.valueOf(deviceSuspectedLog.getRisk()));
        deviceSuspectedStatusPageResp.setMsg(deviceSuspectedLog.getMsg());
        deviceSuspectedStatusPageResp.setTime(deviceSuspectedLog.getTime());
        return deviceSuspectedStatusPageResp;
    }
}
