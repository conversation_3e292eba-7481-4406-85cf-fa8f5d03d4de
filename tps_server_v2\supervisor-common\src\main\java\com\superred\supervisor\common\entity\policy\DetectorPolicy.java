package com.superred.supervisor.common.entity.policy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 监测器策略表
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName(value = "policy_detector_policy", autoResultMap = true)
public class DetectorPolicy extends Model<DetectorPolicy> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 模块所属模块：keyword_detect 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单,file_md5 文件md5
     */
    private String module;

    /**
     * 策略版本
     */
    private String version;

    /**
     * 策略中规则个数
     */
    private Integer num;

    /**
     * 策略生成时间
     */
    private Date createTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 下发状态，0未下发，1已下发
     */
    private Integer issuedStatus;

    /**
     * 下发时间
     */
    private Date issuedTime;

    /**
     * 是否是默认策略
     */
    private Integer isDefault;

    /**
     * 下发方式：reset 全量下发，add 增量下发，del 增量删除
     */
    private String cmd;


}
