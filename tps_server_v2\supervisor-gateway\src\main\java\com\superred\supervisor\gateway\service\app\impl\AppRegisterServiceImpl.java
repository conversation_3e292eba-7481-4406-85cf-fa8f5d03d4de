package com.superred.supervisor.gateway.service.app.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.entity.app.AppAgentInfo;
import com.superred.supervisor.common.entity.app.enums.AppAgentRegStatus;
import com.superred.supervisor.common.entity.system.SysRegion;
import com.superred.supervisor.common.repository.app.AppAgentInfoRepository;
import com.superred.supervisor.common.repository.system.SysRegionRepository;
import com.superred.supervisor.gateway.exception.ApiBaseException;
import com.superred.supervisor.gateway.model.auth.LoginAgent;
import com.superred.supervisor.gateway.model.app.register.AppAgentIdReq;
import com.superred.supervisor.gateway.model.app.register.AppAgentIdResp;
import com.superred.supervisor.gateway.model.app.register.AppRegStatusResp;
import com.superred.supervisor.gateway.model.app.register.AppRegisterReq;
import com.superred.supervisor.gateway.service.cache.AgentCacheService;
import com.superred.supervisor.gateway.service.cache.GatewayCacheService;
import com.superred.supervisor.gateway.service.cache.LockService;
import com.superred.supervisor.gateway.service.app.AppRegisterService;
import com.superred.supervisor.gateway.utils.AgentAuthUtils;
import com.superred.supervisor.gateway.utils.WebExtUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/6/26 17:49
 */
@Service
@Slf4j
public class AppRegisterServiceImpl implements AppRegisterService {


    @Resource
    private SysRegionRepository sysRegionRepository;
    @Resource
    private AppAgentInfoRepository appAgentInfoRepository;
    @Resource
    private LockService lockService;
    @Resource
    private GatewayCacheService gatewayCacheService;
    @Resource
    private AgentCacheService agentCacheService;

    /**
     * 主机唯一编码查询接口
     *
     * @param req 注册请求
     * @return 注册结果
     */
    @Override
    public AppAgentIdResp getAppClientIdRequest(AppAgentIdReq req) {

        RLock clientLock = lockService.getDetectorLock();

        //服务器的MAC地址可能不稳定, 不取
        String md5Str = DigestUtil.md5Hex(req.getHdCode() + req.getVendor());


        boolean locked = false;
        try {
            // 添加锁获取状态标识
            locked = clientLock.tryLock(3, 5, TimeUnit.SECONDS); // 关键修改

            if (!locked) {
                throw new ApiBaseException("系统繁忙，请稍后重试");
            }

            String deviceId = gatewayCacheService.cacheAppAgentId(md5Str);
            if (deviceId == null) {
                deviceId = gatewayCacheService.generateAppAgentId();
                gatewayCacheService.cacheAppAgentId(md5Str, deviceId);
            }

            return AppAgentIdResp.builder().deviceId(deviceId).build();

        } catch (Exception e) {
            throw new ApiBaseException("操作被中断", e);
        } finally {
            // 安全释放锁
            if (locked && clientLock.isHeldByCurrentThread()) {
                clientLock.unlock();
            }
        }

    }

    /**
     * 主机注册接口
     *
     * @param req 注册请求
     */
    @Override
    public void regRequest(AppRegisterReq req) {
        String deviceId = AgentAuthUtils.getDeviceIdFromHeader();
        if (StrUtil.isEmpty(deviceId)) {
            throw new ApiBaseException("设备ID不能为空");
        }
        if (StrUtil.isNotBlank(req.getMemo()) && req.getMemo().getBytes().length > 128) {
            throw new ApiBaseException("备注长度不可超过128字节");
        }
        AppAgentInfo deviceInfo = buildV2(req);
        deviceInfo.setDeviceId(deviceId);
        deviceInfo.setRegisterTime(LocalDateTime.now());
        deviceInfo.setRegisterStatus(AppAgentRegStatus.PENDING);
        deviceInfo.setRegisterMessage("注册待审核");
        deviceInfo.setHeartbeatTime(LocalDateTime.now());

        // 查询是否存在
        AppAgentInfo oldDeviceInfo = appAgentInfoRepository.getById(deviceId);

        if (oldDeviceInfo == null) {
            appAgentInfoRepository.save(deviceInfo);
        } else {
            appAgentInfoRepository.updateById(deviceInfo);
        }

        //清除登录信息
        agentCacheService.appAgentLogout(deviceId);
    }

    private AppAgentInfo buildV2(AppRegisterReq requestVo) {
        SysRegion sysRegion = sysRegionRepository.getById(requestVo.getAddressCode());
        if (sysRegion == null) {
            throw new ApiBaseException("地址编码不存在");
        }

        AppAgentInfo deviceInfo = new AppAgentInfo();
        deviceInfo.setSoftVersion(requestVo.getSoftVersion());
        deviceInfo.setInterfaces(JsonUtil.toJson(requestVo.getInterfaces()));
        deviceInfo.setMemTotal(requestVo.getMemTotal());
        deviceInfo.setCpuInfo(JsonUtil.toJson(requestVo.getCpuInfo()));
        deviceInfo.setDiskInfo(JsonUtil.toJson(requestVo.getDiskInfo()));
        deviceInfo.setOrgans(requestVo.getOrgans());
        deviceInfo.setAddress(requestVo.getAddress());
        deviceInfo.setAddressCode(requestVo.getAddressCode());
        deviceInfo.setRegionPath(sysRegion.getRegionPath());
        deviceInfo.setContact(JsonUtil.toJson(requestVo.getContact()));
        deviceInfo.setMemo(requestVo.getMemo());
        deviceInfo.setDeviceType("05");

        return deviceInfo;
    }

    /**
     * 组件重新注册接口
     *
     * @param req 注册请求
     */
    @Override
    public void reRegRequest(AppRegisterReq req) {
        String deviceId = AgentAuthUtils.getDeviceIdFromHeader();
        if (StrUtil.isEmpty(deviceId)) {
            throw new ApiBaseException("设备ID不能为空");
        }
        if (StrUtil.isNotBlank(req.getMemo()) && req.getMemo().getBytes().length > 128) {
            throw new ApiBaseException("备注长度不可超过128字节");
        }
        AppAgentInfo deviceInfo = buildV2(req);
        deviceInfo.setDeviceId(deviceId);
        deviceInfo.setRegisterTime(LocalDateTime.now());
        deviceInfo.setRegisterStatus(AppAgentRegStatus.PENDING);
        deviceInfo.setRegisterMessage("重新注册待审核");
        deviceInfo.setHeartbeatTime(LocalDateTime.now());

        // 查询是否存在
        AppAgentInfo oldDeviceInfo = appAgentInfoRepository.getById(deviceId);

        if (oldDeviceInfo == null) {
            throw new ApiBaseException("设备ID不存在，请先注册");
        }

        appAgentInfoRepository.updateById(deviceInfo);
        //清除登录信息
        agentCacheService.appAgentLogout(deviceId);
    }

    /**
     * 认证接口
     *
     * @param req 注册请求
     */
    @Override
    public void authLogin(AppRegisterReq req) {
        String deviceId = AgentAuthUtils.getDeviceIdFromHeader();
        if (StrUtil.isEmpty(deviceId)) {
            throw new ApiBaseException("设备ID不能为空");
        }
        AppAgentInfo appAgentInfo = appAgentInfoRepository.getById(deviceId);
        if (appAgentInfo == null){
            throw new ApiBaseException("设备ID不存在，请先注册");
        }
        if (appAgentInfo.getRegisterStatus() != AppAgentRegStatus.PASS) {
            throw new ApiBaseException("设备ID未通过注册审核，请联系管理员");
        }
        AppAgentInfo deviceInfo = new AppAgentInfo();
        deviceInfo.setDeviceId(deviceId);
        deviceInfo.setSoftVersion(req.getSoftVersion());
        deviceInfo.setInterfaces(JsonUtil.toJson(req.getInterfaces()));
        deviceInfo.setMemTotal(req.getMemTotal());
        deviceInfo.setCpuInfo(JsonUtil.toJson(req.getCpuInfo()));
        deviceInfo.setDiskInfo(JsonUtil.toJson(req.getDiskInfo()));

        appAgentInfoRepository.updateById(appAgentInfo);


        String sessionId = WebExtUtils.getSessionId();
        String userAgent = AgentAuthUtils.getUserAgentFromHeader();

        LoginAgent loginAgent = LoginAgent.builder()
                .userAgent(userAgent)
                .sessionId(sessionId)
                .deviceId(deviceId)
                .softVersion(deviceInfo.getSoftVersion())
                .build();

        //设置cookie
        AgentAuthUtils.addSessionIdToCookie(sessionId);
        //设置登录信息
        agentCacheService.appAgentLogin(deviceId, loginAgent);
        agentCacheService.evictAppAgentInfo(deviceId);

    }

    /**
     * 注册状态查询接口
     *
     * @return 注册信息
     */
    @Override
    public AppRegStatusResp registerStatus() {
        String deviceId = AgentAuthUtils.getDeviceIdFromHeader();
        if (StrUtil.isEmpty(deviceId)) {
            throw new ApiBaseException("设备ID不能为空");
        }
        AppAgentInfo appAgentInfo = appAgentInfoRepository.getById(deviceId);
        if (appAgentInfo == null){
            throw new ApiBaseException("设备ID不存在，请先注册");
        }
        AppRegStatusResp resp = new AppRegStatusResp();
        resp.setStatus(appAgentInfo.getRegisterStatus().getCode());
        resp.setMessage(appAgentInfo.getRegisterMessage());

        return resp;
    }
}
