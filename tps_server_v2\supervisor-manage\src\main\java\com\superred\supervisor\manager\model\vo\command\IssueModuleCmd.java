package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 模块启停数据
 *
 * <AUTHOR> leixm
 * @since 2025/03/19 16:01
 */
@Data
public class IssueModuleCmd {

    @Schema(description = "模块名称")
    private String module;

    @Schema(description = "子模块名称")
    private List<String> submodules;

    @Schema(description = "是否全部开启")
    private Integer isAll;

    @Schema(description = "指令名称")
    private String cmd;
}
