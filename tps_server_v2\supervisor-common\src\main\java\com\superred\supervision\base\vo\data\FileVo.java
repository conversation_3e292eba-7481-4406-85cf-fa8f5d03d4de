package com.superred.supervision.base.vo.data;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.ToString;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

/**
 * 文件描述信息
 * <AUTHOR>
 * @since 2022/6/21 11:10
 **/
@Data
@ToString
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(NON_NULL)
public class FileVo {

    private boolean isUpload;

    private String filetype;

    private String time;

    private Long ruleId;

    private String filename;

    private String oriFilenameEncode = "utf8";

    private String id;

    private String fileId;

    private Long fileSize;

    private Integer num;

    private String checksum;

    private String objectType;

    private Integer alertType;

    private String blacklistType;
}
