package com.superred.supervisor.common.repository.settings;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.settings.LocalDeviceSuspectedLog;
import com.superred.supervisor.common.mapper.settings.LocalDeviceSuspectedLogMapper;
import org.springframework.stereotype.Repository;

/**
 * 系统异常日志(LocalDeviceSuspectedLog) Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-27 15:31:59
 */
@Repository
public class LocalDeviceSuspectedLogRepository extends ServiceImpl<LocalDeviceSuspectedLogMapper, LocalDeviceSuspectedLog> {

}

