package com.superred.supervisor.manager.model.auth;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 自定义 Security 用户
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginUser {

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */

    private String username;


    /**
     * 登录过期的具体时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiredDateTime;

    /**
     * 登录过期时间 剩余s
     */
    private Integer expireTimeMin;

    /**
     * 组织机构ID
     */
    private Integer orgId;


    /**
     * 角色ID
     */
    private Integer roleId;

    /**
     * 角色编码
     */
    private String roleCode;


    private List<String> permissions;

}

