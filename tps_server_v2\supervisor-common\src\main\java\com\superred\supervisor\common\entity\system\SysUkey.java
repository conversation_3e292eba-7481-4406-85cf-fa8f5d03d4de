package com.superred.supervisor.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ukey信息 实体
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:22
 */
@Data
@TableName("p_sys_ukey")
public class SysUkey {


    /**
     * 主键自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * ukey型号
     */
    @TableField("key_type")
    private String keyType;

    /**
     * ukey序列号
     */
    @TableField("serial_number")
    private String serialNumber;

    /**
     * key公钥
     */
    @TableField("public_key")
    private String publicKey;

    /**
     * 是否绑定 0 未绑定  1 绑定
     */
    @TableField("bind")
    private Integer bind;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

}

