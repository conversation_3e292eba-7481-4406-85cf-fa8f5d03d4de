package com.superred.supervisor.manager.model.vo.system.org;

import com.superred.supervisor.common.entity.system.SysOrg;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;


/**
 * sys org vo
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

public class SysOrgPageResp {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 组织名称
     */
    @Schema(description = "组织名称")
    private String name;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 父ID
     */
    @Schema(description = "父ID")
    private Integer parentId;


    /**
     * 部门描述
     */
    @Schema(description = "描述")
    private String desc;


    @Schema(description = "区域编码")
    private String regionCode;

    @Schema(description = "区域名称")
    private String regionCodeStr;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime modifiedTime;

    /**
     * 备注
     */
    @Schema(description = "备注", hidden = true)
    private String remarks;


    @Schema(description = "IP地址范围")
    private String ipRanges;


    @Schema(description = "授权点数")
    private Integer issueCount;


    private List<SysOrgPageResp> children;

    public static SysOrgPageResp fromSysOrg(SysOrg sysOrg) {
        return SysOrgPageResp.builder()
                .id(sysOrg.getId())
                .name(sysOrg.getName())
                .sort(sysOrg.getSort())
                .parentId(sysOrg.getParentId())
                .desc(sysOrg.getDesrc())
                .regionCode(sysOrg.getRegionCode())
                .createTime(sysOrg.getCreateTime())
                .modifiedTime(sysOrg.getModifiedTime())
                .remarks(sysOrg.getRemarks())
                .ipRanges(sysOrg.getIpRange())
                .issueCount(sysOrg.getIssueCount())
                .build();
    }
}
