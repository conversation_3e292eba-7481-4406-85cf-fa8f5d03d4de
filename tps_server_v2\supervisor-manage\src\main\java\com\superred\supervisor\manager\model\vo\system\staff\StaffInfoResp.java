package com.superred.supervisor.manager.model.vo.system.staff;

import com.superred.supervisor.common.entity.system.SysStaff;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-08-24 16:30
 */
@Data

public class StaffInfoResp {


    @Schema(description = "主键")
    private Integer id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "人员姓名")
    private String staffName;

    @Schema(description = "身份证号")
//    @Sensitive(type = SensitiveTypeEnum.CUSTOM,beginNoMask = 2,endNoMask = 4,maskStr = "*",encrypt = true)
    private String idCardNum;


    @Schema(description = "所属单位")
    private String unitName;

    @Schema(description = "所属部门id")
    private Integer orgId;

    @Schema(description = "所属部门")
    private String orgName;

    @Schema(description = "联系方式")
//    @Sensitive(type = SensitiveTypeEnum.CUSTOM,beginNoMask = 2,endNoMask = 2,maskStr = "*",encrypt = true)
    private String contactInfo;

    @Schema(description = "邮箱")
//    @Sensitive(type = SensitiveTypeEnum.CUSTOM,beginNoMask = 2,endNoMask = 4,maskStr = "*",encrypt = true)
    private String email;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifiedTime;


    public static StaffInfoResp fromSysStaff(SysStaff sysStaff) {

        StaffInfoResp staffInfoResp = new StaffInfoResp();
        staffInfoResp.setId(sysStaff.getId());
        staffInfoResp.setUsername(sysStaff.getUsername());
        staffInfoResp.setStaffName(sysStaff.getStaffName());
        staffInfoResp.setIdCardNum(sysStaff.getIdCardNum());
        staffInfoResp.setUnitName("北京万里红科技有限公司");
        staffInfoResp.setOrgId(sysStaff.getOrgId());
        staffInfoResp.setContactInfo(sysStaff.getContactInfo());
        staffInfoResp.setEmail(sysStaff.getEmail());
        staffInfoResp.setCreateTime(sysStaff.getCreateTime());
        staffInfoResp.setModifiedTime(sysStaff.getModifiedTime());
        return staffInfoResp;
    }
}
