package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 *  系统阈值
 * @since 2025年03月14日
 */
@Data
public class SysThresholdResp {

    @Schema(description = "系统阈值设置-磁盘使用率")
    private Integer diskUsageRate;

    @Schema(description = "系统阈值设置-CPU使用率")
    private Integer cpuUsageRate;

    @Schema(description = "系统阈值设置-内存使用率")
    private Integer memoryUsageRate;

    @Schema(description = "系统阈值设置-是否开启报警；0：关闭；1：开启")
    private Integer openAlarm;


    @Schema(description = "流量异常阈值-上行流量数")
    private Integer upFlowrate;

    @Schema(description = "流量异常阈值-下行流量数")
    private Integer downFlowrate;

    @Schema(description = "缓存数据阈值-缓存数据大小")
    private Integer cacheSize;

    @Schema(description = "缓存数据阈值-缓存数据存储时间")
    private Integer cacheSaveTime;

    @Schema(description = "检测器和终端保密组件上报的运行状态数据、异常状态数据、审计数据")
    private Integer detectorTerminalReportedData;

    @Schema(description = "终端检测平台自身的运行状态数据、异常状态数据、审计数据")
    private Integer platformSelfData;

    @Schema(description = "终端检测平台本地下发的策略数据")
    private Integer localPolicyData;

    @Schema(description = "检测器离线时长")
    private Integer detectorOfflineDuration;


    @Schema(description = "签名校验周期设置")
    private Integer verifyJarSigner;


}
