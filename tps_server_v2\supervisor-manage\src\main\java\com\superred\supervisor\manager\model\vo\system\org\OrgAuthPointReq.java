package com.superred.supervisor.manager.model.vo.system.org;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 授权点数
 *
 * <AUTHOR>
 * @since 2024/1/18 17:05
 */
@Data
public class OrgAuthPointReq {

    @Schema(description = "部门主键")
    @NotBlank(message = "部门ID不能为空")
    private String sysOrgId;

    @Schema(description = "点数")
    @NotNull(message = "点数不能为空")
    @Max(value = 100000, message = "点数取值范围1-100000")
    @Min(value = 1, message = "点数取值范围1-100000")
    private Integer num;

}
