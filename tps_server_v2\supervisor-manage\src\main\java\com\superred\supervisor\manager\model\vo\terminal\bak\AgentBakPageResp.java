package com.superred.supervisor.manager.model.vo.terminal.bak;

import com.superred.supervisor.common.entity.agent.AgentDeviceInfoBak;
import com.superred.supervisor.common.entity.system.SysOrg;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 终端设备信息
 */
@Data

public class AgentBakPageResp {

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "前八位为年月日，下划线后自定义")
    private String softVersion;


    @Schema(description = "终端责任人ID")
    private String userId;

    @Schema(description = "终端责任人姓名")
    private String userName;


    @Schema(description = "单位名称")
    private String company;

    @Schema(description = "所属部门名称")
    private String orgName;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "终端名称")
    private String hostName;

    @Schema(description = "操作系统")
    private String os;

    @Schema(description = "报备时间")
    private LocalDateTime createTime;

    public static AgentBakPageResp from(AgentDeviceInfoBak agentDeviceInfoBak, Map<Integer, SysOrg> sysOrgMap) {
        AgentBakPageResp resp = new AgentBakPageResp();
        resp.setId(agentDeviceInfoBak.getId());
        resp.setSoftVersion(agentDeviceInfoBak.getSoftVersion());
        resp.setUserId(agentDeviceInfoBak.getUserId());
        resp.setUserName(agentDeviceInfoBak.getUserName());
        resp.setCompany(agentDeviceInfoBak.getCompany());
        SysOrg sysOrg = sysOrgMap.get(agentDeviceInfoBak.getOrgId());
        resp.setOrgName(sysOrg != null ? sysOrg.getName() : "");
        resp.setIp(agentDeviceInfoBak.getIp());
        resp.setMac(agentDeviceInfoBak.getMac());
        resp.setHostName(agentDeviceInfoBak.getDeviceName());
        resp.setOs(agentDeviceInfoBak.getOs());
        resp.setCreateTime(agentDeviceInfoBak.getCreateTime());
        return resp;
    }

}
