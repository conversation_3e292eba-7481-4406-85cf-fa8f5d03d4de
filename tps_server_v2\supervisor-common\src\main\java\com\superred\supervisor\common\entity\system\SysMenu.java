package com.superred.supervisor.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 菜单表 实体
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:21
 */
@Data
@TableName("p_sys_menu")
public class SysMenu {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    /**
     * 菜单权限标识
     */
    @TableField("permission")
    private String permission;

    /**
     * 菜单路径
     */
    @TableField("path_url")
    private String pathUrl;

    /**
     * 图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 父菜单ID
     */
    @TableField("parent_id")
    private Integer parentId;

    /**
     * VUE页面
     */
    @TableField("component")
    private String component;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 状态：0-开启，1- 关闭
     */
    @TableField("keep_alive")
    private Integer keepAlive;

    /**
     * 类型：1：菜单 2：按钮
     */
    @TableField("type")
    private Integer type;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modified_time")
    private LocalDateTime modifiedTime;

    /**
     * 1 表示删除，0 表示未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 叶子节点
     */
    @TableField("leaf")
    private Boolean leaf;


    @TableField("role_code")
    private String roleCode;

}

