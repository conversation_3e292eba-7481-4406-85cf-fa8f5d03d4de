package com.superred.supervisor.gateway.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.superred.supervisor.common.entity.operation.terminal.TerminalCommand;
import com.superred.supervisor.gateway.model.dto.terminal.TerminalCmdDTO;

import java.util.List;

/**
 * 终端指令表表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-24 14:22:54
 */
public interface TerminalCommandExtMapper extends BaseMapper<TerminalCommand> {


    List<TerminalCmdDTO> selectOperationsByDeviceId(String deviceId);
}

