package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 指令响应结果统计响应参数
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data
public class CommandResultStatResp {
    @Schema(description = "命令类型", example = "policy")
    private String type;

    @Schema(description = "指令", example = "start")
    private String cmd;

    @Schema(description = "结果", example = "1")
    private Integer result;

    @Schema(description = "成功失败的数量", example = "10")
    private Integer resCount;
}