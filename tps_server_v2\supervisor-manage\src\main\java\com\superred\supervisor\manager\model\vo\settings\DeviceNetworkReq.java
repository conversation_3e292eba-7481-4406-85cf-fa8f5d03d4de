package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;


/**
 * <AUTHOR>
 *  网卡信息
 * @since 2025年03月12日
 */
@Data
public class DeviceNetworkReq {


    @Schema(description = "网络编号")
    @NotBlank(message = "网络编号不能为空")
    private String flag;

    @Schema(description = "ip地址,************")
    @NotBlank(message = "IP地址不能为空")
    @Pattern(regexp = "((\\d{1,3}\\.){3}\\d{1,3}(/\\d{1,2})?)(,(\\d{1,3}\\.){3}\\d{1,3}(/\\d{1,2})?)*",
            message = "IP地址范围 格式错误")
    @Size(min = 1, max = 500, message = "IP地址范围 长度限制500位")
    private String ip;

    @Pattern(regexp = "((\\d{1,3}\\.){3}\\d{1,3}(/\\d{1,2})?)(,(\\d{1,3}\\.){3}\\d{1,3}(/\\d{1,2})?)*",
            message = "IP地址范围 格式错误")
    @Schema(description = "子网掩码，***********/24")
    @NotBlank(message = "IP地址不能为空")
    private String netmask;

    @Pattern(regexp = "((\\d{1,3}\\.){3}\\d{1,3}(/\\d{1,2})?)(,(\\d{1,3}\\.){3}\\d{1,3}(/\\d{1,2})?)*",
            message = "网关地址 格式错误")
    @Schema(description = "网关地址,*************")
    private String gateway;


}
