package com.superred.supervisor.manager.model.vo.terminal;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 终端软件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
public class AgentSoftwareVO {


    private Integer id;

    @Schema(description = "软件包名")
    private String packetName;

    @Schema(description = "软件包版本")
    private String version;

    @Schema(description = "操作系统")
    private String os;

    @Schema(description = "适用的cpu架构")
    private String cpuArchitecture;

    @Schema(description = "添加时间")
    private LocalDateTime createTime;

    @Schema(description = "安装包地址")
    private String filePath;

    @Schema(description = "文件md5")
    private String md5;

    @Schema(description = "操作系统版本")
    private String osVersion;

    @Schema(description = "是否发布：0未发布，1发布")
    private Integer isPublish;

    @Schema(description = "软件名称")
    private String softwareName;

    @Schema(description = "安装说明")
    private String remark;

    @Schema(description = "软件名称")
    private String packageName;

    @Schema(description = "软件版本")
    private String softVersion;

    @Schema(description = "软件类型：1：安装包；2：升级包")
    private Short softType;
}