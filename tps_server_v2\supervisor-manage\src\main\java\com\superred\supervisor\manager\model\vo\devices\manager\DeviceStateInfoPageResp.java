package com.superred.supervisor.manager.model.vo.devices.manager;


import com.superred.supervisor.common.entity.devices.DeviceInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * resp 设备状态信息页面
 *
 * <AUTHOR>
 * @since 2025/03/14
 */

@Data
public class DeviceStateInfoPageResp {


    @Schema(description = "设备编号")
    private String deviceId;


    @Schema(description = "设备类型")
    private String deviceType;


    @Schema(description = "部署单位")
    private String organs;


    @Schema(description = "内存利用率 %")
    private String mem;


    @Schema(description = "内存总数 MB")
    private String memTotal;


    @Schema(description = "磁盘总空间")
    private String disk;


    @Schema(description = "注册时间")
    private LocalDateTime regTime;


    @Schema(description = "网卡总数")
    private Integer interfaceCount;


    @Schema(description = "网卡在线数")
    private Integer interfaceCountOnline;


    @Schema(description = "总流量MB")
    private Double interfaceFlowCount;


    @Schema(description = "设备状态 0（在线(成功)），1（失败），2（审核中） 3离线；4无效；5已删除")
    private Integer deviceStatus;


    @Schema(description = "运行时长")
    private String uptime;


    @Schema(description = "删除按钮是否展示")
    private Boolean isDelete;


    @Schema(description = "置为无效按钮是否展示")
    private Boolean isInvalid;


    @Schema(description = "设备流量阈值")
    private Integer flowThreshold;


    @Schema(description = "行政区域")
    private String addressCodeName;


    @Schema(description = "行政区域编码")
    private String addressCode;

    public static DeviceStateInfoPageResp from(DeviceInfo deviceInfo) {

        DeviceStateInfoPageResp resp = new DeviceStateInfoPageResp();
        resp.setDeviceId(deviceInfo.getDeviceId());
        resp.setDeviceType(deviceInfo.getDeviceType());
        resp.setOrgans(deviceInfo.getOrgans());

        resp.setMemTotal(String.valueOf(deviceInfo.getMemTotal()));
        resp.setRegTime(deviceInfo.getRegisterTime());
        resp.setDeviceStatus(deviceInfo.getRegisterStatus().getValue());
        resp.setUptime(String.valueOf(deviceInfo.getUptime()));
        //fixme
        resp.setIsDelete(false);
        resp.setIsInvalid(false);
        resp.setAddressCode(deviceInfo.getAddressCode());
        return resp;
    }
}
