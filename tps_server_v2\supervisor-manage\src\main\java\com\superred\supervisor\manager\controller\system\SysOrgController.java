package com.superred.supervisor.manager.controller.system;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.common.BatchIdsReq;
import com.superred.supervisor.manager.model.vo.system.org.OrgAddReq;
import com.superred.supervisor.manager.model.vo.system.org.OrgAuthPointReq;
import com.superred.supervisor.manager.model.vo.system.org.SysOrgPageResp;
import com.superred.supervisor.manager.model.vo.system.org.SysOrgReq;
import com.superred.supervisor.manager.model.vo.system.org.SysOrgTreeVO;
import com.superred.supervisor.manager.service.system.SysOrgService;
import com.superred.supervisor.manager.utils.FileDownloadUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


/**
 * sys组织控制器
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Slf4j
@Tag(name = "1.2. 部门管理")
@RestController
@AllArgsConstructor
@RequestMapping("/sys_org")
public class SysOrgController {

    // 部门管理模块

    private SysOrgService sysOrgService;


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1 分页查询")
    @PostMapping("/list")
    public R<SysOrgPageResp> getSysOrgPage(@Validated @RequestBody SysOrgReq req) {

        SysOrgPageResp sysOrgPageResp = sysOrgService.listAllOrg(req);
        return R.success(sysOrgPageResp);
    }


    @ApiOperationSupport(order = 2)
    @Operation(summary = "2 通过id查询组织机构")
    @GetMapping("/info/{id}")
    public R<SysOrgPageResp> getById(@PathVariable("id") Integer id) {
        SysOrgPageResp sysOrgPageResp = sysOrgService.getById(id);
        return R.success(sysOrgPageResp);
    }


    @ApiOperationSupport(order = 3)
    @Operation(summary = "3 新增组织机构")
    @SysLogAnn(module = LogTypeConstants.DEPARTMENT_MANAGEMENT, operateType = OperateTypeConstants.ADD, desc = "新增组织机构")
    @PostMapping("/add")
    public R<Integer> save(@Validated @RequestBody OrgAddReq orgDTO) {

        Integer id = sysOrgService.addOrg(orgDTO);

        return R.success(id);
    }


    @ApiOperationSupport(order = 4)
    @Operation(summary = "4 修改组织机构")
    @SysLogAnn(module = LogTypeConstants.DEPARTMENT_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "修改组织机构")
    @PostMapping("/edit/{id}")
    public R<Integer> edit(
            @Validated @RequestBody OrgAddReq orgDTO,
            @PathVariable("id") Integer id) {
        sysOrgService.editOrg(orgDTO, id);
        return R.success(id);
    }


    @ApiOperationSupport(order = 5)
    @Operation(summary = "5 删除组织机构")
    @SysLogAnn(module = LogTypeConstants.DEPARTMENT_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除组织机构")
    @PostMapping("/delete_batch")
    public R<String> delete(@Valid @RequestBody BatchIdsReq req) {
        sysOrgService.deleteOrg(req.getIds());

        return R.success(null);
    }


    @ApiOperationSupport(order = 6)
    @Operation(summary = "6 下载模板")
    @PostMapping("/download")
    @SysLogAnn(module = LogTypeConstants.DEPARTMENT_MANAGEMENT, operateType = OperateTypeConstants.DOWNLOAD, desc = "部门管理下载模板")
    public void download(HttpServletResponse response) {
        FileDownloadUtils.downloadClassPathFile(response, "template/部门模板.xlsx", "部门模板.xlsx");
    }

    @ApiOperationSupport(order = 7)
    @Operation(summary = "7 部门导出")
    @PostMapping("/export")
    @SysLogAnn(module = LogTypeConstants.DEPARTMENT_MANAGEMENT, operateType = OperateTypeConstants.EXPORT, desc = "导出部门")
    public void export(HttpServletResponse response,
            @RequestBody SysOrgReq orgDTO) {
        sysOrgService.exportOrgHandle(response);
        log.info("export orgDTO: {}", orgDTO);
    }

    @ApiOperationSupport(order = 8)
    @Operation(summary = "8 部门导入")
    @PostMapping("/import")
    @SysLogAnn(module = LogTypeConstants.DEPARTMENT_MANAGEMENT, operateType = OperateTypeConstants.IMPORT, desc = "导入部门")
    public R<String> importOrg(@RequestParam("file") MultipartFile file) {
        return sysOrgService.importOrgHandle(file);
    }


    @ApiOperationSupport(order = 9)
    @Operation(summary = "9. 分配授权点数")
    @PostMapping(value = "/point/issue")
    @SysLogAnn(module = LogTypeConstants.DEPARTMENT_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "分配授权点数")
    public R<Boolean> issueAuthPoint(@Validated @RequestBody OrgAuthPointReq req) {
        sysOrgService.issueAuthPoint(req);

        return R.success(true);
    }


    @ApiOperationSupport(order = 10)
    @Operation(summary = "10. 回收授权点数")
    @PostMapping(value = "/point/reclaim")
    @SysLogAnn(module = LogTypeConstants.DEPARTMENT_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "回收授权点数")
    public R<Boolean> reclaimAuthPoint(@Validated @RequestBody OrgAuthPointReq req) {
        sysOrgService.reclaimAuthPoint(req);
        return R.success(true);
    }

    @ApiOperationSupport(order = 11)
    @Operation(summary = "11 部门树(包含设备数量)")
    @PostMapping("/tree")
    public R<SysOrgTreeVO> orgTree(@Validated @RequestBody SysOrgReq req) {

        SysOrgTreeVO sysOrgTreeVO = sysOrgService.orgTree(req);
        return R.success(sysOrgTreeVO);
    }
}
