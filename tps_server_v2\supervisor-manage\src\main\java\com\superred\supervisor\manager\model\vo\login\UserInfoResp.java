package com.superred.supervisor.manager.model.vo.login;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


/**
 * 用户信息dto
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data

@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoResp implements Serializable {
    /**
     * 用户基本信息
     */
    @Schema(description = "用户基本信息")
    private SysUserResp sysUser;
    /**
     * 权限标识集合
     */
    @Schema(description = "权限标识集合")
    private List<String> permissions;


    @Schema(description = "角色ID")
    private Integer roleId;

    /**
     * 角色编码
     */
    @Schema(description = "角色编码")
    private String roleCode;

    /**
     * 是否提醒pass修改
     */
    @Schema(description = "是否提醒pass修改")
    private Boolean isTipPassUpdate;


    /**
     * 是否需要添加IP白名单
     */
    @Schema(description = "是否需要添加IP白名单")
    private Boolean needAddIpWhitelist;

}
