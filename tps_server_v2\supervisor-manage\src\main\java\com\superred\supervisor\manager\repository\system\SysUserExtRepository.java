package com.superred.supervisor.manager.repository.system;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.system.SysUser;
import com.superred.supervisor.manager.mapper.system.SysUserExtMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户表 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:22
 */
@Repository
public class SysUserExtRepository extends ServiceImpl<SysUserExtMapper, SysUser> {


    /**
     * 按名称选择用户
     *
     * @param username 用户名
     * @return {@link SysUser }
     */
    public SysUser selectUserByName(String username) {

        LambdaQueryWrapper<SysUser> queryWrapper = Wrappers.<SysUser>lambdaQuery().eq(SysUser::getUsername, username);
        return this.getOne(queryWrapper, false);
    }


    public Boolean checkUserName(String userName) {
        // 查询是否有重名的账号
        List<SysUser> list = this.list(Wrappers.<SysUser>lambdaQuery().eq(SysUser::getUsername, userName));
        if (CollectionUtil.isNotEmpty(list)) {
            return false;
        }
        return true;
    }
}

