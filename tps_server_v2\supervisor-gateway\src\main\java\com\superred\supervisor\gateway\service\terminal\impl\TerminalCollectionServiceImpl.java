package com.superred.supervisor.gateway.service.terminal.impl;

import com.superred.supervisor.common.model.dto.collection.AgentCollResultDTO;
import com.superred.supervisor.file.consant.ExportFile;
import com.superred.supervisor.gateway.kafka.producer.TerminalCollectionEventPublisher;
import com.superred.supervisor.gateway.service.terminal.TerminalCollectionService;
import com.superred.supervisor.standard.v202505.terminal.collection.InformationDeviceInfoReq;
import com.superred.supervisor.standard.v202505.terminal.collection.MediumDeviceReq;
import com.superred.supervisor.standard.v202505.terminal.collection.TerminalAppSoftwareReq;
import com.superred.supervisor.standard.v202505.terminal.collection.TerminalDeviceInfoReq;
import com.superred.supervisor.standard.v202505.terminal.collection.TerminalNetworkBehaviorReq;
import com.superred.supervisor.standard.v202505.terminal.collection.WirelessDeviceReq;
import com.superred.supervisor.standard.v202505.terminal.collection.WirelessHotSpotReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/6/3 16:05
 */
@Service
public class TerminalCollectionServiceImpl implements TerminalCollectionService {

    @Resource
    private TerminalCollectionEventPublisher terminalCollectionEventPublisher;

    /**
     * 上报终端设备信息
     *
     * @param req 终端设备请求
     */
    @Override
    public void reportTerminalDevice(TerminalDeviceInfoReq req) {

        AgentCollResultDTO<TerminalDeviceInfoReq> resultDTO = AgentCollResultDTO.<TerminalDeviceInfoReq>builder()
                .exportFile(ExportFile.TERMINAL_DEVICE)
                .requestBody(req)
                .build();

        terminalCollectionEventPublisher.sendMessage(resultDTO);

    }

    /**
     * 上报信息设备信息
     *
     * @param req 信息设备信息请求
     */
    @Override
    public void reportInformationDeviceInfo(InformationDeviceInfoReq req) {

        AgentCollResultDTO<InformationDeviceInfoReq> resultDTO = AgentCollResultDTO.<InformationDeviceInfoReq>builder()
                .exportFile(ExportFile.INFORMATION_DEVICE)
                .requestBody(req)
                .build();

        terminalCollectionEventPublisher.sendMessage(resultDTO);


    }

    /**
     * 上报介质设备信息
     *
     * @param req 介质设备请求
     */
    @Override
    public void reportMediumDeviceInfo(MediumDeviceReq req) {

        AgentCollResultDTO<MediumDeviceReq> resultDTO = AgentCollResultDTO.<MediumDeviceReq>builder()
                .exportFile(ExportFile.MEDIUM_DEVICE)
                .requestBody(req)
                .build();

        terminalCollectionEventPublisher.sendMessage(resultDTO);

    }

    /**
     * 上报无线设备信息
     *
     * @param req 无线设备请求
     */
    @Override
    public void reportWirelessDeviceInfo(WirelessDeviceReq req) {

        AgentCollResultDTO<WirelessDeviceReq> resultDTO = AgentCollResultDTO.<WirelessDeviceReq>builder()
                .exportFile(ExportFile.WIRELESS_DEVICE)
                .requestBody(req)
                .build();

        terminalCollectionEventPublisher.sendMessage(resultDTO);

    }

    /**
     * 上报无线热点信息
     *
     * @param req 无线热点请求
     */
    @Override
    public void reportWirelessHotspotInfo(WirelessHotSpotReq req) {

        AgentCollResultDTO<WirelessHotSpotReq> resultDTO = AgentCollResultDTO.<WirelessHotSpotReq>builder()
                .exportFile(ExportFile.WIRELESS_HOTSPOT)
                .requestBody(req)
                .build();

        terminalCollectionEventPublisher.sendMessage(resultDTO);

    }

    /**
     * 上报应用软件信息
     *
     * @param req 应用软件请求
     */
    @Override
    public void reportAppSoftwareInfo(TerminalAppSoftwareReq req) {

        AgentCollResultDTO<TerminalAppSoftwareReq> resultDTO = AgentCollResultDTO.<TerminalAppSoftwareReq>builder()
                .exportFile(ExportFile.APP_SOFTWARE)
                .requestBody(req)
                .build();

        terminalCollectionEventPublisher.sendMessage(resultDTO);

    }

    /**
     * 上报网络行为信息
     *
     * @param req 网络行为请求
     */
    @Override
    public void reportNetworkBehavior(TerminalNetworkBehaviorReq req) {

        AgentCollResultDTO<TerminalNetworkBehaviorReq> resultDTO = AgentCollResultDTO.<TerminalNetworkBehaviorReq>builder()
                .exportFile(ExportFile.NETWORK_BEHAVIOR)
                .requestBody(req)
                .build();

        terminalCollectionEventPublisher.sendMessage(resultDTO);

    }
}
