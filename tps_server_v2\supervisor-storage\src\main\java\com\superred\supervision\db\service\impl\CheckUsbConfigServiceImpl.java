package com.superred.supervision.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervision.db.entity.CheckUsbConfig;
import com.superred.supervision.db.mapper.CheckUsbConfigMapper;
import com.superred.supervision.db.service.CheckUsbConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * USB配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Service
public class CheckUsbConfigServiceImpl extends ServiceImpl<CheckUsbConfigMapper, CheckUsbConfig> implements CheckUsbConfigService {

}
