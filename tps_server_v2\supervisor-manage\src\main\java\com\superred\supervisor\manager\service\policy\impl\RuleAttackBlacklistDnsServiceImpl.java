package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleThreeEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleAttackBlacklistDns;
import com.superred.supervisor.common.repository.policy.RuleAttackBlacklistDnsRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.constant.RuleSourceTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.config.BlackDnsPolicyConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackBlacklistDnsPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackBlacklistDnsReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackBlacklistDnsResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleAttackBlacklistDnsService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-12 17:05
 */
@Slf4j
@Service("ruleAttackBlacklistDnsService")
@AllArgsConstructor
public class RuleAttackBlacklistDnsServiceImpl implements RuleAttackBlacklistDnsService, RuleService {

    @Resource
    private RuleAttackBlacklistDnsRepository ruleAttackBlacklistDnsRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    @Override
    public IPage<RuleAttackBlacklistDnsResp> page(RuleAttackBlacklistDnsPageReq ruleAttackBlacklistDns) {
        List<String> ruleIdList = this.getRuleIdList(ruleAttackBlacklistDns);
        LambdaQueryWrapper<RuleAttackBlacklistDns> queryWrapper = new LambdaQueryWrapper<RuleAttackBlacklistDns>()
                .le(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getEndDate()), RuleAttackBlacklistDns::getUpdateTime, ruleAttackBlacklistDns.getEndDate())
                .ge(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getStartDate()), RuleAttackBlacklistDns::getUpdateTime, ruleAttackBlacklistDns.getStartDate())
                .like(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getRuleId()), RuleAttackBlacklistDns::getRuleId, ruleAttackBlacklistDns.getRuleId())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getDns()), RuleAttackBlacklistDns::getDns, ruleAttackBlacklistDns.getDns())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getRuleType()), RuleAttackBlacklistDns::getRuleType, ruleAttackBlacklistDns.getRuleType())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getMatchType()), RuleAttackBlacklistDns::getMatchType, ruleAttackBlacklistDns.getMatchType())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getRuleName()), RuleAttackBlacklistDns::getRuleName, ruleAttackBlacklistDns.getRuleName())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getAttackClass()), RuleAttackBlacklistDns::getAttackClass, ruleAttackBlacklistDns.getAttackClass())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getAttackGroup()), RuleAttackBlacklistDns::getAttackGroup, ruleAttackBlacklistDns.getAttackGroup())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getAttackStage()), RuleAttackBlacklistDns::getAttackStage, ruleAttackBlacklistDns.getAttackStage())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getFacilityType()), RuleAttackBlacklistDns::getFacilityType, ruleAttackBlacklistDns.getFacilityType())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getDesc()), RuleAttackBlacklistDns::getDesc, ruleAttackBlacklistDns.getDesc())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getRisk()), RuleAttackBlacklistDns::getRisk, ruleAttackBlacklistDns.getRisk())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getStatus()), RuleAttackBlacklistDns::getStatus, ruleAttackBlacklistDns.getStatus())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getIsShare()), RuleAttackBlacklistDns::getIsShare, ruleAttackBlacklistDns.getIsShare())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistDns.getRuleSource()), RuleAttackBlacklistDns::getRuleSource, ruleAttackBlacklistDns.getRuleSource())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleAttackBlacklistDns::getRuleId, ruleIdList)
                .ne(RuleAttackBlacklistDns::getRuleSource, RuleSourceTypeEnum.SUPER_POLICY_SHARE.getKey())
                .orderByDesc(RuleAttackBlacklistDns::getUpdateTime);
        Page<RuleAttackBlacklistDns> page = new Page<>(ruleAttackBlacklistDns.getStart(), ruleAttackBlacklistDns.getLimit());
        IPage<RuleAttackBlacklistDns> page1 = this.ruleAttackBlacklistDnsRepository.page(page, queryWrapper);
        return page1.convert(RuleAttackBlacklistDnsResp::fromRuleAttackBlacklistDns);
    }

    @Override
    public RuleAttackBlacklistDnsResp getById(Long ruleId) {
        RuleAttackBlacklistDns ruleAttackBlacklistDns = this.ruleAttackBlacklistDnsRepository.getById(ruleId);
        return RuleAttackBlacklistDnsResp.fromRuleAttackBlacklistDns(ruleAttackBlacklistDns);
    }

    @Override
    public void save(RuleAttackBlacklistDnsReq ruleAttackBlacklistDnsReq) {
        RuleAttackBlacklistDns ruleAttackBlacklistDns = fromRuleAttackBlacklistDnsReq(ruleAttackBlacklistDnsReq);
        // 赋值ruleId
        ruleAttackBlacklistDns.setRuleId(this.ruleIdBuilder.buildRuleId().toString());
        this.ruleAttackBlacklistDnsRepository.save(ruleAttackBlacklistDns);
    }

    public static RuleAttackBlacklistDns fromRuleAttackBlacklistDnsReq(RuleAttackBlacklistDnsReq ruleAttackBlacklistDnsReq) {
        return RuleAttackBlacklistDns.builder()
                .ruleId(ruleAttackBlacklistDnsReq.getRuleId())
                .dns(ruleAttackBlacklistDnsReq.getDns())
                .ruleType(ruleAttackBlacklistDnsReq.getRuleType())
                .matchType(ruleAttackBlacklistDnsReq.getMatchType())
                .ruleName(ruleAttackBlacklistDnsReq.getRuleName())
                .attackClass(ruleAttackBlacklistDnsReq.getAttackClass())
                .attackGroup(ruleAttackBlacklistDnsReq.getAttackGroup())
                .attackStage(ruleAttackBlacklistDnsReq.getAttackStage())
                .facilityType(ruleAttackBlacklistDnsReq.getFacilityType())
                .desc(ruleAttackBlacklistDnsReq.getDesc())
                .risk(ruleAttackBlacklistDnsReq.getRisk())
                .isShare(ruleAttackBlacklistDnsReq.getIsShare())
                .status(ruleAttackBlacklistDnsReq.getStatus())
                .ruleSource(ruleAttackBlacklistDnsReq.getRuleSource())
                .level(ruleAttackBlacklistDnsReq.getLevel())
                .updateTime(ruleAttackBlacklistDnsReq.getUpdateTime())
                .createTime(ruleAttackBlacklistDnsReq.getCreateTime())
                .ext1(ruleAttackBlacklistDnsReq.getExt1())
                .ext2(ruleAttackBlacklistDnsReq.getExt2())
                .ext3(ruleAttackBlacklistDnsReq.getExt3())
                .build();
    }

    @Override
    public void edit(RuleAttackBlacklistDnsReq ruleAttackBlacklistDnsReq) {
        RuleAttackBlacklistDns ruleAttackBlacklistDns = fromRuleAttackBlacklistDnsReq(ruleAttackBlacklistDnsReq);
        this.ruleAttackBlacklistDnsRepository.update(ruleAttackBlacklistDns, Wrappers.<RuleAttackBlacklistDns>lambdaUpdate().eq(RuleAttackBlacklistDns::getRuleId, ruleAttackBlacklistDnsReq.getRuleId()));
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleAttackBlacklistDnsRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 验证是否在使用
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleAttackBlacklistDns> list = this.ruleAttackBlacklistDnsRepository.list(Wrappers.<RuleAttackBlacklistDns>lambdaQuery()
                .in(RuleAttackBlacklistDns::getRuleId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.equals(PolicyApplyStatusEnum.APPLY.getKey().toString(), item.getStatus())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
                Integer ruleSource = Integer.valueOf(item.getRuleSource());
                if (ruleSource != RuleSourceTypeEnum.LOCAL_POLICY.getKey()) {
                    throw new BaseBusinessException("存在上级策略，上级策略不允许被删除");
                }
            });
        }
    }

    /**
     * 获取规则ID列表
     * @param ruleAttackBlacklistDns
     * @return
     */
    private List<String> getRuleIdList(RuleAttackBlacklistDnsPageReq ruleAttackBlacklistDns) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleAttackBlacklistDns.getIssueDeviceType())
                || StrUtil.isBlank(ruleAttackBlacklistDns.getPolicyId())
                || StrUtil.isBlank(ruleAttackBlacklistDns.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleAttackBlacklistDns.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleAttackBlacklistDns.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleAttackBlacklistDns.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleAttackBlacklistDns.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 攻击窃密检测 - 黑名单检测策略 - 域名黑名单检测策略
        return StrUtil.equals(module, PolicyModuleThreeEnum.DOMAIN_BLACKLIST.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 攻击窃密检测 - 黑名单检测策略 - 域名黑名单检测策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleThreeEnum.DOMAIN_BLACKLIST.getKey())
                .moduleStr(PolicyModuleThreeEnum.DOMAIN_BLACKLIST.getValue())
                .moduleParentStr(PolicyModuleOneEnum.ALARM.getValue() + " - " + PolicyModuleTwoEnum.BLACKLIST.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 攻击窃密检测 - 黑名单检测策略 - 域名黑名单检测策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleAttackBlacklistDns> list = this.ruleAttackBlacklistDnsRepository.list(Wrappers.<RuleAttackBlacklistDns>lambdaQuery()
                .in(RuleAttackBlacklistDns::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<BlackDnsPolicyConfigDTO> configDTOS = list.stream().map(item -> {
            return BlackDnsPolicyConfigDTO.getPolicyConfig(item);
        }).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 攻击窃密检测 - 黑名单检测策略 - 域名黑名单检测策略
        this.ruleAttackBlacklistDnsRepository.update(Wrappers.<RuleAttackBlacklistDns>lambdaUpdate()
                .in(RuleAttackBlacklistDns::getRuleId, ruleIds)
                .set(RuleAttackBlacklistDns::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 攻击窃密检测 - 黑名单检测策略 - 域名黑名单检测策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleAttackBlacklistDns> list = this.ruleAttackBlacklistDnsRepository.list(Wrappers.<RuleAttackBlacklistDns>lambdaQuery()
                .in(RuleAttackBlacklistDns::getRuleId, ruleIdList));
        List<RuleAttackBlacklistDnsResp> respList = new ArrayList<>();
        list.forEach(item -> {
            RuleAttackBlacklistDnsResp resp = RuleAttackBlacklistDnsResp.fromRuleAttackBlacklistDns(item);
            respList.add(resp);
        });
        policyDetail.setBlacklistDnsList(respList);
        return policyDetail;
    }
}
