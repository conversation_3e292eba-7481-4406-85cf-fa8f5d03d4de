package com.superred.supervisor.gateway.utils;

import com.superred.supervisor.gateway.exception.ApiBaseException;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 终端认证工具
 *
 * <AUTHOR>
 * @since 2025/5/19 16:00
 */
public final class AgentAuthUtils {

    private AgentAuthUtils() {
    }


    public static String getDeviceIdFromHeader() {

        String userAgentFromHeader = getUserAgentFromHeader();
        return getDeviceId(userAgentFromHeader);
    }


    /**
     * 获取设备ID
     *
     * @param userAgent User-Agent
     * @return 设备ID
     */
    private static String getDeviceId(String userAgent) {
        String[] arr = userAgent.split("/");
        return arr[0];
    }

    public static String getUserAgentFromHeader() {
        HttpServletRequest request = WebExtUtils.getRequest();
        if (request == null) {
            throw new ApiBaseException("请求对象为空");
        }
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null || userAgent.isEmpty()) {
            throw new ApiBaseException("User-Agent 为空");
        }
        return userAgent;
    }

    /**
     * 获取设备ID
     *
     * @param userAgent User-Agent
     * @return 设备ID
     */
    public static String getSoftVersion(String userAgent) {
        String[] arr = userAgent.split("/");
        if (arr.length >= 2) {
            String str = arr[1];
            if (str.indexOf('(') == -1) {
                return str;
            } else {
                return str.substring(0, str.indexOf('('));
            }
        }
        return null;
    }

    public static void addSessionIdToCookie(String sessionId) {
        HttpServletResponse response = WebExtUtils.getResponse();
        if (response == null) {
            throw new ApiBaseException("响应对象为空");
        }
        // 设置Cookie
        Cookie cookie = new Cookie("SESSION", sessionId);
        cookie.setPath("/");

        response.addCookie(cookie);
    }

    public static void addDeviceIdToRequest(String deviceId) {
        HttpServletRequest request = WebExtUtils.getRequest();
        if (request == null) {
            throw new ApiBaseException("请求对象为空");
        }

        request.setAttribute("deviceId", deviceId);
    }

    public static String getDeviceIdFromRequest() {
        HttpServletRequest request = WebExtUtils.getRequest();
        if (request == null) {
            throw new ApiBaseException("请求对象为空");
        }
        return (String) request.getAttribute("deviceId");
    }
}
