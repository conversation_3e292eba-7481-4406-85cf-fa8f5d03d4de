package com.superred.supervisor.gateway.controller.app;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.supervisor.common.constant.CommonConstant;
import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.model.app.detection.AppFileFilterAlertReq;
import com.superred.supervisor.gateway.service.app.AppDetectionService;
import com.superred.supervisor.gateway.utils.WebExtUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.Pattern;

/**
 * 检测数据上报
 *
 * <AUTHOR>
 * @since 2025/5/29 14:00
 */
@Tag(name = "D.3.7 检测数据上报(2025-05)")
@RestController
@Slf4j
@RequestMapping("/A2/")
public class AppDetectionController {

    @Resource
    private AppDetectionService appDetectionService;


    /**
     *  文件筛选数据上报
     *
     * @return {@link ApiResponse }
     */
    @PostMapping("/file_filter/{type}/alert")
    @Operation(summary = "D.3.7.1 文件检测数据上报")
    @ApiOperationSupport(order = 1)
    public ApiResponse<String> reportAlter(
            @RequestBody AppFileFilterAlertReq req,
            @PathVariable @Pattern(regexp = "^(keyword|md5|security_classification_level|secret_level)_filter$", message = "告警类型不支持") String type) {


        appDetectionService.reportAlter(req, type);
        return ApiResponse.success();
    }


    /**
     *  文件筛选数据上报
     *
     * @return {@link ApiResponse }
     */
    @PostMapping("/file_filter/{type}/center_policy_file")
    @Operation(summary = "D.3.7.2 文件检测相关文件上报")
    @ApiOperationSupport(order = 2)
    public ApiResponse<String> reportAlterCenterPolicyFile(
            @RequestHeader(CommonConstant.FILE_DESC_HEADER) String fileDesc,
            @PathVariable @Pattern(regexp = "^(keyword|md5)_filter$", message = "告警类型不支持") String type) {

        MultipartFile singleMultipartFile = WebExtUtils.getSingleMultipartFile();

        appDetectionService.uploadAlterFile(fileDesc, type, singleMultipartFile);
        return ApiResponse.success();
    }


    /**
     *  文件筛选数据上报
     *
     * @return {@link ApiResponse }
     */
    @PostMapping("/file_filter/{type}/inner_policy_file")
    @Operation(summary = "D.3.7.2 文件检测相关文件上报")
    @ApiOperationSupport(order = 3)
    public ApiResponse<String> reportAlterInnerPolicyFile(
            @RequestHeader(CommonConstant.FILE_DESC_HEADER) String fileDesc,
            @PathVariable @Pattern(regexp = "^(security_classification_level|secret_level)_filter$", message = "告警类型不支持") String type) {

        MultipartFile singleMultipartFile = WebExtUtils.getSingleMultipartFile();
        appDetectionService.uploadAlterFile(fileDesc, type, singleMultipartFile);
        return ApiResponse.success();
    }


}
