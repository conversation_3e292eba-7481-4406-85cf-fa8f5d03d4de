package com.superred.supervisor.manager.model.vo.terminal.bak;

import com.superred.supervisor.common.entity.agent.AgentDeviceInfoBak;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 终端设备信息
 * </p>
 *
 */
@Data

public class AgentBakAddReq {

    @Schema(description = "主机名称")
    private String hostName;


    @Schema(description = "主机操作系统")
    private String os;


    @Schema(description = "主机CPU架构")
    private String arch;


    @Schema(description = "单位名称")
    private String company;

    @Schema(description = "单位id")
    private Integer companyId;

    @Schema(description = "所属部门id")
    private Integer orgId;

    @Schema(description = "行政区域")
    private String address;
    @Schema(description = "行政区域编码类型，表示检测器部署所在地的区域编码。")
    private String addressCode;

    @Schema(description = "终端责任人ID")
    private String userId;

    @Schema(description = "终端责任人姓名")
    private String userName;

    @Schema(description = "前八位为年月日，下划线后自定义")
    private String softVersion;
    /*
      终端网络信息
     */

    @Schema(description = "设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值", example = "{\"ip\":\"***************\",\"netmask\":\"*************/24\",\"gateway\":\"*************\",\"mac\":\"00:0c:29:21:72:da\",\"manage\":true}")
    private String interfaces;

    @Schema(description = "内存总数，表示整个设备的内存大小，单位MB。")
    private Long memTotal;

    @Schema(description = "CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。	physical_id：CPU ID，数值类型	core：CPU核心数，数值类型；	clock：CPU主频，数值类型精确到小数点后1位	", example = "[{\"core\":20,\"clock\":2.3,\"physicalId\":0}]")
    private String cpuInfo;

    @Schema(description = "磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。	size为数值类型，表示磁盘大小，单位GB；	serial为字符串类型，最长64个字节，表示磁盘序列号", example = "[{\"size\":476,\"serial\":\"0025_38D3_21B5_824B.\"}]")
    private String diskInfo;


    @Schema(description = "备注信息")
    private String memo;

    public AgentDeviceInfoBak toDo(String ip, String mac) {
        AgentDeviceInfoBak agentDeviceInfoBak = new AgentDeviceInfoBak();
        agentDeviceInfoBak.setDeviceName(this.hostName);
        agentDeviceInfoBak.setOs(this.os);
        agentDeviceInfoBak.setArch(this.arch);
        agentDeviceInfoBak.setCompany(this.company);
        agentDeviceInfoBak.setCompanyId(this.companyId);
        agentDeviceInfoBak.setOrgId(this.orgId);
        agentDeviceInfoBak.setAddressCode(this.addressCode);
        agentDeviceInfoBak.setAddress(this.address);
        agentDeviceInfoBak.setUserId(this.userId);
        agentDeviceInfoBak.setUserName(this.userName);
        agentDeviceInfoBak.setSoftVersion(this.softVersion);
        agentDeviceInfoBak.setInterfaceInfo(this.interfaces);
        agentDeviceInfoBak.setMemTotal(this.memTotal);
        agentDeviceInfoBak.setCpuInfo(this.cpuInfo);
        agentDeviceInfoBak.setDiskInfo(this.diskInfo);
        agentDeviceInfoBak.setMemo(this.memo);
        agentDeviceInfoBak.setIp(ip);
        agentDeviceInfoBak.setMac(mac);
        return agentDeviceInfoBak;
    }
}
