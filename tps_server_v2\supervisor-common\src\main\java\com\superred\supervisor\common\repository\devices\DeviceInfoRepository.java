package com.superred.supervisor.common.repository.devices;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.devices.DeviceInfo;
import com.superred.supervisor.common.mapper.devices.DeviceInfoMapper;
import org.springframework.stereotype.Repository;

/**
 * 检测器设备信息 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-18 20:06:46
 */
@Repository
public class DeviceInfoRepository extends ServiceImpl<DeviceInfoMapper, DeviceInfo> {

}

