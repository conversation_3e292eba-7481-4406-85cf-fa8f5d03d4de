package com.superred.supervision.base.constant;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/2/13 14:34
 * 终端软件检测相关topic
 */
@Data
@Component
public class AgentTopic {

    private String msgAgentSystemStatus = "msg_agent_system_status";

    private String msgAgentSensitive = "msg_agent_sensitive";

    private String filedescAgentSensitive = "filedesc_agent_sensitive";

    private String msgAgentAlarm = "msg_agent_alarm";

    private String filedescAgentAlarm = "filedesc_agent_alarm";

    private String msgAgentTask = "msg_agent_task";

    private String msgAgentUninstall = "msg_agent_uninstall";

    private String msgAgentTaskFile = "msg_agent_task_file";


    private String msgAgentCommandResult = "msg_agent_command_result";

    private String msgAgentPolicyResult = "msg_agent_policy_result";
}
