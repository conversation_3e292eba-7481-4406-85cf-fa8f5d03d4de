package com.superred.supervision.base.constant;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/3/16 11:17
 */
@Data
@Component
public class DisposalTopic {

    private String msgAttackDisposal = "msg_attack_disposal";

    private String filedescAttackDisposal = "filedesc_attack_disposal";

    private String msgSensitiveDisposal = "msg_sensitive_disposal";

    private String filedescSensitiveDisposal = "filedesc_sensitive_disposal";

    /**
     * web处置完后处置topic
     */
    private String msgDisposalTopic = "msg_disposal_topic";
}
