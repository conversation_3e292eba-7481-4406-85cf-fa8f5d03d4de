package com.superred.supervisor.gateway.schedule;

import com.superred.supervisor.gateway.service.terminal.TerminalHeartBeatService;
import com.superred.supervisor.gateway.service.cache.LockService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 客户端状态扫描任务
 *
 * <AUTHOR>
 * @since 2025/7/9 16:26
 */
@Component
@Slf4j
public class AgentHeartbeatScanTask {

    @Resource
    private TerminalHeartBeatService terminalHeartBeatService;

    @Resource
    private LockService lockService;


    @Scheduled(fixedDelay = 40_000)
    public void checkOfflineTerminals() {

        // 分布式锁防止重复处理
        RLock lock = lockService.getTerminalTaskLock();
        boolean tryLock = lock.tryLock();
        if (!tryLock) {
            log.warn(" >>>>>>>>>>>>>>>>> AgentHeartBeatScanTask lock acquired by another thread, skipping this execution.");
            return;
        }
        try {
            terminalHeartBeatService.processOfflineTerminal();
        } catch (Exception e) {
            log.error("checkOfflineTerminals error: ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
