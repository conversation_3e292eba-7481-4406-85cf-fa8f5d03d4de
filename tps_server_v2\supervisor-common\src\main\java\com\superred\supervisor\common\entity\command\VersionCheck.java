package com.superred.supervisor.common.entity.command;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 数据：版本检测
 *
 * <AUTHOR>
 * @since 2025-03-18 11:03
 **/
@Data
@TableName("tb_version_check")
public class VersionCheck {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 监测器编号
     */
    private String deviceId;

    /**
     * 指令编号
     */
    private String cmdId;

    /**
     * 取值方法
     */
    private String method;

    /**
     * 检查文件名
     */
    private String filename;

    /**
     * 读取的开始偏移
     */
    private Integer offset;

    /**
     * 读取长度
     */
    private Integer length;

    /**
     * 检查文件完整路径
     */
    private String path;

    /**
     * 本地上传文件计算值
     */
    private String upValue;

    /**
     * 数据上传时间
     */
    private String upTime;


    /**
     * 预留字段
     */
    private String ext1;

    /**
     * 预留字段
     */
    private String ext2;

    /**
     * 预留字段
     */
    private String ext3;

    /**
     * 预留字段
     */
    private String ext4;

    /**
     * 预留字段
     */
    private String ext5;

}