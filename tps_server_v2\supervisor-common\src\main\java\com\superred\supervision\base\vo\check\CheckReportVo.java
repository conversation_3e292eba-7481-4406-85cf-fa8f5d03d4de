package com.superred.supervision.base.vo.check;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2023/8/30 20:03
 */
@Data
@ToString
public class CheckReportVo {

    private HeadRecordVo headRecord;

    private SchemaRecordVo schemaRecord;

    private RecordsVo hostInfo;

    private RecordsVo accountInfo;

    private RecordsVo networkInfo;

    private RecordsVo hardDiskInfo;

    private RecordsVo fastUSBRecord;

    private RecordsVo fastFileCheck;

    private RecordsVo services;

    private RecordsVo ports;

    // 文件操作记录
    private RecordsVo fileOperateRecord;
    // 硬盘更换情况
    private RecordsVo diskChangeInfo;
    // 进程信息
    private RecordsVo processInfo;
    // 系统安全日志
    private RecordsVo sysSafeLog;
    // 系统驱动日志
    private RecordsVo sysDriverLog;
    // 用户登录日志
    private RecordsVo userLoginLog;
    // shell日志
    private RecordsVo shellLog;
    // 开关机日志
    private RecordsVo powerOnLog;

    // 安全策略
    private RecordsVo safePolicy;


    // 共享信息
    private RecordsVo shareInfo;
    // 日志设置
    private RecordsVo logSetting;
    // 屏幕保护
    private RecordsVo screenSaver;
    // 用户组
    private RecordsVo userGroup;

    /**
     * 深度文件内容
     */
    private RecordsVo deepFileCheck;
    /**
     * 深度文件操作  DeepFileOperate
     */
    private RecordsVo deepFileOperate;
    /**
     * 深度usb
     */
    private RecordsVo deepUSBRecord;


    public void set(String name, RecordsVo recordsVo) {
        try {
            ReflectUtil.setFieldValue(this, StrUtil.lowerFirst(name), recordsVo);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
