package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleAttackMalware;
import com.superred.supervisor.common.repository.policy.RuleAttackMalwareRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.constant.RuleSourceTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.config.MalwarePolicyConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackMalwarePageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackMalwareReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackMalwareResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleAttackMalwareService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-12 14:21
 */
@Slf4j
@Service("ruleAttackMalwareService")
@AllArgsConstructor
public class RuleAttackMalwareServiceImpl implements RuleAttackMalwareService, RuleService {

    @Resource
    private RuleAttackMalwareRepository ruleAttackMalwareRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    @Override
    public IPage<RuleAttackMalwareResp> page(RuleAttackMalwarePageReq ruleAttackMalwarePageReq) {
        // 查询
        List<String> ruleIdList = this.getRuleIdList(ruleAttackMalwarePageReq);
        LambdaQueryWrapper<RuleAttackMalware> queryWrapper = new LambdaQueryWrapper<RuleAttackMalware>()
                .le(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getEndDate()), RuleAttackMalware::getUpdateTime, ruleAttackMalwarePageReq.getEndDate())
                .ge(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getStartDate()), RuleAttackMalware::getUpdateTime, ruleAttackMalwarePageReq.getStartDate())
                .like(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getRuleId()), RuleAttackMalware::getRuleId, ruleAttackMalwarePageReq.getRuleId())
                .eq(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getRuleName()), RuleAttackMalware::getRuleName, ruleAttackMalwarePageReq.getRuleName())
                .eq(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getAttackClass()), RuleAttackMalware::getAttackClass, ruleAttackMalwarePageReq.getAttackClass())
                .eq(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getAttackGroup()), RuleAttackMalware::getAttackGroup, ruleAttackMalwarePageReq.getAttackGroup())
                .eq(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getMd5()), RuleAttackMalware::getMd5, ruleAttackMalwarePageReq.getMd5())
                .eq(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getRule()), RuleAttackMalware::getRule, ruleAttackMalwarePageReq.getRule())
                .eq(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getDesc()), RuleAttackMalware::getDesc, ruleAttackMalwarePageReq.getDesc())
                .eq(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getRisk()), RuleAttackMalware::getRisk, ruleAttackMalwarePageReq.getRisk())
                .eq(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getStatus()), RuleAttackMalware::getStatus, ruleAttackMalwarePageReq.getStatus())
                .eq(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getIsShare()), RuleAttackMalware::getIsShare, ruleAttackMalwarePageReq.getIsShare())
                .eq(StrUtil.isNotEmpty(ruleAttackMalwarePageReq.getRuleSource()), RuleAttackMalware::getRuleSource, ruleAttackMalwarePageReq.getRuleSource())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleAttackMalware::getRuleId, ruleIdList)
                .ne(RuleAttackMalware::getRuleSource, RuleSourceTypeEnum.SUPER_POLICY_SHARE.getKey())
                .orderByDesc(RuleAttackMalware::getUpdateTime);
        Page<RuleAttackMalware> page = new Page<>(ruleAttackMalwarePageReq.getStart(), ruleAttackMalwarePageReq.getLimit());
        IPage<RuleAttackMalware> page1 = this.ruleAttackMalwareRepository.page(page, queryWrapper);
        return page1.convert(RuleAttackMalwareResp::fromRuleAttackMalware);
    }

    @Override
    public RuleAttackMalwareResp getById(Long ruleId) {
        RuleAttackMalware ruleAttackMalware = this.ruleAttackMalwareRepository.getOne(Wrappers.<RuleAttackMalware>lambdaQuery()
                .eq(RuleAttackMalware::getRuleId, ruleId)
                .last("limit 1"));
        return RuleAttackMalwareResp.fromRuleAttackMalware(ruleAttackMalware);
    }

    @Override
    public void save(RuleAttackMalwareReq ruleAttackMalwareReq) {
        RuleAttackMalware ruleAttackMalware = fromRuleAttackMalwareReq(ruleAttackMalwareReq);
        // 赋值ruleId
        ruleAttackMalware.setRuleId(this.ruleIdBuilder.buildRuleId().toString());
        if (StrUtil.isNotBlank(ruleAttackMalwareReq.getMd5())) {
            ruleAttackMalware.setMd5(ruleAttackMalwareReq.getMd5().toLowerCase(Locale.getDefault()));
        }
        this.ruleAttackMalwareRepository.save(ruleAttackMalware);
    }

    @Override
    public void edit(RuleAttackMalwareReq ruleAttackMalwareReq) {
        RuleAttackMalware ruleAttackMalware = fromRuleAttackMalwareReq(ruleAttackMalwareReq);
        if (StrUtil.isNotBlank(ruleAttackMalwareReq.getMd5())) {
            ruleAttackMalware.setMd5(ruleAttackMalwareReq.getMd5().toLowerCase(Locale.getDefault()));
        }
        this.ruleAttackMalwareRepository.update(ruleAttackMalware, Wrappers.<RuleAttackMalware>lambdaUpdate().eq(RuleAttackMalware::getRuleId, ruleAttackMalwareReq.getRuleId()));
    }

    public static RuleAttackMalware fromRuleAttackMalwareReq(RuleAttackMalwareReq ruleAttackMalwareReq) {
        return RuleAttackMalware.builder()
                .ruleId(ruleAttackMalwareReq.getRuleId())
                .ruleName(ruleAttackMalwareReq.getRuleName())
                .attackClass(ruleAttackMalwareReq.getAttackClass())
                .attackGroup(ruleAttackMalwareReq.getAttackGroup())
                .md5(ruleAttackMalwareReq.getMd5())
                .rule(ruleAttackMalwareReq.getRule())
                .desc(ruleAttackMalwareReq.getDesc())
                .risk(ruleAttackMalwareReq.getRisk())
                .status(ruleAttackMalwareReq.getStatus())
                .isShare(ruleAttackMalwareReq.getIsShare())
                .ruleSource(ruleAttackMalwareReq.getRuleSource())
                .level(ruleAttackMalwareReq.getLevel())
                .updateTime(ruleAttackMalwareReq.getUpdateTime())
                .createTime(ruleAttackMalwareReq.getCreateTime())
                .ext1(ruleAttackMalwareReq.getExt1())
                .ext2(ruleAttackMalwareReq.getExt2())
                .ext3(ruleAttackMalwareReq.getExt3())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleAttackMalwareRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 验证是否在使用
     *
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleAttackMalware> list = this.ruleAttackMalwareRepository.list(Wrappers.<RuleAttackMalware>lambdaQuery()
                .in(RuleAttackMalware::getRuleId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.equals(PolicyApplyStatusEnum.APPLY.getKey().toString(), item.getStatus())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
                Integer ruleSource = Integer.valueOf(item.getRuleSource());
                if (ruleSource != RuleSourceTypeEnum.LOCAL_POLICY.getKey()) {
                    throw new BaseBusinessException("存在上级策略，上级策略不允许被删除");
                }
            });
        }
    }

    /**
     * 获取规则ID列表
     * @param ruleAttackMalwarePageReq
     * @return
     */
    private List<String> getRuleIdList(RuleAttackMalwarePageReq ruleAttackMalwarePageReq) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleAttackMalwarePageReq.getIssueDeviceType())
                || StrUtil.isBlank(ruleAttackMalwarePageReq.getPolicyId())
                || StrUtil.isBlank(ruleAttackMalwarePageReq.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleAttackMalwarePageReq.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleAttackMalwarePageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleAttackMalwarePageReq.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleAttackMalwarePageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 攻击窃密检测 - 恶意文件检测策略
        return StrUtil.equals(module, PolicyModuleTwoEnum.MALWARE.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 攻击窃密检测 - 恶意文件检测策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleTwoEnum.MALWARE.getKey())
                .moduleStr(PolicyModuleTwoEnum.MALWARE.getValue())
                .moduleParentStr(PolicyModuleOneEnum.ALARM.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 攻击窃密检测 - 恶意文件检测策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleAttackMalware> list = this.ruleAttackMalwareRepository.list(Wrappers.<RuleAttackMalware>lambdaQuery()
                .in(RuleAttackMalware::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<MalwarePolicyConfigDTO> configDTOS = list.stream().map(item -> {
            return MalwarePolicyConfigDTO.getPolicyConfig(item);
        }).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 攻击窃密检测 - 恶意文件检测策略
        this.ruleAttackMalwareRepository.update(Wrappers.<RuleAttackMalware>lambdaUpdate()
                .in(RuleAttackMalware::getRuleId, ruleIds)
                .set(RuleAttackMalware::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 攻击窃密检测 - 恶意文件检测策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleAttackMalware> list = this.ruleAttackMalwareRepository.list(Wrappers.<RuleAttackMalware>lambdaQuery()
                .in(RuleAttackMalware::getRuleId, ruleIdList));
        List<RuleAttackMalwareResp> respList = new ArrayList<>();
        list.forEach(item -> {
            RuleAttackMalwareResp resp = RuleAttackMalwareResp.fromRuleAttackMalware(item);
            respList.add(resp);
        });
        policyDetail.setMalwareList(respList);
        return policyDetail;
    }
}

