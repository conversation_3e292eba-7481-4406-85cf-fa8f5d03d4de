package com.superred.supervisor.manager.controller.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.system.user.SysUkeyResp;
import com.superred.supervisor.manager.model.vo.system.user.SysUkeyUnbindReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * sys ukey控制器
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Tag(name = "1.5. ukey信息表")
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys_ukey")
public class SysUkeyController {


    /**
     * 分页查询.
     *
     * @Param page: 分页对象
     * @return: sysUkey:ukey信息表
     * <AUTHOR>
     * @since 2023-08-21 10:34
     **/
    @ApiOperationSupport(order = 1)
    @Operation(summary = "1. 分页查询")
    @GetMapping("/page")
    public RPage<SysUkeyResp> getSysUkeyPage() {
        return new RPage<>(new Page<>());
    }


    @ApiOperationSupport(order = 2)
    @Operation(summary = "2. 解除绑定用户")
    @SysLogAnn(module = LogTypeConstants.UKEY_MANAGEMENT, operateType = OperateTypeConstants.UNBIND, desc = "解除ukey绑定")
    @PostMapping("/unbind")
    public R<Boolean> unbind(@Valid @RequestBody SysUkeyUnbindReq sysUkeyDTO) {

        return R.success(null, "解绑成功");
    }


    /**
     * 删除ukey.
     *
     * @Param id:
     * @return: R
     * <AUTHOR>
     * @since 2023-08-21 10:57
     **/
    @Operation(summary = "3. 删除ukey信息")
    @SysLogAnn(module = LogTypeConstants.UKEY_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除ukey信息")
    @PostMapping("/delete/{id}")
    public R<Boolean> removeId(@PathVariable("id") Integer id) {
        //        SysUkey one = sysUkeyService.getById(id);
        //        //SysUkey one = sysUkeyService.getOne(Wrappers.<SysUkey>lambdaQuery().eq(SysUkey::getSerialNumber, sysUkey.getSerialNumber()));
        //        if (Objects.equals(one.getBind(), UkeyStatus.BOUND.getKey())) {
        //            return R.failed("没有解除绑定，不可删除");
        //        }
        //        sysUkeyService.removeById(id);
        //        sysUserUkeyService.remove(Wrappers.<SysUserUkey>lambdaQuery().eq(SysUserUkey::getUkeyId, id));
        return R.success(true);
    }


    @ApiOperationSupport(order = 4)
    @Operation(summary = "4. 绑定用户")
    @SysLogAnn(module = LogTypeConstants.UKEY_MANAGEMENT, operateType = OperateTypeConstants.BINDING, desc = "ukey绑定用户")
    @PostMapping("/bind")
    public R<Boolean> bind(@Valid @RequestBody SysUkeyUnbindReq sysUkeyDTO) {
        //        SysUkey one = sysUkeyService.getOne(Wrappers.<SysUkey>lambdaQuery().eq(SysUkey::getSerialNumber, sysUkeyDTO.getSerialNumber()));
        //        if (Objects.equals(one.getBind(), UkeyStatus.BOUND.getKey())) {
        //            return R.failed("绑定失败，已绑定用户");
        //        }
        //        bind(one.getId(), sysUkeyDTO.getUserId());
        return R.success(null, "绑定成功");
    }


}
