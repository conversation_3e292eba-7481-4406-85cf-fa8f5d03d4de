package com.superred.supervisor.manager.model.vo.terminal.manage;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 代理商业务状况
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Data
@Builder
public class TerminalBusinessStatusResp {

    @Schema(description = "告警类型")
    private String moduleName;

    @Schema(description = "子策略版本")
    private String policyVersion;

    @Schema(description = "本级策略数量")
    private Integer selfRuleCount;

    @Schema(description = "本级策略告警数量")
    private Integer selfAlarmCount;


    @Schema(description = "上级策略数量")
    private Integer upRuleCount;

    @Schema(description = "上级策略告警总数")
    private Integer upAlarmCount;

    @Schema(description = "子类模块业务状态统计")
    private List<TerminalBusinessStatusResp> itemList;
}
