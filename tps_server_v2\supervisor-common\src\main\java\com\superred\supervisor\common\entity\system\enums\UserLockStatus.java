package com.superred.supervisor.common.entity.system.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 锁定状态(1-正常，2-锁定)
 *
 * <AUTHOR>
 * @since 2025/3/10 20:22
 */
@Getter
public enum UserLockStatus implements IEnum<Integer> {

    NORMAL(1, "正常"),
    LOCKED(2, "锁定");

    @JsonValue
    private final Integer value;

    private final String desc;

    UserLockStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    // 根据 value 获取枚举值的方法
    public static UserLockStatus fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (UserLockStatus status : UserLockStatus.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No matching UserLockStatus for value: " + value);
    }

}
