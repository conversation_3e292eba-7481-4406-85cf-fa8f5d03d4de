package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 命令查询类
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data
public class DetectorCommandStatisticsReq {
    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * 指令编号
     */
    @Schema(description = "指令编号")
    private String cmdId;

    /**
     * 指令
     */
    @Schema(description = "指令")
    private String cmd;

    /**
     * 指令来源： 1 本级  2 上级
     */
    @Schema(description = "指令来源： 1 本级  2 上级")
    private String cmdSource;

    @Schema(description = "执行结果")
    private Integer result;

    @Schema(description = "当前页", example = "1")
    private Integer start;

    @Schema(description = "每页显示条数", example = "10")
    private Integer limit;
}