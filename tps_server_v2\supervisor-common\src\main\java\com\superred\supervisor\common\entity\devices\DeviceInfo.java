package com.superred.supervisor.common.entity.devices;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.superred.supervisor.common.entity.devices.enums.DeviceAuditType;
import com.superred.supervisor.common.entity.devices.enums.RegisterStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 检测器设备信息 实体
 *
 * <AUTHOR>
 * @since 2025-03-18 20:06:46
 */
@Data
@TableName("d_device_info")
public class DeviceInfo {


    /**
     * 设备编号
     */
    @TableId(value = "device_id", type = IdType.AUTO)
    private String deviceId;

    /**
     * 全数字组成的最长为2个字节的字符串，检测器为“01” 终端为"02"
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 前八位为年月日，下划线后自定义
     */
    @TableField("soft_version")
    private String softVersion;

    /**
     * 厂商英文名
     */
    @TableField("vendor_name")
    private String vendorName;

    /**
     * 设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值
     */
    @TableField("interface")
    private String interfaces;

    /**
     * 内存总数，表示整个设备的内存大小，单位MB。
     */
    @TableField("mem_total")
    private Long memTotal;

    /**
     * CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。
     physical_id：CPU ID，数值类型
     core：CPU核心数，数值类型；
     clock：CPU主频，数值类型精确到小数点后1位

     */
    @TableField("cpu_info")
    private String cpuInfo;

    /**
     * 磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。
     size为数值类型，表示磁盘大小，单位GB；
     serial为字符串类型，最长64个字节，表示磁盘序列号
     */
    @TableField("disk_info")
    private String diskInfo;

    /**
     * 检测器部署的单位名
     */
    @TableField("organs")
    private String organs;

    /**
     * 检测器部署的地理位置
     */
    @TableField("address")
    private String address;

    /**
     * 行政区域编码类型，表示检测器部署所在地的区域编码。
     */
    @TableField("address_code")
    private String addressCode;

    /**
     * 单位联系人信息。
     name表示联系人，最长为64个字节的字符串；
     email表示联系人邮件地址，最长为64个字节的字符串；
     phone表示联系人电话，最长为32字节，；
     position表示联系人职务，最长为64个字节的字符串；

     */
    @TableField("contact")
    private String contact;

    /**
     * 注册时间
     */
    @TableField("register_time")
    private LocalDateTime registerTime;

    /**
     * 注册状态，0（成功），1（失败），2（审核中） 3离线；4无效；5已删除 6在线
     */
    @TableField("register_status")
    private RegisterStatus registerStatus;

    /**
     * 注册状态描述
     */
    @TableField("register_message")
    private String registerMessage;

    /**
     * 审核时间
     */
    @TableField("verify_time")
    private LocalDateTime verifyTime;

    /**
     * 备注信息
     */
    @TableField("memo")
    private String memo;

    /**
     * 开机时间,运行时长，单位秒
     */
    @TableField("uptime")
    private Integer uptime;

    /**
     * 心跳时间
     */
    @TableField("heartbeat_time")
    private LocalDateTime heartbeatTime;

    /**
     * 业务状态时间
     */
    @TableField("business_time")
    private LocalDateTime businessTime;

    /**
     * 上报类型 add update
     */
    @TableField("report_type")
    private String reportType;

    /**
     * 公网ip地址
     */
    @TableField("public_ip_address")
    private String publicIpAddress;


    @TableField("device_ca")
    private String deviceCa;

    /**
     * 证书有效期起始时间
     */
    @TableField("ca_validate_start")
    private LocalDateTime caValidateStart;
    /**
     * 证书有效期结束时间
     */
    @TableField("ca_validate_end")
    private LocalDateTime caValidateEnd;

    /**
     * 流量阈值
     */
    @TableField("flow_threshold")
    private Integer flowThreshold;
    /**
     * 0人工审核。1自动审核
     */
    @TableField("audit_type")
    private DeviceAuditType auditType;


    @TableField("region_path")
    private String regionPath;


    /**
     * 本地备注
     */
    @TableField("local_remarks")
    private String localRemarks;

    /**
     * 无效原因
     */
    @TableField("invalid_msg")
    private String invalidMsg;

    //fixme 重构完删除
    @TableField(exist = false)
    private String path;
}

