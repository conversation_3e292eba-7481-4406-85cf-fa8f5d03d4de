package com.superred.supervision.base.vo.data;

import lombok.Data;

/**
 * AgentKeywordAlertVO.
 * 终端检测组件异常通信检测数据上报.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-29 14:14
 */
@Data
public class AgentBlackAlertVO {

    /**
     * 告警ID.
     **/
    private String id;

    /**
     * 告警时间.
     **/
    private String time;

    /**
     * 攻击阶段.
     **/
    private int attack_stage;

    /**
     * 攻击结果.
     **/
    private Byte attack_result;

    /**
     * 攻击类型.
     **/
    private int threat_type;

    /**
     * 下发规则的风险级别，	取值为：0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）
     */
    private Integer risk;

    /**
     * 告警文件MD5.
     **/
    private String file_md5;

    /**
     * 域名.
     **/
    private String domain;

    /**
     * 告警文件路径.
     **/
    private String file_path;

    /**
     * 告警文件文件名.
     **/
    private String filename;

    /**
     * 告警文件文件大小.
     **/
    private double filesize;

    /**
     * 命中策略 ID.
     **/
    private String rule_id;

    /**
     * 单位名称.
     **/
    private String company;

    /**
     * 主机名称.
     **/
    private String computer_name;

    /**
     * 组织机构 id.
     **/
    private String org_id;

    /**
     * 组织机构全路径.
     **/
    private String org_path;

    /**
     * 责任人.
     **/
    private String user_name;

    /**
     * 责任人 ID.
     **/
    private String user_id;

    /**
     * 源 IP.
     **/
    private String sip;

    /**
     * 源 mac.
     **/
    private String smac;

    /**
     * 源端口.
     **/
    private String sport;

    /**
     * 目的 IP.
     **/
    private String dip;

    /**
     * 目的 MAC.
     **/
    private String dmac;

    /**
     * 目的端口.
     **/
    private String dport;

    /**
     * 拓展字段，json 格式.
     **/
    private String extended_fields;


}
