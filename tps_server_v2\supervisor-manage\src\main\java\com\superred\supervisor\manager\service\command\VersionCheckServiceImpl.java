package com.superred.supervisor.manager.service.command;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.superred.supervisor.common.entity.command.VersionCheck;
import com.superred.supervisor.common.repository.command.VersionCheckRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class VersionCheckServiceImpl implements VersionCheckService {
    @Resource
    private VersionCheckRepository versionCheckRepository;

    @Override
    public void batchSave(List<VersionCheck> versionChecks) {
        if (CollUtil.isEmpty(versionChecks)) {
            log.info("versionChecks is empty");
            return;
        }
        List<List<VersionCheck>> partitionList = ListUtil.partition(versionChecks, 500);
        partitionList.forEach(versionChecks1 -> {
            versionCheckRepository.saveBatch(versionChecks1);
        });
    }
}
