package com.superred.supervisor.file.consant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * 存放数据的文件夹名称
 *
 * <AUTHOR>
 * @since 2025/3/31 19:47
 */
@AllArgsConstructor
@Getter
public enum FileDirEnum {

    /**
     * 攻击窃密检测告警数据
     */
    ALARM("alarm", ExportFile.ALARM),
    /**
     * 文件筛选结果数据
     */
    FILE_FILTER("file_filter", ExportFile.FILE_FILTER),
    /**
     * 网络行为审计数据-通联关系审计
     */
    NET_LOG("net_log", ExportFile.NET_LOG_SET),
    /**
     * 网络行为审计数据-检测器的应用行为
     */
    APP_BEHAVIOR("app_behavior", ExportFile.APP_BEHAVIOR_SET),
    /**
     * 终端的风险软件使用行为
     */
    NETWORK_BEHAVIOR("network_behavior", ExportFile.NETWORK_BEHAVIOR_SET),
    /**
     * 策略数据
     */
    POLICY("policy", ExportFile.POLICY_SET),
    /**
     * 设备信息采集
     */
    DEVICE_INFO("device_info", ExportFile.DEVICE_INFO_SET);


    private final String dirName;


    private final Set<ExportFile> supportTypes;

    public static FileDirEnum getEnumByDirName(String dirName) {
        for (FileDirEnum value : values()) {
            if (value.getDirName().equals(dirName)) {
                return value;
            }
        }
        throw new IllegalArgumentException("不支持的文件夹名称：" + dirName);
    }
}
