package com.superred.supervisor.manager.model.vo.command.terminal;


import com.superred.supervisor.manager.model.vo.command.OperationRangeReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TerminalSoftUpgradeCmdReq extends OperationRangeReq {

    @Schema(description = "升级文件记录id")
    @NotNull(message = "升级文件记录id不能为空")
    private Integer id;

    @Schema(description = "升级方式：0:静默；1：手动")
    private Integer updateMethod;

}
