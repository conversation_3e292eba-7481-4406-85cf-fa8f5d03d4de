#!/bin/bash

ETHCFG_PATH="/etc/sysconfig/network-scripts"

# 初始化网卡信息

ethName=$1
if [ $# -ge 1 ]; then
	if [ -f /etc/sysconfig/network-scripts/ifcfg-${ethName} ]; then
		# status=`cat /etc/sysconfig/network-scripts/ifcfg-${ethName} | grep "ONBOOT" |awk -F = '{print $2}'`
		# echo $status
		ethstr=`ifconfig ${ethName} | grep "flags=" | grep "RUNNING"`
		if [ -n "$ethstr" ]; then
			echo "yes"
		else
			echo "no"
		fi
	else
		echo "{state:1,message:\"ethernet '${ethName}' not exists!\"}"
	fi
else
	echo "{state:1,message:\"parameter error!\"}"
fi


