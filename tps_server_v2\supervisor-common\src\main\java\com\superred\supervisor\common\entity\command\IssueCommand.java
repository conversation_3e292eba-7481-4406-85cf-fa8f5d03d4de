package com.superred.supervisor.common.entity.command;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 命令下发表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@TableName("sys_issue_command")
public class IssueCommand {

    /**
     * 数据库主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 命令ID，主要用于跟踪每次命令的执行情况，每次下发命令时，该值递增或更新。
     */
    private String cmdId;

    /**
     * 设备编号
     */
    private String deviceId;

    /**
     * 命令类型
     */
    private String type;

    /**
     * 命令具体类型
     */
    private String cmd;

    /**
     * 系统命令参数
     */
    private String param;

    /**
     * 模块
     */
    private String module;

    /**
     * 子模块
     */
    private String submodule;

    /**
     * 策略版本号
     */
    private String version;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 指令下发状态，0（未下发）、1（已下发）
     */
    private Integer status;

    /**
     * 下发时间
     */
    private LocalDateTime issueTime;

    /**
     * 执行结果，取值为：0（成功）、1（失败）
     */
    private Integer result;

    /**
     * 执行结果描述，result为1，说明失败原因
     */
    private String message;

    /**
     * 指令详情
     */
    private String detail;

    /**
     * 结果上报时间
     */
    private LocalDateTime time;

    /**
     * 是否管理中心下发，0：否，1：是
     */
    private Integer isMgr;

    /**
     * 是否上报到管理中心，0：否，1：是
     */
    private Integer isReport;

    /**
     * 上报到管理中心时间
     */
    private LocalDateTime reportTime;

}
