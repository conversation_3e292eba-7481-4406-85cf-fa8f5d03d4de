package com.superred.supervisor.manager.model.dto.policy.config;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleAttackMalware;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-03-27 20:39
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MalwarePolicyConfigDTO {


    /**
     * 策略ID
     */
    private Long ruleId;

    /**
     * 当md5有值时以MD5为准
     */
    private String md5;

    /**
     * Yara
     * 规则内容
     */
    private String rule;

    /**
     * 恶意程序种类 1. 窃密木马 2. 远控木马 3. 电脑病毒 4. 僵尸网络 5. 网络蠕虫 6. 间谍软件 7. 挖矿木马 8. 黑客工具 9. 勒索软件 10. 恶意文档 11. 后门程序 99. 其它
     */
    private Integer attackClass;

    /**
     * 攻击组织
     */
    private String attackGroup;

    /**
     * 规则名称
     */
    private String ruleName;


    /**
     * 告警描述
     */
    private String desc;

    /**
     * 告警级别，0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）。
     */
    private Integer risk;

    public static MalwarePolicyConfigDTO getPolicyConfig(RuleAttackMalware malware) {
        if (malware == null) {
            return null;
        }
        return MalwarePolicyConfigDTO.builder()
                .ruleId(Long.parseLong(malware.getRuleId()))
                .md5(PolicyUtils.handleStrNull(malware.getMd5()))
                .rule(PolicyUtils.base64Encode(PolicyUtils.handleStrNull(malware.getRule())))
                .attackClass(PolicyUtils.strToInt(malware.getAttackClass()))
                .attackGroup(PolicyUtils.handleStrNull(malware.getAttackGroup()))
                .ruleName(PolicyUtils.handleStrNull(malware.getRuleName()))
                .desc(PolicyUtils.handleStrNull(malware.getDesc()))
                .risk(PolicyUtils.strToInt(malware.getRisk()))
                .build();
    }

}
