package com.superred.supervisor.common.repository.devices;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.devices.DeviceSystemStatus;
import com.superred.supervisor.common.mapper.devices.DeviceSystemStatusMapper;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * 检测器运行状态 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-18 20:06:46
 */
@Repository
public class DeviceSystemStatusRepository extends ServiceImpl<DeviceSystemStatusMapper, DeviceSystemStatus> {

    public List<DeviceSystemStatus> listByDeviceIds(List<String> deviceIds) {
        if (CollUtil.isEmpty(deviceIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<DeviceSystemStatus> queryWrapper = Wrappers.<DeviceSystemStatus>lambdaQuery().in(DeviceSystemStatus::getDeviceId, deviceIds);
        return list(queryWrapper);
    }
}

