package com.superred.common.core.listener;

import cn.hutool.core.util.StrUtil;
import com.superred.common.core.constant.CoreConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationPreparedEvent;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;

import java.util.Collections;
import java.util.List;

/**
 * 配置注入类
 * 早于 ApplicationContext 初始化的事件，都得用 SPI(spring.factories) 注册监听器或 EnvironmentPostProcessor
 *
 * <AUTHOR>
 * @since 2025/6/4 11:27
 */
@Slf4j
public class SystemPropertyInjector implements ApplicationListener<ApplicationPreparedEvent> {


    @Override
    public void onApplicationEvent(ApplicationPreparedEvent event) {
        ConfigurableEnvironment environment = event.getApplicationContext().getEnvironment();
        List<String> keys = Binder.get(environment)
                .bind(CoreConstant.INJECT_SYS_PROPERTY_KEYS, Bindable.listOf(String.class))
                .orElse(Collections.emptyList());

        if (keys == null || keys.isEmpty()) {
            log.warn("No system property keys configured to inject");
            return;
        }
        for (String key : keys) {
            injectProperty(environment, key);
        }
    }

    private void injectProperty(Environment environment, String key) {
        String value = environment.getProperty(key);
        if (value != null) {
            System.setProperty(key, value);
            log.warn(">>>>>>>System property [{}] has been set to [{}]", key, StrUtil.sub(value, 0, 3) + "******");
        }
    }
}
