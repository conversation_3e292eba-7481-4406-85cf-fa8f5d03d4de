package com.superred.supervisor.manager.model.vo.devices;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:
 * @Date: 2020/5/25 16:01
 */
@Data
public class CheckTaskItemVO {

    @Schema(description = "检查项")
    private String item;

    @Schema(description = "违规项")
    private Integer illegalCount;

    @Schema(description = "总数")
    private Integer total;
}
