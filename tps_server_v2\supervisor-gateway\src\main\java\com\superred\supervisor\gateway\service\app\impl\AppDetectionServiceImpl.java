package com.superred.supervisor.gateway.service.app.impl;

import com.superred.common.core.utils.JsonUtil;
import com.superred.common.minio.core.utils.SimpleMinioUploadClient;
import com.superred.supervisor.common.model.dto.detection.AgentDetectionResultDTO;
import com.superred.supervisor.file.consant.ExportFile;
import com.superred.supervisor.gateway.exception.ApiBaseException;
import com.superred.supervisor.gateway.kafka.producer.AgentDetectionEventPublisher;
import com.superred.supervisor.gateway.model.app.detection.AppFileFilterAlertReq;
import com.superred.supervisor.gateway.service.app.AppDetectionService;
import com.superred.supervisor.standard.v202505.terminal.detection.FileDescReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;

import static com.superred.supervisor.common.constant.CommonConstant.FILE_FILTER_FILE_MINIO_PATH;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/6/30 15:53
 */
@Service
@Slf4j
public class AppDetectionServiceImpl implements AppDetectionService {
    @Resource
    private SimpleMinioUploadClient simpleMinioUploadClient;

    @Resource
    private AgentDetectionEventPublisher agentDetectionEventPublisher;

    /**
     * 文件检测数据上报
     *
     * @param req  req
     * @param type (keyword|md5|security_classification_level|secret_level)_filter
     */
    @Override
    public void reportAlter(AppFileFilterAlertReq req, String type) {
        ExportFile exportFileType = ExportFile.getExportFileType(type);
        if (exportFileType == null) {
            log.error("上报应用系统告警失败，告警类型不支持，类型：{}", type);
            throw new ApiBaseException("上报应用系统告警失败，告警类型不支持");
        }

        AgentDetectionResultDTO<AppFileFilterAlertReq> agentDetectionResultDTO = AgentDetectionResultDTO.<AppFileFilterAlertReq>builder()
                .exportFile(exportFileType)
                .requestBody(req)
                .build();

        agentDetectionEventPublisher.sendMessage(agentDetectionResultDTO);
    }

    /**
     * 文件检测相关文件上报
     *
     * @param fileDesc 文件描述
     * @param type     (keyword|md5|security_classification_level|secret_level)_filter
     * @param singleMultipartFile 文件
     */
    @Override
    public void uploadAlterFile(String fileDesc, String type, MultipartFile singleMultipartFile) {

        FileDescReq fileDescReq = JsonUtil.fromJson(fileDesc, FileDescReq.class);
        if (fileDescReq == null) {
            log.error("文件检测相关文件上报失败，文件描述解析失败，类型：{}, {}", type, fileDesc);
            throw new ApiBaseException("文件检测相关文件上报，文件描述解析失败");
        }

        try (InputStream inputStream = singleMultipartFile.getInputStream()) {
            simpleMinioUploadClient.uploadFileSync(FILE_FILTER_FILE_MINIO_PATH, fileDescReq.getChecksum(), inputStream);
        } catch (Exception e) {
            log.error("文件检测相关文件上报失败，文件描述：{}，类型：{}，异常：{}", fileDesc, type, e.getMessage(), e);
            throw new ApiBaseException("上传攻击文件失败", e);
        }

        log.info("上传检测文件成功，文件描述：{}，类型：{}", fileDesc, type);

        AgentDetectionResultDTO<FileDescReq> agentDetectionResultDTO = AgentDetectionResultDTO.<FileDescReq>builder()
                .exportFile(ExportFile.FILE_FILTER_SOURCE_FILE)
                .requestBody(fileDescReq)
                .minioPath(FILE_FILTER_FILE_MINIO_PATH + fileDescReq.getChecksum())
                .fileChecksum(fileDescReq.getChecksum())
                .build();

        // 发送消息到Kafka
        agentDetectionEventPublisher.sendMessage(agentDetectionResultDTO);
    }
}
