<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.superred</groupId>
        <artifactId>supervisor</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>supervisor-common</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- mybatis相关注解 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
            <version>${mybatis-plus-bom.verison}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-core</artifactId>
            <version>2.2.9</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.superred</groupId>
            <artifactId>supervisor-model</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>${redission-spring-boot-starter.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>


    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>http://nx.superred.com.cn:8081/repository/maven2-releases/</url> <!-- B服务器的releases仓库地址 -->
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>http://nx.superred.com.cn:8081/repository/maven2-snapshots/</url> <!-- B服务器的snapshots仓库地址 -->
        </snapshotRepository>
    </distributionManagement>
</project>
