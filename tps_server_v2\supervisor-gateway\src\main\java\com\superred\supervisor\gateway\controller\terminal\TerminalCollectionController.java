package com.superred.supervisor.gateway.controller.terminal;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.service.terminal.TerminalCollectionService;
import com.superred.supervisor.standard.v202505.terminal.collection.InformationDeviceInfoReq;
import com.superred.supervisor.standard.v202505.terminal.collection.MediumDeviceReq;
import com.superred.supervisor.standard.v202505.terminal.collection.TerminalAppSoftwareReq;
import com.superred.supervisor.standard.v202505.terminal.collection.TerminalDeviceInfoReq;
import com.superred.supervisor.standard.v202505.terminal.collection.TerminalNetworkBehaviorReq;
import com.superred.supervisor.standard.v202505.terminal.collection.WirelessDeviceReq;
import com.superred.supervisor.standard.v202505.terminal.collection.WirelessHotSpotReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 采集数据上报
 *
 * <AUTHOR>
 * @since 2025/5/29 14:00
 */
@Tag(name = "1.5 采集数据上报(2025-05)")
@ApiSupport(order = 5)
@RestController
@Slf4j
@RequestMapping("/C2/")
public class TerminalCollectionController {

    @Resource
    private TerminalCollectionService terminalCollectionService;


    /**
     *  开机后上报
     *
     * @return {@link ApiResponse }
     */
    @PostMapping("/collect/terminal_device/info")
    @Operation(summary = "C.3.8.1.1 终端设备信息上报")
    @ApiOperationSupport(order = 1)
    public ApiResponse<String> reportTerminalDevice(@RequestBody TerminalDeviceInfoReq req) {

        terminalCollectionService.reportTerminalDevice(req);

        return ApiResponse.success();
    }


    /**
     * 信息化设备上报：通过USB接口、网络接口方式等直接外接的信息化类设备信息
     * 默认每小时采集上报一次
     * @return {@link ApiResponse }
     */
    @PostMapping("/collect/info_mation_device/info")
    @Operation(summary = "C.3.8.1.2 信息化设备上报")
    @ApiOperationSupport(order = 2)
    public ApiResponse<String> reportInformationDeviceInfo(@RequestBody InformationDeviceInfoReq req) {


        terminalCollectionService.reportInformationDeviceInfo(req);

        return ApiResponse.success();
    }

    /**
     * 存储介质类设备信息上报
     * 默认每小时采集上报一次
     * @return {@link ApiResponse }
     */
    @PostMapping("/collect/medium_device/info")
    @Operation(summary = "C.3.8.1.3 存储介质类设备信息上报")
    @ApiOperationSupport(order = 3)
    public ApiResponse<String> reportMediumDeviceInfo(@RequestBody MediumDeviceReq req) {

        terminalCollectionService.reportMediumDeviceInfo(req);
        return ApiResponse.success();
    }

    /**
     * 智能设备信息上报
     * 默认每小时采集上报一次
     * @return {@link ApiResponse }
     */
    @PostMapping("/collect/wireless_device/info")
    @Operation(summary = "C.3.8.1.4 智能设备信息上报")
    @ApiOperationSupport(order = 4)
    public ApiResponse<String> reportWirelessDeviceInfo(@RequestBody WirelessDeviceReq req) {

        terminalCollectionService.reportWirelessDeviceInfo(req);

        return ApiResponse.success();
    }

    /**
     * 无线热点网络状态上报
     * 默认每小时采集上报一次
     * @return {@link ApiResponse }
     */
    @PostMapping("/collect/wireless_hotspot/info")
    @Operation(summary = "C.3.8.1.5 无线热点网络状态上报")
    @ApiOperationSupport(order = 5)
    public ApiResponse<String> reportWirelessHotspotInfo(@RequestBody WirelessHotSpotReq req) {

        terminalCollectionService.reportWirelessHotspotInfo(req);

        return ApiResponse.success();
    }

    /**
     * 应用类软件信息上报
     * 默认每小时采集上报一次
     * @return {@link ApiResponse }
     */
    @PostMapping("/collect/app_software/info")
    @Operation(summary = "C.3.8.1.6 应用类软件信息上报")
    @ApiOperationSupport(order = 6)
    public ApiResponse<String> reportAppSoftwareInfo(@RequestBody TerminalAppSoftwareReq req) {

        terminalCollectionService.reportAppSoftwareInfo(req);
        return ApiResponse.success();
    }

    /**
     *  每小时采集一次
     *
     * @return {@link ApiResponse }
     */
    @PostMapping("/collect/network_behavior/info")
    @Operation(summary = "C.3.8.2 风险软件使用行为上报")
    @ApiOperationSupport(order = 7)
    public ApiResponse<String> reportNetworkBehavior(@RequestBody TerminalNetworkBehaviorReq req) {
        terminalCollectionService.reportNetworkBehavior(req);

        return ApiResponse.success();
    }
}
