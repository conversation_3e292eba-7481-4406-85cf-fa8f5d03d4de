package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-08-29 14:26
 */
@Data
public class AgentInnerPolicyVO {

    @Schema(description = "内置策略文件ID")
    private Integer id;

    @Schema(description = "内置策略文件名")
    private String filename;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}