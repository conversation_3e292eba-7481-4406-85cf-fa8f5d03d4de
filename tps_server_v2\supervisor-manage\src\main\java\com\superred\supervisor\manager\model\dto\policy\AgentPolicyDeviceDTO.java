package com.superred.supervisor.manager.model.dto.policy;

import cn.hutool.core.collection.CollectionUtil;
import com.superred.supervisor.common.entity.policy.AgentPolicyDevice;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:47
 */
@Data
public class AgentPolicyDeviceDTO {

    private Long id;

    /**
     * 策略ID
     */
    @Schema(description = "策略ID")
    private Long policyId;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * 下发状态
     */
    @Schema(description = "下发状态")
    private Integer status;

    /**
     * 模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单
     */
    @Schema(description = "模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单")
    private String module;

    public static List<AgentPolicyDeviceDTO> fromAgentPolicyDevice(List<AgentPolicyDevice> agentPolicyDeviceList) {
        List<AgentPolicyDeviceDTO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(agentPolicyDeviceList)) {
            agentPolicyDeviceList.forEach(item -> {
                AgentPolicyDeviceDTO agentPolicyDeviceDTO = new AgentPolicyDeviceDTO();
                agentPolicyDeviceDTO.setId(item.getId());
                agentPolicyDeviceDTO.setPolicyId(item.getPolicyId());
                agentPolicyDeviceDTO.setDeviceId(item.getDeviceId());
                agentPolicyDeviceDTO.setStatus(item.getStatus());
                agentPolicyDeviceDTO.setModule(item.getModule());
                list.add(agentPolicyDeviceDTO);
            });
        }
        return list;
    }
}
