package com.superred.supervisor.manager.aop;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 **/
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface SysLogAnn {

    /**
     * 操作模块
     */
    String module() default "";

    /**
     * 操作动作
     */
    String operateType() default "";

    /**
     * 行为类型
     * 1.违规行为;2.异常行为;3 一般行为
     */
    String behaviourType() default "一般行为";

    /**
     * 日志风险级别
     * 1紧急、2重要、3一般、4信息
     */
    String level() default "信息";

    /**
     * 描述
     */
    String desc() default "";

    /**
     * 描述参数
     */
    String para1() default "";

    /**
     * 描述参数
     */
    String para2() default "";

    /**
     * 描述参数
     */
    String para3() default "";

    /**
     * 描述参数
     */
    String para4() default "";

    /**
     * 描述参数
     */
    String para5() default "";
}
