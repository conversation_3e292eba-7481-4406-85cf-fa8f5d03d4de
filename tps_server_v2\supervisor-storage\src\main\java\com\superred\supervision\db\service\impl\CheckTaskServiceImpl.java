package com.superred.supervision.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.Constants;
import com.superred.supervision.base.constant.OtherTopic;
import com.superred.supervision.base.constant.ViolationConstants;
import com.superred.supervision.base.vo.check.CheckReportVo;
import com.superred.supervision.base.vo.check.FieldVo;
import com.superred.supervision.base.vo.check.HeadRecordVo;
import com.superred.supervision.base.vo.check.RecordVo;
import com.superred.supervision.base.vo.check.RecordsVo;
import com.superred.supervision.base.vo.log.LogVo;
import com.superred.supervision.db.config.TaskMapConfig;
import com.superred.supervision.db.entity.AgentCheckDeduction;
import com.superred.supervision.db.entity.CheckConfig;
import com.superred.supervision.db.entity.CheckDeepContent;
import com.superred.supervision.db.entity.CheckDeepOperate;
import com.superred.supervision.db.entity.CheckDeepUsb;
import com.superred.supervision.db.entity.CheckDiskInfo;
import com.superred.supervision.db.entity.CheckDiskReplace;
import com.superred.supervision.db.entity.CheckDriveLog;
import com.superred.supervision.db.entity.CheckFileAccessRecord;
import com.superred.supervision.db.entity.CheckFileContent;
import com.superred.supervision.db.entity.CheckItem;
import com.superred.supervision.db.entity.CheckItemResult;
import com.superred.supervision.db.entity.CheckLogSetting;
import com.superred.supervision.db.entity.CheckLoginLog;
import com.superred.supervision.db.entity.CheckNetInfo;
import com.superred.supervision.db.entity.CheckOnOff;
import com.superred.supervision.db.entity.CheckOpenPort;
import com.superred.supervision.db.entity.CheckOsInfo;
import com.superred.supervision.db.entity.CheckProcessInfo;
import com.superred.supervision.db.entity.CheckScreenSaver;
import com.superred.supervision.db.entity.CheckSecureLog;
import com.superred.supervision.db.entity.CheckSecurePolicy;
import com.superred.supervision.db.entity.CheckServiceInfo;
import com.superred.supervision.db.entity.CheckShareInfo;
import com.superred.supervision.db.entity.CheckShellLog;
import com.superred.supervision.db.entity.CheckTask;
import com.superred.supervision.db.entity.CheckTaskProgress;
import com.superred.supervision.db.entity.CheckUsbConfig;
import com.superred.supervision.db.entity.CheckUsbMark;
import com.superred.supervision.db.entity.CheckUserGroup;
import com.superred.supervision.db.entity.CheckUserInfo;
import com.superred.supervision.db.enums.TaskStatusEnum;
import com.superred.supervision.db.mapper.CheckTaskMapper;
import com.superred.supervision.db.service.AgentCheckDeductionService;
import com.superred.supervision.db.service.CheckConfigService;
import com.superred.supervision.db.service.CheckDeepContentService;
import com.superred.supervision.db.service.CheckDeepOperateService;
import com.superred.supervision.db.service.CheckDeepUsbService;
import com.superred.supervision.db.service.CheckDiskInfoService;
import com.superred.supervision.db.service.CheckDiskReplaceService;
import com.superred.supervision.db.service.CheckDriveLogService;
import com.superred.supervision.db.service.CheckFileAccessRecordService;
import com.superred.supervision.db.service.CheckFileContentService;
import com.superred.supervision.db.service.CheckItemResultService;
import com.superred.supervision.db.service.CheckItemService;
import com.superred.supervision.db.service.CheckLogSettingService;
import com.superred.supervision.db.service.CheckLoginLogService;
import com.superred.supervision.db.service.CheckNetInfoService;
import com.superred.supervision.db.service.CheckOnOffService;
import com.superred.supervision.db.service.CheckOpenPortService;
import com.superred.supervision.db.service.CheckOsInfoService;
import com.superred.supervision.db.service.CheckProcessInfoService;
import com.superred.supervision.db.service.CheckScreenSaverService;
import com.superred.supervision.db.service.CheckSecureLogService;
import com.superred.supervision.db.service.CheckSecurePolicyService;
import com.superred.supervision.db.service.CheckServiceInfoService;
import com.superred.supervision.db.service.CheckShareInfoService;
import com.superred.supervision.db.service.CheckShellLogService;
import com.superred.supervision.db.service.CheckTaskProgressService;
import com.superred.supervision.db.service.CheckTaskService;
import com.superred.supervision.db.service.CheckUsbConfigService;
import com.superred.supervision.db.service.CheckUsbMarkService;
import com.superred.supervision.db.service.CheckUserGroupService;
import com.superred.supervision.db.service.CheckUserInfoService;
import com.superred.supervision.db.util.Sm4Util;
import com.superred.supervision.db.vo.agent.TaskVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 终端检测任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Service
@AllArgsConstructor
@Slf4j
public class CheckTaskServiceImpl extends ServiceImpl<CheckTaskMapper, CheckTask> implements CheckTaskService {

    private final TaskMapConfig taskMapConfig;

    private final CheckTaskProgressService checkTaskProgressService;

    private final CheckFileContentService checkFileContentService;

    private final CheckFileAccessRecordService checkFileAccessRecordService;

    private final CheckUsbMarkService checkUsbMarkService;

    private final CheckOsInfoService checkOsInfoService;

    private final CheckDiskInfoService checkDiskInfoService;

    private final CheckDiskReplaceService checkDiskReplaceService;

    private final CheckNetInfoService checkNetInfoService;

    private final CheckUserInfoService checkUserInfoService;

    private final CheckServiceInfoService checkServiceInfoService;

    private final CheckProcessInfoService checkProcessInfoService;

    private final CheckOpenPortService checkOpenPortService;

    private final CheckSecureLogService checkSecureLogService;

    private final CheckDriveLogService checkDriveLogService;

    private final CheckLoginLogService checkLoginLogService;

    private final CheckShellLogService checkShellLogService;

    private final CheckOnOffService checkOnOffService;

    private final CheckSecurePolicyService checkSecurePolicyService;

    private final CheckShareInfoService checkShareInfoService;

    private final CheckLogSettingService checkLogSettingService;

    private final CheckScreenSaverService checkScreenSaverService;

    private final CheckUserGroupService checkUserGroupService;

    private final CheckDeepContentService checkDeepContentService;

    private final CheckDeepOperateService checkDeepOperateService;

    private final CheckDeepUsbService checkDeepUsbService;

    private final CheckItemResultService checkItemResultService;

    private final CheckItemService checkItemService;

    private final CheckUsbConfigService checkUsbConfigService;

    private final CheckConfigService checkConfigService;

    private final AgentCheckDeductionService checkDeductionService;

    private final KafkaTemplate<String, String> kafkaTemplate;

    private OtherTopic otherTopic;


    @Override
    public void process(TaskVo taskVo) {

        final String taskId = taskVo.getTaskId();
        final String deviceId = taskVo.getDeviceId();
        final Integer progress = taskVo.getProgress();

        List<CheckItemResult> checkItemResultList = new ArrayList<>();

        List<CheckItemResult> checkItemResultListFromDb = checkItemResultService.findByTaskIdAndDeviceId(deviceId, taskId);
        // 所有配置列表
        final List<CheckConfig> checkConfigList = checkConfigService.list();

        final Map<String, CheckItemResult> checkItemResultMap = checkItemResultListFromDb.stream().collect
                (Collectors.toMap(i -> i.getTaskId() + "-" + i.getDeviceId() + "-" + i.getCheckType()
                        + "-" + i.getCheckItem(), Function.identity()));
        // 更新进度
        updateProgress(taskId, deviceId, progress, "");
        // 配置列表
        final List<CheckItem> list = checkItemService.list();
        final Map<String, CheckItem> itemConfig = list.stream()
                .collect(Collectors.toMap(i -> i.getCheckType() + "-" + i.getCheckItem(), Function.identity()));
        checkFileContentService.saveBatch(taskVo.getFileContent());
        checkFileAccessRecordService.saveBatch(taskVo.getTraceFileOperate());
        final List<String> usbSerialNumberList = new ArrayList<>();
        final List<CheckUsbMark> traceUsbList = taskVo.getTraceUsb();

        if (CollUtil.isNotEmpty(traceUsbList)) {
            checkUsbMarkService.saveBatch(traceUsbList);
            usbSerialNumberList.addAll(traceUsbList.stream().map(CheckUsbMark::getSerialNumber)
                    .filter(StrUtil::isNotEmpty).distinct()
                    .collect(Collectors.toList()));
        }

        final CheckOsInfo osInfo = taskVo.getOsInfo();
        if (osInfo != null) {
            updateOsInfo(osInfo);
        }

        final List<CheckDiskInfo> diskInfo = taskVo.getDiskInfo();
        if (CollUtil.isNotEmpty(diskInfo)) {
            checkDiskInfoService.remove(Wrappers.<CheckDiskInfo>lambdaQuery().eq(CheckDiskInfo::getDeviceId, deviceId).eq(CheckDiskInfo::getTaskId, taskId));
            checkDiskInfoService.saveBatch(diskInfo);
        }

        checkDiskReplaceService.saveBatch(taskVo.getDiskChangeInfo());

        final List<CheckNetInfo> networkInfo = taskVo.getNetworkInfo();
        if (CollUtil.isNotEmpty(networkInfo)) {
            checkNetInfoService.remove(Wrappers.<CheckNetInfo>lambdaQuery().eq(CheckNetInfo::getDeviceId, deviceId).eq(CheckNetInfo::getTaskId, taskId));
            checkNetInfoService.saveBatch(networkInfo);
        }

        final List<CheckUserInfo> userInfo = taskVo.getUserInfo();
        if (CollUtil.isNotEmpty(userInfo)) {
            checkUserInfoService.remove(Wrappers.<CheckUserInfo>lambdaQuery().eq(CheckUserInfo::getDeviceId, deviceId).eq(CheckUserInfo::getTaskId, taskId));
            checkUserInfoService.saveBatch(userInfo);
        }

        if (CollUtil.isNotEmpty(taskVo.getServerInfo())) {
            checkServiceInfoService.remove(Wrappers.<CheckServiceInfo>lambdaQuery().eq(CheckServiceInfo::getDeviceId, deviceId).eq(CheckServiceInfo::getTaskId, taskId));
            checkServiceInfoService.saveBatch(taskVo.getServerInfo());
        }
        if (CollUtil.isNotEmpty(taskVo.getProcessInfo())) {
            checkProcessInfoService.remove(Wrappers.<CheckProcessInfo>lambdaQuery().eq(CheckProcessInfo::getDeviceId, deviceId).eq(CheckProcessInfo::getTaskId, taskId));
            checkProcessInfoService.saveBatch(taskVo.getProcessInfo());
        }

        if (CollUtil.isNotEmpty(taskVo.getOpenPort())) {
            checkOpenPortService.remove(Wrappers.<CheckOpenPort>lambdaQuery().eq(CheckOpenPort::getDeviceId, deviceId).eq(CheckOpenPort::getTaskId, taskId));
            checkOpenPortService.saveBatch(taskVo.getOpenPort());
        }
        checkSecureLogService.saveBatch(taskVo.getSysSafeLog());

        checkDriveLogService.saveBatch(taskVo.getSysDriverLog());

        checkLoginLogService.saveBatch(taskVo.getUserLoginLog());

        checkShellLogService.saveBatch(taskVo.getShellLog());

        checkOnOffService.saveBatch(taskVo.getPowerOnLog());

        if (CollUtil.isNotEmpty(taskVo.getSafePolicy())) {
            checkSecurePolicyService.remove(Wrappers.<CheckSecurePolicy>lambdaQuery().eq(CheckSecurePolicy::getDeviceId, deviceId).eq(CheckSecurePolicy::getTaskId, taskId));
            checkSecurePolicyService.saveBatch(taskVo.getSafePolicy());
        }

        final List<CheckShareInfo> shareInfo = taskVo.getShareInfo();
        if (CollUtil.isNotEmpty(shareInfo)) {
            checkShareInfoService.remove(Wrappers.<CheckShareInfo>lambdaQuery().eq(CheckShareInfo::getDeviceId, deviceId).eq(CheckShareInfo::getTaskId, taskId));
            checkShareInfoService.saveBatch(shareInfo);
        }

        if (CollUtil.isNotEmpty(taskVo.getLogSetting())) {
            checkLogSettingService.remove(Wrappers.<CheckLogSetting>lambdaQuery().eq(CheckLogSetting::getDeviceId, deviceId).eq(CheckLogSetting::getTaskId, taskId));
            checkLogSettingService.saveBatch(taskVo.getLogSetting());
        }
        final CheckScreenSaver screenSaver = taskVo.getScreenSaver();
        if (screenSaver != null) {
            LambdaUpdateWrapper<CheckScreenSaver> uw = new LambdaUpdateWrapper<>();
            uw.eq(CheckScreenSaver::getTaskId, screenSaver.getTaskId());
            uw.eq(CheckScreenSaver::getDeviceId, screenSaver.getDeviceId());
            checkScreenSaverService.update(screenSaver, uw);
        }

        if (CollUtil.isNotEmpty(taskVo.getUserGroup())) {
            checkUserGroupService.remove(Wrappers.<CheckUserGroup>lambdaQuery().eq(CheckUserGroup::getDeviceId, deviceId).eq(CheckUserGroup::getTaskId, taskId));
            checkUserGroupService.saveBatch(taskVo.getUserGroup());
        }
        checkDeepContentService.saveBatch(taskVo.getDeepFileContent());

        checkDeepOperateService.saveBatch(taskVo.getDeepFileOperate());
        final List<CheckDeepUsb> deepUsb = taskVo.getDeepUsb();
        if (CollUtil.isNotEmpty(deepUsb)) {
            checkDeepUsbService.saveBatch(deepUsb);
            usbSerialNumberList.addAll(deepUsb.stream().map(CheckDeepUsb::getSerialNumber).filter(StrUtil::isNotEmpty)
                    .distinct().collect(Collectors.toList()));
        }

        // os 违规项
        if (osInfo != null) {
            osMore(taskId, deviceId, checkItemResultList, checkItemResultMap, itemConfig, osInfo);
        }
        // 磁盘违规项
        if (CollUtil.isNotEmpty(diskInfo)) {
            if (diskInfo.size() >= 2) {
                commonViolation(taskId, deviceId, checkItemResultList, checkItemResultMap, itemConfig,
                        ViolationConstants.CHECK_TYPE_HOST, ViolationConstants.CHECK_TYPE_HOST_DISK_MORE,
                        ViolationConstants.CHECK_TYPE_HOST_DISK_MORE_DESC);
            }
        }
        // 磁盘更换情况
        if (CollUtil.isNotEmpty(taskVo.getDiskChangeInfo())) {
            commonViolation(taskId, deviceId, checkItemResultList, checkItemResultMap, itemConfig,
                    ViolationConstants.CHECK_TYPE_HOST, ViolationConstants.CHECK_TYPE_HOST_DISK_CHANGE,
                    ViolationConstants.CHECK_TYPE_HOST_DISK_CHANGE_DESC);
        }

        // usb 违规判断
        if (CollUtil.isNotEmpty(usbSerialNumberList)) {
            // 违规usb列表
            final List<String> violationUsbList = checkUsbConfigService.list().stream().filter(i -> i.getType() == 2)
                    .map(CheckUsbConfig::getUsbSerial).collect(Collectors.toList());

            final boolean usbFlag = CollUtil.containsAny(usbSerialNumberList, violationUsbList);
            if (usbFlag) {
                commonViolation(taskId,
                        deviceId, checkItemResultList, checkItemResultMap, itemConfig,
                        ViolationConstants.CHECK_TYPE_USB,
                        ViolationConstants.CHECK_TYPE_USB_ILLEGAL,
                        ViolationConstants.CHECK_TYPE_USB_ILLEGAL_DESC);
            }
        }
        // mac 地址修改
        if (CollUtil.isNotEmpty(networkInfo)) {
            // 如果存在 mac和originalMac 不相同记录违规信息
            networkInfo.stream().filter(i -> StrUtil.isNotEmpty(i.getMac()))
                    .filter(i -> StrUtil.isNotEmpty(i.getOriginalMac()))
                    .filter(net -> !net.getMac().equals(net.getOriginalMac())).findAny()
                    .ifPresent(i -> commonViolation(taskId, deviceId, checkItemResultList, checkItemResultMap, itemConfig,
                            ViolationConstants.CHECK_TYPE_HOST, ViolationConstants.CHECK_TYPE_HOST_MAC_CHANGE,
                            ViolationConstants.CHECK_TYPE_HOST_MAC_CHANGE_DESC));
        }
        // 空口令或者弱口令
        if (CollUtil.isNotEmpty(userInfo)) {
            userInfo.stream().map(CheckUserInfo::getPasswordType).filter(Objects::nonNull)
                    .filter(i -> i == 0 || i == 1).findAny().ifPresent(i ->
                            commonViolation(taskId, deviceId, checkItemResultList, checkItemResultMap, itemConfig,
                                    ViolationConstants.CHECK_TYPE_SYSTEM, ViolationConstants.CHECK_TYPE_SYSTEM_WEEK_PASSWORD,
                                    ViolationConstants.CHECK_TYPE_SYSTEM_WEEK_PASSWORD_DESC));
        }
        // 黑名单列表中的服务
        if (CollUtil.isNotEmpty(taskVo.getServerInfo())) {
            // 黑名单服务
            final List<String> blackServiceList = checkConfigList.stream()
                    .filter(i -> Constants.CHECK_CONFIG_TYPE_SERVICE.equals(i.getConfigType()))
                    .filter(i -> Constants.CHECK_CONFIG_TYPE_BLACK.equals(i.getType()))
                    .map(CheckConfig::getContent)
                    .collect(Collectors.toList());

            final List<String> serverInfoList = taskVo.getServerInfo().stream().map(CheckServiceInfo::getServerName).collect(Collectors.toList());
            // 判断是否存在黑名单服务
            final boolean serviceFlag = CollUtil.containsAny(serverInfoList, blackServiceList);
            if (serviceFlag) {
                commonViolation(taskId,
                        deviceId, checkItemResultList, checkItemResultMap, itemConfig,
                        ViolationConstants.CHECK_TYPE_SYSTEM,
                        ViolationConstants.CHECK_TYPE_SYSTEM_SERVER_BLACK,
                        ViolationConstants.CHECK_TYPE_SYSTEM_SERVER_BLACK_DESC);
            }
        }
        // 进程
        if (CollUtil.isNotEmpty(taskVo.getProcessInfo())) {
            // 黑名单进程
            final List<String> blackProcessList = checkConfigList.stream()
                    .filter(i -> Constants.CHECK_CONFIG_TYPE_PROCESS.equals(i.getConfigType()))
                    .filter(i -> Constants.CHECK_CONFIG_TYPE_BLACK.equals(i.getType()))
                    .map(CheckConfig::getContent)
                    .collect(Collectors.toList());

            final List<String> processList = taskVo.getProcessInfo().stream().map(CheckProcessInfo::getProcessName).collect(Collectors.toList());
            final boolean processFlag = CollUtil.containsAny(processList, blackProcessList);
            if (processFlag) {
                commonViolation(taskId,
                        deviceId, checkItemResultList, checkItemResultMap, itemConfig,
                        ViolationConstants.CHECK_TYPE_SYSTEM,
                        ViolationConstants.CHECK_TYPE_SYSTEM_PROCESS_BLACK,
                        ViolationConstants.CHECK_TYPE_SYSTEM_PROCESS_BLACK_DESC);
            }
        }
        // 端口
        if (CollUtil.isNotEmpty(taskVo.getOpenPort())) {
            // 黑名单端口
            final List<Integer> blackPortList = checkConfigList.stream()
                    .filter(i -> Constants.CHECK_CONFIG_TYPE_PORT.equals(i.getConfigType()))
                    .filter(i -> Constants.CHECK_CONFIG_TYPE_BLACK.equals(i.getType()))
                    .map(CheckConfig::getContent).map(Integer::valueOf)
                    .collect(Collectors.toList());
            final List<Integer> portList = taskVo.getOpenPort().stream().map(CheckOpenPort::getOuterPort).collect(Collectors.toList());
            final boolean portFlag = CollUtil.containsAny(portList, blackPortList);
            if (portFlag) {
                commonViolation(taskId,
                        deviceId, checkItemResultList, checkItemResultMap, itemConfig,
                        ViolationConstants.CHECK_TYPE_SYSTEM,
                        ViolationConstants.CHECK_TYPE_SYSTEM_PORT_BLACK,
                        ViolationConstants.CHECK_TYPE_SYSTEM_PORT_BLACK_DESC);
            }
        }
        // 共享信息
        if (CollUtil.isNotEmpty(shareInfo)) {
            shareInfo.stream().map(CheckShareInfo::getName).filter(StrUtil::isNotEmpty)
                    .filter("IPC$"::equals).findAny()
                    .ifPresent(i ->
                            commonViolation(taskId,
                                    deviceId, checkItemResultList, checkItemResultMap, itemConfig,
                                    ViolationConstants.CHECK_TYPE_CONFIG,
                                    ViolationConstants.CHECK_TYPE_CONFIG_SHARE_DIR,
                                    ViolationConstants.CHECK_TYPE_CONFIG_SHARE_DIR_DESC)
                    );
        }
        if (CollUtil.isNotEmpty(taskVo.getSafePolicy())) {
            taskVo.getSafePolicy().stream()
                    .filter(i -> "password_complexity".equals(i.getPolicyName()) && ("0").equals(i.getSafeSettion()))
                    .findAny().ifPresent(i ->
                            commonViolation(taskId,
                                    deviceId, checkItemResultList, checkItemResultMap, itemConfig,
                                    ViolationConstants.CHECK_TYPE_SYSTEM,
                                    ViolationConstants.CHECK_TYPE_SYSTEM_SAFE_PASSWORD,
                                    ViolationConstants.CHECK_TYPE_SYSTEM_SAFE_PASSWORD_DESC)
                    );
        }
        // 违规判定结果 新增
        if (CollUtil.isNotEmpty(checkItemResultList)) {
            checkItemResultService.saveOrUpdateBatch(checkItemResultList);
        }
    }

    @Override
    public void processFile(CheckReportVo checkReportVo, String deviceId, String checksum) {

        final HeadRecordVo headRecord = checkReportVo.getHeadRecord();
        final String taskId = headRecord.getCheckGUID();
        if (StrUtil.isEmpty(taskId)) {
            log.info("taskId : " + taskId);
            return;
        }
        // 基本检查 --- 操作系统信息
        this.handleHostInfo(checkReportVo, deviceId, taskId, taskMapConfig);
        // 账户信息
        this.handleAccountInfo(checkReportVo, deviceId, taskId, taskMapConfig);
        // 网络信息
        this.handleNetworkInfo(checkReportVo, deviceId, taskId, taskMapConfig);
        // 硬盘信息
        this.handleHardDiskInfo(checkReportVo, deviceId, taskId, taskMapConfig);
        // usb信息
        this.handleFastUSBRecord(checkReportVo, deviceId, taskId, taskMapConfig);
        // 文件检查
        this.handleFastFileCheck(checkReportVo, deviceId, taskId, taskMapConfig);
        // 服务情况
        this.handleServices(checkReportVo, deviceId, taskId, taskMapConfig);
        // 端口情况
        this.handlePorts(checkReportVo, deviceId, taskId, taskMapConfig);
        // 文件操作记录
        this.handleFileOperateRecord(checkReportVo, deviceId, taskId, taskMapConfig);
        // 硬盘更换情况
        this.handleDiskChangeInfo(checkReportVo, deviceId, taskId, taskMapConfig);
        // 进程信息
        this.handleProcessInfo(checkReportVo, deviceId, taskId, taskMapConfig);
        // 系统安全日志
        this.handleSysSafeLog(checkReportVo, deviceId, taskId, taskMapConfig);
        // 系统驱动日志
        this.handleSysDriverLog(checkReportVo, deviceId, taskId, taskMapConfig);
        // 用户登录日志
        this.handleUserLoginLog(checkReportVo, deviceId, taskId, taskMapConfig);
        // shell日志
        this.handleShellLog(checkReportVo, deviceId, taskId, taskMapConfig);
        // 开关机日志
        this.handlePowerOnLog(checkReportVo, deviceId, taskId, taskMapConfig);
        // 安全策略
        this.handleSafePolicy(checkReportVo, deviceId, taskId, taskMapConfig);
        // 共享信息
        this.handleShareInfo(checkReportVo, deviceId, taskId, taskMapConfig);
        // 日志设置
        this.handleLogSetting(checkReportVo, deviceId, taskId, taskMapConfig);
        // 屏幕保护
        this.handleScreenSaver(checkReportVo, deviceId, taskId, taskMapConfig);
        // 用户组
        this.handleUserGroup(checkReportVo, deviceId, taskId, taskMapConfig);
        // 深度文件内容
        this.handleDeepFileCheck(checkReportVo, deviceId, taskId, taskMapConfig);
        // 深度文件操作
        this.handleDeepFileOperate(checkReportVo, deviceId, taskId, taskMapConfig);
        // 深度usb
        this.handleDeepUSBRecord(checkReportVo, deviceId, taskId, taskMapConfig);

        //updateProgress(taskId, deviceId, 100, checksum);
        updateTaskProgress(taskId, deviceId, checksum, TaskStatusEnum.COMPLETED.getCode());
    }

    /**
     * 深度usb
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleDeepUSBRecord(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckDeepUsb> checkDeepUsbs = null;
        try {
            checkDeepUsbs = xmlListToDb(checkReportVo.getDeepUSBRecord(), deviceId, taskId,
                    CheckDeepUsb.class, taskMapConfig.getDeepUsbMap());
            if (CollectionUtil.isEmpty(checkDeepUsbs)) {
                return;
            }
            checkDeepUsbService.saveBatch(checkDeepUsbs);
        } catch (Exception e) {
            log.error("深度usb saveBatch error", e);
            checkDeepUsbs.forEach(item -> {
                try {
                    checkDeepUsbService.save(item);
                } catch (Exception e1) {
                    log.error("深度usb saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 深度文件操作
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleDeepFileOperate(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckDeepOperate> checkDeepOperates = null;
        try {
            checkDeepOperates = xmlListToDb(checkReportVo.getDeepFileOperate(), deviceId, taskId,
                    CheckDeepOperate.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkDeepOperates)) {
                return;
            }
            checkDeepOperateService.saveBatch(checkDeepOperates);
        } catch (Exception e) {
            log.error("深度文件操作 saveBatch error", e);
            checkDeepOperates.forEach(item -> {
                try {
                    checkDeepOperateService.save(item);
                } catch (Exception e1) {
                    log.error("深度文件操作 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 深度文件内容
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleDeepFileCheck(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckDeepContent> checkDeepContents = null;
        try {
            checkDeepContents = xmlListToDb(checkReportVo.getDeepFileCheck(), deviceId, taskId,
                    CheckDeepContent.class, taskMapConfig.getDeepFileCheckMap());
            if (CollectionUtil.isEmpty(checkDeepContents)) {
                return;
            }
            checkDeepContents.forEach(item -> {
                if (StringUtils.isNotBlank(item.getSmDesc())) {
                    String str = Sm4Util.encryptBase64Str(item.getSmDesc());
                    item.setSmDesc(str);
                }
            });
            checkDeepContentService.saveBatch(checkDeepContents);
        } catch (Exception e) {
            log.error("深度文件内容 saveBatch error", e);
            checkDeepContents.forEach(item -> {
                try {
                    checkDeepContentService.save(item);
                } catch (Exception e1) {
                    log.error("深度文件内容 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 用户组
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleUserGroup(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckUserGroup> checkUserGroups = null;
        try {
            checkUserGroups = xmlListToDb(checkReportVo.getUserGroup(), deviceId, taskId,
                    CheckUserGroup.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkUserGroups)) {
                return;
            }
            checkUserGroupService.saveBatch(checkUserGroups);
        } catch (Exception e) {
            log.error("用户组 saveBatch error", e);
            checkUserGroups.forEach(item -> {
                try {
                    checkUserGroupService.save(item);
                } catch (Exception e1) {
                    log.error("用户组 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 屏幕保护
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleScreenSaver(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckScreenSaver> checkScreenSavers = null;
        try {
            checkScreenSavers = xmlListToDb(checkReportVo.getScreenSaver(), deviceId, taskId,
                    CheckScreenSaver.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkScreenSavers)) {
                return;
            }
            checkScreenSaverService.saveBatch(checkScreenSavers);
        } catch (Exception e) {
            log.error("屏幕保护 saveBatch error", e);
            checkScreenSavers.forEach(item -> {
                try {
                    checkScreenSaverService.save(item);
                } catch (Exception e1) {
                    log.error("屏幕保护 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 日志设置
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleLogSetting(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckLogSetting> checkLogSettings = null;
        try {
            checkLogSettings = xmlListToDb(checkReportVo.getLogSetting(), deviceId, taskId,
                    CheckLogSetting.class, taskMapConfig.getLogSettingMap());
            if (CollectionUtil.isEmpty(checkLogSettings)) {
                return;
            }
            checkLogSettingService.saveBatch(checkLogSettings);
        } catch (Exception e) {
            log.error("日志设置 saveBatch error", e);
            checkLogSettings.forEach(item -> {
                try {
                    checkLogSettingService.save(item);
                } catch (Exception e1) {
                    log.error("日志设置 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 共享信息
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleShareInfo(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckShareInfo> checkShareInfos = null;
        try {
            checkShareInfos = xmlListToDb(checkReportVo.getShareInfo(), deviceId, taskId,
                    CheckShareInfo.class, taskMapConfig.getShareMap());
            if (CollectionUtil.isEmpty(checkShareInfos)) {
                return;
            }
            checkShareInfoService.saveBatch(checkShareInfos);
        } catch (Exception e) {
            log.error("共享信息 saveBatch error", e);
            checkShareInfos.forEach(item -> {
                try {
                    checkShareInfoService.save(item);
                } catch (Exception e1) {
                    log.error("共享信息 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 安全策略
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleSafePolicy(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckSecurePolicy> checkSecurePolicies = null;
        try {
            checkSecurePolicies = xmlListToDb(checkReportVo.getSafePolicy(), deviceId, taskId,
                    CheckSecurePolicy.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkSecurePolicies)) {
                return;
            }
            checkSecurePolicyService.saveBatch(checkSecurePolicies);
        } catch (Exception e) {
            log.error("安全策略 saveBatch error", e);
            checkSecurePolicies.forEach(item -> {
                try {
                    checkSecurePolicyService.save(item);
                } catch (Exception e1) {
                    log.error("安全策略 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 开关机日志
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handlePowerOnLog(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckOnOff> checkOnOffs = null;
        try {
            checkOnOffs = xmlListToDb(checkReportVo.getPowerOnLog(), deviceId, taskId,
                    CheckOnOff.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkOnOffs)) {
                return;
            }
            checkOnOffService.saveBatch(checkOnOffs);
        } catch (Exception e) {
            log.error("开关机日志 saveBatch error", e);
            checkOnOffs.forEach(item -> {
                try {
                    checkOnOffService.save(item);
                } catch (Exception e1) {
                    log.error("开关机日志 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * shell日志
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleShellLog(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckShellLog> checkShellLogs = null;
        try {
            checkShellLogs = xmlListToDb(checkReportVo.getShellLog(), deviceId, taskId,
                    CheckShellLog.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkShellLogs)) {
                return;
            }
            checkShellLogService.saveBatch(checkShellLogs);
        } catch (Exception e) {
            log.error("shell日志 saveBatch error", e);
            checkShellLogs.forEach(item -> {
                try {
                    checkShellLogService.save(item);
                } catch (Exception e1) {
                    log.error("shell日志 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 用户登录日志
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleUserLoginLog(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckLoginLog> checkLoginLogs = null;
        try {
            checkLoginLogs = xmlListToDb(checkReportVo.getUserLoginLog(), deviceId, taskId,
                    CheckLoginLog.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkLoginLogs)) {
                return;
            }
            checkLoginLogService.saveBatch(checkLoginLogs);
        } catch (Exception e) {
            log.error("用户登录日志 saveBatch error", e);
            checkLoginLogs.forEach(item -> {
                try {
                    checkLoginLogService.save(item);
                } catch (Exception e1) {
                    log.error("用户登录日志 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 系统驱动日志
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleSysDriverLog(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckDriveLog> checkDriveLogs = null;
        try {
            checkDriveLogs = xmlListToDb(checkReportVo.getSysDriverLog(), deviceId, taskId,
                    CheckDriveLog.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkDriveLogs)) {
                return;
            }
            checkDriveLogService.saveBatch(checkDriveLogs);
        } catch (Exception e) {
            log.error("系统驱动日志 saveBatch error", e);
            checkDriveLogs.forEach(item -> {
                try {
                    checkDriveLogService.save(item);
                } catch (Exception e1) {
                    log.error("系统驱动日志 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 系统安全日志
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleSysSafeLog(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckSecureLog> checkSecureLogs = null;
        try {
            checkSecureLogs = xmlListToDb(checkReportVo.getSysSafeLog(), deviceId, taskId,
                    CheckSecureLog.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkSecureLogs)) {
                return;
            }
            checkSecureLogService.saveBatch(checkSecureLogs);
        } catch (Exception e) {
            log.error("系统安全日志 saveBatch error", e);
            checkSecureLogs.forEach(item -> {
                try {
                    checkSecureLogService.save(item);
                } catch (Exception e1) {
                    log.error("系统安全日志 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 进程信息
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleProcessInfo(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckProcessInfo> checkProcessInfos = null;
        try {
            checkProcessInfos = xmlListToDb(checkReportVo.getProcessInfo(), deviceId, taskId,
                    CheckProcessInfo.class, taskMapConfig.getProcessMap());
            if (CollectionUtil.isEmpty(checkProcessInfos)) {
                return;
            }
            checkProcessInfoService.saveBatch(checkProcessInfos);
        } catch (Exception e) {
            log.error("进程信息 saveBatch error", e);
            checkProcessInfos.forEach(item -> {
                try {
                    checkProcessInfoService.save(item);
                } catch (Exception e1) {
                    log.error("进程信息 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 硬盘更换情况
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleDiskChangeInfo(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckDiskReplace> checkDiskReplaces = null;
        try {
            checkDiskReplaces = xmlListToDb(checkReportVo.getDiskChangeInfo(), deviceId, taskId,
                    CheckDiskReplace.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkDiskReplaces)) {
                return;
            }
            checkDiskReplaceService.saveBatch(checkDiskReplaces);
        } catch (Exception e) {
            log.error("硬盘更换情况 saveBatch error", e);
            checkDiskReplaces.forEach(item -> {
                try {
                    checkDiskReplaceService.save(item);
                } catch (Exception e1) {
                    log.error("硬盘更换情况 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 文件操作记录
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleFileOperateRecord(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckFileAccessRecord> checkFileAccessRecords = null;
        try {
            checkFileAccessRecords = xmlListToDb(checkReportVo.getFileOperateRecord(), deviceId, taskId,
                    CheckFileAccessRecord.class, new HashMap<>());
            if (CollectionUtil.isEmpty(checkFileAccessRecords)) {
                return;
            }
            checkFileAccessRecordService.saveBatch(checkFileAccessRecords);
        } catch (Exception e) {
            log.error("文件操作记录 saveBatch error", e);
            checkFileAccessRecords.forEach(item -> {
                try {
                    checkFileAccessRecordService.save(item);
                } catch (Exception e1) {
                    log.error("文件操作记录 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 端口情况
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handlePorts(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckOpenPort> checkOpenPorts = null;
        try {
            checkOpenPorts = xmlListToDb(checkReportVo.getPorts(), deviceId, taskId,
                    CheckOpenPort.class, taskMapConfig.getPortMap());
            if (CollectionUtil.isEmpty(checkOpenPorts)) {
                return;
            }
            checkOpenPortService.saveBatch(checkOpenPorts);
        } catch (Exception e) {
            log.error("端口情况 saveBatch error", e);
            checkOpenPorts.forEach(item -> {
                try {
                    checkOpenPortService.save(item);
                } catch (Exception e1) {
                    log.error("端口情况 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 服务情况
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleServices(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckServiceInfo> checkServiceInfos = null;
        try {
            checkServiceInfos = xmlListToDb(checkReportVo.getServices(), deviceId, taskId,
                    CheckServiceInfo.class, taskMapConfig.getServiceMap());
            if (CollectionUtil.isEmpty(checkServiceInfos)) {
                return;
            }
            checkServiceInfoService.saveBatch(checkServiceInfos);
        } catch (Exception e) {
            log.error("服务情况 saveBatch error", e);
            checkServiceInfos.forEach(item -> {
                try {
                    checkServiceInfoService.save(item);
                } catch (Exception e1) {
                    log.error("服务情况 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 文件检查
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleFastFileCheck(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckFileContent> checkFileContents = null;
        List<CheckFileContent> distinctList = new ArrayList<>();
        try {
            checkFileContents = xmlListToDb(checkReportVo.getFastFileCheck(), deviceId, taskId,
                    CheckFileContent.class, taskMapConfig.getFileMap());
            if (CollectionUtil.isEmpty(checkFileContents)) {
                return;
            }
            checkFileContents.forEach(item -> {
                if (StringUtils.isNotBlank(item.getSmDesc())) {
                    String str = Sm4Util.encryptBase64Str(item.getSmDesc());
                    item.setSmDesc(str);
                }
            });
            // 2024-05-14 因为终端上报的有重复数据，产品说服务端做下去重
            List<String> flagStrList = new ArrayList<>();
            checkFileContents.forEach(item -> {
                String str = this.handerCheckFileContentFlag(item.getTaskId(), item.getDeviceId(), item.getSmDesc(),
                        item.getUserName(), item.getFilePath(), item.getHitKeyword(), item.getChecksum());
                if (!flagStrList.contains(str)) {
                    distinctList.add(item);
                    flagStrList.add(str);
                }
            });
            checkFileContentService.saveBatch(distinctList);
        } catch (Exception e) {
            log.error("文件检查 saveBatch error", e);
            distinctList.forEach(item -> {
                try {
                    checkFileContentService.save(item);
                } catch (Exception e1) {
                    log.error("文件检查 saveSingle error", e1);
                }
            });
        }
    }

    private String handerCheckFileContentFlag(String... args) {
        StringBuffer sb = new StringBuffer();
        if (args != null && args.length > 0) {
            for (String arg : args) {
                if (StringUtils.isNotBlank(arg)) {
                    sb.append("_");
                    sb.append(arg);
                }
            }
        }
        return sb.toString();
    }

    /**
     * usb信息
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleFastUSBRecord(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckUsbMark> checkUsbMarks = null;
        try {
            checkUsbMarks = xmlListToDb(checkReportVo.getFastUSBRecord(), deviceId, taskId,
                    CheckUsbMark.class, taskMapConfig.getUsbMap());
            if (CollectionUtil.isEmpty(checkUsbMarks)) {
                return;
            }
            checkUsbMarkService.saveBatch(checkUsbMarks);
        } catch (Exception e) {
            log.error("usb信息 saveBatch error", e);
            checkUsbMarks.forEach(item -> {
                try {
                    checkUsbMarkService.save(item);
                } catch (Exception e1) {
                    log.error("usb信息 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 硬盘信息
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleHardDiskInfo(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckDiskInfo> checkDiskInfos = null;
        try {
            checkDiskInfos = xmlListToDb(checkReportVo.getHardDiskInfo(), deviceId, taskId,
                    CheckDiskInfo.class, taskMapConfig.getDiskMap());
            if (CollectionUtil.isEmpty(checkDiskInfos)) {
                return;
            }
            checkDiskInfoService.saveBatch(checkDiskInfos);
        } catch (Exception e) {
            log.error("硬盘信息 saveBatch error", e);
            checkDiskInfos.forEach(item -> {
                try {
                    checkDiskInfoService.save(item);
                } catch (Exception e1) {
                    log.error("硬盘信息 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 网络信息
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleNetworkInfo(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckNetInfo> checkNetInfos = null;
        try {
            checkNetInfos = xmlListToDb(checkReportVo.getNetworkInfo(), deviceId, taskId,
                    CheckNetInfo.class, taskMapConfig.getNetMap());
            if (CollectionUtil.isEmpty(checkNetInfos)) {
                return;
            }
            checkNetInfoService.saveBatch(checkNetInfos);
        } catch (Exception e) {
            log.error("网络信息 saveBatch error", e);
            checkNetInfos.forEach(item -> {
                try {
                    checkNetInfoService.save(item);
                } catch (Exception e1) {
                    log.error("网络信息 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 账户信息
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleAccountInfo(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckUserInfo> checkUserInfos = null;
        try {
            checkUserInfos = xmlListToDb(checkReportVo.getAccountInfo(), deviceId, taskId,
                    CheckUserInfo.class, taskMapConfig.getAccountMap());
            if (CollectionUtil.isEmpty(checkUserInfos)) {
                return;
            }
            checkUserInfoService.saveBatch(checkUserInfos);
        } catch (Exception e) {
            log.error("账户信息 saveBatch error", e);
            checkUserInfos.forEach(item -> {
                try {
                    checkUserInfoService.save(item);
                } catch (Exception e1) {
                    log.error("账户信息 saveSingle error", e1);
                }
            });
        }
    }

    /**
     * 基本检查 --- 操作系统信息
     * @param checkReportVo
     * @param deviceId
     * @param taskId
     * @param taskMapConfig
     */
    private void handleHostInfo(CheckReportVo checkReportVo, String deviceId, String taskId, TaskMapConfig taskMapConfig) {
        List<CheckOsInfo> checkOsInfos = null;
        try {
            checkOsInfos = xmlListToDb(checkReportVo.getHostInfo(), deviceId, taskId,
                    CheckOsInfo.class, taskMapConfig.getOsMap());
            if (CollectionUtil.isEmpty(checkOsInfos)) {
                return;
            }
            checkOsInfoService.saveBatch(checkOsInfos);
        } catch (Exception e) {
            log.error("操作系统信息 --- saveBatch error", e);
            checkOsInfos.forEach(item -> {
                try {
                    checkOsInfoService.save(item);
                } catch (Exception e1) {
                    log.error("操作系统信息 --- saveSingle error", e1);
                }
            });
        }
    }


    public <T> List<T> xmlListToDb(RecordsVo recordsVo, String deviceId, String taskId, Class<T> clazz, Map<String, String> configMap) {
        List<T> dataList = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        if (recordsVo == null) {
            return dataList;
        }
        final List<RecordVo> list = recordsVo.getList();
        if (CollUtil.isEmpty(list)) {
            return dataList;
        }
        final String deduction = recordsVo.getDeduction();
        if (StrUtil.isNotEmpty(deduction)) {
            final String simpleName = clazz.getSimpleName();
            AgentCheckDeduction checkDeduction = new AgentCheckDeduction();
            checkDeduction.setDeviceId(deviceId);
            checkDeduction.setTaskId(taskId);
            checkDeduction.setCheckItem(simpleName);
            checkDeduction.setDeduction(deduction);
            checkDeduction.setTime(LocalDateTime.now());
            checkDeductionService.save(checkDeduction);
        }
        // 验证字段是否缺失
        this.validateFields(clazz, recordsVo.getList().get(0), configMap);
        for (RecordVo recordVo : list) {
            final Object o = ReflectUtil.newInstanceIfPossible(clazz);
            ReflectUtil.setFieldValue(o, "deviceId", deviceId);
            ReflectUtil.setFieldValue(o, "taskId", taskId);
            for (FieldVo fieldVo : recordVo.getFieldVoList()) {
                //log.info("field :" + fieldVo.getName());
                String fieldName;
                if (fieldVo.getName().equals("Deduction")) {
                    fieldName = "violationDesc";
                } else {
                    fieldName = configMap.getOrDefault(fieldVo.getName(), StrUtil.lowerFirst(fieldVo.getName()));
                }
                String value = fieldVo.getValue();
                if (StrUtil.isEmpty(value)) {
                    value = null;
                }
                // 补齐时间,深度usb，终端上报上来时间为 04-16 20:03:38
                if (clazz == CheckDeepUsb.class
                        && StringUtils.isNotEmpty(value)
                        && (StringUtils.equals("firstTime", fieldName) || StringUtils.equals("lastTime", fieldName))) {
                    // 根据正则验证时间格式，判断是否存在年
                    if (ReUtil.isMatch("\\d{1,2}-\\d{1,2}(\\s\\d{1,2}:\\d{1,2}(:\\d{1,2})?)?(.\\d{1,3})?", value)) {
                        int year = DateUtil.year(new Date());
                        value = year + "-" + value;
                    }
                }
                try {
                    ReflectUtil.setFieldValue(o, fieldName, value);
                } catch (Exception e) {
                    //log.error(e.getMessage(), e);
                }
            }
            final T t = objectMapper.convertValue(o, clazz);
            dataList.add(t);
        }
        return dataList;
    }

    /**
     * 验证字段是否缺失
     * @param clazz
     * @param recordVo
     * @param configMap
     * @param <T>
     */
    private <T> void validateFields(Class<T> clazz, RecordVo recordVo, Map<String, String> configMap) {
        // 通过反射获取类的字段数组
        Field[] fields = ReflectUtil.getFields(clazz);
        if (fields != null && fields.length > 0) {
            List<String> fieldList = new ArrayList<>(fields.length);
            List<String> xmlFieldList = new ArrayList<>(fields.length);
            for (Field field : fields) {
                if (StringUtils.equals("serialVersionUID", field.getName())
                        || StringUtils.equals("id", field.getName())
                        || StringUtils.equals("taskId", field.getName())
                        || StringUtils.equals("deviceId", field.getName())
                        || StringUtils.equals("createTime", field.getName())
                        || StringUtils.equals("updateTime", field.getName())
                        || StringUtils.equals("dataTime", field.getName())
                        || StringUtils.equals("description", field.getName())
                        || StringUtils.equals("violationDesc", field.getName())) {
                    continue;
                }
                fieldList.add(field.getName());
            }
            for (FieldVo fieldVo : recordVo.getFieldVoList()) {
                String fieldName;
                if (fieldVo.getName().equals("Deduction")) {
                    fieldName = "violationDesc";
                } else {
                    fieldName = configMap.getOrDefault(fieldVo.getName(), StrUtil.lowerFirst(fieldVo.getName()));
                }
                xmlFieldList.add(fieldName);
            }
            if (!xmlFieldList.containsAll(fieldList)) {
                log.info("xmlFieldList: {}； fieldList: {}", JSONUtil.toJsonStr(xmlFieldList), JSONUtil.toJsonStr(fieldList));
                log.warn("xmlFieldList: {}； fieldList: {}", JSONUtil.toJsonStr(xmlFieldList), JSONUtil.toJsonStr(fieldList));
                LogVo logVo = new LogVo();
                logVo.setUuid(IdUtil.fastUUID());
                logVo.setRole("1034");
                logVo.setUsername("sysadmin");
                logVo.setOperateModule("数据格式校验");
                logVo.setOperateType("终端检查任务结果上报");
                logVo.setDescription("终端检查任务结果上报 - 数据格式校验错误");
                logVo.setBehaviourType("一般行为");
                logVo.setCreateTime(DateUtil.now());
                logVo.setResult(2);
                logVo.setHostIp(NetUtil.localIpv4s().stream().findFirst().orElse(""));
                try {
                    final ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(otherTopic.getMsgSysLog(),
                            JsonUtil.toJson(logVo));
                    // 同步发送
                    future.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 不得存在安装多操作系统
     * @param taskId 任务id
     * @param deviceId 设备id
     * @param checkItemResultList  任务结果项列表
     * @param checkItemResultMap 已存在任务结果项map
     * @param itemConfig 任务项配置
     * @param osInfo 操作系统信息
     */
    private void osMore(String taskId, String deviceId, List<CheckItemResult> checkItemResultList,
            Map<String, CheckItemResult> checkItemResultMap, Map<String, CheckItem> itemConfig, CheckOsInfo osInfo) {
        final CheckItem checkItem =
                itemConfig.getOrDefault(ViolationConstants.CHECK_TYPE_HOST
                        + "-" + ViolationConstants.CHECK_TYPE_HOST_OS_MORE, new CheckItem());
        if (checkItem.isNeedCheck()) {
            final Byte isMultiOs = osInfo.getIsMultiOs();
            if (isMultiOs == 1) {
                String key = taskId + "-" + deviceId + "-" + ViolationConstants.CHECK_TYPE_HOST + "-" + ViolationConstants.CHECK_TYPE_HOST_OS_MORE;
                if (!checkItemResultMap.containsKey(key)) {
                    CheckItemResult itemResult = new CheckItemResult();
                    itemResult.setTaskId(taskId);
                    itemResult.setDeviceId(deviceId);
                    itemResult.setCheckType(ViolationConstants.CHECK_TYPE_HOST);
                    itemResult.setCheckItem(ViolationConstants.CHECK_TYPE_HOST_OS_MORE);
                    itemResult.setCheckResult(ViolationConstants.CHECK_TYPE_HOST_OS_MORE_DESC);
                    itemResult.setUpdateTime(new Date());
                    checkItemResultList.add(itemResult);
                }
            }
        }
    }


    /**
     * @param taskId 任务id
     * @param deviceId 设备id
     * @param checkItemResultList  任务结果项列表
     * @param checkItemResultMap 已存在任务结果项map
     * @param itemConfig 任务项配置
     */
    private void commonViolation(String taskId, String deviceId, List<CheckItemResult> checkItemResultList,
            Map<String, CheckItemResult> checkItemResultMap, Map<String, CheckItem> itemConfig,
            String type, String item, String checkResult) {
        final CheckItem checkItem =
                itemConfig.getOrDefault(type
                        + "-" + item, new CheckItem());
        if (checkItem.isNeedCheck()) {
            String key = taskId + "-" + deviceId + "-" + type + "-" + item;
            CheckItemResult itemResult = new CheckItemResult();
            itemResult.setTaskId(taskId);
            itemResult.setDeviceId(deviceId);
            itemResult.setCheckType(type);
            itemResult.setCheckItem(item);
            itemResult.setCheckResult(checkResult);
            itemResult.setUpdateTime(new Date());
            checkItemResultList.add(itemResult);
        }
    }

    /**
     * 更新操作系统信息
     * @param osInfo 操作系统
     */
    private void updateOsInfo(CheckOsInfo osInfo) {
        LambdaUpdateWrapper<CheckOsInfo> uw = new LambdaUpdateWrapper<>();
        uw.eq(CheckOsInfo::getTaskId, osInfo.getTaskId());
        uw.eq(CheckOsInfo::getDeviceId, osInfo.getDeviceId());

        checkOsInfoService.update(osInfo, uw);
    }


    /**
     * 更新任务进度
     * @param taskId 任务id
     * @param deviceId 设备id
     * @param progress 进度
     * @param checksum
     */
    private void updateProgress(String taskId, String deviceId, Integer progress, String checksum) {

        CheckTaskProgress checkTaskProgress = new CheckTaskProgress();
        checkTaskProgress.setTaskId(taskId);
        checkTaskProgress.setDeviceId(deviceId);
        LambdaUpdateWrapper<CheckTaskProgress> uw = new LambdaUpdateWrapper<>();
        uw.eq(CheckTaskProgress::getTaskId, taskId);
        uw.eq(CheckTaskProgress::getDeviceId, deviceId);
        uw.set(CheckTaskProgress::getProgress, progress);
        uw.set(CheckTaskProgress::getChecksum, checksum);
        uw.set(CheckTaskProgress::getTime, DateUtil.now());
        if (progress == 100) {
            uw.set(CheckTaskProgress::getStatus, 4);
        }
        checkTaskProgressService.update(checkTaskProgress, uw);
    }

    @Override
    public void updateTaskProgress(String taskId, String deviceId, String checksum, Integer code) {
        CheckTaskProgress checkTaskProgress = new CheckTaskProgress();
        checkTaskProgress.setTaskId(taskId);
        checkTaskProgress.setDeviceId(deviceId);
        LambdaUpdateWrapper<CheckTaskProgress> uw = new LambdaUpdateWrapper<>();
        uw.eq(CheckTaskProgress::getTaskId, taskId);
        uw.eq(CheckTaskProgress::getDeviceId, deviceId);
        if (code.equals(TaskStatusEnum.COMPLETED.getCode())) {
            uw.set(CheckTaskProgress::getProgress, 100);
        } else {
            uw.set(CheckTaskProgress::getProgress, 0);
        }
        uw.set(CheckTaskProgress::getStatus, code);
        uw.set(CheckTaskProgress::getChecksum, checksum);
        uw.set(CheckTaskProgress::getTime, DateUtil.now());
        checkTaskProgressService.update(checkTaskProgress, uw);
    }
}
