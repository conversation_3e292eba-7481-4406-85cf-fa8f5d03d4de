package com.superred.supervisor.common.utils;

import cn.hutool.crypto.digest.SM3;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 国密sm3摘要算法
 *
 * <AUTHOR>
 * @since 2025/7/8 10:00
 */
@Slf4j
public final class SM3Utils {
    private SM3Utils() {
    }

    private static final Lock LOCK = new ReentrantLock();
    /**
     * 实例化SM3摘要算法对象, 次对象不是线程安全的
     */
    private static final SM3 SM3_INSTANCE = new SM3();

    /**
     * 计算SM3摘要
     *
     * @param data 待计算的字节数组
     * @return SM3摘要的十六进制字符串
     */
    public static String sm3Digest(String data) {
        LOCK.lock();
        try {
            return SM3_INSTANCE.digestHex(data);
        } catch (Exception e) {
            log.error("SM3摘要计算失败: {}", e.getMessage(), e);
            throw new RuntimeException("SM3摘要计算失败", e);
        } finally {
            LOCK.unlock();
        }
    }

}
