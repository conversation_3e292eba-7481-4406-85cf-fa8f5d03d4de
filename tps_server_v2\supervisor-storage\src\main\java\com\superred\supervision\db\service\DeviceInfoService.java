package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.base.vo.device.BusinessStatusVo;
import com.superred.supervision.db.entity.DeviceInfo;

/**
 * <p>
 * 检测器设备信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
public interface DeviceInfoService extends IService<DeviceInfo> {
    /**
     * 保存业务状态
     * @param deviceId 设备id
     * @param businessStatusVo 业务状态
     */
    void saveBusiness(String deviceId, BusinessStatusVo businessStatusVo);
}
