package com.superred.supervision.base.constant;

/**
 * AgentItem.
 * 终端检查枚举.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-24 16:09
 **/
public enum AgentItemEnu {

    WIFI(0, "无线网卡"),
    BLUETOOTH(1, "蓝牙设备"),
    PHONE(2, "手机设备"),
    NETTOOL(3, "网络工具"),
    INTELRECORD(4, "上网记录"),
    REGINTELRECORD(5, "注册表上网记录"),
    FILEOPTRECORD(6, "文件操作记录"),
    SYSDBFILERECORD(7, "系统数据库文件记录"),
    MOVEMEDIA(8, "移动介质"),
    PCINFO(9, "计算机基本信息检查"),
    SYSPROCESS(10, "系统进程"),
    SYSERVICE(11, "系统服务"),
    SECPROSOFTWARE(12, "安全防护软件"),
    OPENPORT(13, "开放端口"),
    SECPOLICY(14, "安全策略"),
    ANTIFORENSIC(15, "反取证"),
    OS(16, "操作系统"),
    SOFTINSTALL(17, "软件安装"),
    SYSLOG(18, "系统日志"),
    IDENTIFICATION(19, "身份鉴别"),
    INTELDEEPCHECK(20, "上网深度检查"),
    FILEDEEPOPTCHK(21, "文件操作深度"),
    MOVEMEDIADEEP(22, "移动介质深度"),
    LOGANSDEEP(23, "日志分析深度"),
    FILEINFOCHK(24, "文件信息检查"),
    FILEINFODEEPCHK(25, "文件信息深度"),
    CAMERA(26, "摄像头"),
    MICROPHONE(27, "麦克风"),
    SCREENSAVER(28, "屏幕保护"),
    DIALINFO(29, "拨号信息"),
    NETCON(30, "网络联接情况"),
    EMAILREC(31, "收发邮件记录"),
    NETWORKINFO(32, "网卡信息"),
    HARDWAREINFO(33, "硬件信息"),
    DISKINFO(34, "硬盘信息"),
    HARDDISKPAR(35, "硬盘分区"),
    SYSTEMSHARING(36, "系统共享"),
    SYSTEMDRIVER(37, "系统驱动"),
    SYSTEMPATCH(38, "系统补丁"),
    VIRTUALSOFT(39, "虚拟机软件"),
    THREEIN1(40, "三合一"),
    PRINTER(41, "打印机"),
    DEVICECHECK(42, "设备检查"),
    USERINFO(43, "用户信息"),
    CURRENTACCOUNT(44, "当前帐户信息"),
    NETINFO(45, "网络信息"),
    ACCOUNTPOLICY(46, "帐户策略"),
    AUDITPOLICY(47, "审核策略"),
    ACCOUNTPERCONF(48, "帐户权限配置"),
    STARTUPITEM(49, "开机启动项"),
    SWITCHLOG(50, "开关机日志"),
    TIMEDTASK(51, "定时任务"),
    SECSOFTWARE(52, "安全保密软件"),
    WIFICON(53, "网络联接情况(WIFI)"),
    SINGLEFILEOPTRECORD(54, "单导文件记录检查"),
    HOSTAUDIT(55, "主机审计"),
    PRINTANDBURN(56, "打印刻录"),
    SECURELOGIN(57, "安全登录"),
    SECURITYMARK(58, "密级标识");

    AgentItemEnu(int key, String value) {
        this.key = key;
        this.value = value;
    }

    private int key;

    private String value;

    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
