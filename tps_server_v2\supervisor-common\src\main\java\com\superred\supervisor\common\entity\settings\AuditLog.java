package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 系统审计日志(SysAuditLog) 实体
 *
 * <AUTHOR>
 * @since 2025-03-28 11:03:24
 */
@Data
@TableName("sys_audit_log")
public class AuditLog {


    /**
     * 自增ID
     */
    @TableId(value = "auto_incre_id", type = IdType.AUTO)
    private Integer autoIncreId;

    /**
     * 日志ID，不得使用除字母、数字、下划线"_"以外的其他字符，并且保证ID的唯一性不得重复。最长20个字节。
     */
    @TableField("id")
    private String id;

    /**
     * 设备编号
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 操作用户
     */
    @TableField("user")
    private String user;

    /**
     * 事件时间
     */
    @TableField("time")
    private Date time;

    /**
     * 日志类型。
     远程系统命令，远程策略更新，本地用户操作，本地系统配置，本地策略配置，其他日志类型
     */
    @TableField("event_type")
    private String eventType;

    /**
     * 操作类型。
     关机、重启，启动模块、停止模块，更新内置策略，升级固件；添加策略、删除策略、重置策略；用户登陆、用户注销、关机、重启、恢复出厂设置，添加用户、删除用户，导出数据，离线升级；网络配置、路由配置、管理机IP配置、管理系统IP配置、监测器部署信息配置；添加关键词、删除关键词，其他操作类型
     */
    @TableField("opt_type")
    private String optType;

    /**
     * 日志详情
     */
    @TableField("message")
    private String message;

    /**
     * 是否上报到管理中心，0：否，1：是 
     */
    @TableField("is_report")
    private Integer isReport;

    /**
     * 上报到管理中心时间
     */
    @TableField("report_time")
    private Date reportTime;

    /**
     * 日志来源
     */
    @TableField("source")
    private String source;

}

