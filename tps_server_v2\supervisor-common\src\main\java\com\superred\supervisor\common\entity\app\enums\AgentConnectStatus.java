package com.superred.supervisor.common.entity.app.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 在线状态， 1 在线 2 离线 3 已卸载
 *
 * <AUTHOR>
 * @since 2025/6/30 10:57
 */
@Getter
@AllArgsConstructor
public enum AgentConnectStatus {

    ONLINE(1, "在线"),
    OFFLINE(2, "离线"),
    UNINSTALLED(3, "已卸载");

    private final Integer code;
    private final String desc;

    public static AgentConnectStatus of(Integer code) {
        for (AgentConnectStatus status : AgentConnectStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
