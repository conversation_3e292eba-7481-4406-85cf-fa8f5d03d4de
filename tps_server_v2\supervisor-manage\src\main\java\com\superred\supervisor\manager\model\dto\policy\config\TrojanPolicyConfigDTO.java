package com.superred.supervisor.manager.model.dto.policy.config;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleAttackTrojan;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-03-27 20:05
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TrojanPolicyConfigDTO {

    /**
     * 策略ID
     */
    private Long ruleId;

    /**
     * 命中策略后是否进行流的
     * 报文留存 1（on）、2（off）。
     */
    private Integer storePcap;

    /**
     * 策略名称
     */
    private String ruleName;


    /**
     * 攻击分类 1 窃密木马 2 远控木马 3 电脑病毒 4 僵尸网络 5 网络蠕虫 6 间谍软件 7 挖矿木马 8 黑客工具 11 后门程序 99 其它
     */
    private Integer attackClass;

    /**
     * 攻击组织
     */
    private String attackGroup;

    /**
     * 策略内容
     */
    private String rule;

    /**
     * 攻击窃密告警描述。简要描述木马性质、木马进行操作（取文件、控制…）或适用系统（windows linux unix…）
     */
    private String desc;

    /**
     * 告警级别;告警级别型，取值为：0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）
     */
    private Integer risk;

    public static TrojanPolicyConfigDTO getPolicyConfig(RuleAttackTrojan trojan) {
        if (trojan == null) {
            return null;
        }
        return TrojanPolicyConfigDTO.builder()
                .ruleId(Long.parseLong(trojan.getRuleId()))
                .storePcap(PolicyUtils.strToInt(trojan.getStorePcap()))
                .ruleName(PolicyUtils.handleStrNull(trojan.getRuleName()))
                .attackClass(PolicyUtils.strToInt(trojan.getAttackClass()))
                .attackGroup(PolicyUtils.handleStrNull(trojan.getAttackGroup()))
                .rule(PolicyUtils.handleStrNull(trojan.getRule()))
                .desc(PolicyUtils.handleStrNull(trojan.getDesc()))
                .risk(PolicyUtils.strToInt(trojan.getRisk()))
                .build();
    }
}
