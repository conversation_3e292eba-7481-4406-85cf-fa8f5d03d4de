package com.superred.supervisor.common.model.dto.cmd;

import com.superred.supervisor.common.constant.cmd.CmdResultType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 响应结果异步统一实体
 *
 * <AUTHOR>
 * @since 2025/5/28 16:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmdResultDTO<Req> {

    private String deviceId;

    private CmdResultType cmdResultType;


    private Req requestBody;
}
