package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 下发指令VO
 *
 * <AUTHOR>
 * @since 2025/03/11
 **/
@Data
public class IssueCommandVO {


    /**
     *
     */
    @Schema(description = "ID")
    private Long id;

    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */
    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    private String deviceId;

    /**
     * 任务编号
     */
    @Schema(description = "任务编号")
    private String cmdId;

    /**
     * 命令类型，策略：policy，指令：command
     */
    @Schema(description = "命令类型，策略：policy，指令：command")
    private String type;

    /**
     * 模块名
     */
    @Schema(description = "模块名")
    private String module;

    /**
     * 策略版本
     */
    @Schema(description = "策略版本")
    private String version;

    /**
     * 启动或停止模块的子模块
     */
    @Schema(description = "启动或停止模块的子模块")
    private String submodule;

    /**
     * 命令
     */
    @Schema(description = "命令")
    private String cmd;

    /**
     * 下发的参数
     */
    @Schema(description = "下发的参数")
    private String param;

    /**
     * 规则级别：
     * 001国家级；
     * 010省级（省、直辖市、自治州等）、行业主管部门；
     * 011地市级（地市、直辖市区县）、行业下级部门；
     * 100区县级、行业三级部门；
     * 999其他预留
     */

    private String level;

    /**
     * 命令生成时间
     */
    @Schema(description = "命令生成时间")
    private LocalDateTime createTime;

    /**
     * 下发状态，0未下发，1已下发
     */
    @Schema(description = "下发状态，0未下发，1已下发")
    private Integer issuedStatus;

    /**
     * 上报时间
     */
    @Schema(description = "上报时间")
    private LocalDateTime reportTime;

    /**
     * 上报detail
     */
    @Schema(description = "上报detail")
    private String reportDetail;

    /**
     * 上报的消息
     */
    @Schema(description = "上报的消息")
    private String reportMessage;

    /**
     * 下发时间
     */
    @Schema(description = "下发时间")
    private LocalDateTime issuedTime;

    /**
     * 上报失败结果
     */
    @Schema(description = "上报失败结果")
    private String fail;

    /**
     * 上报成功结果
     */
    @Schema(description = "上报成功结果")
    private String success;

    /**
     * 执行结果
     */
    @Schema(description = "执行结果")
    private Integer result;

    /**
     * 预留字段
     */
    @Schema(description = "预留字段")
    private String ext1;

    /**
     * 预留字段
     */
    @Schema(description = "预留字段")
    private String ext2;

    /**
     * 预留字段
     */
    @Schema(description = "预留字段")
    private String ext3;

    /**
     * 预留字段
     */
    @Schema(description = "预留字段")
    private String ext4;

    /**
     * 预留字段
     */
    @Schema(description = "预留字段")
    private String ext5;

    /**
     * 生效范围
     */
    @Schema(description = "生效范围")
    private String effectZone;


    /**
     * 自监管： 00-自监管 01-监测器 02-终端保密
     */
    @Schema(description = "自监管： 00-自监管 01-监测器 02-终端保密")
    private String zjgType;

    /**
     * 来源设备
     */
    @Schema(description = "来源设备")
    private String srcDevice;


    /**
     * 设备类型
     */
    @Schema(description = "设备类型")
    private String deviceType;

    /**
     * 指令来源： 1 本级  2 上级
     */
    @Schema(description = "指令来源： 1 本级  2 上级")
    private String cmdSource;


    /**
     * 下级是否上报 1-未上报 2-已上报
     */
    @Schema(description = "下级是否上报 1-未上报 2-已上报")
    private Integer uploadFlag;


    public IssueCommandVO() {
    }

    public IssueCommandVO(String deviceId, String cmdId, String cmd, String type, String module, String submodule) {
        this.deviceId = deviceId;
        this.cmdId = cmdId;
        this.cmd = cmd;
        this.type = type;
        this.module = module;
        this.submodule = submodule;
    }


}
