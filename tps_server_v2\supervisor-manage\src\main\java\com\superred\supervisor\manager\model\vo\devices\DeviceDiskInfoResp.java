package com.superred.supervisor.manager.model.vo.devices;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DeviceDiskInfo
 * <p> 磁盘信息
 * @since 2022-08-12 10:07:33
 **/
@Data
public class DeviceDiskInfoResp {


    /**
     * 磁盘大小，单位GB
     */
    @Schema(description = "磁盘大小，单位GB")
    private Integer size;

    /**
     * 磁盘序列号，“ST1000NM0012”
     */
    @Schema(description = "磁盘序列号，“ST1000NM0012”")
    private String serial;


}
