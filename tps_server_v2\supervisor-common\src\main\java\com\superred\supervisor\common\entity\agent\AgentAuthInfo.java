package com.superred.supervisor.common.entity.agent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.superred.supervisor.common.entity.agent.enums.AuthStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端授权信息 实体
 *
 * <AUTHOR>
 * @since 2025-03-18 19:41:02
 */
@Data
@TableName("agent_device_authorization_info")
@Deprecated
public class AgentAuthInfo {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 终端id
     */
    @TableField("agent_device_id")
    private String agentDeviceId;

    /**
     * 绑定授权文件id
     */
    @TableField("authorization_file_id")
    private Long authorizationFileId;

    /**
     * 激活状态：0未激活，1激活
     */
    @TableField("activation_status")
    private AuthStatus activationStatus;

    /**
     * 激活时间
     */
    @TableField("change_time")
    private LocalDateTime changeTime;

}

