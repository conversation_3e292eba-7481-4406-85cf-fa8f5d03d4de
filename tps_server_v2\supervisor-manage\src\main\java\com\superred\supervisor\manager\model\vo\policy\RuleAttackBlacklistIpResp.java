package com.superred.supervisor.manager.model.vo.policy;

import com.superred.supervisor.common.entity.policy.RuleAttackBlacklistIp;
import com.superred.supervisor.manager.common.annotation.BlankOrPattern;
import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-03-12 16:32
 */
@Data

@Builder
public class RuleAttackBlacklistIpResp {

    private static final long serialVersionUID = 1L;

    @Schema(description = "策略ID")
    private String ruleId;

    @Schema(description = "源IP地址  IP子网类型。未标注子网掩码时表示单个IP地址。0.0.0.0/0或空时表示所有IP地址。")
    //@RuleIpValidator(message = "源IP格式错误")
    private String sip;

    @Schema(description = "源端口范围  端口范围类型，0或空时表示所有端口 例，443-65534")
    //@RulePortValidator(message  = "源端口范围格式错误")
    private String sport;

    @Schema(description = "目的IP地址 IP子网类型。 未标注子网掩码时表示单个IP地址。0.0.0.0/0或空时表示所有IP地址。")
    //@RuleIpValidator(message = "目的IP格式错误")
    private String dip;

    @Schema(description = "目的端口范围  端口范围类型，0或空时表示所有端口  例，1024-2033")
    //@RulePortValidator(message  = "目的端口范围格式错误")
    private String dport;

    @Schema(description = "通信协议  6表示TCP、17表示UDP、0表示无限制。")
    private String protocol;

    @Schema(description = "策略名称")
    @ByteSize(max = 128, message = "策略名称长度不能超过128个字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]+$", message = "策略名称格式错误,只允许输入中文、英文、数字")
    private String ruleName;

    @Schema(description = "攻击分类 1. 窃密木马 2. 远控木马 3. 电脑病毒 4. 僵尸网络 5. 网络蠕虫 6. 间谍软件 7. 挖矿木马 8. 黑客工具 9. 勒索软件 10. 恶意文档 11. 后门程序 99. 其它")
    private String attackClass;

    @Schema(description = "攻击组织")
    @ByteSize(max = 128, message = "攻击组织长度不能超过128个字节")
    private String attackGroup;

    @Schema(description = "攻击阶段枚举分类 1 侦查扫描 2 攻击渗透 3 样本投递 4 持续控制 5 横向移动 6 数据窃取 99 其它")
    private String attackStage;

    @Schema(description = "攻击设施类型ID 1 密码爆破 2 漏洞扫描 3 样本分发 4 恶意发邮 5 钓鱼网站 6 信息搜集 7 数据窃取 8 命令控制 99 其它")
    private String facilityType;

    @Schema(description = "描述信息")
    private String desc;

    @Schema(description = "告警级别，0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）。")
    private String risk;

    @Schema(description = "是否共享状态，0是，1否")
    private String isShare;

    @Schema(description = "策略应用状态，0未应用，1已应用")
    private String status;

    @Schema(description = "策略来源 1 本级 2上级")
    private String ruleSource;

    @Schema(description = "平台级别")
    private String level;

    @Schema(description = "策略更新时间")
    private String updateTime;

    @Schema(description = "策略创建时间")
    private String createTime;

    @Schema(description = "扩展字段1")
    private Long ext1;

    @Schema(description = "扩展字段2")
    private String ext2;

    @Schema(description = "扩展字段3")
    private String ext3;

    @Schema(description = "开始时间")
    private String startDate;

    @Schema(description = "结束时间")
    private String endDate;

    public static RuleAttackBlacklistIpResp fromRuleAttackBlacklistIp(RuleAttackBlacklistIp item) {
        return RuleAttackBlacklistIpResp.builder()
                .ruleId(item.getRuleId())
                .sip(item.getSip())
                .sport(item.getSport())
                .dip(item.getDip())
                .dport(item.getDport())
                .protocol(item.getProtocol())
                .ruleName(item.getRuleName())
                .attackClass(item.getAttackClass())
                .attackGroup(item.getAttackGroup())
                .attackStage(item.getAttackStage())
                .facilityType(item.getFacilityType())
                .desc(item.getDesc())
                .risk(item.getRisk())
                .status(item.getStatus())
                .isShare(item.getIsShare())
                .ruleSource(item.getRuleSource())
                .level(item.getLevel())
                .updateTime(item.getUpdateTime())
                .createTime(item.getCreateTime())
                .ext1(item.getExt1())
                .ext2(item.getExt2())
                .ext3(item.getExt3())
                .build();
    }
}
