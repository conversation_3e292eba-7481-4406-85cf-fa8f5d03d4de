package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleAuditAppBehavior;
import com.superred.supervisor.common.repository.policy.RuleAuditAppBehaviorRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.constant.RuleSourceTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.config.AuditAppBehaviorConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleAuditAppBehaviorPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAuditAppBehaviorReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAuditAppBehaviorResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleAuditAppBehaviorService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-04-07 10:43
 */
@Slf4j
@Service("ruleAuditAppBehaviorService")
@AllArgsConstructor
public class RuleAuditAppBehaviorServiceImpl implements RuleAuditAppBehaviorService, RuleService {

    @Resource
    private RuleAuditAppBehaviorRepository ruleAuditAppBehaviorRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    @Override
    public IPage<RuleAuditAppBehaviorResp> page(RuleAuditAppBehaviorPageReq ruleAuditAppBehaviorPageReq) {
        List<String> ruleIdList = this.getRuleIdList(ruleAuditAppBehaviorPageReq);
        LambdaQueryWrapper<RuleAuditAppBehavior> queryWrapper = new LambdaQueryWrapper<RuleAuditAppBehavior>()
                .le(StrUtil.isNotEmpty(ruleAuditAppBehaviorPageReq.getEndDate()), RuleAuditAppBehavior::getUpdateTime, ruleAuditAppBehaviorPageReq.getEndDate())
                .ge(StrUtil.isNotEmpty(ruleAuditAppBehaviorPageReq.getStartDate()), RuleAuditAppBehavior::getUpdateTime, ruleAuditAppBehaviorPageReq.getStartDate())
                .like(StrUtil.isNotEmpty(ruleAuditAppBehaviorPageReq.getRuleId()), RuleAuditAppBehavior::getRuleId, ruleAuditAppBehaviorPageReq.getRuleId())
                .eq(StrUtil.isNotEmpty(ruleAuditAppBehaviorPageReq.getIp()), RuleAuditAppBehavior::getIp, ruleAuditAppBehaviorPageReq.getIp())
                .eq(StrUtil.isNotEmpty(ruleAuditAppBehaviorPageReq.getStatus()), RuleAuditAppBehavior::getStatus, ruleAuditAppBehaviorPageReq.getStatus())
                .eq(StrUtil.isNotEmpty(ruleAuditAppBehaviorPageReq.getIsShare()), RuleAuditAppBehavior::getIsShare, ruleAuditAppBehaviorPageReq.getIsShare())
                .eq(StrUtil.isNotEmpty(ruleAuditAppBehaviorPageReq.getRuleSource()), RuleAuditAppBehavior::getRuleSource, ruleAuditAppBehaviorPageReq.getRuleSource())
                .eq(StrUtil.isNotEmpty(ruleAuditAppBehaviorPageReq.getAuditType()), RuleAuditAppBehavior::getAuditType, ruleAuditAppBehaviorPageReq.getAuditType())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleAuditAppBehavior::getRuleId, ruleIdList)
                .ne(RuleAuditAppBehavior::getRuleSource, RuleSourceTypeEnum.SUPER_POLICY_SHARE.getKey())
                .orderByDesc(RuleAuditAppBehavior::getUpdateTime);
        Page<RuleAuditAppBehavior> page = new Page<>(ruleAuditAppBehaviorPageReq.getStart(), ruleAuditAppBehaviorPageReq.getLimit());
        IPage<RuleAuditAppBehavior> page1 = this.ruleAuditAppBehaviorRepository.page(page, queryWrapper);
        return page1.convert(RuleAuditAppBehaviorResp::fromRuleAuditAppBehavior);
    }

    @Override
    public RuleAuditAppBehaviorResp getById(Long ruleId) {
        if (ruleId == null) {
            throw new BaseBusinessException("策略ID不可为空");
        }
        RuleAuditAppBehavior ruleAuditAppBehavior = this.ruleAuditAppBehaviorRepository.getById(ruleId);
        return RuleAuditAppBehaviorResp.fromRuleAuditAppBehavior(ruleAuditAppBehavior);
    }

    @Override
    public void save(RuleAuditAppBehaviorReq ruleAuditAppBehaviorReq) {
        // 验证
        this.validateParam(ruleAuditAppBehaviorReq);
        RuleAuditAppBehavior ruleAuditAppBehavior = fromRuleAuditAppBehaviorReq(ruleAuditAppBehaviorReq);
        // 赋值ruleId
        ruleAuditAppBehavior.setRuleId(this.ruleIdBuilder.buildRuleId().toString());
        this.ruleAuditAppBehaviorRepository.save(ruleAuditAppBehavior);
    }

    @Override
    public void edit(RuleAuditAppBehaviorReq ruleAuditAppBehaviorReq) {
        // 验证
        this.validateId(ruleAuditAppBehaviorReq);
        this.validateParam(ruleAuditAppBehaviorReq);
        RuleAuditAppBehavior ruleAuditAppBehavior = fromRuleAuditAppBehaviorReq(ruleAuditAppBehaviorReq);
        this.ruleAuditAppBehaviorRepository.updateById(ruleAuditAppBehavior);
    }

    public static RuleAuditAppBehavior fromRuleAuditAppBehaviorReq(RuleAuditAppBehaviorReq ruleAuditAppBehaviorReq) {
        return RuleAuditAppBehavior.builder()
                .ruleId(ruleAuditAppBehaviorReq.getRuleId())
                .ruleName(ruleAuditAppBehaviorReq.getRuleName())
                .ruleDesc(ruleAuditAppBehaviorReq.getRuleDesc())
                .ip(ruleAuditAppBehaviorReq.getIp())
                .auditType(ruleAuditAppBehaviorReq.getAuditType())
                .expireTime(ruleAuditAppBehaviorReq.getExpireTime())
                .param(ruleAuditAppBehaviorReq.getParam())
                .status(ruleAuditAppBehaviorReq.getStatus())
                .isShare(ruleAuditAppBehaviorReq.getIsShare())
                .ruleSource(ruleAuditAppBehaviorReq.getRuleSource())
                .level(ruleAuditAppBehaviorReq.getLevel())
                .updateTime(ruleAuditAppBehaviorReq.getUpdateTime())
                .createTime(ruleAuditAppBehaviorReq.getCreateTime())
                .ext1(ruleAuditAppBehaviorReq.getExt1())
                .ext2(ruleAuditAppBehaviorReq.getExt2())
                .ext3(ruleAuditAppBehaviorReq.getExt3())
                .upRuleId(ruleAuditAppBehaviorReq.getUpRuleId())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleAuditAppBehaviorRepository.removeByIds(batchIdsReq.getIds());
    }


    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 校验是否在使用
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleAuditAppBehavior> list = this.ruleAuditAppBehaviorRepository.list(Wrappers.<RuleAuditAppBehavior>lambdaQuery()
                .in(RuleAuditAppBehavior::getRuleId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.equals(PolicyApplyStatusEnum.APPLY.getKey().toString(), item.getStatus())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
                Integer ruleSource = Integer.valueOf(item.getRuleSource());
                if (ruleSource != RuleSourceTypeEnum.LOCAL_POLICY.getKey()) {
                    throw new BaseBusinessException("存在上级策略，上级策略不允许被删除");
                }
            });
        }
    }

    /**
     * 校验ID
     * @param ruleAuditAppBehaviorReq
     */
    private void validateId(RuleAuditAppBehaviorReq ruleAuditAppBehaviorReq) {
        if (StrUtil.isBlank(ruleAuditAppBehaviorReq.getRuleId())) {
            throw new BaseBusinessException("策略ID不可为空");
        }
    }

    /**
     * 校验参数
     * @param ruleAuditAppBehaviorReq
     */
    private void validateParam(RuleAuditAppBehaviorReq ruleAuditAppBehaviorReq) {
        if (!Validator.isIpv4(ruleAuditAppBehaviorReq.getIp())) {
            throw new BaseBusinessException("IP地址格式错误");
        }
    }

    /**
     * 获取规则ID列表
     * @param ruleAuditAppBehaviorPageReq
     * @return
     */
    private List<String> getRuleIdList(RuleAuditAppBehaviorPageReq ruleAuditAppBehaviorPageReq) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleAuditAppBehaviorPageReq.getIssueDeviceType())
                || StrUtil.isBlank(ruleAuditAppBehaviorPageReq.getPolicyId())
                || StrUtil.isBlank(ruleAuditAppBehaviorPageReq.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleAuditAppBehaviorPageReq.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleAuditAppBehaviorPageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleAuditAppBehaviorPageReq.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleAuditAppBehaviorPageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 网络行为审计策略 - 应用行为审计策略
        return StrUtil.equals(module, PolicyModuleTwoEnum.APP_BEHAVIOR.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 网络行为审计策略 - 应用行为审计策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleTwoEnum.APP_BEHAVIOR.getKey())
                .moduleStr(PolicyModuleTwoEnum.APP_BEHAVIOR.getValue())
                .moduleParentStr(PolicyModuleOneEnum.NET_AUDIT.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 网络行为审计策略 - 应用行为审计策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleAuditAppBehavior> list = this.ruleAuditAppBehaviorRepository.list(Wrappers.<RuleAuditAppBehavior>lambdaQuery()
                .in(RuleAuditAppBehavior::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<AuditAppBehaviorConfigDTO> configDTOS = list.stream().map(item -> AuditAppBehaviorConfigDTO.getPolicyConfig(item)).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 网络行为审计策略 - 应用行为审计策略
        this.ruleAuditAppBehaviorRepository.update(Wrappers.<RuleAuditAppBehavior>lambdaUpdate()
                .in(RuleAuditAppBehavior::getRuleId, ruleIds)
                .set(RuleAuditAppBehavior::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 网络行为审计策略 - 应用行为审计策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleAuditAppBehavior> list = this.ruleAuditAppBehaviorRepository.list(Wrappers.<RuleAuditAppBehavior>lambdaQuery()
                .in(RuleAuditAppBehavior::getRuleId, ruleIdList));
        List<RuleAuditAppBehaviorResp> respList = list.stream().map(item -> RuleAuditAppBehaviorResp.fromRuleAuditAppBehavior(item)).collect(Collectors.toList());
        policyDetail.setAuditAppBehaviorList(respList);
        return policyDetail;
    }

}
