package com.superred.supervisor.gateway.service.terminal;


import com.superred.supervisor.standard.v202505.terminal.cmd.CheckUpdateReq;
import com.superred.supervisor.standard.v202505.terminal.cmd.CheckUpdateResp;
import com.superred.supervisor.standard.v202505.terminal.register.RegInfoResp;
import com.superred.supervisor.standard.v202505.terminal.register.TerminalIdReq;
import com.superred.supervisor.standard.v202505.terminal.register.TerminalIdResp;
import com.superred.supervisor.standard.v202505.terminal.register.TerminalRegisterReq;

/**
 * 终端注册、认证接口
 *
 * <AUTHOR>
 * @since 2025/5/16 16:30
 */
public interface TerminalRegisterService {


    /**
     * 组织机构/人员信息查询接口
     *
     * @return 注册结果
     */
    RegInfoResp regInfo();


    /**
     * 主机唯一编码查询接口
     *
     * @param req 注册请求
     * @return 注册结果
     */
    TerminalIdResp getComputerClientId(TerminalIdReq req);


    /**
     * 终端注册接口
     *
     * @param req 注册请求
     */
    void regRequest(TerminalRegisterReq req);

    /**
     * 终端认证接口
     *
     * @param req 注册请求
     */
    void authLogin(TerminalRegisterReq req);


    /**
     * 终端注销接口
     *
     */
    void regCancel();

    /**
     * 检查更新接口
     *
     * @param req 更新请求
     * @return 更新响应
     */
    CheckUpdateResp checkUpdate(CheckUpdateReq req);

    /**
     * 终端卸载回调接口
     * 该接口由终端主动调用，表示终端已卸载
     */
    void agentUninstallCallback();
}
