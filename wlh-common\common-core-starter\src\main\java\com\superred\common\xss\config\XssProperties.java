package com.superred.common.xss.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;


/**
 * XSS 配置
 *
 * <AUTHOR>
 * @since 2025/05/31
 */
@Data
@EnableConfigurationProperties(XssProperties.class)
@ConfigurationProperties(XssProperties.PREFIX)
public class XssProperties {

    public static final String PREFIX = "superred.common.xss";

    /**
     * 开启xss
     */
    private boolean enabled = true;

    /**
     * 模式：clear 清理（默认），escape 转义
     */
    private Mode mode = Mode.clear;

    /**
     * [clear 专用] prettyPrint，默认关闭： 保留换行
     */
    private boolean prettyPrint = false;

    /**
     * [clear 专用] 使用转义，默认关闭
     */
    private boolean enableEscape = false;


    public enum Mode {

        /**
         * 清理
         */
        clear,
        /**
         * 转义
         */
        escape,
        /**
         * 校验，抛出异常
         */
        validate
    }

}
