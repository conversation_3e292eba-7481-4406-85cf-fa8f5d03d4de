package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 指令设备详情查询req
 */
@Data
public class TerminalCmdExecPageReq {

    @Schema(description = "指令id")
    @NotNull(message = "指令ID不能为空")
    private String cmdId;

    
    @Schema(description = "执行结果，取值为：0（成功）、1（失败）")
    private Integer result;

    @Schema(description = "下发状态，取值为：0（未下发）、1（已下发）")
    private Integer status;


    @Schema(description = "当前页", example = "1")
    private Integer start;

    @Schema(description = "每页显示条数", example = "10")
    private Integer limit;
}