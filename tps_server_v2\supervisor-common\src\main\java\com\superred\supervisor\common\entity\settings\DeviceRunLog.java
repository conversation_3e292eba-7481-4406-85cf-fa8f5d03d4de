package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 监测器离线异常告警(DDeviceRunLog) 实体
 *
 * <AUTHOR>
 * @since 2025-03-27 15:31:59
 */
@Data
@TableName("d_device_run_log")
public class DeviceRunLog {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 异常类型
     */
    @TableField("type")
    private String type;

    /**
     * 异常描述
     */
    @TableField("description")
    private String description;

    /**
     * 产生时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 告警级别
     */
    @TableField("level")
    private Integer level;

    /**
     * 设备编号
     */
    @TableField("device_id")
    private String deviceId;

}

