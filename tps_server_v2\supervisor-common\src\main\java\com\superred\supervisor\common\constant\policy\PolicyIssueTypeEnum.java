package com.superred.supervisor.common.constant.policy;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2025-03-26 10:10
 */
@Getter
public enum PolicyIssueTypeEnum {
    CMD_ADD("add", "增量下发"),
    CMD_RESET("reset", "全量下发"),
    CMD_DEL("del", "增量删除");

    private final String key;
    private final String value;

    PolicyIssueTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static PolicyIssueTypeEnum getByKey(String key) {
        for (PolicyIssueTypeEnum policyIssueTypeEnum : PolicyIssueTypeEnum.values()) {
            if (policyIssueTypeEnum.getKey().equals(key)) {
                return policyIssueTypeEnum;
            }
        }
        return null;
    }
}
