package com.superred.common.xss.utils;

import com.superred.common.xss.core.XssIgnore;

/**
 * 利用 ThreadLocal 缓存线程间的数据
 *  参考：<a href="https://github.com/pig-mesh/pig/tree/jdk17/pig-common/pig-common-xss">pig</a>
 * <AUTHOR>
 */
public final class XssHolder {

    private XssHolder() {
    }

    private static final ThreadLocal<Boolean> TL = new ThreadLocal<>();

    private static final ThreadLocal<XssIgnore> TL_IGNORE = new ThreadLocal<>();

    /**
     * 是否开启
     * @return boolean
     */
    public static boolean isEnabled() {
        return Boolean.TRUE.equals(TL.get());
    }

    /**
     * 标记为开启
     */
    public static void setEnable() {
        TL.set(Boolean.TRUE);
    }

    /**
     * 保存接口上的 XssCleanIgnore
     * @param xssCleanIgnore XssCleanIgnore
     */
    public static void setXssIgnore(XssIgnore xssCleanIgnore) {
        TL_IGNORE.set(xssCleanIgnore);
    }

    /**
     * 获取接口上的 XssCleanIgnore
     * @return XssCleanIgnore
     */
    public static XssIgnore getXssIgnore() {
        return TL_IGNORE.get();
    }

    /**
     * 关闭 xss 清理
     */
    public static void remove() {
        TL.remove();
        TL_IGNORE.remove();
    }

}
