package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端指令统计Resp
 */

@Data
public class TerminalCmdRecordPageResp {

    @Schema(description = "指令id")
    private String cmdId;

    @Schema(description = "指令名称 比如 stopm")
    private String cmd;

    @Schema(description = "指令中文名称 比如 模块停止")
    private String cmdName;


    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "生效范围")
    private Integer deviceCount;

    @Schema(description = "执行成功数量")
    private Integer successCount;

    @Schema(description = "执行失败数量")
    private Integer failCount;

    @Schema(description = "已经下发数量")
    private Integer issueCount;

    @Schema(description = "未下发数量")
    private Integer unIssueCount;

}
