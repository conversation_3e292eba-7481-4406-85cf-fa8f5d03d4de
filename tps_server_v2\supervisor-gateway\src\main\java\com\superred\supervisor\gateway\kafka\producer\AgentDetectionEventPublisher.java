package com.superred.supervisor.gateway.kafka.producer;

import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.model.dto.detection.AgentDetectionResultDTO;
import com.superred.supervisor.gateway.service.cache.GatewayCacheService;
import com.superred.supervisor.gateway.utils.AgentAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *  终端信息采集事件上报
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AgentDetectionEventPublisher {


    @Resource
    private KafkaProducerService kafkaProducerService;

    @Resource
    private GatewayCacheService gatewayCacheService;


    @Value("${spring.kafka.agent-detection.topic:agent-detection-request-msg}")
    private String topic;


    public void sendMessage(AgentDetectionResultDTO<?> resultDTO) {

        resultDTO.setLocalDeviceId(gatewayCacheService.cacheLocalDeviceId());
        resultDTO.setSrcDevice(AgentAuthUtils.getDeviceIdFromRequest());

        String json = JsonUtil.toJson(resultDTO);

        kafkaProducerService.sendMessage(json, topic,
                AgentDetectionResultDTO.KAFKA_HEADER_NAME, resultDTO.getExportFile().name());
    }


}
