package com.superred.supervisor.manager.model.dto.policy.config;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleFilterMd5;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-03-27 21:29
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FilterMd5PolicyConfigDTO {

    /**
     * 策略id
     */
    private Long ruleId;

    /**
     * 策略内容
     */
    private String ruleContent;

    /**
     * 策略描述
     */
    private String ruleDesc;

    public static FilterMd5PolicyConfigDTO getPolicyConfig(RuleFilterMd5 filterMd5) {
        if (filterMd5 == null) {
            return null;
        }
        return FilterMd5PolicyConfigDTO.builder()
                .ruleId(filterMd5.getRuleId())
                .ruleContent(PolicyUtils.handleStrNull(filterMd5.getRuleContent()))
                .ruleDesc(PolicyUtils.handleStrNull(filterMd5.getRuleDesc()))
                .build();
    }
}
