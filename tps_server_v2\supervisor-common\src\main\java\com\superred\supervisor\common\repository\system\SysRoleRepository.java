package com.superred.supervisor.common.repository.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.system.SysRole;
import com.superred.supervisor.common.entity.system.SysUserRole;
import com.superred.supervisor.common.mapper.system.SysRoleMapper;
import com.superred.supervisor.common.mapper.system.SysUserRoleMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * 角色表 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:21
 */
@Repository
public class SysRoleRepository extends ServiceImpl<SysRoleMapper, SysRole> {

    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    public SysRole selectRoleByUserId(Integer id) {
        LambdaQueryWrapper<SysUserRole> eq = Wrappers.<SysUserRole>lambdaQuery().eq(SysUserRole::getUserId, id);

        SysUserRole sysUserRole = sysUserRoleMapper.selectOne(eq, false);
        if (sysUserRole == null) {
            return null;
        }

        return this.getById(sysUserRole.getRoleId());
    }
}

