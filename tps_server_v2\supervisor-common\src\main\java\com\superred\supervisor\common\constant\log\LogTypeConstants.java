package com.superred.supervisor.common.constant.log;

/**
 * 日志类型常量
 *
 * <AUTHOR>
 * @since 2025/7/21 16:06
 */
public class LogTypeConstants {
    public static final String USER_MANAGEMENT = "用户管理";
    public static final String DEPARTMENT_MANAGEMENT = "部门管理";
    public static final String ROLES = "角色管理";
    public static final String TERMINAL_PERSONNEL_MANAGEMENT = "终端人员管理";
    public static final String SYSTEM_AUTHORIZATION = "系统授权管理";
    public static final String SYSTEM_CONFIGURATION = "系统配置";
    public static final String SYSTEM_MAINTENANCE = "系统维护";
    public static final String UKEY_MANAGEMENT = "Ukey管理";
    public static final String SYSTEM_UPGRADE_FILE_MANAGEMENT = "系统升级文件管理";
    public static final String TERMINAL_CONFIDENTIALITY_COMPONENT_DOWNLOAD_MANAGEMENT = "终端保密组件下载管理";
    public static final String ACCESS_DEVICE_MANAGEMENT = "接入设备管理";
    public static final String TERMINAL_DEVICE_REPORTING_MANAGEMENT = "终端设备报备管理";
    public static final String TERMINAL_SOFTWARE = "终端软件管理";
    public static final String TERMINAL_STRATEGY_ISSUANCE = "终端策略下发";
    public static final String MONITOR_STRATEGY_ISSUANCE = "监测器策略下发";
    public static final String TERMINAL_COMMAND_ISSUANCE = "终端指令下发";
    public static final String DETECTOR_INSTRUCTION_ISSUANCE = "检测器指令下发";
    public static final String DOMAIN_BLACKLIST_DETECTION_STRATEGY = "域名黑名单检测策略";
    public static final String IP_BLACKLIST_DETECTION_STRATEGY = "IP黑名单检测策略";
    public static final String URL_BLACKLIST_DETECTION_STRATEGY = "URL黑名单检测策略";
    public static final String MALICIOUS_FILE_DETECTION_STRATEGY = "恶意文件检测策略";
    public static final String PENETRATION_ATTACK_DETECTION_STRATEGY = "渗透攻击检测策略";
    public static final String TROJAN_ACTIVITY_DETECTION_STRATEGY = "木马活动检测策略";
    public static final String APPLICATION_BEHAVIOR_AUDIT_STRATEGY = "应用行为审计策略";
    public static final String EQUIPMENT_INFORMATION_COLLECTION_STRATEGY = "设备信息采集策略";
    public static final String ACCOUNT_FILTERING_STRATEGY = "账号筛选策略";
    public static final String KEYWORD_STRATEGY = "关键词策略";
    public static final String FILE_MD5_STRATEGY = "文件MD5策略";
    public static final String RISK_SOFTWARE_USAGE_STRATEGY = "风险软件使用策略";
}
