package com.superred.supervisor.manager.model.dto.policy;

import cn.hutool.core.collection.CollectionUtil;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:47
 */
@Data
public class AgentPolicyRuleDTO {

    private Long id;

    /**
     * 规则ID
     */
    //@JsonFormat(shape = JsonFormat.Shape.STRING)
    @Schema(description = "规则ID")
    private Long ruleId;

    /**
     * 策略ID
     */
    @Schema(description = "策略ID")
    private Long policyId;

    /**
     * 模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单
     */
    @Schema(description = "模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单")
    private String module;

    public static List<AgentPolicyRuleDTO> fromAgentPolicyRule(List<AgentPolicyRule> agentPolicyRuleList) {
        List<AgentPolicyRuleDTO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
            agentPolicyRuleList.forEach(item -> {
                AgentPolicyRuleDTO agentPolicyRuleDTO = new AgentPolicyRuleDTO();
                agentPolicyRuleDTO.setId(item.getId());
                agentPolicyRuleDTO.setRuleId(item.getRuleId());
                agentPolicyRuleDTO.setPolicyId(item.getPolicyId());
                agentPolicyRuleDTO.setModule(item.getModule());
                list.add(agentPolicyRuleDTO);
            });
        }
        return list;
    }
}
