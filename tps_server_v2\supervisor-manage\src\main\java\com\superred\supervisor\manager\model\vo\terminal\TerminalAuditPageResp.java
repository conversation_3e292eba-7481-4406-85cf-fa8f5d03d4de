package com.superred.supervisor.manager.model.vo.terminal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端设备信息
 */
@Data

public class TerminalAuditPageResp {


    @Schema(description = "设备编号")
    private String deviceId;


    @Schema(description = "前八位为年月日，下划线后自定义")
    private String softVersion;


    @Schema(description = "注册时间")
    private LocalDateTime registerTime;


    @Schema(description = "激活时间")
    private LocalDateTime verifyTime;

    @Schema(description = "备注信息")
    private String memo;

    @Schema(description = "心跳时间")
    private LocalDateTime heartbeatTime;


    @Schema(description = "终端责任人ID")
    private String userId;

    @Schema(description = "终端责任人姓名")
    private String userName;


    @Schema(description = "单位名称")
    private String company;

    @Schema(description = "所属部门名称")
    private String orgName;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "主机操作系统")
    private String os;

    @Schema(description = "终端状态0 审核通过 1  审核不通过 2 待审核 3 禁用 4 注销")
    private Integer status;

    /**
     * 审核方式 1 自动审核 2人工审核
     */
    @Schema(description = "审核方式 1 自动审核 2人工审核")
    private Integer auditWay;


}
