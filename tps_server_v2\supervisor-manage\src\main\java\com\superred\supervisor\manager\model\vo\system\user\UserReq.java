package com.superred.supervisor.manager.model.vo.system.user;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;


/**
 * <AUTHOR>
 *  用户请求实体
 * @since 2025年03月11日
 */
@Data
public class UserReq implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;
    /**
     * 账号
     */
    @Schema(description = "账号")
    @NotBlank(message = "用户名 不可为空")
    @Size(min = 1, max = 20, message = "用户名 长度限制1-20位")
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5]+$", message = "用户名 只能包含中文、英文、数字")
    private String username;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    @NotBlank(message = "姓名 不可为空")
    @Size(min = 1, max = 20, message = "姓名 长度限制1-20位")
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5]+$", message = "姓名 只能包含中文、英文、数字")
    private String realName;


    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    @NotNull(message = "部门 不可为空")
    private Integer orgId;


    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 300, message = "备注 长度不能超过300")
    private String remarks;


    /**
     * 身份证号
     */
    @Schema(description = "电话号码")
    @Pattern(regexp = "(?:0|86|\\+86)?1[3-9]\\d{9}", message = "联系方式 格式错误")
    private String card;


    /**
     * 密级
     * 1：一般涉密
     * 2：重要涉密
     * 3：核心涉密
     */
    @Schema(description = "密级 1：一般涉密 2：重要涉密 3：核心涉密")
    @Min(value = 1, message = "密级 格式错误")
    @Max(value = 3, message = "密级 格式错误")
    private Integer secret;


    @Schema(description = "是否是主账号：0否，1是")
    private Integer isMaster;

    @Schema(description = "是否是第三方账号：N否，Y是")
    private String isThirdClient;

}
