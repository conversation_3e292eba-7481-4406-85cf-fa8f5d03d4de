package com.superred.supervisor.manager.controller.policy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.annotation.role.IgnoreAuth;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.policy.terminal.TerminalPolicyHistoryVersionResp;
import com.superred.supervisor.manager.model.vo.policy.terminal.TerminalPolicyIssueDetailResp;
import com.superred.supervisor.manager.model.vo.policy.terminal.TerminalPolicyIssueRecordPageReq;
import com.superred.supervisor.manager.model.vo.policy.terminal.TerminalPolicyIssueRecordResp;
import com.superred.supervisor.manager.model.vo.policy.terminal.TerminalPolicyIssueReq;
import com.superred.supervisor.manager.model.vo.policy.terminal.TerminalPolicyModuleTreeResp;
import com.superred.supervisor.manager.service.policy.terminal.TerminalPolicyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 终端策略下发控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Slf4j
@Tag(name = "8.2 终端策略下发模块")
@RestController
@RequestMapping("/v2/agent/policy/")
@IgnoreAuth
public class TerminalPolicyController {

    @Resource
    private TerminalPolicyService terminalPolicyService;

    @ApiOperationSupport(order = 1)
    @Operation(summary = "1. 策略下发")
    @PostMapping("/issue")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_STRATEGY_ISSUANCE,
            operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "终端策略下发")
    public R<String> issuePolicy(@Valid @RequestBody TerminalPolicyIssueReq req) {
        String operationId = terminalPolicyService.issuePolicy(req);
        return R.success(operationId);
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2. 策略下发记录-分页")
    @GetMapping("/issue/record/page")
    public RPage<TerminalPolicyIssueRecordResp> issueRecordPage(TerminalPolicyIssueRecordPageReq req) {
        IPage<TerminalPolicyIssueRecordResp> page = terminalPolicyService.issueRecordPage(req);
        return new RPage<>(page);
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "3. 策略下发详情")
    @GetMapping("/issue/detail/{policyId}")
    public R<TerminalPolicyIssueDetailResp> issueDetail(@PathVariable String policyId) {
        TerminalPolicyIssueDetailResp detail = terminalPolicyService.getIssueDetail(policyId);
        return R.success(detail);
    }

    @ApiOperationSupport(order = 4)
    @Operation(summary = "4. 获取策略模块树形结构")
    @GetMapping("/module/tree")
    public R<List<TerminalPolicyModuleTreeResp>> getPolicyModuleTree() {
        List<TerminalPolicyModuleTreeResp> tree = terminalPolicyService.getPolicyModuleTree();
        return R.success(tree);
    }

    @ApiOperationSupport(order = 5)
    @Operation(summary = "5. 获取模块历史版本")
    @GetMapping("/history/{module}")
    public R<List<TerminalPolicyHistoryVersionResp>> getPolicyHistoryVersions(@PathVariable String module) {
        List<TerminalPolicyHistoryVersionResp> versions = terminalPolicyService.getPolicyHistoryVersions(module);
        return R.success(versions);
    }
}
