package com.superred.supervision.base.constant;

/**
 * <AUTHOR>
 * @since 2023/2/8 14:26
 */
public final class UrlConstants {

    private UrlConstants() {
        // Prevent instantiation
    }

//	public static final String SESSION_ID = "";

    /**
     * 注册请求
     */
    public static final String REG_REQUEST = "register/reg_request";

    public static final String REG_REQUEST_DESC = "注册接口";

    /**
     * 重新注册
     */
    public static final String RE_REG_REQUEST = "register/re_reg_request";
    public static final String RE_REG_REQUEST_DESC = "重新注册";

    /**
     * 认证
     */
    public static final String LOGIN = "auth/login";

    public static final String LOGIN_DESC = "认证";

    /**
     * 注册状态查询
     */
    public static final String REG_STATUS = "register/regstatus";

    public static final String REG_STATUS_DESC = "注册状态查询";

    /**
     * 心跳
     */
    public static final String HEARTBEAT = "heartbeat";

    public static final String HEARTBEAT_DESC = "心跳";


    public static final String SYSTEM_STATUS = "system_status";


    public static final String SYSTEM_STATUS_DESC = "系统运行状态上报";


    public static final String BUSINESS_STATUS = "business_status";

    public static final String BUSINESS_STATUS_DESC = "业务状态上报";


    public static final String BUSINESS_STATUS_DETECTOR = "business_status/detector";

    public static final String BUSINESS_STATUS_DETECTOR_DESC = "检测器业务状态上报";


    public static final String BUSINESS_STATUS_SELF = "business_status/self";

    public static final String BUSINESS_STATUS_SELF_DESC = "自监管业务状态上报";

    public static final String SYSTEM_AUDIT = "system_audit";

    public static final String SYSTEM_AUDIT_DESC = "系统审计";

    public static final String COMMAND_RESULT = "sys_manager/command_result";

    public static final String COMMAND_RESULT_DESC = "指令响应上报";


    public static final String COMMAND_RESULT_COLLECT = "sys_manager/command_result_collect";

    public static final String COMMAND_RESULT_COLLECT_DESC = "指令响应汇总上报";


    public static final String POLICY_RESULT = "sys_manager/policy_result";

    public static final String POLICY_RESULT_DESC = "策略响应上报";

    public static final String POLICY_RESULT_COLLECT = "sys_manager/policy_result_collect";

    public static final String POLICY_RESULT_COLLECT_DESC = "策略响应汇总上报";

    public static final String REPORT_POLICY = "sys_manager/report_policy";

    public static final String REPORT_POLICY_DESC = "策略内容上报";


    // sensitive 相关
    public static final String SENSITIVE = "/sensitive/";

    public static final String KEYWORD_DETECT_CENTER_POLICY = "keyword_detect/center_policy";

    public static final String KEYWORD_DETECT_CENTER_POLICY_DESC = "关键词检测结果上报";

    public static final String KEYWORD_DETECT_CENTER_POLICY_FILE = "keyword_detect/center_policy_file";

    public static final String KEYWORD_DETECT_CENTER_FILE_POLICY_DESC = "关键词检测文件上传";


    public static final String FILE_MD5_CENTER_POLICY = "file_md5/center_policy";

    public static final String FILE_MD5_CENTER_POLICY_DESC = "文件Md5检测结果上报";

    public static final String FILE_MD5_CENTER_POLICY_FILE = "file_md5/center_policy_file";

    public static final String FILE_MD5_CENTER_POLICY_FILE_DESC = "文件Md5检测文件上传";


    public static final String SENSITIVE_FILE_INNER_POLICY = "sensitive_file/inner_policy";

    public static final String SENSITIVE_FILE_INNER_POLICY_DESC = "标密文件筛选结果上报";

    public static final String SENSITIVE_FILE_INNER_POLICY_FILE = "sensitive_file/inner_policy_file";

    public static final String SENSITIVE_FILE_INNER_POLICY_FILE_DESC = "标密文件上传";

    public static final String KEYWORD_FILE_CENTER_POLICY = "file_keyword/center_policy";

    public static final String KEYWORD_FILE_CENTER_POLICY_DESC = "关键词筛选结果上报(中心下发规则)";

    public static final String KEYWORD_FILE_CENTER_POLICY_FILE = "file_keyword/center_policy_file";

    public static final String KEYWORD_FILE_CENTER_POLICY_FILE_DESC = "关键词文件上传(中心下发规则)";

    public static final String KEYWORD_FILE_INNER_POLICY = "file_keyword/inner_policy";

    public static final String KEYWORD_FILE_INNER_POLICY_DESC = "关键词筛选结果上报(内置规则)";

    public static final String KEYWORD_FILE_INNER_POLICY_FILE = "file_keyword/inner_policy_file";

    public static final String KEYWORD_FILE_INNER_POLICY_FILE_DESC = "关键词文件上传(内置规则)";


    public static final String STYLE_FILE_CENTER_POLICY = "file_style/center_policy";

    public static final String STYLE_FILE_CENTER_POLICY_DESC = "版式文件筛选结果上报(中心下发规则)";


    public static final String STYLE_FILE_CENTER_POLICY_FILE = "file_style/center_policy_file";

    public static final String STYLE_FILE_CENTER_POLICY_FILE_DESC = "版式文件上传(中心下发规则)";

    public static final String STYLE_FILE_INNER_POLICY = "file_style/inner_policy";

    public static final String STYLE_FILE_INNER_POLICY_DESC = "版式文件筛选结果上报(内置规则)";

    public static final String STYLE_FILE_INNER_POLICY_FILE = "file_style/inner_policy_file";

    public static final String STYLE_FILE_INNER_POLICY_FILE_DESC = "版式文件上传(内置规则)";


    public static final String COVER_FILE_INNER_POLICY = "file_cover/inner_policy";

    public static final String COVER_FILE_INNER_POLICY_DESC = "遮盖文件筛选结果上报(内置规则)";

    public static final String COVER_FILE_INNER_POLICY_FILE = "file_cover/inner_policy_file";

    public static final String COVER_FILE_INNER_POLICY_FILE_DESC = "遮盖文件上传(内置规则)";


    public static final String SEAL_FILE_CENTER_POLICY = "file_seal/center_policy";

    public static final String SEAL_FILE_CENTER_POLICY_DESC = "印章文件筛选结果上报(中心下发规则)";

    public static final String SEAL_FILE_CENTER_POLICY_FILE = "file_seal/center_policy_file";

    public static final String SEAL_FILE_CENTER_POLICY_FILE_DESC = "印章文件上传(中心下发规则)";

    public static final String SEAL_FILE_INNER_POLICY = "file_seal/inner_policy";

    public static final String SEAL_FILE_INNER_POLICY_DESC = "印章文件筛选结果上报(内置规则)";

    public static final String SEAL_FILE_INNER_POLICY_FILE = "file_seal/inner_policy_file";

    public static final String SEAL_FILE_INNER_POLICY_FILE_DESC = "印章文件上传(内置规则)";


    public static final String FILE_HASH_CENTER_POLICY = "file_hash/center_policy";

    public static final String FILE_HASH_CENTER_POLICY_DESC = "文件哈希筛选结果";

    public static final String FILE_HASH_CENTER_POLICY_FILE = "file_hash/center_policy_file";

    public static final String FILE_HASH_CENTER_POLICY_FILE_DESC = "文件哈希文件上传";


    public static final String EXTENDED_INNER_POLICY = "extended/inner_policy";
    public static final String EXTENDED_INNER_POLICY_DESC = "扩展告警上报";

    public static final String EXTENDED_INNER_POLICY_FILE = "extended/inner_policy_file";

    public static final String EXTENDED_INNER_POLICY_FILE_DESC = "扩展告警文件上传";

    public static final String EXTENDED_CENTER_POLICY = "extended/center_policy";
    public static final String EXTENDED_CENTER_POLICY_DESC = "扩展告警上报";

    public static final String EXTENDED_CENTER_POLICY_FILE = "extended/center_policy_file";

    public static final String EXTENDED_CENTER_POLICY_FILE_DESC = "扩展告警文件上传";

    public static final String TROJAN_INNER_POLICY = "alarm/trojan/inner_policy";

    public static final String TROJAN_INNER_POLICY_DESC = "木马活动告警信息上传(内置规则)";

    public static final String TROJAN_CENTER_POLICY = "alarm/trojan/center_policy";

    public static final String TROJAN_CENTER_POLICY_DESC = "木马活动告警信息上传(中心下发规则)";


    public static final String TROJAN_INNER_POLICY_PCAP = "alarm/trojan/inner_policy_file";

    public static final String TROJAN_INNER_POLICY_PCAP_DESC = "木马活动告警文件上传(内置规则)";

    public static final String TROJAN_CENTER_POLICY_PCAP = "alarm/trojan/center_policy_file";

    public static final String TROJAN_CENTER_POLICY_PCAP_DESC = "木马活动告警文件上传(中心下发规则)";


    public static final String ATTACK_INNER_POLICY = "alarm/attack/inner_policy";

    public static final String ATTACK_INNER_POLICY_DESC = "渗透行为告警信息上传(内置规则)";

    public static final String ATTACK_CENTER_POLICY = "alarm/attack/center_policy";

    public static final String ATTACK_CENTER_POLICY_DESC = "渗透行为告警信息上传(中心下发规则)";

    public static final String ATTACK_INNER_POLICY_PCAP = "alarm/attack/inner_policy_file";

    public static final String ATTACK_INNER_POLICY_PCAP_DESC = "渗透行为告警文件上传(内置规则)";

    public static final String ATTACK_CENTER_POLICY_PCAP = "alarm/attack/center_policy_file";

    public static final String ATTACK_CENTER_POLICY_PCAP_DESC = "渗透行为告警文件上传(中心下发规则)";


    public static final String MALWARE_INNER_POLICY = "alarm/malware/inner_policy";

    public static final String MALWARE_INNER_POLICY_DESC = "恶意文件告警信息上传(内置规则)";

    public static final String MALWARE_CENTER_POLICY = "alarm/malware/center_policy";

    public static final String MALWARE_CENTER_POLICY_DESC = "恶意文件告警信息上传(中心下发规则)";

    public static final String MALWARE_INNER_POLICY_PCAP = "alarm/malware/inner_policy_file";

    public static final String MALWARE_INNER_POLICY_PCAP_DESC = "恶意文件告警文件上传(内置规则)";

    public static final String MALWARE_CENTER_POLICY_PCAP = "alarm/malware/center_policy_file";

    public static final String MALWARE_CENTER_POLICY_PCAP_DESC = "恶意文件告警文件上传(中心下发规则)";


    public static final String IP_BLACKLIST_INNER_POLICY = "alarm/ip_blacklist/inner_policy";

    public static final String IP_BLACKLIST_INNER_POLICY_DESC = "IP黑名单告警数据上报(内置规则)";


    public static final String IP_BLACKLIST_INNER_POLICY_FILE = "alarm/ip_blacklist/inner_policy_file";

    public static final String IP_BLACKLIST_INNER_POLICY_FILE_DESC = "IP黑名单文件上传(内置规则)";


    public static final String IP_BLACKLIST_CENTER_POLICY = "alarm/ip_blacklist/center_policy";

    public static final String IP_BLACKLIST_CENTER_POLICY_DESC = "IP黑名单告警数据上报(中心下发规则)";


    public static final String IP_BLACKLIST_CENTER_POLICY_FILE = "alarm/ip_blacklist/center_policy_file";

    public static final String IP_BLACKLIST_CENTER_POLICY_FILE_DESC = "IP黑名单文件上传(中心下发规则)";


    public static final String DOMAIN_BLACKLIST_INNER_POLICY = "alarm/domain_blacklist/inner_policy";

    public static final String DOMAIN_BLACKLIST_INNER_POLICY_DESC = "域名黑名单告警数据上报(内置规则)";


    public static final String DOMAIN_BLACKLIST_CENTER_POLICY = "alarm/domain_blacklist/center_policy";

    public static final String DOMAIN_BLACKLIST_CENTER_POLICY_DESC = "域名黑名单告警数据上报(中心下发规则)";


    public static final String DOMAIN_BLACKLIST_INNER_POLICY_FILE = "alarm/domain_blacklist/inner_policy_file";

    public static final String DOMAIN_BLACKLIST_INNER_POLICY_FILE_DESC = "域名黑名单文件上传(内置规则)";


    public static final String DOMAIN_BLACKLIST_CENTER_POLICY_FILE = "alarm/domain_blacklist/center_policy_file";

    public static final String DOMAIN_BLACKLIST_CENTER_POLICY_FILE_DESC = "域名黑名单文件上传(中心下发规则)";


    public static final String URL_BLACKLIST_INNER_POLICY = "alarm/url_blacklist/inner_policy";

    public static final String URL_BLACKLIST_INNER_POLICY_DESC = "URL黑名单告警数据上报(内置规则)";

    public static final String URL_BLACKLIST_CENTER_POLICY = "alarm/url_blacklist/center_policy";

    public static final String URL_BLACKLIST_CENTER_POLICY_DESC = "URL黑名单告警数据上报(中心下发规则)";


    public static final String URL_BLACKLIST_INNER_POLICY_FILE = "alarm/url_blacklist/inner_policy_file";

    public static final String URL_BLACKLIST_INNER_POLICY_FILE_DESC = "URL黑名单告警文件上传(内置规则)";

    public static final String URL_BLACKLIST_CENTER_POLICY_FILE = "alarm/url_blacklist/center_policy_file";

    public static final String URL_BLACKLIST_CENTER_POLICY_FILE_DESC = "URL黑名单告警文件上传(中心下发规则)";


    public static final String ACCOUNT_BLACKLIST_INNER_POLICY = "blacklist/account_blacklist/inner_policy";
    public static final String ACCOUNT_BLACKLIST_INNER_POLICY2 = "alarm/account_blacklist/inner_policy";

    public static final String ACCOUNT_BLACKLIST_INNER_POLICY_DESC = "账号黑名单告警数据上报(内置规则)";

    public static final String ACCOUNT_BLACKLIST_CENTER_POLICY = "blacklist/account_blacklist/center_policy";
    public static final String ACCOUNT_BLACKLIST_CENTER_POLICY2 = "alarm/account_blacklist/center_policy";

    public static final String ACCOUNT_BLACKLIST_CENTER_POLICY_DESC = "账号黑名单告警数据上报(中心下发规则)";


    public static final String ACCOUNT_BLACKLIST_INNER_POLICY_FILE = "blacklist/account_blacklist/inner_policy_file";
    public static final String ACCOUNT_BLACKLIST_INNER_POLICY_FILE2 = "alarm/account_blacklist/inner_policy_file";

    public static final String ACCOUNT_BLACKLIST_INNER_POLICY_FILE_DESC = "账号黑名单告警文件上传(内置规则)";

    public static final String ACCOUNT_BLACKLIST_CENTER_POLICY_FILE = "blacklist/account_blacklist/center_policy_file";
    public static final String ACCOUNT_BLACKLIST_CENTER_POLICY_FILE2 = "alarm/account_blacklist/center_policy_file";

    public static final String ACCOUNT_BLACKLIST_CENTER_POLICY_FILE_DESC = "账号黑名单告警文件上传(中心下发规则)";


    public static final String ABNORMAL_INNER_POLICY = "alarm/abnormal/inner_policy";

    public static final String ABNORMAL_INNER_POLICY_DESC = "异常告警信息上报(内置规则)";

    public static final String ABNORMAL_INNER_POLICY_PCAP = "alarm/abnormal/inner_policy_file";

    public static final String ABNORMAL_INNER_POLICY_PCAP_DESC = "异常告警文件上传(内置规则)";


    public static final String ALARM_EXTENDED_INNER_POLICY = "alarm/extended/inner_policy";

    public static final String ALARM_EXTENDED_INNER_POLICY_DESC = "扩展告警信息上报(内置规则)";

    public static final String ALARM_EXTENDED_INNER_POLICY_FILE = "alarm/extended/inner_policy_file";

    public static final String ALARM_EXTENDED_INNER_POLICY_FILE_DESC = "扩展告警文件上传(内置规则)";


    public static final String FILE_SELECTION_CENTER_POLICY = "file_filter/center_policy";

    public static final String FILE_SELECTION_CENTER_POLICY_DESC = "文件筛选结果上报";


    public static final String FILE_SELECTION_KEYWORD_FILTER_CENTER_POLICY = "file_filter/keyword_filter/center_policy";

    public static final String FILE_SELECTION_KEYWORD_FILTER_CENTER_POLICY_DESC = "关键词筛选结果上报";

    public static final String FILE_SELECTION_ACCOUNT_FILTER_CENTER_POLICY = "file_filter/account_filter/center_policy";

    public static final String FILE_SELECTION_ACCOUNT_FILTER_CENTER_POLICY_DESC = "账号筛选结果上报";


    public static final String FILE_SELECTION_ENCRYPTION_FILTER_CENTER_POLICY = "file_filter/encryption_filter/center_policy";

    public static final String FILE_SELECTION_ENCRYPTION_FILTER_CENTER_POLICY_DESC = "加密文件筛选结果上报";

    public static final String FILE_SELECTION_CENTER_POLICY_FILE = "file_filter/center_policy_file";

    public static final String FILE_SELECTION_CENTER_POLICY_FILE_DESC = "筛选文件上传";


    public static final String FILE_SELECTION_KEYWORD_FILTER_CENTER_POLICY_FILE = "file_filter/keyword_filter/center_policy_file";

    public static final String FILE_SELECTION_KEYWORD_FILTER_CENTER_POLICY_FILE_DESC = "关键词筛选文件上传";

    public static final String FILE_SELECTION_ACCOUNT_FILTER_CENTER_POLICY_FILE = "file_filter/account_filter/center_policy_file";

    public static final String FILE_SELECTION_ACCOUNT_FILTER_CENTER_POLICY_FILE_DESC = "账号筛选文件上传";


    public static final String FILE_SELECTION_ENCRYPTION_FILTER_CENTER_POLICY_FILE = "file_filter/encryption_filter/center_policy_file";

    public static final String FILE_SELECTION_ENCRYPTION_FILTER_CENTER_POLICY_FILE_DESC = "加密文件筛选文件上传";


    public static final String NET_AUDIT_NET_LOG = "net_audit/net_log";

    public static final String NET_AUDIT_NET_LOG_DESC = "网络通联关系上报";


    public static final String NET_AUDIT_APP_BEHAVIOR = "net_audit/app_behavior";

    public static final String NET_AUDIT_APP_BEHAVIOR_DESC = "应用行为审计上报";


    public static final String NET_AUDIT_UNKNOWN_PROTOCOL_LOG = "net_audit/unknown_protocol/log";

    public static final String NET_AUDIT_UNKNOWN_PROTOCOL_LOG_DESC = "未知协议数据上报";

    public static final String NET_AUDIT_UNKNOWN_PROTOCOL_PCAP_FILE = "net_audit/unknown_protocol/pcap_file";

    public static final String NET_AUDIT_UNKNOWN_PROTOCOL_PCAP_FILE_DESC = "未知协议文件上传";


    public static final String NET_AUDIT_ENCRYPT_PROTOCOL_LOG = "net_audit/encrypt_protocol/log";

    public static final String NET_AUDIT_ENCRYPT_PROTOCOL_LOG_DESC = "加密协议数据上报";

    public static final String NET_AUDIT_ENCRYPT_PROTOCOL_PCAP_FILE = "net_audit/encrypt_protocol/pcap_file";

    public static final String NET_AUDIT_ENCRYPT_PROTOCOL_PCAP_FILE_DESC = "加密协议文件上传";


    public static final String NET_AUDIT_APP_BEHAVIOR_EXTENDED = "net_audit/app_behavior/extended";

    public static final String NET_AUDIT_APP_BEHAVIOR_EXTENDED_DESC = "应用行为扩展数据上报";


    public static final String OBJECT_LISTEN_LOG = "object_listen/log";

    public static final String OBJECT_LISTEN_LOG_DESC = "目标审计日志上报";


    public static final String OBJECT_LISTEN_PCAP_FILE = "object_listen/pcap_file";

    public static final String OBJECT_LISTEN_PCAP_FILE_DESC = "目标审计文件上传";


    public static final String TARGET_IDENT_APPS = "active_object_audit/apps";

    public static final String TARGET_IDENT_APPS_DESC = "应用活动对象审计结果上报";


    public static final String TARGET_IDENT_DEVICES = "active_object_audit/devices";

    public static final String TARGET_IDENT_DEVICES_DESC = "设备活动对象审计结果上报";

    public static final String TARGET_IDENT_ACCOUNTS = "active_object_audit/accounts";

    public static final String TARGET_IDENT_ACCOUNTS_DESC = "账号活动对象审计结果上报";


    public static final String SYS_MANAGER_SYNC_TIME = "sys_manager/sync_time";

    public static final String SYS_MANAGER_SYNC_TIME_DESC = "时间同步接口";


    public static final String EXTENDED_DATA = "extended/data";

    public static final String EXTENDED_DATA_DESC = "扩展数据上报";


    public static final String SYS_MANAGER_UPDATE = "sys_manager/update";

    public static final String SYS_MANAGER_UPDATE_DESC = "系统固件升级";

    public static final String SYS_MANAGER_INNER_POLICY_UPDATE = "sys_manager/inner_policy_update";
    public static final String ATTACHMENT_DOWNLOAD = "sys_manager/attachment_download";
    public static final String SYS_MANAGER_INNER_POLICY_UPDATE_DESC = "内置策略更新";

    public static final String CERT_UPDATE = "sys_manager/cert_update";


    public static final String CERT_UPDATE_DESC = "通信证书更新";


    public static final String SYS_MANAGER_VERSION_CHECK = "sys_manager/version_check";

    public static final String SYS_MANAGER_VERSION_CHECK_DESC = "版本一致性检查命令响应";

    //终端组件接口对接-V2---------------start----------------
    public static final String AGENT_REGISTER_REG_INFO = "register/reg_info";
    public static final String AGENT_REGISTER_REG_INFO_DESC = "终端检测组件组织机构/人员信息查询接口";

    public static final String AGENT_GET_COMPUTER_CLIENT_ID = "register/get_computer_client_id";
    public static final String AGENT_GET_COMPUTER_CLIENT_ID_DESC = "主机唯一编码查询接口";

    public static final String AGENT_REGISTER_REG_REQUEST = "register/reg_request";
    public static final String AGENT_REGISTER_REG_REQUEST_DESC = "终端检测组件注册接口";

    public static final String AGENT_AUTH_LOGIN = "auth/login";
    public static final String AGENT_AUTH_LOGIN_DESC = "终端检测组件认证接口";

    public static final String AGENT_REGISTER_REG_CANCEL = "register/reg_cancel";
    public static final String AGENT_REGISTER_REG_CANCEL_DESC = "终端检测组件注销接口";

    public static final String AGENT_HEARTBEAT = "heartbeat";
    public static final String AGENT_HEARTBEAT_DESC = "终端检测组件心跳";

    public static final String AGENT_INNER_POLICY_UPDATE = "sys_manager/inner_policy_update";
    public static final String AGENT_INNER_POLICY_UPDATE_DESC = "终端检测组件内置策略更新指令响应";

    public static final String AGENT_SYS_MANAGER_UPDATE = "sys_manager/update";
    public static final String AGENT_SYS_MANAGER_UPDATE_DESC = "终端检测组件系统软件升级指令响应";

    public static final String AGENT_COMMAND_RESULT = "sys_manager/command_result";
    public static final String AGENT_COMMAND_RESULT_DESC = "终端检测组件指令响应上报";

    public static final String AGENT_POLICY_RESULT = "sys_manager/policy_result";
    public static final String AGENT_POLICY_RESULT_DESC = "终端检测组件策略响应上报";

    public static final String AGENT_SENSITIVE_KEYWORD_DETECT_ALERT = "sensitive/keyword_detect/alert";
    public static final String AGENT_SENSITIVE_KEYWORD_DETECT_ALERT_DESC = "终端检测组件监测数据上报";

    public static final String AGENT_SENSITIVE_IP_BLACKLIST_ALERT = "alarm/ip_blacklist/alert";
    public static final String AGENT_SENSITIVE_IP_BLACKLIST_ALERT_DESC = "终端检测组件异常通信检测数据上报-IP黑名单";

    public static final String AGENT_SENSITIVE_DOMAIN_BLACKLIST_ALERT = "alarm/domain_blacklist/alert";
    public static final String AGENT_SENSITIVE_DOMAIN_BLACKLIST_ALERT_DESC = "终端检测组件异常通信检测数据上报-域名黑名单";

    public static final String AGENT_ALARM_ATTACK_FILE = "alarm/attack_file";
    public static final String AGENT_ALARM_IP_FILE = "alarm/ip_blacklist/center_policy_file";
    public static final String AGENT_ALARM_DOMAIN_FILE = "alarm/domain_blacklist/center_policy_file";
    public static final String AGENT_ALARM_ATTACK_FILE_DESC = "终端检测组件异常通信检测数据相关文件上报";
    public static final String AGENT_ALARM_IP_FILE_DESC = "终端检测组件异常通信IP黑名单检测数据相关文件上报";
    public static final String AGENT_ALARM_DOMAIN_FILE_DESC = "终端检测组件异常通信域名黑名单检测数据相关文件上报";


    public static final String AGENT_CHECK_DATA_UPLOAD = "check_data/upload";
    public static final String AGENT_CHECK_DATA_UPLOAD_DESC = "检查报告上报";

    public static final String AGENT_SYSMANAGER_CHECKUPDATE = "sys_manager/check_update";
    public static final String AGENT_SYSMANAGER_CHECKUPDATE_DESC = "系统软件手动检测升级接口";


    //终端组件接口对接-V2----------------end----------------


    public static final String AGENT_REGISTER_REG = "register/reg";
    public static final String AGENT_REGISTER_REG_DESC = "终端检测组件注册接口";

    public static final String AGENT_REGISTER_RE_REG = "register/re_reg";
    public static final String AGENT_REGISTER_RE_REG_DESC = "终端检测组件重新注册";


    public static final String AGENT_RESET_POLICY = "reset_policy";

    public static final String AGENT_RESET_POLICY_DESC = "全量策略更新";

    public static final String AGENT_SYSTEM_STATUS = "system_status";

    public static final String AGENT_SYSTEM_STATUS_DESC = "终端检测组件系统运行状态上报";

    public static final String AGENT_SENSITIVE_MSG = "sensitive/msg";

    public static final String AGENT_SENSITIVE_MSG_DESC = "涉密信息检测告警数据上报";

    public static final String AGENT_SENSITIVE = "sensitive";

    public static final String AGENT_SENSITIVE_DESC = "涉密信息检测告警上报";


    public static final String AGENT_SENSITIVE_FILE = "sensitive/file";

    public static final String AGENT_SENSITIVE_FILE_DESC = "涉密信息检测告警文件上报";

    public static final String AGENT_ALARM_MSG = "alarm/msg";

    public static final String AGENT_ALARM_MSG_DESC = "攻击窃密检测告警数据上报";

    public static final String AGENT_ALARM = "alarm";

    public static final String AGENT_ALARM_DESC = "攻击窃密检测告警上报";

    public static final String AGENT_ALARM_FILE = "alarm/file";

    public static final String AGENT_ALARM_FILE_DESC = "攻击窃密检测告警文件上报";

    public static final String AGENT_ORG = "org";

    public static final String AGENT_ORG_DESC = "部门信息";

    /**
     * A.3.4接入设备信息上报
     */
    public static final String TOPOLOGY_REPORT = "topology/report";

    public static final String DEVICE_REPORT = "device_report";

    public static final String DISPOSE_SENSITIVE = "dispose/sensitive";

    public static final String DISPOSE_SENSITIVE_DESC = "泄密事件处置上报";


    public static final String DISPOSE_SENSITIVE_FILE = "dispose/sensitive/file";

    public static final String DISPOSE_SENSITIVE_FILE_DESC = "泄密事件处置文件上报";


    public static final String DISPOSE_ATTACK = "dispose/attack";

    public static final String DISPOSE_ATTACK_DESC = "威胁预警事件处置上报";


    public static final String DISPOSE_ATTACK_FILE = "dispose/attack/file";

    public static final String DISPOSE_ATTACK_FILE_DESC = "威胁预警事件处置文件上报";


    public static final String AGENT_TASK_RESULT = "/task_result";

    public static final String AGENT_TASK_RESULT_DESC = "任务结果上报";

    public static final String AGENT_UNINSTALL_CALLBACK = "uninstall/callback";

    public static final String AGENT_UNINSTALL_CALLBACK_DESC = "终端卸载回调";
}
