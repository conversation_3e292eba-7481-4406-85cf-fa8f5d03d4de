package com.superred.supervisor.gateway.controller.terminal;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.service.terminal.TerminalStatusService;
import com.superred.supervisor.standard.v202505.terminal.status.TerminalBusinessStatusReq;
import com.superred.supervisor.standard.v202505.terminal.status.TerminalSystemAuditReq;
import com.superred.supervisor.standard.v202505.terminal.status.TerminalSystemStatusReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 状态上报接口
 *
 * <AUTHOR>
 * @since 2025/5/29 16:03
 */
@Tag(name = "1.3 状态上报接口(2025-02)")
@RestController
@Slf4j
@RequestMapping("/C2/")
public class TerminalStatusController {


    @Resource
    private TerminalStatusService terminalStatusService;

    /**
     * 每小时上报一次
     *
     * @param req 系统状态
     * @return {@link ApiResponse }
     */
    @PostMapping("/system_status")
    @Operation(summary = "C.3.6.1 系统运行状态上报")
    @ApiOperationSupport(order = 1)
    public ApiResponse<String> systemStatus(@RequestBody TerminalSystemStatusReq req) {

        terminalStatusService.reportSystemStatus(req);
        return ApiResponse.success();
    }


    /**
     * 当终端组件检测到异常时上报
     *
     * @return {@link ApiResponse }
     */
    @PostMapping("/business_status")
    @Operation(summary = "C.3.6.2 业务运行状态上报")
    @ApiOperationSupport(order = 2)
    public ApiResponse<String> businessStatus(@RequestBody List<TerminalBusinessStatusReq> req) {

        terminalStatusService.reportBusinessStatus(req);
        return ApiResponse.success();
    }

    /**
     *  定时上报
     *
     * @return {@link ApiResponse }
     */
    @PostMapping("/system_audit")
    @Operation(summary = "C.3.10 终端日志上报")
    public ApiResponse<String> reportSystemAudit(@RequestBody TerminalSystemAuditReq req) {

        terminalStatusService.reportSystemAudit(req);
        return ApiResponse.success();
    }


}
