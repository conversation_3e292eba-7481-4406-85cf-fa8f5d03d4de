package com.superred.supervisor.common.entity.devices.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 0人工审核。1自动审核
 *
 * <AUTHOR>
 * @since 2025/3/19 9:14
 */
@Getter
@AllArgsConstructor
public enum DeviceAuditType implements IEnum<Integer> {
    /**
     * 人工审核
     */
    MANUAL(0, "人工审核"),
    /**
     * 自动审核
     */
    AUTO(1, "自动审核");

    private final Integer value;
    private final String desc;

}
