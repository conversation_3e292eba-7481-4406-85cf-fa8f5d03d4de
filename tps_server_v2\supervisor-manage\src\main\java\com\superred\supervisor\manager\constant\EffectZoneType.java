package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * <p> 生效范围
 * <p>[{"130104":"1","130105":"2020030302,2020030301"}]
 * <p>当是部分设备的时候后面是逗号隔开的 <b>设备id<b/>
 *
 * <AUTHOR>
 * @since 2025/03/14 14:02
 **/
@Getter
public enum EffectZoneType {

    ALL_DEVICE(1, "全部设备"),
    ALL_DETECTOR(2, "全部监测器"),
    ALL_MANAGER(3, "全部管理系统"),
    ALL_SELF_REGULATION(4, "全部自监管"),
    PART_DEVICE(9, "部分设备");

    private final int id;
    private final String type;

    EffectZoneType(int id, String type) {
        this.id = id;
        this.type = type;
    }
}