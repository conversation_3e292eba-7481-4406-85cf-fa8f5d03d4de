package com.superred.supervisor.common.entity.command;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 版本一致性检查
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_version_check_info")
public class VersionCheckInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 检测器编号
     */
    private String deviceId;

    /**
     * 取值方法
     */
    private String method;

    /**
     * 检查文件名
     */
    private String filename;

    /**
     * 读取的开始偏移
     */
    @TableField(value = "`offset`")
    private String offset;

    /**
     * 读取长度
     */
    private Integer length;

    /**
     * 检查文件完整路径
     */
    private String path;

    /**
     * 本地上传文件计算值
     */
    private String upValue;

    /**
     * 客户端上传文件计算值
     */
    private String checkValue;

    /**
     * 检查结果，0不一致，1一致
     */
    private Integer checkResult;

    /**
     * 数据上传时间
     */
    private Date upTime;

    /**
     * 检查时间
     */
    private Date checkTime;

    /**
     * 指令id
     */
    private String cmdId;


}
