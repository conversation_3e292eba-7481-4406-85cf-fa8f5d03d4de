package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.base.vo.command.PolicyResultVo;
import com.superred.supervision.base.vo.data.AgentCmdRepVO;
import com.superred.supervision.db.entity.IssueAgent;

/**
 * <p>
 * 终端保密检查组件指令表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
public interface IssueAgentService extends IService<IssueAgent> {
    /**
     * 保存上报的指令响应结果
     * @param deviceId 设备id
     * @param cmdRepVO 指令响应结果
     */
    void saveResult(String deviceId, AgentCmdRepVO cmdRepVO);

    /**
     * 保存上报的策略响应结果
     * @param deviceId 设备id
     * @param policyResultVo 策略响应结果
     */
    void savePolicyResult(String deviceId, PolicyResultVo policyResultVo);
}
