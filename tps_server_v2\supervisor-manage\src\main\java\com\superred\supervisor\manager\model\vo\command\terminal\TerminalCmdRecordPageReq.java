package com.superred.supervisor.manager.model.vo.command.terminal;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 终端指令统计Req
 */
@Data
public class TerminalCmdRecordPageReq {

    @Schema(description = "指令id")
    private String cmdId;

    @Schema(description = "指令类型")
    private String cmd;


    @Schema(description = "当前页", example = "1")
    private Integer start;

    @Schema(description = "每页显示条数", example = "10")
    private Integer limit;
}