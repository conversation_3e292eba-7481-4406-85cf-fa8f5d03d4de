package com.superred.supervisor.manager.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;


/**
 * 定时任务线程池配置
 *
 * <AUTHOR>
 * @since 2024/10/29
 */
@Configuration
@EnableScheduling
@Slf4j
public class ScheduleTaskConfig implements SchedulingConfigurer {

    private static final int POOL_SIZE = Runtime.getRuntime().availableProcessors();


    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(taskExecutor());

        log.warn("定时任务线程池配置：POOL_SIZE ： {} ===================", POOL_SIZE);
    }

    @Bean("tpsTaskExecutor")
    public TaskScheduler taskExecutor() {
        ThreadPoolTaskScheduler taskExecutor = new ThreadPoolTaskScheduler();
        taskExecutor.setPoolSize(POOL_SIZE);
        taskExecutor.setThreadNamePrefix("SCHEDULE-TASK-");
        taskExecutor.initialize();
        return taskExecutor;
    }
}