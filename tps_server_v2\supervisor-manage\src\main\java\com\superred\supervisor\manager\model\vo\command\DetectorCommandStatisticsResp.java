package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 指令统计响应结果
 *
 * <AUTHOR>
 * @since 2025/03/11
 **/
@Data
public class DetectorCommandStatisticsResp {

    @Schema(description = "指令ID")
    private String cmdId;

    @Schema(description = "指令类型")
    private String cmd;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "生效范围")
    private Integer deviceCount;

    @Schema(description = "执行结果，取值为：0（成功）、1（失败）")
    private Integer result;

    @Schema(description = "执行结果描述，result为1，说明失败原因")
    private String message;

    @Schema(description = "指令详情")
    private String detail;

    @Schema(description = "执行成功数量")
    private Integer successCount;

    @Schema(description = "执行失败数量")
    private Integer failCount;

    @Schema(description = "执行失败数量")
    private Integer issueCount;

    @Schema(description = "执行失败数量")
    private Integer unIssueCount;

}
