package com.superred.supervision.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervision.db.entity.CheckOpenPort;
import com.superred.supervision.db.mapper.CheckOpenPortMapper;
import com.superred.supervision.db.service.CheckOpenPortService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 开放端口 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Service
public class CheckOpenPortServiceImpl extends ServiceImpl<CheckOpenPortMapper, CheckOpenPort> implements CheckOpenPortService {

}
