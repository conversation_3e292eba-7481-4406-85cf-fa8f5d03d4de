package com.superred.supervisor.manager.service.command;

import com.superred.supervisor.common.entity.command.UpdateFile;
import com.superred.supervisor.manager.model.vo.command.AgentInnerPolicyDTO;
import com.superred.supervisor.manager.model.vo.command.AgentInnerPolicyVO;
import com.superred.supervisor.manager.model.vo.command.CertListReq;
import com.superred.supervisor.manager.model.vo.command.CertUploadResp;
import com.superred.supervisor.manager.model.vo.command.UpdateFileListReq;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UpdateFileService {
    // 终端内置策略
    String AGENT_BUILTIN_POLICY_BUCKET = "agent-built-in-policy";
    // 终端软件包
    String AGENT_SOFTWARE_BUCKET = "agent-software";
    // 监测器内置策略
    String DETECTOR_BUILTIN_POLICY_BUCKET = "detector-built-in-policy";
    // 监测器升级包
    String DETECTOR_SOFTWARE_BUCKET = "detector-software";
    // 监测器通信证书
    String DETECTOR_COMMUNICATION_CERT_BUCKET = "detector-communication-cert";
    // 服务端升级包
    String SERVER_SOFTWARE_BUCKET = "server-software";


    void softwareUpdateUpload(MultipartFile file);

    void innerPolicyUpdateUpload(MultipartFile file);

    List<UpdateFile> list(UpdateFileListReq req);

    void cmdDelete(List<Integer> idList);

    CertUploadResp certUpload(MultipartFile file);

    List<CertUploadResp> certList(CertListReq req);

    void certDelete(CertListReq req);

    AgentInnerPolicyVO agentInnerPolicyUpload(MultipartFile file);

    List<AgentInnerPolicyVO> agentInnerPolicyList(AgentInnerPolicyDTO req);

    void agentInnerPolicyDelete(AgentInnerPolicyDTO req);

}
