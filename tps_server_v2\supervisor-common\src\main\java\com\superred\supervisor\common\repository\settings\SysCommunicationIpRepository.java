package com.superred.supervisor.common.repository.settings;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.settings.SysCommunicationIp;
import com.superred.supervisor.common.mapper.settings.SysCommunicationIpMapper;
import org.springframework.stereotype.Repository;

/**
 * 通信口白名单管理(PSysCommunicationIp) Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-27 15:32:00
 */
@Repository
public class SysCommunicationIpRepository extends ServiceImpl<SysCommunicationIpMapper, SysCommunicationIp> {

}

