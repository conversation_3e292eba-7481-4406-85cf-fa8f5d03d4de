package com.superred.supervisor.common.entity.app;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 检测器运行状态 实体
 *
 * <AUTHOR>
 * @since 2025-03-21 16:53:58
 */
@Data
@TableName("app_system_status")
public class AppSystemStatus {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备编号
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * 表示CPU使用率，取0-100的数值，多个CPU以列表方式上传。
     physical_id： CPU ID，数值类型;
     cpu_usage：CPU使用率百分比，数值类型，取0-100的数值

     */
    @TableField("cpu")
    private String cpu;

    /**
     * 表示内存利用率，取0-100数值
     */
    @TableField("mem")
    private Integer mem;

    /**
     * 表示数据磁盘整体可用空间，单位GB
     */
    @TableField("disk")
    private Integer disk;

    /**
     * 系统运行状态采集
     */
    @TableField("time")
    private LocalDateTime time;

    /**
     * 当检测器由多台服务器组成时，表示服务器的编号
     */
    @TableField("did")
    private Integer did;


}

