package com.superred.supervision.base.util;

import cn.hutool.core.util.StrUtil;
import com.superred.supervision.base.constant.Constants;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/5/6 11:18
 */

public final class RuleUtil {

    private RuleUtil() {
    }

    /**
     * 0000000000000000000000000100000000000000000000000000000000000000
     * 0x2000000000
     *
     */
    private static final Long ZJG = 274877906944L;


    public static void main(String[] args) {
        System.out.println(getRuleType(1789911196653060201L));
    }

    public static int getRuleType(Long ruleId) {
        try {
            String binary = Long.toBinaryString(ruleId);
            binary = StrUtil.fillBefore(binary, '2', 64);
            String subStr = binary.substring(25, 27);
            if ("00".equals(subStr)) {
                return Constants.TYPE_MGR;
            } else if ("10".equals(subStr)) {
                return Constants.TYPE_LOCAL;
            }
        } catch (Exception e) {
            //do nothing
        }
        return Constants.TYPE_INNER;
    }

//    /**
//     *
//     * 是否为本地规则，本地规则 总共64位 26-27位为 10
//     * 0000000000000000000000000100000000000000000000000000000000000000
//     * @param ruleId ruleId
//     * @return 是否
//     */
//    public static boolean  isLocal(Long ruleId) {
//        return (ruleId & ZJG) == ZJG;
//    }
//
//    public static boolean isInner(Long ruleId) {
//       return ruleId == null || ruleId == 0;
//    }

    /**
     * 是否需要上报
     * @param ruleId 规则id
     * @return 是否需要
     */
    public static boolean isReport(Long ruleId) {
        return !isSave(ruleId);
    }

    /**
     * 是否需要保存
     * @param ruleId 规则id
     * @return 是否保存
     */
    public static boolean isSave(Long ruleId) {
        return getRuleType(ruleId) == Constants.TYPE_INNER || getRuleType(ruleId) == Constants.TYPE_LOCAL;
    }

    /**
     * 获取设备类型
     * @param deviceId 设备id
     * @return 设备类型
     */
    public static String deviceType(String deviceId) {
        if (StrUtil.isNotEmpty(deviceId)) {
            if (deviceId.length() >= 7) {
                return deviceId.substring(6, 8);
            }
        }
        return null;

    }


    public static final Map<String, String> RULE_SUB_MODULE_MODULE = new HashMap<>(16);

    static {
        RULE_SUB_MODULE_MODULE.put("trojan", Constants.MODULE_ALARM);
        RULE_SUB_MODULE_MODULE.put("attack", Constants.MODULE_ALARM);
        RULE_SUB_MODULE_MODULE.put("malware", Constants.MODULE_ALARM);
        RULE_SUB_MODULE_MODULE.put("ip_blacklist", Constants.MODULE_ALARM);
        RULE_SUB_MODULE_MODULE.put("domain_blacklist", Constants.MODULE_ALARM);
        RULE_SUB_MODULE_MODULE.put("url_blacklist", Constants.MODULE_ALARM);
        RULE_SUB_MODULE_MODULE.put("abnormal", Constants.MODULE_ALARM);
        RULE_SUB_MODULE_MODULE.put("alarm_ip_whitelist", Constants.MODULE_ALARM);
        RULE_SUB_MODULE_MODULE.put("alarm_hash_whitelist", Constants.MODULE_ALARM);

        RULE_SUB_MODULE_MODULE.put("sensitive_file", Constants.MODULE_SENSITIVE);
        RULE_SUB_MODULE_MODULE.put("file_keyword", Constants.MODULE_SENSITIVE);
        RULE_SUB_MODULE_MODULE.put("file_style", Constants.MODULE_SENSITIVE);
        RULE_SUB_MODULE_MODULE.put("file_seal", Constants.MODULE_SENSITIVE);
        RULE_SUB_MODULE_MODULE.put("file_hash", Constants.MODULE_SENSITIVE);
        RULE_SUB_MODULE_MODULE.put("sensitive_whitelist", Constants.MODULE_SENSITIVE);
        RULE_SUB_MODULE_MODULE.put("file_cover", Constants.MODULE_SENSITIVE);

        RULE_SUB_MODULE_MODULE.put("file_encryption", Constants.MODULE_FILE_SELECTION);
        RULE_SUB_MODULE_MODULE.put("file_attributes", Constants.MODULE_FILE_SELECTION);


        RULE_SUB_MODULE_MODULE.put("net_log", Constants.MODULE_NET_AUDIT);
        RULE_SUB_MODULE_MODULE.put("app_behavior", Constants.MODULE_NET_AUDIT);
        RULE_SUB_MODULE_MODULE.put("unknown_protocol", Constants.MODULE_NET_AUDIT);
        RULE_SUB_MODULE_MODULE.put("encrypt_protocol", Constants.MODULE_NET_AUDIT);
        RULE_SUB_MODULE_MODULE.put("audit_ip_whitelist", Constants.MODULE_NET_AUDIT);
        RULE_SUB_MODULE_MODULE.put("audit_domain_whitelist", Constants.MODULE_NET_AUDIT);

        RULE_SUB_MODULE_MODULE.put("ip_listen", Constants.MODULE_OBJECT_LISTEN);
        RULE_SUB_MODULE_MODULE.put("domain_listen", Constants.MODULE_OBJECT_LISTEN);


        RULE_SUB_MODULE_MODULE.put("target_ident", Constants.MODULE_TARGET_IDENT);


    }


}
