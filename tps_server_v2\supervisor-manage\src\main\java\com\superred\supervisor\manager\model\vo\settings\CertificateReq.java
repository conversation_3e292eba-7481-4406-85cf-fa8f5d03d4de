package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 *  证书上传实体类
 * @since 2025年03月20日
 */
@Data
public class CertificateReq {

    @Schema(description = "名称")
    private String certName;

    @Schema(description = "类型 1监测器  2管理系统")
    private Integer type;

    @Schema(description = "ca证书")
    private MultipartFile caPem;

    @Schema(description = "签名证书")
    private MultipartFile signPem;

    @Schema(description = "加密证书")
    private MultipartFile encPem;

    @Schema(description = "签名私钥")
    private MultipartFile signKey;

    @Schema(description = "加密私钥")
    private MultipartFile encKey;
}
