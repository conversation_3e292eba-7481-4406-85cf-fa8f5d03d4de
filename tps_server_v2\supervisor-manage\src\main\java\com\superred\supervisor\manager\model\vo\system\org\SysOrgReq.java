package com.superred.supervisor.manager.model.vo.system.org;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * q
 *
 * <AUTHOR>
 * @since 2025/3/11 14:56
 */


@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

public class SysOrgReq extends PageReqDTO {


    @Schema(description = "组织名称")
    private String orgName;
}
