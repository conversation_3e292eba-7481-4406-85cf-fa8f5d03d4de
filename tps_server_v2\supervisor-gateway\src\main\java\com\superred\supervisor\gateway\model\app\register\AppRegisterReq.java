package com.superred.supervisor.gateway.model.app.register;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * AgentRegisterReq.
 * 终端组件注册信息.
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppRegisterReq {

    /**
     * 产品软件版本号.
     **/
    @Schema(description = "产品软件版本号", example = "20220303_9873")
    @JsonProperty("soft_version")
    private String softVersion;

    /**
     * 设备信息.
     **/
    @JsonProperty("interface")
    private List<InterfaceReq> interfaces;

    /**
     * 内存总数，表示整个设备的内存大小，单位 MB。
     */
    @Schema(description = "内存总数", example = "1048576")
    @JsonProperty("mem_total")
    private Long memTotal;

    /**
     * CPU信息.
     **/
    @Schema(description = "CPU信息")
    @JsonProperty("cpu_info")
    private List<CpuInfoReq> cpuInfo;

    /**
     * 磁盘信息.
     **/
    @Schema(description = "磁盘信息")
    @JsonProperty("disk_info")
    private List<DiskInfoReq> diskInfo;

    /**
     * 部署单位的名称.
     **/
    @Schema(description = "部署单位的名称")
    @JsonProperty("organs")
    private String organs;

    /**
     * 部署的地理位置.
     **/
    @Schema(description = "部署的地理位置", example = "北京市海淀区复兴路机房")
    @JsonProperty("address")
    private String address;

    /**
     * 地理位置编码.
     **/
    @Schema(description = "地理位置编码", example = "511100")
    @JsonProperty("address_code")
    private String addressCode;

    /**
     * 单位联系人.
     **/
    @Schema(description = "单位联系人")
    @JsonProperty("contact")
    private List<ContactReq> contact;


    /**
     * 备注信息.
     **/
    @Schema(description = "备注信息", example = "首次注册")
    @JsonProperty("memo")
    private String memo;


    /**
     * CPU 信息请求
     * <AUTHOR>
     * @since 2025/07/28
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CpuInfoReq {

        /**
         * cpu核数
         */
        @Schema(description = "cpu核数")
        private Integer core;
        /**
         * 主频
         */
        @Schema(description = "主频")
        private Double clock;
        /**
         *
         */
        @Schema(description = "物理ID")
        @JsonProperty("physical_id")
        private Integer physicalId;
    }


    /**
     * 磁盘信息请求
     * <AUTHOR>
     * @since 2025/07/28
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DiskInfoReq {
        /**
         * 磁盘大小
         */
        @Schema(description = "磁盘大小")
        private Integer size;
        /**
         * 磁盘序列号
         */
        @Schema(description = "磁盘序列号")
        private String serial;


    }

    /**
     * 接口请求
     * <AUTHOR>
     * @since 2025/07/28
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InterfaceReq {


        /**
         * ip地址
         */
        @Schema(description = "ip地址")
        private String ip;
        /**
         * 子网掩码
         */
        @Schema(description = "子网掩码")
        private String netmask;
        /**
         * 网关地址
         */
        @Schema(description = "网关地址")
        private String gateway;
        /**
         * mac地址
         */
        @Schema(description = "mac地址")
        private String mac;

        /**
         * 是否是管理IP
         */
        @Schema(description = "是否是管理IP")
        private Boolean manage;
    }

    /**
     * 联系请求
     * <AUTHOR>
     * @since 2025/07/28
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ContactReq {

        /**
         * 联系人姓名
         */
        @JsonProperty("name")
        @Schema(description = "联系人姓名", example = "张三")
        private String name;
        /**
         * 联系人邮箱
         */
        @JsonProperty("email")
        @Schema(description = "联系人邮箱")
        private String email;
        /**
         * 联系人电话
         */
        @JsonProperty("phone")
        @Schema(description = "联系人电话", example = "13800138000")
        private String phone;
        /**
         * 联系人职务
         */
        @Schema(description = "联系人职务", example = "技术支持")
        private String position;

    }

}
