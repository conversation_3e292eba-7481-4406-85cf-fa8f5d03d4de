package com.superred.supervisor.manager.controller.terminal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.terminal.TerminalAuditDetailResp;
import com.superred.supervisor.manager.model.vo.terminal.TerminalAuditPageReq;
import com.superred.supervisor.manager.model.vo.terminal.TerminalAuditPageResp;
import com.superred.supervisor.manager.model.vo.terminal.TerminalCheckReq;
import com.superred.supervisor.manager.service.terminal.TerminalDeviceInfoService;
import com.superred.supervisor.manager.service.system.SystemSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * 终端审核管理
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Tag(name = "6.0. 终端审核管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agent")
public class TerminalAuditController {


    private static final String MODULE = "接入设备管理";

    @Resource
    private TerminalDeviceInfoService terminalDeviceInfoService;

    @Resource
    private SystemSettingService systemSettingService;


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1. 分页查询")
    @PostMapping("/audit/page")
    public RPage<TerminalAuditPageResp> getDeviceBaseInfoPage(@Valid @RequestBody TerminalAuditPageReq req) {

        IPage<TerminalAuditPageResp> page = terminalDeviceInfoService.getDeviceBaseInfoPage(req);

        return new RPage<>(page);

    }


    @Operation(summary = "2. 设备注册状态")
    @GetMapping("/audit/{deviceId}")
    public R<TerminalAuditDetailResp> getAuditDetail(@PathVariable("deviceId") String deviceId) {
        TerminalAuditDetailResp resp = terminalDeviceInfoService.getAuditDetail(deviceId);
        return R.success(resp);
    }


    @Operation(summary = "3. 审核")
    @PostMapping("/audit/check")
    @SysLogAnn(module = LogTypeConstants.ACCESS_DEVICE_MANAGEMENT, operateType = OperateTypeConstants.TO_EXAMINE, desc = "审核驳回")
    public R<Boolean> check(@Valid @RequestBody TerminalCheckReq req) {
        terminalDeviceInfoService.agentCheck(req);
        return R.success(true);
    }


    @Operation(summary = "4. 获取是否自动审核")
    @GetMapping("/audit/get_auto_check_state")
    public R<Boolean> getAutoCheckState() {

        return R.success(systemSettingService.isAgentAutoAudit());
    }

    /**
     * 更新自动检查状态
     *
     * @param state 状态 0 自动审核 1 手动审核
     * @return {@link R }<{@link Boolean }>
     */
    @Operation(summary = "5. 更新自动审核")
    @PostMapping("/update/auto_check_state/{state}")
    @SysLogAnn(module = LogTypeConstants.ACCESS_DEVICE_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "更新自动审核")
    public R<Boolean> updateAutoCheckState(
            @Parameter(description = "状态 0 自动审核 1 手动审核") @PathVariable String state) {

        systemSettingService.updateAgentAutoAudit(state);

        //自动审核
        terminalDeviceInfoService.autoCheck();
        return R.success(true);
    }
}
