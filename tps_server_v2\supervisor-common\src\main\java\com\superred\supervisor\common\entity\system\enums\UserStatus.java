package com.superred.supervisor.common.entity.system.enums;

/**
 * <AUTHOR>
 *  用户状态
 * @since 2025年03月19日
 */
public enum UserStatus {
    UNAUTHORIZED(0, "未授权"),
    NORMAL(1, "正常"),
    LOCKED(2, "锁定"),
    DISABLED(3, "禁用");

    private final int code;
    private final String name;

    UserStatus(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static UserStatus fromCode(int code) {
        for (UserStatus status : UserStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }

    public static UserStatus fromName(String name) {
        for (UserStatus status : UserStatus.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + name + "]");
    }
}

