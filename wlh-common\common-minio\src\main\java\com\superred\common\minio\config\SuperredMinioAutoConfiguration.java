package com.superred.common.minio.config;

import com.superred.common.minio.core.utils.MinioUploadClient;
import com.superred.common.minio.core.utils.SimpleMinioUploadClient;
import io.minio.MinioClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Data
@Configuration
@Slf4j
@EnableConfigurationProperties({MinioProperties.class})
public class SuperredMinioAutoConfiguration {


    @Resource
    private MinioProperties minioProperties;

    /**
     * "默认存储桶"
     */
    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
                .credentials(minioProperties.getAccessKey(), minioProperties.getSecretKey())
                .endpoint(minioProperties.getEndpoint())
                .build();
    }

    @Bean
    public MinioUploadClient minioUploadUtil() {
        return new MinioUploadClient();
    }

    @Bean
    public SimpleMinioUploadClient simpleMinioUploadClient(MinioClient minioClient) {
        return new SimpleMinioUploadClient(minioProperties, minioClient);
    }

}
