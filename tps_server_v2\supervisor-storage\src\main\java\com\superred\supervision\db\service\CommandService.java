package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.base.vo.command.CommandResultVo;
import com.superred.supervision.db.entity.Command;

/**
 * <p>
 * 指令表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
public interface CommandService extends IService<Command> {
    /**
     * 指令上传结果保存
     * @param deviceId 设备id
     * @param commandResultVo 指令结果
     */
    void saveResult(String deviceId, CommandResultVo commandResultVo);
}
