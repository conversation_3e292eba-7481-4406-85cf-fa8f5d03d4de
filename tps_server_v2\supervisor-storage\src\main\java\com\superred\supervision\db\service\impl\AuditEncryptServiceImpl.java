package com.superred.supervision.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervision.db.entity.AuditEncrypt;
import com.superred.supervision.db.mapper.AuditEncryptMapper;
import com.superred.supervision.db.service.AuditEncryptService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 加密协议审计数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Service
public class AuditEncryptServiceImpl extends ServiceImpl<AuditEncryptMapper, AuditEncrypt> implements AuditEncryptService {

}
