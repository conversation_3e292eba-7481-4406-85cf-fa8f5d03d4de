package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月14日
 */
@Data
public class SysCacheDataThresholdReq {


    @Schema(description = "缓存数据阈值-缓存数据大小")
    @Min(value = 0, message = "缓存数据 取值范围0-500")
    @Max(value = 500, message = "缓存数据 取值范围0-500")
    @NotNull
    private Integer cacheSize;

    @Schema(description = "缓存数据阈值-缓存数据存储时间")
    @Min(value = 48, message = "缓存数据存储时间 取值范围48-168")
    @Max(value = 168, message = "缓存数据存储时间 取值范围48-168")
    @NotNull
    private Integer cacheSaveTime;


}
