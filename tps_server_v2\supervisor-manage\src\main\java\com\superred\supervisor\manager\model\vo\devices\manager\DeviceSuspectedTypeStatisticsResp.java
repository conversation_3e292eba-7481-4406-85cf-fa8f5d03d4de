package com.superred.supervisor.manager.model.vo.devices.manager;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 消息类型统计
 */
@Data

public class DeviceSuspectedTypeStatisticsResp {

    /**
     * 告警详情小类
     */
    @Schema(description = "告警详情小类")
    private String suspectedType;

    /**
     * 告警数量
     */
    @Schema(description = "告警数量")
    private Integer suspectedTypeNum;
}