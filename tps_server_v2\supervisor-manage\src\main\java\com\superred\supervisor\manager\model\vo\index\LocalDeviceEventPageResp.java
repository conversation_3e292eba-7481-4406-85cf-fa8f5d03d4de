package com.superred.supervisor.manager.model.vo.index;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统异常日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@Data

public class LocalDeviceEventPageResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @Schema(description = "异常类型：	1（系统异常）	2（软件异常）	3（安全异常）	4（策略异常） 5（流量异常）")
    private String eventType;

    @Schema(description = "异常产生时间")
    private LocalDateTime time;

    @Schema(description = "告警级别:	0（无风险）	1（一般级）	2（关注级）	3（严重级）	4（紧急级）")
    private String risk;

    @Schema(description = "异常事件描述")
    private String msg;


    @Schema(description = "是否已读 0未读 1已读")
    private Integer status;

}
