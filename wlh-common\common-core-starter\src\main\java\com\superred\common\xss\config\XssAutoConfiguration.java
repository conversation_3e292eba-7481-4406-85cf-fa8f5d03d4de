package com.superred.common.xss.config;

import com.superred.common.xss.core.XssCleanDeserializer;
import com.superred.common.xss.core.XssCleaner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.core.Ordered;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <PERSON> xss
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Slf4j
@AutoConfiguration
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = XssProperties.PREFIX, name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(XssProperties.class)
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
public class XssAutoConfiguration implements WebMvcConfigurer {

    private final XssProperties xssProperties;

    @Bean
    @ConditionalOnMissingBean
    public XssCleaner xssCleaner() {
        return new XssCleaner(xssProperties);
    }


    @Bean
    public Jackson2ObjectMapperBuilderCustomizer xssJacksonCustomizer(XssCleaner xssCleaner) {
        log.warn(">>>>> XSS清理器已启用，所有String类型的字段将进行XSS清理");

        return new XssJackson2ObjectMapperBuilderCustomizer(xssCleaner);
    }

    /**
     * 添加XSS拦截器
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        XssCleanInterceptor interceptor = new XssCleanInterceptor(xssProperties);

        registry.addInterceptor(interceptor)
                .order(Ordered.LOWEST_PRECEDENCE);
    }


    public static class XssJackson2ObjectMapperBuilderCustomizer implements Jackson2ObjectMapperBuilderCustomizer, Ordered {

        private final XssCleaner xssCleaner;

        public XssJackson2ObjectMapperBuilderCustomizer(XssCleaner xssCleaner) {
            this.xssCleaner = xssCleaner;
        }

        @Override
        public void customize(Jackson2ObjectMapperBuilder builder) {
            builder.deserializerByType(String.class, new XssCleanDeserializer(xssCleaner));
        }

        @Override
        public int getOrder() {
            return 2;
        }
    }
}
