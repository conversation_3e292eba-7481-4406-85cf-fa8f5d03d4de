package com.superred.supervisor.manager.model.vo.devices.bak;


import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p> 接入设备信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/20 13:42
 **/
@Data

public class DevicebakDetailResp {


    /**
     * 设备基本信息
     */
    @NotNull(message = "设备基本信息不能为空")
    @Valid
    private DevicebakAddBaseInfoReq deviceBaseInfo;

    /**
     * 联系人
     */
    @Valid
    private List<DevicebakContactAddReq> deviceContacts;

    /**
     * cpu信息
     */
    @Valid
    private List<DevicebakAddCpuInfoReq> deviceCpuInfo;

    /**
     * 磁盘信息
     */
    @Valid
    private List<DevicebakDiskInfoAddReq> deviceDiskInfo;

    /**
     * 管理口网卡信息
     */
    @Valid
    private List<DevicebakInterfaceManageAddReq> deviceInterfaceManages;

}
