package com.superred.supervision.base.constant;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/2/13 10:18
 * 攻击窃密 topic
 */
@Data
@Component
public class AlarmTopic {

    //C.*******.1 木马活动告警信息
    private String msgTrojan = "msg_trojan";

    private String msgTrojanInner = "msg_trojan_inner";

    //C.*******.2 木马活动告警文件
    private String filedescTrojan = "filedesc_trojan";

    //C.*******.1 渗透行为告警信息
    private String msgAttack = "msg_attack";

    //C.*******.2 渗透行为告警文件
    private String filedescAttack = "filedesc_attack";

    //C.*******.1 恶意文件告警信息
    private String msgMalware = "msg_malware";

    //C.*******.2 恶意文件告警文件
    private String filedescMalware = "filedesc_malware";

    //C.*******.1 IP黑名单告警信息
    private String msgIpBlacklist = "msg_ip_blacklist";

    //C.*******.1 IP黑名单告警文件
    private String filedescIpBlacklist = "filedesc_ip_blacklist";

    //C.*******.2 域名黑名单告警信息
    private String msgDomainBlacklist = "msg_domain_blacklist";

    //C.*******.2 域名黑名单告警文件
    private String filedescDomainBlacklist = "filedesc_domain_blacklist";

    //C.*******.3 URL黑名单告警信息
    private String msgUrlBlacklist = "msg_url_blacklist";

    //C.*******.3 URL黑名单告警文件
    private String filedescUrlBlacklist = "filedesc_url_blacklist";

    //C.*******.4 账号黑名单告警信息
    private String msgAccountBlacklist = "msg_account_blacklist";

    //C.*******.4 账号黑名单告警文件
    private String filedescAccountBlacklist = "filedesc_account_blacklist";

    //C.*******.1 异常行为检测告警信息
    private String msgAbnormal = "msg_abnormal";

    //C.*******.1 异常行为检测告警文件
    private String filedescAbnormal = "filedesc_abnormal";

    //C.*******.1 扩展告警信息
    private String msgAlarmExtended = "msg_alarm_extended";

    //C.*******.2 扩展告警文件数据上报
    private String filedescAlarmExtended = "filedesc_alarm_extended";

    //C.*******.2 扩展告警文件
    private String msgFileSelection = "msg_file_selection";

    private String filedescFileSelection = "filedesc_file_selection";
}
