package com.superred.common.core.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.superred.common.core.constant.ResultCode;
import com.superred.common.core.constant.ResultStatus;
import lombok.Data;
import lombok.ToString;

/**
 * Base wrapper for RESTFul response.
 *
 * @param <T> Real data for response.
 * <AUTHOR>
 * @since 2021/5/21 15:40
 */
@ToString
@Data

public final class R<T> {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;


    private R() {
    }

    private R(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private R(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public R(ResultStatus status) {
        this.msg = status.getMessage();
        this.code = status.getResultCode();
    }

    public R(ResultStatus status, T data) {
        this.msg = status.getMessage();
        this.code = status.getResultCode();
        this.data = data;
    }

    public static R<String> success() {
        return new R<>(ResultStatus.SUCCESS);
    }


    public static <T> R<T> success(T data) {
        return new R<>(ResultStatus.SUCCESS, data);
    }

    public static <T> R<T> success(T data, String msg) {
        return new R<>(ResultStatus.SUCCESS.getResultCode(), msg, data);
    }


    public static <T> R<T> failure(Integer code, String msg) {
        return new R<>(code, msg);
    }

    public static <T> R<T> failure(ResultStatus status) {

        return new R<>(status);
    }

    public static <T> R<T> failure(ResultStatus status, String msg) {
        return new R<>(status.getResultCode(), status.getMessage() + msg);
    }


    public R<T> msg(String message) {
        this.msg = message;
        return this;
    }

    public R<T> code(int outCode) {
        this.code = outCode;
        return this;
    }

    public R<T> data(T response) {
        this.data = response;
        return this;
    }

    @JsonIgnore
    public boolean isSuccess() {
        return ResultCode.SUCCESS.equals(this.code);
    }
}
