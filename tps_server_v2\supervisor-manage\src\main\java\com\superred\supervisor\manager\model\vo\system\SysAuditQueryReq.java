package com.superred.supervisor.manager.model.vo.system;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 **/
@Data
public class SysAuditQueryReq extends PageReqDTO {

    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */
    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    private String deviceId;

    /**
     * 操作用户名，如“admin”
     */
    @Schema(description = "操作用户名，如“admin")
    private String user;


    @Schema(description = "ip")
    private String loginIp;

    /**
     * 审计日志类型，manage操作行为审计，platform响应管理平台，detector涉密处理事件，system系统事件审计，monitor本地策略事件
     */
    @Schema(description = "审计日志类型，manage操作行为审计，platform响应管理平台，detector涉密处理事件，system系统事件审计，monitor本地策略事件")
    private String eventType;

    @Schema(description = "操作类型")
    private String optType;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private String beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")

    private String endTime;

    /**
     * 数据类型 1 管理员操作日志   2  业务用户日志
     */
    @Schema(description = "数据类型 1 管理员操作日志   2  业务用户日志")
    private String dataType;

    /**
     * 数据类型 1 管理员操作日志   2  业务用户日志
     */
    @Schema(description = "日志详情")
    private String details;
}
