package com.superred.supervisor.common.entity.devices;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022/6/14 15:55
 **/
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CpuInfoVo {

    /**
     * cpu核数
     */
    @Schema(description = "cpu核数")
    private Integer core;
    /**
     * 主频
     */
    @Schema(description = "主频")
    private Double clock;
    /**
     *
     */
    @Schema(description = "主频")
    private Integer physicalId;
}
