package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleFilterKeyword;
import com.superred.supervisor.common.repository.policy.RuleFilterKeywordRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.model.dto.policy.config.FilterKeywordPolicyConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterKeywordPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterKeywordReq;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterKeywordResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleFilterKeywordService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-11 16:48
 */
@Slf4j
@Service("ruleFilterKeywordService")
public class RuleFilterKeywordServiceImpl implements RuleFilterKeywordService, RuleService {

    @Resource
    private RuleFilterKeywordRepository ruleFilterKeywordRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    // private static final Pattern PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9!\"#$%&'()*+,\\-./:;<=>?@\\[\\\\\\]^_`{|}~]+$");

    @Override
    public IPage<RuleFilterKeywordResp> page(RuleFilterKeywordPageReq ruleFilterKeywordPageReq) {
        // 验证
        this.validateQureyParam(ruleFilterKeywordPageReq);
        // 查询
        List<String> ruleIdList = this.getRuleIdList(ruleFilterKeywordPageReq);
        LambdaQueryWrapper<RuleFilterKeyword> queryWrapper = new LambdaQueryWrapper<RuleFilterKeyword>()
                .like(StrUtil.isNotBlank(ruleFilterKeywordPageReq.getRuleIdStr()), RuleFilterKeyword::getRuleId, ruleFilterKeywordPageReq.getRuleIdStr())
                .eq(ruleFilterKeywordPageReq.getRuleType() != null, RuleFilterKeyword::getRuleType, ruleFilterKeywordPageReq.getRuleType())
                .eq(ruleFilterKeywordPageReq.getRisk() != null, RuleFilterKeyword::getRisk, ruleFilterKeywordPageReq.getRisk())
                .eq(ruleFilterKeywordPageReq.getStatus() != null, RuleFilterKeyword::getStatus, ruleFilterKeywordPageReq.getStatus())
                .like(StrUtil.isNotBlank(ruleFilterKeywordPageReq.getRuleContent()), RuleFilterKeyword::getRuleContent, ruleFilterKeywordPageReq.getRuleContent())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleFilterKeyword::getRuleId, ruleIdList)
                .orderByDesc(RuleFilterKeyword::getUpdateTime);
        Page<RuleFilterKeyword> page = new Page<>(ruleFilterKeywordPageReq.getStart(), ruleFilterKeywordPageReq.getLimit());
        IPage<RuleFilterKeyword> page1 = this.ruleFilterKeywordRepository.page(page, queryWrapper);
        return page1.convert(item -> {
            RuleFilterKeywordResp resp = RuleFilterKeywordResp.fromRuleFilterKeyword(item);
            long detectorCount = this.detectorPolicyRuleExtRepository.count(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getRuleId, item.getRuleId()));
            resp.setDetectorPolicyCount((int) detectorCount);
            long agentCount = this.agentPolicyRuleExtRepository.count(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getRuleId, item.getRuleId()));
            resp.setAgentPolicyCount((int) agentCount);
            return resp;
        });
    }

    @Override
    public RuleFilterKeywordResp getById(Integer id) {
        RuleFilterKeyword ruleFilterKeyword = this.ruleFilterKeywordRepository.getById(id);
        return RuleFilterKeywordResp.fromRuleFilterKeyword(ruleFilterKeyword);
    }

    @Override
    public void save(RuleFilterKeywordReq ruleKeyword) {
        // 验证
        this.validateParam(ruleKeyword);
        // 验重复
        this.validateSaveRepeat(ruleKeyword);
        RuleFilterKeyword ruleFilterKeyword = fromRuleFilterKeywordReq(ruleKeyword);
        // 赋值ruleId
        ruleFilterKeyword.setRuleId(this.ruleIdBuilder.buildRuleId());
        this.ruleFilterKeywordRepository.save(ruleFilterKeyword);
    }

    @Override
    public void edit(RuleFilterKeywordReq ruleKeyword) {
        // 验证ID
        this.validateId(ruleKeyword);
        // 验证参数
        this.validateParam(ruleKeyword);
        // 验重复
        this.validateEditRepeat(ruleKeyword);
        RuleFilterKeyword ruleFilterKeyword = fromRuleFilterKeywordReq(ruleKeyword);
        this.ruleFilterKeywordRepository.update(ruleFilterKeyword, Wrappers.<RuleFilterKeyword>lambdaUpdate().eq(RuleFilterKeyword::getRuleId, ruleKeyword.getRuleId()));
    }

    public static RuleFilterKeyword fromRuleFilterKeywordReq(RuleFilterKeywordReq ruleKeyword) {
        return RuleFilterKeyword.builder()
                .ruleId(ruleKeyword.getRuleId())
                .ruleType(ruleKeyword.getRuleType())
                .ruleDesc(ruleKeyword.getRuleDesc())
                .minMatchCount(ruleKeyword.getMinMatchCount())
                .ruleContent(ruleKeyword.getRuleContent())
                .risk(ruleKeyword.getRisk())
                .status(ruleKeyword.getStatus())
                .updateTime(ruleKeyword.getUpdateTime())
                .ruleContain(ruleKeyword.getRuleContain())
                .ruleExclude(ruleKeyword.getRuleExclude())
                .rulePosition(ruleKeyword.getRulePosition())
                .fileFilterType(ruleKeyword.getFileFilterType())
                .fileFilterMinSize(ruleKeyword.getFileFilterMinSize())
                .fileFilterMaxSize(ruleKeyword.getFileFilterMaxSize())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleFilterKeywordRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 验证查询参数
     *
     * @param ruleFilterKeywordPageReq
     */
    private void validateQureyParam(RuleFilterKeywordPageReq ruleFilterKeywordPageReq) {
        if (StrUtil.isNotBlank(ruleFilterKeywordPageReq.getRuleIdStr())) {
            String ruleIdStr = ruleFilterKeywordPageReq.getRuleIdStr();
            if (!NumberUtil.isLong(ruleIdStr)) {
                throw new BaseBusinessException("输入的策略id有非数字，请重新输入");
            }
            if (Double.parseDouble(ruleIdStr) > Long.MAX_VALUE) {
                throw new BaseBusinessException("输入的策略id超过最大范围，请检查格式后重新输入");
            }
            ruleFilterKeywordPageReq.setRuleId(Long.parseLong(ruleIdStr));
        }
    }

    /**
     * 验证是否在使用
     *
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleFilterKeyword> list = this.ruleFilterKeywordRepository.list(Wrappers.<RuleFilterKeyword>lambdaQuery()
                .in(RuleFilterKeyword::getId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (item.getStatus() == 1) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
            });
        }
    }


    /**
     * 验证重复
     *
     * @param ruleKeyword
     */
    private void validateSaveRepeat(RuleFilterKeywordReq ruleKeyword) {
        LambdaQueryWrapper<RuleFilterKeyword> queryWrapper = new LambdaQueryWrapper<RuleFilterKeyword>()
                .eq(CharSequenceUtil.isNotEmpty(ruleKeyword.getRuleContent()), RuleFilterKeyword::getRuleContent, ruleKeyword.getRuleContent())
                .eq(ruleKeyword.getMinMatchCount() != null, RuleFilterKeyword::getMinMatchCount, ruleKeyword.getMinMatchCount());
        // 对文件类型枚举进行排序
        List<Integer> fileTypeLists = new ArrayList<>();
        if (StrUtil.isNotBlank(ruleKeyword.getFileFilterType())) {
            List<String> fileTypeList = JsonUtil.fromJson(ruleKeyword.getFileFilterType(), new TypeReference<List<String>>() {
            });
            fileTypeList.forEach(item -> {
                fileTypeLists.add(Integer.valueOf(item));
            });
            Collections.sort(fileTypeLists);
            String fileFilterType = JsonUtil.toJson(fileTypeLists);
            ruleKeyword.setFileFilterType(fileFilterType);
        }
        queryWrapper.eq(RuleFilterKeyword::getFileFilterType, ruleKeyword.getFileFilterType());
        List<RuleFilterKeyword> list = this.ruleFilterKeywordRepository.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("策略内容 存在重复数据，请检查");
        }
    }

    /**
     * 验证重复
     *
     * @param ruleKeyword
     */
    private void validateEditRepeat(RuleFilterKeywordReq ruleKeyword) {
        LambdaQueryWrapper<RuleFilterKeyword> queryWrapper = new LambdaQueryWrapper<RuleFilterKeyword>()
                .eq(CharSequenceUtil.isNotEmpty(ruleKeyword.getRuleContent()), RuleFilterKeyword::getRuleContent, ruleKeyword.getRuleContent())
                .eq(ruleKeyword.getMinMatchCount() != null, RuleFilterKeyword::getMinMatchCount, ruleKeyword.getMinMatchCount())
                .ne(RuleFilterKeyword::getRuleId, ruleKeyword.getRuleId());
        // 对文件类型枚举进行排序
        List<Integer> fileTypeLists = new ArrayList<>();
        if (StrUtil.isNotBlank(ruleKeyword.getFileFilterType())) {
            List<String> fileTypeList = Arrays.asList(ruleKeyword.getFileFilterType().split(","));
            if (CollectionUtils.isNotEmpty(fileTypeList)) {
                fileTypeList.forEach(item -> {
                    fileTypeLists.add(Integer.valueOf(item));
                });
            }
            Collections.sort(fileTypeLists);
            String fileFilterType = fileTypeLists.stream().map(item -> {
                return String.valueOf(item);
            }).collect(Collectors.joining(","));
            ruleKeyword.setFileFilterType(fileFilterType);
        }
        queryWrapper.eq(RuleFilterKeyword::getFileFilterType, ruleKeyword.getFileFilterType());
        List<RuleFilterKeyword> list = this.ruleFilterKeywordRepository.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("策略内容 存在重复数据，请检查");
        }
    }

    /**
     * 验证参数
     *
     * @param ruleKeyword
     */
    private void validateParam(RuleFilterKeywordReq ruleKeyword) {
        if (StrUtil.isNotBlank(ruleKeyword.getRuleContent())) {
            // todo 原先传参是base64加密后的
//            ruleKeyword.setRuleContent(Base64.decodeStr(ruleKeyword.getRuleContent()));
            ruleKeyword.setRuleContent(ruleKeyword.getRuleContent());
//            Matcher matcher = PATTERN.matcher(ruleKeyword.getRuleContent());
//            if (!matcher.matches()) {
//                throw new BaseBusinessException("策略内容 格式错误");
//            }
            if (ruleKeyword.getRuleContent().getBytes().length > 512) {
                throw new BaseBusinessException("策略内容 长度不可超过512字节");
            }
        }
        if (StrUtil.isNotBlank(ruleKeyword.getRuleContain())) {
            //ruleKeyword.setRuleContain(Base64.decodeStr(ruleKeyword.getRuleContain()));
            ruleKeyword.setRuleContain(ruleKeyword.getRuleContain());
            if (ruleKeyword.getRuleContain().getBytes().length > 512) {
                throw new BaseBusinessException("包含关键词 长度不可超过512字节");
            }
        }
        if (StrUtil.isNotBlank(ruleKeyword.getRuleExclude())) {
            //ruleKeyword.setRuleExclude(Base64.decodeStr(ruleKeyword.getRuleExclude()));
            ruleKeyword.setRuleExclude(ruleKeyword.getRuleExclude());
            if (ruleKeyword.getRuleExclude().getBytes().length > 512) {
                throw new BaseBusinessException("排除关键词 长度不可超过512字节");
            }
        }
        if (ruleKeyword.getFileFilterMinSize() == null) {
            ruleKeyword.setFileFilterMinSize(0);
        }
        if (ruleKeyword.getFileFilterMaxSize() == null) {
            ruleKeyword.setFileFilterMaxSize(4194304);
        }
    }

    /**
     * 验证ID
     *
     * @param ruleKeyword
     */
    private void validateId(RuleFilterKeywordReq ruleKeyword) {
        if (ruleKeyword.getRuleId() == null) {
            throw new BaseBusinessException("策略ID为空错误");
        }
    }

    /**
     * 获取规则ID列表
     * @param ruleFilterKeywordPageReq
     * @return
     */
    private List<String> getRuleIdList(RuleFilterKeywordPageReq ruleFilterKeywordPageReq) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleFilterKeywordPageReq.getIssueDeviceType())
                || StrUtil.isBlank(ruleFilterKeywordPageReq.getPolicyId())
                || StrUtil.isBlank(ruleFilterKeywordPageReq.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleFilterKeywordPageReq.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleFilterKeywordPageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleFilterKeywordPageReq.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleFilterKeywordPageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 文件筛选 - 关键词筛选策略
        return StrUtil.equals(module, PolicyModuleTwoEnum.KEYWORD_FILTER.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 文件筛选 - 关键词筛选策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleTwoEnum.KEYWORD_FILTER.getKey())
                .moduleStr(PolicyModuleTwoEnum.KEYWORD_FILTER.getValue())
                .moduleParentStr(PolicyModuleOneEnum.FILE_FILTER.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 文件筛选 - 关键词筛选策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleFilterKeyword> list = this.ruleFilterKeywordRepository.list(Wrappers.<RuleFilterKeyword>lambdaQuery()
                .in(RuleFilterKeyword::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<FilterKeywordPolicyConfigDTO> configDTOS = list.stream().map(item -> {
            return FilterKeywordPolicyConfigDTO.getPolicyConfig(item);
        }).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 文件筛选 - 关键词筛选策略
        this.ruleFilterKeywordRepository.update(Wrappers.<RuleFilterKeyword>lambdaUpdate()
                .in(RuleFilterKeyword::getRuleId, ruleIds)
                .set(RuleFilterKeyword::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 文件筛选 - 关键词筛选策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleFilterKeyword> list = this.ruleFilterKeywordRepository.list(Wrappers.<RuleFilterKeyword>lambdaQuery()
                .in(RuleFilterKeyword::getRuleId, ruleIdList));
        List<RuleFilterKeywordResp> respList = new ArrayList<>();
        list.forEach(item -> {
            RuleFilterKeywordResp resp = RuleFilterKeywordResp.fromRuleFilterKeyword(item);
            respList.add(resp);
        });
        policyDetail.setFilterKeywordList(respList);
        return policyDetail;
    }
}
