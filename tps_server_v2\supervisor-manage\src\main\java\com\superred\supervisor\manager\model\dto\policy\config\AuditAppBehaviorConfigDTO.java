package com.superred.supervisor.manager.model.dto.policy.config;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleAuditAppBehavior;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025-04-07 10:51
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AuditAppBehaviorConfigDTO {

    /**
     * 策略ID
     */
    private Long ruleId;

    /**
     * IP地址
     */
    private List<String> ip;

    /**
     * 审计行为类型
     */
    private Integer auditType;

    /**
     * JSON字符串 过滤规则参数
     */
    private Map<String, Object> param;

    /**
     * 过滤类型，满足param过滤参数规则后，是否进行审计
     * 0：不审计，1：审计 默认为0
     * 只有param不为空时，该字段才有意义
     */
    private Integer isAudit;

    /**
     * 策略失效时间
     */
    private String expireTime;


    public static AuditAppBehaviorConfigDTO getPolicyConfig(RuleAuditAppBehavior item) {
        if (item == null) {
            return null;
        }
        return AuditAppBehaviorConfigDTO.builder()
                .ruleId(Long.parseLong(item.getRuleId()))
                .ip(PolicyUtils.strToStrListCommaSplit(item.getIp()))
                .auditType(PolicyUtils.strToInt(item.getAuditType()))
                .param(PolicyUtils.strToMap(item.getParam()))
                .isAudit(0)
                .expireTime(item.getExpireTime())
                .build();
    }
}
