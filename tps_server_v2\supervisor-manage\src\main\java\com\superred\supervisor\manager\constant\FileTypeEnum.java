package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2025-04-07 14:18
 */
@Getter
public enum FileTypeEnum {
    TYPE_1("wps", "wps"),
    TYPE_2("wpt", "wpt"),
    TYPE_3("dps", "dps"),
    TYPE_4("dpt", "dpt"),
    TYPE_5("et", "et"),
    TYPE_6("ett", "ett"),
    TYPE_7("doc", "doc"),
    TYPE_8("docx", "docx"),
    TYPE_9("dot", "dot"),
    TYPE_10("docm", "docm"),
    TYPE_11("xls", "xls"),
    TYPE_12("xlsx", "xlsx"),
    TYPE_13("xlt", "xlt"),
    TYPE_14("xlsm", "xlsm"),
    TYPE_15("ppt", "ppt"),
    TYPE_16("pps", "pps"),
    TYPE_17("pptx", "pptx"),
    TYPE_18("pptm", "pptm"),
    TYPE_19("pot", "pot"),
    TYPE_20("ofd", "ofd"),
    TYPE_21("pdf", "pdf"),
    TYPE_22("eid", "eid"),
    TYPE_23("eis", "eis"),
    TYPE_24("eip", "eip"),
    TYPE_25("uof", "uof"),
    TYPE_26("uot", "uot"),
    TYPE_27("uos", "uos"),
    TYPE_28("uop", "uop"),
    TYPE_29("odt", "odt"),
    TYPE_30("ods", "ods"),
    TYPE_31("odp", "odp");

    private final String key;
    private final String value;

    FileTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

}
