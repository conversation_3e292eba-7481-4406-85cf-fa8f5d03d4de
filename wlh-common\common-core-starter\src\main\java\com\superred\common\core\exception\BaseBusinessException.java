package com.superred.common.core.exception;


import com.superred.common.core.constant.ResultCode;

public class BaseBusinessException extends RuntimeException {

    private Integer code;

    private String msg;

    public BaseBusinessException(String message) {
        super(message);
        this.msg = message;
        this.code = ResultCode.INVALID_PARAMETER_ERROR;
    }

    public BaseBusinessException(String message, Throwable throwable) {
        super(message, throwable);
        this.msg = message;
        this.code = ResultCode.INVALID_PARAMETER_ERROR;
    }

}