package com.superred.supervisor.manager.controller.policy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.policy.AgentPolicyPageReq;
import com.superred.supervisor.manager.model.vo.policy.AgentPolicyReq;
import com.superred.supervisor.manager.model.vo.policy.AgentPolicyResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyHistoryVersionResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyIssueDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyTreeResp;
import com.superred.supervisor.manager.service.policy.AgentPolicyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:08
 */
@Tag(name = "4.0. 终端策略下发")
@RestController
@RequestMapping("/agent/policy")
@Slf4j
@Validated
public class AgentPolicyController {
    @Resource
    private AgentPolicyService agentPolicyService;

    @Operation(summary = "1 分页")
    @GetMapping("/page")
    public RPage<AgentPolicyResp> page(AgentPolicyPageReq agentPolicyPageReq) {
        IPage<AgentPolicyResp> page = this.agentPolicyService.page(agentPolicyPageReq);
        return new RPage<>(page);
    }

//    @Operation(summary = "2 查详情")
//    @GetMapping("/{policyId}")
//    public R<AgentPolicyResp> getById(@PathVariable("policyId") Long policyId) {
//        AgentPolicyResp resp = this.agentPolicyService.getById(policyId);
//        return R.success(resp);
//    }

    @Operation(summary = "2 查详情")
    @GetMapping("/detail")
    public RPage<PolicyIssueDetailResp> detail(AgentPolicyPageReq agentPolicyPageReq) {
        IPage<PolicyIssueDetailResp> page = this.agentPolicyService.detail(agentPolicyPageReq);
        return new RPage<>(page);
    }

    @Operation(summary = "3 新增")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_STRATEGY_ISSUANCE, operateType = OperateTypeConstants.ADD, desc = "添加终端策略下发")
    @PostMapping("/save")
    public R save(@Valid @RequestBody AgentPolicyReq agentPolicyReq) {
        this.agentPolicyService.save(agentPolicyReq);
        return R.success();
    }

//    @Deprecated
//    @Operation(summary = "4 编辑")
//    @SysLogAnn(module = MODEL, action = "编辑", value = "编辑" + MODEL)
//    @PostMapping("/edit")
//    public R edit(@Valid @RequestBody AgentPolicyReq agentPolicyReq) {
//        this.agentPolicyService.edit(agentPolicyReq);
//        return R.success();
//    }
//
//    @Deprecated
//    @Operation(summary = "5 删除")
//    @PostMapping("/del")
//    @SysLogAnn(module = MODEL, action = "删除", value = "删除" + MODEL)
//    public R del(@Valid @RequestBody PolicyBatchIdsReq batchIdsReq) {
//        this.agentPolicyService.del(batchIdsReq);
//        return R.success();
//    }
//
//    @Deprecated
//    @Operation(summary = "6 下发")
//    @PostMapping("/issue")
//    @SysLogAnn(module = MODEL, action = "下发", value = MODEL)
//    public R issue(@Valid @RequestBody AgentPolicyIssueReq agentPolicyIssueReq) {
//        this.agentPolicyService.issue(agentPolicyIssueReq);
//        return R.success();
//    }

    @Operation(summary = "7 获取终端策略模块信息")
    @GetMapping("/list")
    public R<List<PolicyTreeResp>> getPolicyRulesAgent() {
        List<PolicyTreeResp> list = this.agentPolicyService.getPolicyRulesAgent();
        return R.success(list);
    }

    @Operation(summary = "8 获取所有同类型的历史版本")
    @GetMapping("/history/{module}")
    public R<List<PolicyHistoryVersionResp>> getPolicyByType(@PathVariable String module) {
        List<PolicyHistoryVersionResp> list = this.agentPolicyService.getPolicyByModule(module);
        return R.success(list);
    }

}
