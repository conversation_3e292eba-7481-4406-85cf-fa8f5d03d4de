package com.superred.supervisor.manager.model.vo.policy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:38
 */
@Data

public class AgentPolicyReq {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单")
    private String module;

    @Schema(description = "策略名称")
    private String name;

    @Schema(description = "策略版本")
    private String version;

    @Schema(description = "策略中规则个数")
    private Integer num;

    @Schema(description = "策略内容")
    private String config;

    @Schema(description = "策略生成时间")
    private Date createTime;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "下发状态，0未下发，1已下发")
    private Integer issuedStatus;

    @Schema(description = "下发时间")
    private Date issuedTime;

    @Schema(description = "是否是默认策略：0否，1是")
    private Integer isDefault;

    @Schema(description = "下发方式：reset 全量下发，add 增量下发，del 增量删除")
    private String cmd;

    @Schema(description = "规则列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> ruleIds;

    @Schema(description = "设备列表")
    private List<String> deviceIds;
}
