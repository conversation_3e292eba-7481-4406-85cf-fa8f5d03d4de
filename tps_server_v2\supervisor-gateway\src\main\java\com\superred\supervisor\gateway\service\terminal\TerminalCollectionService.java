package com.superred.supervisor.gateway.service.terminal;


import com.superred.supervisor.standard.v202505.terminal.collection.InformationDeviceInfoReq;
import com.superred.supervisor.standard.v202505.terminal.collection.MediumDeviceReq;
import com.superred.supervisor.standard.v202505.terminal.collection.TerminalAppSoftwareReq;
import com.superred.supervisor.standard.v202505.terminal.collection.TerminalDeviceInfoReq;
import com.superred.supervisor.standard.v202505.terminal.collection.TerminalNetworkBehaviorReq;
import com.superred.supervisor.standard.v202505.terminal.collection.WirelessDeviceReq;
import com.superred.supervisor.standard.v202505.terminal.collection.WirelessHotSpotReq;

/**
 * 终端信息采集
 *
 * <AUTHOR>
 * @since 2025/6/3 16:04
 */
public interface TerminalCollectionService {

    /**
     * 上报终端设备信息
     *
     * @param req 终端设备请求
     */
    void reportTerminalDevice(TerminalDeviceInfoReq req);

    /**
     * 上报信息设备信息
     *
     * @param req 信息设备信息请求
     */
    void reportInformationDeviceInfo(InformationDeviceInfoReq req);

    /**
     * 上报介质设备信息
     *
     * @param req 介质设备请求
     */
    void reportMediumDeviceInfo(MediumDeviceReq req);

    /**
     * 上报无线设备信息
     *
     * @param req 无线设备请求
     */
    void reportWirelessDeviceInfo(WirelessDeviceReq req);

    /**
     * 上报无线热点信息
     *
     * @param req 无线热点请求
     */
    void reportWirelessHotspotInfo(WirelessHotSpotReq req);

    /**
     * 上报应用软件信息
     *
     * @param req 应用软件请求
     */
    void reportAppSoftwareInfo(TerminalAppSoftwareReq req);

    /**
     * 上报网络行为信息
     *
     * @param req 网络行为请求
     */
    void reportNetworkBehavior(TerminalNetworkBehaviorReq req);
}
