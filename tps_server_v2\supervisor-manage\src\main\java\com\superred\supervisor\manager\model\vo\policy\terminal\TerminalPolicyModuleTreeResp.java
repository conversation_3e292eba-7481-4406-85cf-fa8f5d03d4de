package com.superred.supervisor.manager.model.vo.policy.terminal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 终端策略模块树响应（兼容前端PolicyTreeResp格式）
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "终端策略模块树响应")
public class TerminalPolicyModuleTreeResp {

    @Schema(description = "模块")
    private String module;

    @Schema(description = "模块名")
    private String label;

    @Schema(description = "子节点")
    private List<TerminalPolicyModuleTreeResp> children;
}
