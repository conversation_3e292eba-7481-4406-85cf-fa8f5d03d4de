package com.superred.supervisor.manager.aop;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.WebUtils;
import com.superred.supervisor.common.entity.system.SysLog;
import com.superred.supervisor.common.repository.system.SysLogRepository;
import com.superred.supervisor.manager.constant.CommonConstants;
import com.superred.supervisor.manager.model.auth.LoginUser;
import com.superred.supervisor.manager.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.MDC;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Set;


/**
 * 系统日志方面
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Aspect
@Slf4j
public class SysLogAspect {

    @Resource
    private SysLogRepository sysLogRepository;

    @Resource(name = "commonExecutor")
    private ThreadPoolTaskExecutor executor;

    private static final Set<String> EXCLUDE_MSG = CollUtil.newHashSet("验证码");

    @Around("@annotation(sysLog)")
    public Object around(ProceedingJoinPoint point, SysLogAnn sysLog) throws Throwable {
        String strClassName = point.getTarget().getClass().getName();
        String strMethodName = point.getSignature().getName();
        log.debug("开始记录操作日志: [类名]:{},[方法]:{}", strClassName, strMethodName);


        // 发送异步日志事件
        Object obj = null;
        Throwable exception = null;
        try {
            obj = point.proceed();
        } catch (Throwable e) {
            exception = e;
        }

        SysLog syslog = this.buildSysLog(sysLog);
        if (exception != null) {
            syslog.setResult(2);
            if (exception instanceof BaseBusinessException) {
                syslog.setDescription(exception.getMessage());
            }
        } else {
            syslog.setResult(1);
        }
        this.saveSysLogAsync(syslog);
        if (exception != null) {
            throw exception;
        }
        return obj;
    }

    private void saveSysLogAsync(SysLog syslog) {

        String description = syslog.getDescription();
        if (CollUtil.isNotEmpty(EXCLUDE_MSG) && EXCLUDE_MSG.stream().anyMatch(description::contains)) {
            return;
        }
        executor.execute(() -> {
            try {
                sysLogRepository.save(syslog);
            } catch (Exception e) {
                log.error("保存操作日志失败", e);
            }
        });
    }


    private SysLog buildSysLog(SysLogAnn sysLog) {
        SysLog log = new SysLog();

        LoginUser loginUser = SecurityUtils.getUserNullable();
        if (Objects.isNull(loginUser)) {
            log.setUsername(MDC.get(CommonConstants.LOG_USERNAME));
        } else {
            log.setUsername(loginUser.getUsername());
            log.setRole(String.valueOf(loginUser.getRoleId()));
            log.setUserId(loginUser.getUserId());
        }

        log.setHostIp(WebUtils.getClientIp());

        log.setOperateType(sysLog.operateType());
        log.setOperateModule(sysLog.module());
//        log.setBehaviourType(sysLog.behaviourType());
        log.setLevel(sysLog.level());
        log.setDescription(sysLog.desc());
        log.setOperateDate(LocalDateTime.now());
        log.setCreateTime(LocalDateTime.now());
        log.setUuid(UUID.fastUUID().toString());

        return log;
    }

}
