package com.superred.supervisor.manager.model.vo.command.terminal;


import com.superred.supervisor.manager.model.vo.command.OperationRangeReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TerminalInnerPolicyUpdateCmdReq extends OperationRangeReq {

    @Schema(description = "内置策略ID")
    @NotNull(message = "内置策略ID不能为空")
    private Integer id;

}
