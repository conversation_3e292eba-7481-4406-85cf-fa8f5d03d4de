package com.superred.supervisor.manager.controller.system;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.supervisor.manager.model.vo.settings.SysAuditLogResp;
import com.superred.supervisor.manager.model.vo.system.SysAuditQueryReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 *  系统操作日志
 * @since 2025年03月14日
 */

@Tag(name = "1.8 审计日志管理")
@RestController
@RequestMapping("/sys_audit")
@Slf4j
public class SysOperatorLogController {


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1.审计日志分页查询")
    @PostMapping("/page")
    public R<SysAuditLogResp> getSysAuditPage(SysAuditQueryReq sysAudit) {
        return null;
    }

}
