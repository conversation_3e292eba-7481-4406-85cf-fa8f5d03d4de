package com.superred.supervisor.manager.model.vo.devices;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TerminalDeviceInfoPageReq {

    @Schema(description = "主机名称")
    private String hostName;


    @Schema(description = "部门ID")
    private List<Integer> orgIdList;

    @Schema(description = "分页起始位置")
    private Integer start;

    @Schema(description = "分页大小")
    private Integer limit;
}
