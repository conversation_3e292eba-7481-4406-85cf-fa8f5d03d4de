package com.superred.supervisor.manager.model.vo.terminal.manage;

import com.baomidou.mybatisplus.annotation.TableField;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 终端设备信息
 * </p>
 *
 */
@Data

public class TerminalStatusDetailResp {


    @Schema(description = "设备编号")
    private String deviceId;

    @Schema(description = "全数字组成的最长为2个字节的字符串，检测器为“01”")
    private String deviceType;


    @Schema(description = "前八位为年月日，下划线后自定义")
    private String softVersion;

    @Schema(description = "设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值")
    @TableField("interface")
    private String interfaces;

    @Schema(description = "内存总数，表示整个设备的内存大小，单位MB。")
    private Long memTotal;

    @Schema(description = "CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。	physical_id：CPU ID，数值类型	core：CPU核心数，数值类型；	clock：CPU主频，数值类型精确到小数点后1位	")
    private String cpuInfo;

    @Schema(description = "磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。	size为数值类型，表示磁盘大小，单位GB；	serial为字符串类型，最长64个字节，表示磁盘序列号")
    private String diskInfo;


    @Schema(description = "行政区域编码类型，表示检测器部署所在地的区域编码。")
    private String addressCode;

    @Schema(description = "注册时间")
    private LocalDateTime registerTime;


    @Schema(description = "审核时间")
    private LocalDateTime verifyTime;

    @Schema(description = "备注信息")
    private String memo;

    @Schema(description = "心跳时间")
    private LocalDateTime heartbeatTime;


    @Schema(description = "ip")
    private String ip;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "终端责任人ID")
    private String userId;

    @Schema(description = "终端责任人姓名")
    private String userName;

    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "主机操作系统")
    private String os;

    @Schema(description = "主机CPU架构")
    private String arch;

    @Schema(description = "终端审核状态 0 审核通过 1  审核不通过 2 待审核 3 禁用 4 注销")
    private Integer status;

    @Schema(description = "终端在线状态 1 在线 2 离线 3 已卸载")
    private Integer connectionStatus;

    @Schema(description = "单位名称")
    private String company;

    @Schema(description = "所属部门名称")
    private String orgName;

    @Schema(description = "磁盘总大小G")
    private Integer totalDiskSize;


    @Schema(description = "激活状态， 1 未激活 2 已激活")
    private Integer activationStatus;

    public static TerminalStatusDetailResp fromAgentDeviceInfo(TerminalAgentInfo agentDeviceInfo) {

        TerminalStatusDetailResp terminalStatusDetailResp = new TerminalStatusDetailResp();
        terminalStatusDetailResp.setDeviceId(agentDeviceInfo.getDeviceId());

        terminalStatusDetailResp.setSoftVersion(agentDeviceInfo.getSoftVersion());
        terminalStatusDetailResp.setInterfaces(agentDeviceInfo.getInterfaces());
        terminalStatusDetailResp.setMemTotal(agentDeviceInfo.getMemTotal());
        terminalStatusDetailResp.setCpuInfo(agentDeviceInfo.getCpuInfo());
        terminalStatusDetailResp.setDiskInfo(agentDeviceInfo.getDiskInfo());
//        terminalStatusDetailResp.setAddressCode(agentDeviceInfo.getAddressCode());
        terminalStatusDetailResp.setRegisterTime(agentDeviceInfo.getRegisterTime());
        terminalStatusDetailResp.setVerifyTime(agentDeviceInfo.getVerifyTime());
        terminalStatusDetailResp.setMemo(agentDeviceInfo.getMemo());
        terminalStatusDetailResp.setHeartbeatTime(agentDeviceInfo.getHeartbeatTime());
        terminalStatusDetailResp.setIp(agentDeviceInfo.getIp());
        terminalStatusDetailResp.setMac(agentDeviceInfo.getMac());
        terminalStatusDetailResp.setUserId(agentDeviceInfo.getUserId());
        terminalStatusDetailResp.setUserName(agentDeviceInfo.getUserName());
        terminalStatusDetailResp.setHostName(agentDeviceInfo.getHostName());
        terminalStatusDetailResp.setOs(agentDeviceInfo.getOs());
        terminalStatusDetailResp.setArch(agentDeviceInfo.getArch());
        terminalStatusDetailResp.setStatus(agentDeviceInfo.getRegisterStatus().getValue());
        terminalStatusDetailResp.setConnectionStatus(agentDeviceInfo.getConnectionStatus().getValue());
        terminalStatusDetailResp.setActivationStatus(agentDeviceInfo.getActivateStatus().getValue());

        return terminalStatusDetailResp;
    }
}
