package com.superred.supervisor.common.repository.policy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.RuleFilterKeyword;
import com.superred.supervisor.common.mapper.policy.RuleFilterKeywordMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2025-03-11 15:49
 */
@Slf4j
@Repository
@AllArgsConstructor
public class RuleFilterKeywordRepository extends ServiceImpl<RuleFilterKeywordMapper, RuleFilterKeyword> {
}
