package com.superred.common.xss.core;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.xss.config.XssProperties;
import com.superred.common.xss.utils.XssUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Entities;
import org.springframework.web.util.HtmlUtils;

/**
 * 默认的XSS清理器实现类，提供HTML内容的安全清理功能
 * 参考：<a href="https://github.com/pig-mesh/pig/tree/jdk17/pig-common/pig-common-xss">pig</a>
 *
 * <AUTHOR>
 * @since 2025/05/31
 */

public class XssCleaner {

    private final XssProperties properties;

    public XssCleaner(XssProperties properties) {
        this.properties = properties;
    }

    /**
     * 获取文档输出设置
     * @param properties PigXss配置属性
     * @return 文档输出设置对象
     */
    private static Document.OutputSettings getOutputSettings(XssProperties properties) {
        return new Document.OutputSettings()
                // 2. 转义，没找到关闭的方法，目前这个规则最少
                .escapeMode(Entities.EscapeMode.xhtml)
                // 3. 保留换行
                .prettyPrint(properties.isPrettyPrint());
    }

    /**
     * 清理HTML内容，根据XSS类型和模式进行处理
     * @param bodyHtml 待清理的HTML内容
     * @return 清理后的HTML内容
     * @throws BaseBusinessException 当模式为validate且内容不合法时抛出异常
     */
    public String clean(String bodyHtml) {
        // 1. 为空直接返回
        if (StrUtil.isBlank(bodyHtml)) {
            return bodyHtml;
        }
        XssProperties.Mode mode = properties.getMode();
        if (XssProperties.Mode.escape == mode) {
            // html 转义
            return HtmlUtils.htmlEscape(bodyHtml, CharsetUtil.UTF_8);
        } else if (XssProperties.Mode.validate == mode) {
            // 校验
            if (Jsoup.isValid(bodyHtml, XssUtil.WHITE_LIST)) {
                return bodyHtml;
            }
            throw new BaseBusinessException("Xss validate fail, input value:" + bodyHtml);
        } else {
            // 4. 清理后的 html
            String escapedHtml = Jsoup.clean(bodyHtml, "", XssUtil.WHITE_LIST, getOutputSettings(properties));
            if (properties.isEnableEscape()) {
                return escapedHtml;
            }
            // 5. 反转义
            return Entities.unescape(escapedHtml);
        }
    }

}
