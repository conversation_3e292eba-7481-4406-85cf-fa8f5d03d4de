package com.superred.supervisor.manager.controller.settings;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.settings.DeviceStatusResp;
import com.superred.supervisor.manager.service.settings.LocalDeviceInfoService;
import com.superred.supervisor.manager.utils.shell.ShellUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 系统维护
 * @since 2025年03月14日
 */


@Tag(name = "5.2 系统配置-系统维护")
@RestController
@RequestMapping("/sys/maintain")
@Slf4j
public class SysMaintainController {



    @Resource
    private LocalDeviceInfoService localDeviceInfoService;


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1.获取服务器状态信息")
    @GetMapping("/status")
    public R<DeviceStatusResp> getSystemStatus() {
        DeviceStatusResp resp = localDeviceInfoService.getSystemStatus();
        return R.success(resp);
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2.系统升级-上传升级包并升级")
    @PostMapping("/upgrade")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_MAINTENANCE, operateType = OperateTypeConstants.SYSTEM_UPGRADE, desc = "系统升级")
    public R<Boolean> upload(@RequestParam(value = "file") MultipartFile file) throws Exception {
        localDeviceInfoService.upgradeSystem(file);
        return R.success(true);

    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "3.关机")
    @GetMapping("/cmd/shutdown")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_MAINTENANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发关机指令")
    public R<Boolean> issCmd() {

        ShellUtils.exec("shutdown");
        return R.success(true);
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "3.1.重启")
    @GetMapping("/cmd/reboot")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_MAINTENANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发重启指令")
    public R<Boolean> issCmdReboot() {
        ShellUtils.exec("reboot");
        return R.success(true);
    }

    @ApiOperationSupport(order = 4)
    @Operation(summary = "4.恢复出厂设置")
    @PostMapping("/cmd/restore/factory")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_MAINTENANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "恢复出厂设置")
    public R<Boolean> issueCmdRestoreFactory() {

        return R.success(true);
    }


}
