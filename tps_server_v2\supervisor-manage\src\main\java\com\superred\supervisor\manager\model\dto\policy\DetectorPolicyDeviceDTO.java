package com.superred.supervisor.manager.model.dto.policy;

import cn.hutool.core.collection.CollectionUtil;
import com.superred.supervisor.common.entity.policy.DetectorPolicyDevice;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-26 16:07
 */
@Data
public class DetectorPolicyDeviceDTO {

    /**
     * 策略ID
     */
    @Schema(description = "策略ID")
    private Long policyId;

    /**
     * 设备id
     */
    @Schema(description = "设备id")
    private String deviceId;

    /**
     * 下发状态
     */
    @Schema(description = "下发状态")
    private Integer status;

    /**
     * 模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单
     */
    @Schema(description = "模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单")
    private String module;

    public static List<DetectorPolicyDeviceDTO> fromDetectorPolicyDevice(List<DetectorPolicyDevice> detectorPolicyDeviceList) {
        List<DetectorPolicyDeviceDTO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(detectorPolicyDeviceList)) {
            detectorPolicyDeviceList.forEach(item -> {
                DetectorPolicyDeviceDTO detectorPolicyDeviceDTO = new DetectorPolicyDeviceDTO();
                detectorPolicyDeviceDTO.setDeviceId(item.getDeviceId());
                detectorPolicyDeviceDTO.setPolicyId(item.getPolicyId());
                detectorPolicyDeviceDTO.setModule(item.getModule());
                detectorPolicyDeviceDTO.setStatus(item.getStatus());
                list.add(detectorPolicyDeviceDTO);
            });
        }
        return list;
    }
}
