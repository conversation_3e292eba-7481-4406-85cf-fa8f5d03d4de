package com.superred.supervisor.manager.model.vo.policy;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-04-02 17:55
 */
@Data

public class PolicyIssueDeviceResp {

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "设备类型")
    private String deviceType;

    @Schema(description = "组织机构")
    private String organs;

    @Schema(description = "部署层级")
    private String level = "1";

    @Schema(description = "失败原因")
    private String message;
}
