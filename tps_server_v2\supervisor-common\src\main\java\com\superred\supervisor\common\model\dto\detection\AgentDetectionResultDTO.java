package com.superred.supervisor.common.model.dto.detection;

import com.superred.supervisor.file.consant.ExportFile;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 终端检测数据上报
 *
 * <AUTHOR>
 * @since 2025/6/4 10:35
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentDetectionResultDTO<Req> {


    public static final String KAFKA_HEADER_NAME = "ExportFile";
    /**
     * 数据源设备ID
     */
    private String srcDevice;


    /**
     * 导出文件的检测平台设备ID
     */
    private String localDeviceId;
    /**
     * 导出数据类型
     */
    private ExportFile exportFile;


    private Req requestBody;

    /**
     * minio存储路径
     */
    private String minioPath;


    private String fileChecksum;
}
