package com.superred.supervision.base.vo.data;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

/**
 * <AUTHOR>
 * @since 2023/4/27 17:52
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(NON_NULL)
public class CommonVo {
    /**
     * 告警id
     */
    private String id;

    private String deviceId;

    private Long ruleId;

    private String time;

    private String module;

    private String objectType;

    private String subModule;

    private Integer risk;

    private Integer type;

    private Integer count;

    private Integer alertType;

    private String blacklistType;

}
