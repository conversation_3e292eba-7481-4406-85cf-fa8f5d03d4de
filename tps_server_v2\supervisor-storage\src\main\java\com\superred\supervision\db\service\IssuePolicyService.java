package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.base.vo.command.PolicyResultVo;
import com.superred.supervision.db.entity.IssuePolicy;

/**
 * <p>
 * 策略下发表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
public interface IssuePolicyService extends IService<IssuePolicy> {
    /**
     * 保存策略上报结果
     * @param deviceId 设备id
     * @param policyResultVo 策略结果
     */
    void saveResult(String deviceId, PolicyResultVo policyResultVo);

}
