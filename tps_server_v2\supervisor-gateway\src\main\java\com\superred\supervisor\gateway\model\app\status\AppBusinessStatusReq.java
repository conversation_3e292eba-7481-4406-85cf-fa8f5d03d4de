package com.superred.supervisor.gateway.model.app.status;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * APP业务状态
 *
 * <AUTHOR>
 **/
@Data
@ToString
public class AppBusinessStatusReq {


    /**
     * 软件版本
     */
    @JsonProperty("soft_version")
    private String softVersion;

    /**
     * 上报时间
     */
    private String time;

    /**
     * 上次加点启动开始运行时长
     */
    private Integer uptime;


    private List<SuspectedInfo> suspected;

    /**
     * 统计间隔内接入文件数
     */
    @JsonProperty("input_file_num")
    private Integer inputFileNum;

    /**
     * 统计间隔内产生的告警数
     */
    @JsonProperty("output_file_num")
    private Integer outputFileNum;

    /**
     * 积压的文件数
     */
    @JsonProperty("backlog_file_num")
    private Integer backlogFileNum;


    /**
     * 可疑信息
     * <AUTHOR>
     * @since 2025/07/28
     */
    @Data
    public static class SuspectedInfo {

        /**
         * 异常类型：	1（系统异常）	2（软件异常）	3（安全异常）	4（策略异常） 5（流量异常）
         */
        @JsonProperty("event_type")
        private Integer eventType;

        /**
         * 告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级
         */
        @JsonProperty("risk")
        private Integer risk;
        /**
         * 异常描述，如“关键词检测服务崩溃”
         */

        private String msg;

        /**
         * 异常发生时间
         */
        private String time;
    }
}
