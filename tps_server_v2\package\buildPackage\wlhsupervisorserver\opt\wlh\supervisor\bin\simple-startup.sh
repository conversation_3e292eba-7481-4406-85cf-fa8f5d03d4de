#!/bin/sh

#开始时间
START_TIME=`date +%s`

# 处理参数
# 参数1 本机IP
LOCAL_IP=$1

# 参数3 数据库密码
MARIADB_PASSWORD=$2


# 备份目录
BACKUP_PATH=/opt/wlh/supervisor/tmp/$START_TIME
mkdir -p $BACKUP_PATH

# 颜色
RED='\E[1;31m'
GREEN='\E[1;32m'
RES='\E[0m'

set_param()
{
    read -r -p "请输入本机IP地址：" LOCAL_IP
    read -r -p "请输入数据库superred账户密码（无密码直接回车即可）：" MARIADB_PASSWORD
}

print_param()
{
    echo ""
    echo "本机IP：$LOCAL_IP"
    echo "数据库superred账户密码：$MARIADB_PASSWORD"
    echo ""
}

check_mysql()
{
    if ! command -v mysql > /dev/null 2>&1; then
        echo "请先安装mariadb/mysql数据库"
        exit 1
    fi
}

check_java()
{
    if ! command -v /opt/wlh/platform/lib/jdk/bin/java > /dev/null 2>&1; then
        echo "请先安装java，如已安装需参照部署说明配置系统JAVA环境变量"
        exit 1
    fi
}

check_curl()
{
    if ! command -v curl > /dev/null 2>&1; then
        echo "请先安装curl"
        exit 1
    fi
}

#初始化设备编号
function init_deviceId() {
  while [ 1=1 ]
  do
	read -p "请输入设备编号 示例0001，请输入：" code
	echo $code|grep "^[0-9]\{4\}$" > /dev/null;
	if [ $? -ne 0 ];then
		echo "设备编号格式错误，请重新输入！"
	else
		/opt/wlh/supervisor/bin/init/update_device_id.sh ${code} supervision_manage
		if [ $? -ne 0 ]; then
      exit 1
    else
      echo "初始化设备编号-结束"
      echo ""
    fi
		break
	fi
  done
}



# 如果传入参数小于1个，需要手动输入参数
if [ $# -lt 1 ]; then
    set_param
fi

echo "运行环境检测-开始"
# 检查mysql/mariadb
check_mysql
# 检查java环境
check_java
check_curl
echo "运行环境检测-结束"

echo "-----------------------------"

echo "备份mysql数据库-开始"
bash /opt/wlh/supervisor/bin/db/dump-mysql.sh $BACKUP_PATH $MARIADB_PASSWORD
echo "备份mysql数据库-结束"

echo "-----------------"

echo "初始化mysql数据库-开始"
bash /opt/wlh/supervisor/bin/db/init-mysql.sh supervision_manage $MARIADB_PASSWORD
if [ $? -ne 0 ]; then
  exit 1
else
  echo "初始化mysql数据库-结束"
  echo ""
fi

#初始化设备编号
init_deviceId


#初始化网卡配置信息
bash /opt/wlh/supervisor/bin/init/init_network_config.sh
if [ $? -ne 0 ]; then
  echo -e "${RED}初始化网卡配置信息失败，请检查网络配置文件/etc/wlh/supervisor/network_config${RES}"
  exit 1
else
  echo "初始化网卡配置信息-结束"
  echo ""
fi

#初始化防火墙日志基于iptables的方案，centos8默认使用了nftables 可能需要兼容
bash /opt/wlh/supervisor/bin/init/init_firewalld_log.sh
if [ $? -ne 0 ]; then
  exit 1
else
  echo "初始化防火墙日志-结束"
  echo ""
fi

echo "初始化LicenseId"
bash /opt/wlh/supervisor/bin/init/init_license_id.sh

echo "启动应用服务-开始"
systemctl restart supervisor-monitor.service
systemctl restart supervisor-web.service
systemctl restart supervisor-gateway.service
systemctl restart supervisor-storage.service
systemctl restart supervisor-report.service
systemctl restart wlhplatformnginx.service
sleep 10
echo "启动应用服务-结束"
echo ""


echo "服务状态检测-开始"
bash /opt/wlh/supervisor/bin/check.sh
echo "服务状态检测-结束"
echo ""

END_TIME=`date +%s`
SUM_TIME=$[ $END_TIME - $START_TIME ]

echo -e "${GREEN}启动完成，耗时:$SUM_TIME秒${RES}"
echo -e "${GREEN}(国密)互联网BM自监管检测平台-访问地址：https://$LOCAL_IP:12300${RES}"

echo -e "${GREEN}！！！开发测试环境访问添加防火墙白名单, 可执行: sh ./init/dev_add_firewall_port.sh${RES}"
echo -e "${GREEN}！！！测试完毕移除白名单, 务必执行: sh ./init/remove_dev_firewall_port.sh${RES}"