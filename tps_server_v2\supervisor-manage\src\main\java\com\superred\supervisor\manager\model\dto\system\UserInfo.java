package com.superred.supervisor.manager.model.dto.system;


import com.superred.supervisor.common.entity.system.SysUser;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 用户信息dto
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data
public class UserInfo implements Serializable {
    /**
     * 用户基本信息
     */
    private SysUser sysUser;
    /**
     * 权限标识集合
     */
    private List<String> permissions;

    /**
     * 角色集合
     */
    private List<String> roles;

    /**
     * 角色编码
     */
    private List<String> roleCodes;


    /**
     * 是否提醒pass修改
     */
    private Boolean isTipPassUpdate;


}
