package com.superred.supervisor.manager.service.command.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.minio.core.utils.MinioUploadClient;
import com.superred.supervisor.common.entity.command.UpdateFile;
import com.superred.supervisor.common.repository.command.UpdateFileRepository;
import com.superred.supervisor.manager.common.enums.command.CommandType;
import com.superred.supervisor.manager.model.vo.command.AgentInnerPolicyDTO;
import com.superred.supervisor.manager.model.vo.command.AgentInnerPolicyVO;
import com.superred.supervisor.manager.model.vo.command.CertListReq;
import com.superred.supervisor.manager.model.vo.command.CertUploadResp;
import com.superred.supervisor.manager.model.vo.command.UpdateFileListReq;
import com.superred.supervisor.manager.service.CmdIdBuilder;
import com.superred.supervisor.manager.service.command.UpdateFileService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpdateFileServiceImpl implements UpdateFileService {
    @Resource
    private UpdateFileRepository updateFileRepository;
    @Resource
    private CmdIdBuilder cmdIdBuilder;
    @Resource
    private MinioUploadClient minioUploadClient;

    @Override
    public void softwareUpdateUpload(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        // 验证文件格式为zip
        if (StrUtil.isBlank(fileName)) {
            throw new BaseBusinessException("文件名称不能为空");
        }
        String softVersion = validateAndBuildSoftVersion(fileName, CommandType.UPDATE);

        // 将文件上传到minio
        String fileMd5 = this.uploadFile(file, DETECTOR_SOFTWARE_BUCKET);

        UpdateFile updateFile = new UpdateFile();
        updateFile.setCmd(CommandType.UPDATE.getValue());
        updateFile.setFilename(fileName);
        updateFile.setSoftVersion(softVersion);
        updateFile.setFilePath("");
        updateFile.setMd5(fileMd5);
        updateFileRepository.save(updateFile);
    }


    @Override
    public void innerPolicyUpdateUpload(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        // 验证文件格式为zip
        if (StrUtil.isBlank(fileName)) {
            throw new BaseBusinessException("文件名称不能为空");
        }
        String softVersion = validateAndBuildSoftVersion(fileName, CommandType.INNER_POLICY_UPDATE);
        // 将文件上传到minio
        String fileMd5 = this.uploadFile(file, DETECTOR_BUILTIN_POLICY_BUCKET);

        UpdateFile updateFile = new UpdateFile();
        updateFile.setCmd(CommandType.INNER_POLICY_UPDATE.getValue());
        updateFile.setFilename(fileName);
        updateFile.setSoftVersion(softVersion);
        updateFile.setFilePath("");
        updateFile.setMd5(fileMd5);
        updateFileRepository.save(updateFile);
    }

    @Override
    public List<UpdateFile> list(UpdateFileListReq req) {
        LambdaQueryWrapper<UpdateFile> query = new LambdaQueryWrapper<>();
        query.eq(UpdateFile::getCmd, req.getCmd());
        query.eq(CharSequenceUtil.isNotBlank(req.getFilename()), UpdateFile::getFilename, req.getFilename());
        query.orderByDesc(UpdateFile::getCreateTime);
        return updateFileRepository.list(query);

    }

    @Override
    public void cmdDelete(List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        updateFileRepository.removeByIds(idList);
    }

    @Override
    public CertUploadResp certUpload(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName)) {
            throw new BaseBusinessException("文件名称不能为空");
        }
        if (!fileName.startsWith("cert_update_")) {
            throw new BaseBusinessException("文件名称格式错误,文件名称格式：cert_update_设备ID_厂商自定义版本号信息");
        }
        String[] arr = fileName.split("_");
        if (arr.length != 4) {
            throw new BaseBusinessException("文件名称格式错误,文件名称格式：cert_update_设备ID_厂商自定义版本号信息");
        }
        // 将文件上传到minio
        String fileMd5 = this.uploadFile(file, DETECTOR_COMMUNICATION_CERT_BUCKET);
        // 存数据库
        UpdateFile updateFile = new UpdateFile();
        updateFile.setCmd(CommandType.CERT_UPDATE.getValue());
        updateFile.setFilename(fileName);
        updateFile.setMd5(fileMd5);
        updateFile.setFilePath("");
        updateFile.setCreateTime(LocalDateTime.now());

        LambdaQueryWrapper<UpdateFile> query = new LambdaQueryWrapper<>();
        query.eq(UpdateFile::getCmd, CommandType.CERT_UPDATE.getValue());
        query.eq(UpdateFile::getMd5, fileMd5);
        UpdateFile dbData = updateFileRepository.getOne(query);
        if (dbData == null) {
            boolean boo = updateFileRepository.save(updateFile);
            if (!boo) {
                throw new BaseBusinessException("数据库错误");
            }
        } else {
            updateFile.setId(dbData.getId());
            updateFileRepository.updateById(updateFile);
        }

        dbData = updateFileRepository.getOne(query);
        if (dbData == null) {
            throw new BaseBusinessException("查询文件ID错误");
        }

        CertUploadResp resp = new CertUploadResp();
        resp.setId(dbData.getId());
        resp.setFilename(dbData.getFilename());
        resp.setCreateTime(dbData.getCreateTime());
        return resp;
    }

    @Override
    public List<CertUploadResp> certList(CertListReq req) {
        LambdaQueryWrapper<UpdateFile> query = new LambdaQueryWrapper<>();
        query.eq(UpdateFile::getCmd, CommandType.CERT_UPDATE.getValue());
        query.like(CharSequenceUtil.isNotBlank(req.getFilename()), UpdateFile::getFilename, req.getFilename());
        List<UpdateFile> list = updateFileRepository.list(query);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(updateFile -> {
            CertUploadResp resp = new CertUploadResp();
            resp.setId(updateFile.getId());
            resp.setFilename(updateFile.getFilename());
            resp.setCreateTime(updateFile.getCreateTime());
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public void certDelete(CertListReq req) {
        // 先查询出磁盘上的文件，删除文件
        LambdaQueryWrapper<UpdateFile> query = new LambdaQueryWrapper<>();
        query.eq(UpdateFile::getCmd, CommandType.CERT_UPDATE.getValue());
        List<Integer> idList = req.getIdList();
        query.in(UpdateFile::getId, idList);
        List<UpdateFile> list = updateFileRepository.list(query);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        list.forEach(item -> {
            if (CharSequenceUtil.isNotBlank(item.getFilePath()) && FileUtil.exist(item.getFilePath())) {
                FileUtil.del(item.getFilePath());
            }
        });
        this.updateFileRepository.removeByIds(idList);
    }

    @Override
    public AgentInnerPolicyVO agentInnerPolicyUpload(MultipartFile file) {
        if (file == null) {
            log.error("文件为空");
            throw new BaseBusinessException("文件为空");
        }
        String fileName = file.getOriginalFilename();
        // 验证文件名称
        if (StrUtil.isBlank(fileName)) {
            throw new BaseBusinessException("文件名称不能为空");
        }
        if (!fileName.startsWith("own_") || !fileName.endsWith(".zip")) {
            log.error("文件名称格式错误,文件名称格式：own_厂商标识_厂商自定义名.zip");
            throw new BaseBusinessException("文件名称格式错误,文件名称格式：own_厂商标识_厂商自定义名.zip");
        }
        String[] arr = fileName.split("_");
        if (arr.length != 3) {
            log.error("文件名称格式错误,文件名称格式：own_厂商标识_厂商自定义名.zip");
            throw new BaseBusinessException("文件名称格式错误,文件名称格式：own_厂商标识_厂商自定义名.zip");
        }
        // 上传到minio
        String md5 = this.uploadFile(file, AGENT_BUILTIN_POLICY_BUCKET);
        UpdateFile updateFile = new UpdateFile();
        updateFile.setCmd("agent_inner_policy_update");
        updateFile.setFilename(fileName);
        updateFile.setMd5(md5);
        updateFile.setFilePath("");
        updateFile.setCreateTime(LocalDateTime.now());
        boolean boo = updateFileRepository.save(updateFile);
        if (!boo) {
            throw new BaseBusinessException("数据库错误");
        }
        LambdaQueryWrapper<UpdateFile> query = new LambdaQueryWrapper<>();
        query.eq(UpdateFile::getCmd, "agent_inner_policy_update");
        query.eq(UpdateFile::getMd5, md5);
        query.orderByDesc(UpdateFile::getCreateTime);
        List<UpdateFile> list = updateFileRepository.list(query);
        if (CollectionUtil.isEmpty(list)) {
            throw new BaseBusinessException("查询文件ID错误");
        }
        AgentInnerPolicyVO agentBuiltInPolicyVO = new AgentInnerPolicyVO();
        agentBuiltInPolicyVO.setId(list.get(0).getId());
        agentBuiltInPolicyVO.setFilename(fileName);
        agentBuiltInPolicyVO.setCreateTime(updateFile.getCreateTime());
        return agentBuiltInPolicyVO;
    }

    @Override
    public List<AgentInnerPolicyVO> agentInnerPolicyList(AgentInnerPolicyDTO req) {
        LambdaQueryWrapper<UpdateFile> query = new LambdaQueryWrapper<>();
        query.eq(UpdateFile::getCmd, "agent_inner_policy_update");
        query.like(CharSequenceUtil.isNotBlank(req.getFilename()), UpdateFile::getFilename, req.getFilename());
        List<UpdateFile> list = updateFileRepository.list(query);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<AgentInnerPolicyVO> agentBuiltInPolicyList = new ArrayList<>(list.size());
        list.forEach(item -> {
            AgentInnerPolicyVO agentBuiltInPolicyVO = new AgentInnerPolicyVO();
            agentBuiltInPolicyVO.setId(item.getId());
            agentBuiltInPolicyVO.setFilename(item.getFilename());
            agentBuiltInPolicyVO.setCreateTime(item.getCreateTime());
            agentBuiltInPolicyList.add(agentBuiltInPolicyVO);
        });
        return agentBuiltInPolicyList;
    }

    @Override
    public void agentInnerPolicyDelete(AgentInnerPolicyDTO req) {
        if (CollectionUtil.isEmpty(req.getIdList())) {
            return;
        }
        // 先查询出磁盘上的文件，删除文件
        LambdaQueryWrapper<UpdateFile> query = new LambdaQueryWrapper<>();
        query.eq(UpdateFile::getCmd, "agent_inner_policy_update");
        query.in(UpdateFile::getId, req.getIdList());
        List<UpdateFile> list = updateFileRepository.list(query);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        for (UpdateFile item : list) {
            if (CharSequenceUtil.isNotBlank(item.getFilePath()) && FileUtil.exist(item.getFilePath())) {
                FileUtil.del(item.getFilePath());
            }
        }
        updateFileRepository.removeByIds(req.getIdList());
    }


    @SneakyThrows
    private String uploadFile(MultipartFile file, String bucket) {
        if (file == null) {
            return null;
        }
        minioUploadClient.makeBucketIfNotExists(bucket, false);
        // 上次文件到minio中
        byte[] bytes = file.getBytes();
        String md5 = MD5.create().digestHex(bytes);
        minioUploadClient.uploadFile(
                bucket,
                "",
                new ByteArrayInputStream(bytes),
                (long) bytes.length,
                md5
        );
        return md5;
    }

    @NotNull
    private String validateAndBuildSoftVersion(String fileName, CommandType update) {
        if (!fileName.endsWith(".zip")) {
            throw new IllegalArgumentException("上传文件错误");
        }
        UpdateFile dbUpdateFile = updateFileRepository.getOne(
                Wrappers.<UpdateFile>lambdaQuery().eq(UpdateFile::getCmd, update.getValue())
                        .eq(UpdateFile::getFilename, fileName)
                        .last("limit 1")
        );
        if (dbUpdateFile != null) {
            throw new IllegalArgumentException("上传文件错误,存在重名文件");
        }

        String softVersion = cmdIdBuilder.buildUploadSoftVersion();
        log.info("length: {}", softVersion.getBytes().length);
        if (CharSequenceUtil.isNotBlank(softVersion) && softVersion.getBytes().length > 32) {
            throw new IllegalArgumentException("产品版本号，不能大于32字节");
        }
        return softVersion;
    }

}
