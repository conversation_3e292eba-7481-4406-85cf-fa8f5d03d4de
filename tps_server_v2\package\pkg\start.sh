#!/bin/bash

SERVER_HOME=$(dirname $(cd `dirname $0`; pwd))
# 日志路径
LOGBACK_LOGPATH=${SERVER_HOME}/logs
# 配置路径
CONFPATH=${SERVER_HOME}/conf
# java 配置参数文件
JVMFILE=${CONFPATH}/jvm.conf
#服务名
APPNAME=${project.name}-${project.version}
# jar路径
RUNJAR=${SERVER_HOME}/lib/${APPNAME}.jar

JVM_OPTS="-Duser.timezone=GMT+08 -server -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${SERVER_HOME}/bin/"
#run!
isStart="false"
isRunning="0"
# 创建日志目录
if [ ! -d ${LOGBACK_LOGPATH} ];then
    mkdir -p ${LOGBACK_LOGPATH}
fi

# 启动
start() {

    # 读取java配置参数
    if [ -f ${JVMFILE} ];then
            jvmParam=$(sed ':a ; N;s/\n/ / ; t a ; ' ${JVMFILE})
            if [ -z "$jvmParam" ];then
                    jvmParam="-Xms256m -Xmx1024m"
            fi
    else
            jvmParam="-Xms256m -Xmx1024m"
    fi

    while ps -ef | grep ${APPNAME} | grep java | grep -v grep > /dev/null
    do
        isStart="true"
        isRunning="1"
        break
    done
    if [ ${isStart} = "false" ]
    then
        echo "Start ${APPNAME} program..."
        nohup /opt/wlh/platform/lib/jdk/bin/java  ${jvmParam} ${JVM_OPTS} -Dspring.config.import=file:${CONFPATH}/prod-config.yaml -Dlogging.file.path=${LOGBACK_LOGPATH}  -jar ${RUNJAR} 1>> /dev/null 2>&1 &
    else
        echo "${APPNAME} is already running..."
    fi
}

#停止
stop() {
    for PID in $(ps -ef | grep ${APPNAME} | grep java | grep -v grep | awk '{ print $2 }'); do
            echo ${PID}
            kill -9 ${PID}
            echo ${APPNAME} $PID were killed!
    done
}

case "$1" in

    start)
        start;;

    stop)
        stop;;

    restart)
        "$0" "stop"
        sleep 3
        "$0" "start"
        ;;
    *)
        echo "Please use start or stop or restart as first argument"
        ;;
esac

