package com.superred.supervisor.manager.model.auth;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 验证码响应
 *
 * <AUTHOR>
 * @since 2025/3/11 13:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KeyPairResp {

    /**
     * SM2密钥对公钥
     */
    @Schema(description = "SM2密钥对公钥")
    private String publicKeyHex;

    /**
     * 密钥对keyId
     */
    @Schema(description = "密钥对keyId")
    private String keyId;
}
