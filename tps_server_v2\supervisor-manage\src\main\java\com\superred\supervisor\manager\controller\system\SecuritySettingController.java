package com.superred.supervisor.manager.controller.system;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.supervisor.manager.model.vo.system.LogSettingReq;
import com.superred.supervisor.manager.model.vo.system.LogSettingResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 *  安全配置
 * @since 2025年03月14日
 */

@Slf4j
@Tag(name = "1.7 安全配置")
@RestController
@AllArgsConstructor
@RequestMapping("/security/setting")
public class SecuritySettingController {


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1.日志设置查询")
    @GetMapping("/log/storage")
    public R<LogSettingResp> get() {
        return null;
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2.日志设置保存")
    @PostMapping("/log/storage/save")
    public R<Boolean> save(@RequestBody @Valid LogSettingReq req) {

        return null;
    }


    @ApiOperationSupport(order = 3)
    @Operation(summary = "3.日志设置保存-安全策略")
    @GetMapping("/log/storage/rest")
    public R<Boolean> reset() {

        return null;
    }
}
