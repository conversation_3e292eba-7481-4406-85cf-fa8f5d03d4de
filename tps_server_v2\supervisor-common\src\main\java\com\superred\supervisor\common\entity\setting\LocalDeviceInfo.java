package com.superred.supervisor.common.entity.setting;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 自监管设备信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("local_device_info")
public class LocalDeviceInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号
     */
    @TableId(value = "device_id")
    private String deviceId;

    /**
     * 全数字组成的最长为2个字节的字符串，检测器为“01”
     */
    private String deviceType;

    /**
     * 内存总数，表示整个设备的内存大小，单位MB。
     */
    private Integer memTotal;

    /**
     * 设备配置信息，表示包括配置的 IP 地址、子网掩码、MAC 地址、网关地址、是否为管理口。
     * ip 为单一IP 地址类型，
     * netmask 为 IP 子 网 类 型，
     * gateway 为单一 IP 地址类型，
     * mac 为 MAC 地址类型，
     * manage 为布尔值
     */
    @TableField("interface")
    private String interfaces;

    /**
     * CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。
     * physical_id：CPU ID，数值类型
     * core：CPU核心数，数值类型；
     * clock：CPU主频，数值类型精确到小数点后1位
     */
    private String cpuInfo;

    /**
     * 磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。
     * size为数值类型，表示磁盘大小，单位GB；
     * serial为字符串类型，最长64个字节，表示磁盘序列号
     */
    private String diskInfo;

    /**
     * 软件版本，前八位为年月日，下划线后自定义
     */
    private String softVersion;

    /**
     * 厂商英文名
     */
    private String vendorName;

    /**
     * 检测器部署的单位名
     */
    private String organs;

    /**
     * 检测器部署的地理位置
     */
    private String address;

    /**
     * 行政区域编码类型，表示检测器部署所在地的区域编码。
     */
    private String addressCode;

    /**
     * 单位联系人信息。
     * name表示联系人，最长为64个字节的字符串；
     * email表示联系人邮件地址，最长为64个字节的字符串；
     * phone表示联系人电话，最长为32字节；
     * position表示联系人职务，最长为64个字节的字符串；
     */
    private String contact;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 注册状态，0（成功），1（失败），2（审核中）
     */
    private Integer registerStatus;

    /**
     * 注册状态描述
     */
    private String registerMessage;

    /**
     * 审核时间
     */
    private Date verifyTime;

    /**
     * 备注信息
     */
    private String memo;

    /**
     * 在线时长
     */
    private Integer uptime;

    /**
     * 心跳时间
     */
    private Date heartbeatTime;

    /**
     * 业务状态时间
     */
    private Date businessTime;

    /**
     * 平台类型 001 国家级 010 省级 011地市级 100 区县级
     */
    private String platformType;

    /**
     * 1行政监管，2行业监管
     */
    private Integer addressType;

    /**
     * 部署单位的公网地址
     */
    private String publicIpAddress;


}
