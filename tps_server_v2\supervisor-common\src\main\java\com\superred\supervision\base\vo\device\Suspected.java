package com.superred.supervision.base.vo.device;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/7/19 16:06
 **/
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class Suspected implements Serializable {

    /**
     * 异常类型：	1（系统异常）	2（软件异常）	3（安全异常）	4（策略异常） 5（流量异常）
     */
    private Integer eventType;

    /**
     * 告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级
     */
    private Integer risk;
    /**
     * 异常描述，如“关键词检测服务崩溃”
     */
    private String msg;

    private String time;
}
