package com.superred.supervisor.manager.repository.command;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.command.IssueAgent;
import com.superred.supervisor.manager.mapper.command.IssueAgentExtMapper;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalCmdRecordPageReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdRecordPageResp;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class IssueAgentExtRepository extends ServiceImpl<IssueAgentExtMapper, IssueAgent> {
    public Page<TerminalCmdRecordPageResp> statisticsPage(TerminalCmdRecordPageReq req) {
        return this.baseMapper.statisticsPage(new Page<>(req.getStart(), req.getLimit()), req);
    }
}
