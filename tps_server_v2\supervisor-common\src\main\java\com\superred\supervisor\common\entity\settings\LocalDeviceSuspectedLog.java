package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 系统异常日志(LocalDeviceSuspectedLog) 实体
 *
 * <AUTHOR>
 * @since 2025-03-27 15:31:59
 */
@Data
@TableName("local_device_suspected_log")
public class LocalDeviceSuspectedLog {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("device_id")
    private String deviceId;

    /**
     * 异常类型：1（系统异常）2（软件异常）3（安全异常）4（策略异常）5（流量异常）
     */
    @TableField("event_type")
    private Integer eventType;

    /**
     * 异常产生时间
     */
    @TableField("time")
    private Date time;

    /**
     * 告警级别:
     0（无风险）
     1（一般级）
     2（关注级）
     3（严重级）
     4（紧急级）
     */
    @TableField("risk")
    private Integer risk;

    /**
     * 异常事件描述
     */
    @TableField("msg")
    private String msg;

    /**
     * 是否上报
     */
    @TableField("report")
    private Integer report;

    /**
     * 是否已读
     */
    @TableField("status")
    private Integer status;

    /**
     * 当检测器由多台服务器组成时，表示服务器的编号
     */
    @TableField("did")
    private Integer did;

}

