package com.superred.supervisor.gateway.controller.app;

import cn.hutool.core.date.DateUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.model.app.status.AppBusinessStatusReq;
import com.superred.supervisor.gateway.model.app.status.AppSyncTimeResp;
import com.superred.supervisor.gateway.model.app.status.AppSystemAuditReq;
import com.superred.supervisor.gateway.model.app.status.AppSystemStatusReq;
import com.superred.supervisor.gateway.service.app.AppStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 状态上报接口
 *
 * <AUTHOR>
 * @since 2025/5/29 16:03
 */
@Tag(name = "D.3.6 状态上报接口(2025-05)")
@RestController
@Slf4j
@RequestMapping("/A2/")
public class AppStatusController {


    @Resource
    private AppStatusService appStatusService;

    /**
     * 每小时上报一次
     *
     */
    @PostMapping("/system_status")
    @Operation(summary = "D.3.6.1 系统运行状态上报")
    @ApiOperationSupport(order = 1)
    public ApiResponse<String> systemStatus(@RequestBody AppSystemStatusReq req) {

        appStatusService.reportSystemStatus(req);
        return ApiResponse.success();
    }


    /**
     * 当APP组件检测到异常时上报
     * 启动认证成功上报一次，之后10分钟一次
     *
     */
    @PostMapping("/business_status")
    @Operation(summary = "D.3.6.2 业务运行状态上报")
    @ApiOperationSupport(order = 2)
    public ApiResponse<String> businessStatus(@RequestBody AppBusinessStatusReq req) {

        appStatusService.reportBusinessStatus(req);
        return ApiResponse.success();
    }


    /**
     *  时间同步接口
     *
     */
    @PostMapping("/sys_manager/sync_time")
    @Operation(summary = "D.3.8 时间同步接口")
    @ApiOperationSupport(order = 3)
    public AppSyncTimeResp syncTime() {

        return AppSyncTimeResp.builder()
                .time(DateUtil.formatDateTime(new Date()))
                .build();
    }

    /**
     *  扩展数据上报接口
     *
     */
    @PostMapping("/extended/data")
    @Operation(summary = "D.3.9 扩展数据上报接口")
    @ApiOperationSupport(order = 4)
    public ApiResponse<String> reportExtended(@RequestBody String req) {

        return ApiResponse.success();
    }

    /**
     *  组件自身的审计日志数据上报
     *
     */
    @PostMapping("/system_audit")
    @Operation(summary = "D.3.10 审计上报")
    @ApiOperationSupport(order = 5)
    public ApiResponse<String> reportSystemAudit(@RequestBody List<AppSystemAuditReq> req) {

        appStatusService.reportSystemAudit(req);
        return ApiResponse.success();
    }


}
