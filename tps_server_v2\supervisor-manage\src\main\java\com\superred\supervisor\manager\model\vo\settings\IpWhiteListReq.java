package com.superred.supervisor.manager.model.vo.settings;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;


/**
 * <AUTHOR>
 *  访问控制白名单
 * @since 2025年03月13日
 */
@Data
public class IpWhiteListReq {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "名称")
    private String name;

    @NotBlank(message = "IP地址范围不可为空")
    @Size(min = 1, max = 500, message = "IP地址范围 长度限制500位")
    @Schema(description = "源IP/源网段(支持单IP，CIDR网段，IP段 - 分隔)")
    private String source;


    @Schema(description = "1：系统访问控制；2：通信访问控制 3 下载服务控制")
    @NotNull(message = "访问控制类型 不可为空")
    @Min(value = 1, message = "访问控制类型选项错误")
    @Max(value = 3, message = "访问控制类型选项错误")
    private Integer aclType;

    @Schema(description = "是否启用")
    @NotNull(message = "应用状态 不可为空")
    private Boolean enabled;


    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 300, message = "备注 长度不能超过300位")
    private String remarks;


}
