package com.superred.supervisor.manager.model.vo.policy.terminal;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端策略下发记录响应
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Data
@Schema(description = "终端策略下发记录响应")
public class TerminalPolicyIssueRecordResp {

    @Schema(description = "策略ID")
    private Long id;

    @Schema(description = "策略编码")
    private String policyCode;

    @Schema(description = "下发名称")
    private String policyName;

    @Schema(description = "策略类型")
    private String module;

    @Schema(description = "策略数量")
    private Integer ruleCount;

    @Schema(description = "下发类型：reset-全量下发，add-增量下发，del-增量删除")
    private String policyType;

    @Schema(description = "下发范围（设备数量）")
    private Integer deviceCount;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String creatorId;
}
