package com.superred.supervisor.gateway.model.app.register;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/6/27 9:15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppRegStatusResp {

    /**
     * 注册状态码 0 审核通过 1 审核不通过 2 待审核
     */
    private Integer status;

    /**
     * 注册状态消息
     */
    private String message;
}
