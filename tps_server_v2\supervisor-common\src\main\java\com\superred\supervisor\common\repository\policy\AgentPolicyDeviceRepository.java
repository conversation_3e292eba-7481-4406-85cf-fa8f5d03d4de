package com.superred.supervisor.common.repository.policy;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.constant.policy.PolicyIssueStatusEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyDevice;
import com.superred.supervisor.common.mapper.policy.AgentPolicyDeviceMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:32
 */
@Slf4j
@Repository
@AllArgsConstructor
public class AgentPolicyDeviceRepository extends ServiceImpl<AgentPolicyDeviceMapper, AgentPolicyDevice> {
    public long issueCount(Long id) {
        return this.count(Wrappers.<AgentPolicyDevice>lambdaQuery()
                .eq(AgentPolicyDevice::getPolicyId, id)
                .eq(AgentPolicyDevice::getStatus, PolicyIssueStatusEnum.ISSUE.getKey()));
    }

    public long unIssueCount(Long id) {
        return this.count(Wrappers.<AgentPolicyDevice>lambdaQuery()
                .eq(AgentPolicyDevice::getPolicyId, id)
                .eq(AgentPolicyDevice::getStatus, PolicyIssueStatusEnum.UN_ISSUE.getKey()));
    }
}
