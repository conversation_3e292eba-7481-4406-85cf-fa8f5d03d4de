package com.superred.supervisor.gateway.service.app;

import com.superred.supervisor.gateway.model.app.detection.AppFileFilterAlertReq;
import org.springframework.web.multipart.MultipartFile;

/**
 * 检测数据上报
 *
 * <AUTHOR>
 */
public interface AppDetectionService {


    /**
     * 文件检测数据上报
     * @param req req
     * @param type (keyword|md5|security_classification_level|secret_level)_filter
     */
    void reportAlter(AppFileFilterAlertReq req, String type);


    /**
     * 文件检测相关文件上报
     *
     * @param fileDesc 文件描述
     * @param type     (keyword|md5|security_classification_level|secret_level)_filter
     * @param singleMultipartFile 文件
     */
    void uploadAlterFile(String fileDesc, String type, MultipartFile singleMultipartFile);
}
