package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月13日
 */
@Data
public class SuperSysRegisterResp {


    @Schema(description = "上级系统配置")
    private SuperSysConfigReq superSysConfig;

    @Schema(description = "ca证书")
    private MultipartFile caFile;

    @Schema(description = "加密证书")
    private MultipartFile encryptFile;

    @Schema(description = "加密私钥")
    private MultipartFile encryptKeyFile;

    @Schema(description = "签名证书")
    private MultipartFile signFile;

    @Schema(description = "签名私钥")
    private MultipartFile signKeyFile;

    @Schema(description = "注册状态")
    private Integer registerState;

    @Schema(description = "注册状态描述")
    private Integer registerStateDecs;

    @Schema(description = "注册时间")
    private Date registerTime;

}
