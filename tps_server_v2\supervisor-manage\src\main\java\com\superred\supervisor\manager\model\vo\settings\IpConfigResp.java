package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月13日
 */
@Data
public class IpConfigResp {

    @Schema(description = "主键")
    private Integer id;

    @Schema(description = "公网ip出口")
    private String ip;

    @Schema(description = "备注")
    @Size(max = 300, message = "备注 长度不能超过300位")
    private String remark;

    @Schema(description = "创建时间")
    private Date createTime;

}
