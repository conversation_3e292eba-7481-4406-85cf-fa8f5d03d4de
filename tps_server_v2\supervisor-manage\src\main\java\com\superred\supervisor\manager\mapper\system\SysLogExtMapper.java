package com.superred.supervisor.manager.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.supervisor.common.entity.system.SysLog;
import com.superred.supervisor.manager.model.vo.system.log.SyslogReq;
import com.superred.supervisor.manager.model.vo.system.log.SyslogResp;
import org.apache.ibatis.annotations.Param;

/**
 * 日志表表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:20
 */
public interface SysLogExtMapper extends BaseMapper<SysLog> {

    Page<SyslogResp> querySysLogList(Page<SyslogResp> page, @Param(value = "query") SyslogReq query);
}

