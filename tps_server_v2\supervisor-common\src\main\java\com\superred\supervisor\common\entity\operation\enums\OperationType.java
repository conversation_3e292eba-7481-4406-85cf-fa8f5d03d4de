package com.superred.supervisor.common.entity.operation.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * policy-策略,command-指令
 *
 * <AUTHOR>
 * @since 2025/7/25 11:11
 */
@Getter
@AllArgsConstructor
public enum OperationType implements IEnum<String> {

    POLICY("policy", "策略"),
    COMMAND("command", "指令");

    private final String value;
    private final String name;

    public static OperationType fromCode(String code) {
        for (OperationType type : OperationType.values()) {
            if (type.getValue().equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown OperationType code: " + code);
    }
}
