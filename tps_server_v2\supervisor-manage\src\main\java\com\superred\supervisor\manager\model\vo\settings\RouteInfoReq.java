package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 *  路由
 * @since 2025年03月12日
 */
@Data
public class RouteInfoReq {

    @Schema(description = "主键")
    private Integer id;

    @Pattern(regexp = "((\\d{1,3}\\.){3}\\d{1,3}(/\\d{1,2})?)(,(\\d{1,3}\\.){3}\\d{1,3}(/\\d{1,2})?)*",
            message = "IP地址范围 格式错误")
    @Schema(description = "目标网络地址")
    private String ip;

    @Pattern(regexp = "((\\d{1,3}\\.){3}\\d{1,3}(/\\d{1,2})?)(,(\\d{1,3}\\.){3}\\d{1,3}(/\\d{1,2})?)*",
            message = "网关格式错误")
    @Schema(description = "网关")
    private String gateway;


    @Schema(description = "网卡名称")
    private String network;
}
