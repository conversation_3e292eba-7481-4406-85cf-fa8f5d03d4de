package com.superred.supervisor.manager.model.auth;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.superred.common.core.jackson.FlexibleLocalDateDeserializer;
import lombok.Data;

import java.time.LocalDate;

/**
 * LicenseInfo.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/9/14 11:25
 */
@Data
public class LicenseInfo {

    /**
     * 厂商
     */
    private String vendor;


    /**
     * 机器码
     */
    private String machine;

    /**
     * license生成时间
     */
    private String generated;

    /**
     * 签名
     */
    private String signature;

    /**
     * 授权类型
     */
    private Integer authorityType;

    /**
     * 有效天数：-1 永久有效
     */
    private Integer authValidDays;

    /**
     * 生效时间
     */
    @JsonDeserialize(using = FlexibleLocalDateDeserializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate validStartDate;

    /**
     * 失效时间
     */
    @JsonDeserialize(using = FlexibleLocalDateDeserializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate validEndDate;

    /**
     * 终端组件授权数
     */
    private Integer agentAuthCount;

    /**
     * 获取签名内容
     *
     * @return 签名内容
     */
    @JsonIgnore
    public String getSignContent() {
        return this.getVendor()
                + " " + this.getMachine()
                + " " + this.getAuthorityType()
                + " " + this.getAuthValidDays()
                + " " + this.getValidStartDate()
                + " " + this.getValidEndDate()
                + " " + this.getAgentAuthCount();
    }
}
