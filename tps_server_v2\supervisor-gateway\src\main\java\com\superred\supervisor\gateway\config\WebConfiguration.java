package com.superred.supervisor.gateway.config;

import com.superred.supervisor.gateway.config.interceptor.AppAgentAuthInterceptor;
import com.superred.supervisor.gateway.config.interceptor.TerminalAgentAuthInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;


/**
 * springmvc web 配置
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Resource
    private TerminalAgentAuthInterceptor terminalAgentAuthInterceptor;
    @Resource
    private AppAgentAuthInterceptor appAgentAuthInterceptor;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {


        registry.addInterceptor(terminalAgentAuthInterceptor)
                .addPathPatterns("/C2/**")
                .order(1);

        registry.addInterceptor(appAgentAuthInterceptor)
                .addPathPatterns("/A2/**")
                .order(2);

//        registry.addInterceptor(licenseAuthInterceptor)
//                .addPathPatterns("/**");
    }
}
