package com.superred.supervisor.gateway.config.interceptor;

import cn.hutool.core.util.StrUtil;
import com.superred.common.core.utils.WebUtils;
import com.superred.supervisor.common.entity.app.AppAgentInfo;
import com.superred.supervisor.gateway.aop.IgnoreLogin;
import com.superred.supervisor.gateway.exception.ApiUnAuthException;
import com.superred.supervisor.gateway.model.auth.LoginAgent;
import com.superred.supervisor.gateway.service.cache.AgentCacheService;
import com.superred.supervisor.gateway.utils.AgentAuthUtils;
import com.superred.supervisor.gateway.utils.WebExtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;


/**
 * 应用程序 身份验证拦截器
 *
 * <AUTHOR>
 * @since 2025/06/30
 */
@Slf4j
@Component
public class AppAgentAuthInterceptor implements HandlerInterceptor {


    @Resource
    private AgentCacheService agentCacheService;


    /**
     *  //fixme 拦截器中应该只拦截登录状态，对于审核状态和激活状态的修改，应该直接让设备登录失效即可
     *
     *
     *
     * @param request
     * @param response
     * @param handler
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (WebUtils.isAuthIgnoredUrl()) {
            return true;
        }
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        if (ignoreAuth(handlerMethod)) {
            return true;
        }

        String deviceId = AgentAuthUtils.getDeviceIdFromHeader();

        if (StrUtil.isEmpty(deviceId)) {
            throw new ApiUnAuthException("设备ID不能为空");
        }
        AgentAuthUtils.addDeviceIdToRequest(deviceId);

        AppAgentInfo appAgentInfo = agentCacheService.cacheAppAgentInfo(deviceId);
        if (appAgentInfo == null) {
            throw new ApiUnAuthException("设备未注册");
        }

        LoginAgent loginAgent = agentCacheService.getLoginAppAgent(deviceId);
        if (loginAgent == null) {
            throw new ApiUnAuthException("设备未登录认证");
        }

        if (!StrUtil.equalsIgnoreCase(WebExtUtils.getCookie("session"),
                loginAgent.getSessionId())) {
            log.info("AgentV2Interceptor deviceId: {}, sessionId不一致，返回401", deviceId);
            throw new ApiUnAuthException("sessionId不一致, 请重新登录");
        }

        return true;
    }


    private boolean ignoreAuth(HandlerMethod handlerMethod) {
        Method method = handlerMethod.getMethod();
        if (method.isAnnotationPresent(IgnoreLogin.class)) {
            return true;
        }
        IgnoreLogin passToken = method.getAnnotation(IgnoreLogin.class);
        if (passToken != null) {
            return true;
        }

        Class<?> beanType = handlerMethod.getBeanType();

        IgnoreLogin ignoreAuth = AnnotationUtils.findAnnotation(beanType, IgnoreLogin.class);
        return ignoreAuth != null;
    }

}
