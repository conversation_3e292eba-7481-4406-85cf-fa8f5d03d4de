package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模块启停指令查询响应参数
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data
public class ModuleSwitchCommandQueryResp {
    /**
     * ID
     */
    @Schema(description = "ID")
    private Long id;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    private String deviceId;

    /**
     * 模块名称
     */
    @Schema(description = "模块名称")
    private String moduleName;

    /**
     * 指令类型（启动/停止）
     */
    @Schema(description = "指令类型（启动/停止）")
    private String commandType;

    /**
     * 命令生成时间
     */
    @Schema(description = "命令生成时间")
    private LocalDateTime createTime;

    /**
     * 下发状态，0未下发，1已下发
     */
    @Schema(description = "下发状态，0未下发，1已下发")
    private Integer issuedStatus;

    /**
     * 下发时间
     */
    @Schema(description = "下发时间")
    private LocalDateTime issuedTime;

    /**
     * 执行结果
     */
    @Schema(description = "执行结果")
    private Integer result;
}
