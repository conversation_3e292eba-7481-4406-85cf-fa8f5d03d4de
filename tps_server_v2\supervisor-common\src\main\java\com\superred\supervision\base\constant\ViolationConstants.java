package com.superred.supervision.base.constant;

/**
 * <AUTHOR>
 * @since 2023/6/29 17:10
 */
public class ViolationConstants {
    /**
     * 主机终终端安全
     */
    public static final String CHECK_TYPE_HOST = "host_security";

    /**
     * 不得存在安装使用多硬盘
     */
    public static final String CHECK_TYPE_HOST_DISK_CHANGE = "disk_change";


    public static final String CHECK_TYPE_HOST_DISK_CHANGE_DESC = "存在更换过硬盘";

    /**
     * 不得存在更换过硬盘
     */
    public static final String CHECK_TYPE_HOST_DISK_MORE = "disk_more";

    public static final String CHECK_TYPE_HOST_DISK_MORE_DESC = "安装使用多硬盘";
    /**
     * 不得存在安装多操作系统
     */
    public static final String CHECK_TYPE_HOST_OS_MORE = "os_more";


    public static final String CHECK_TYPE_HOST_OS_MORE_DESC = "安装了多操作系统";

    /**
     * 不得存在MAC
     */
    public static final String CHECK_TYPE_HOST_MAC_CHANGE = "mac_change";

    public static final String CHECK_TYPE_HOST_MAC_CHANGE_DESC = "发现MAC地址修改";
    /**
     * 主机终终端安全
     */
    public static final String CHECK_TYPE_SYSTEM = "system_security";
    /**
     * 已禁用配置信息中的服务列表
     */
    public static final String CHECK_TYPE_SYSTEM_SERVER_BLACK = "server_black";


    public static final String CHECK_TYPE_SYSTEM_SERVER_BLACK_DESC = "发现黑名单列表中的服务";

    /**
     * 已禁用配置信息中的进程列表
     */
    public static final String CHECK_TYPE_SYSTEM_PROCESS_BLACK = "process_black";

    public static final String CHECK_TYPE_SYSTEM_PROCESS_BLACK_DESC = "发现黑名单列表中的进程";
    /**
     * 已禁用配置信息中的端口号列表
     */
    public static final String CHECK_TYPE_SYSTEM_PORT_BLACK = "port_black";

    public static final String CHECK_TYPE_SYSTEM_PORT_BLACK_DESC = "发现黑名单列表中的端口";
    /**
     * 不得存在空口令或弱口令账户
     */
    public static final String CHECK_TYPE_SYSTEM_WEEK_PASSWORD = "week_password";


    public static final String CHECK_TYPE_SYSTEM_WEEK_PASSWORD_DESC = "发现空口令或弱口令账户";
    /**
     * 密码必须符合复杂性要求
     */
    public static final String CHECK_TYPE_SYSTEM_SAFE_PASSWORD = "safe_password";


    public static final String CHECK_TYPE_SYSTEM_SAFE_PASSWORD_DESC = "未启用密码复杂性要求";
    /**
     * 不允许通过远程桌面服务登录
     */
    public static final String CHECK_TYPE_SYSTEM_REMOTE_HOST = "remote_host";
    /**
     * 未安装虚拟机
     */
    public static final String CHECK_TYPE_SYSTEM_VIRTUAL = "virtual";

    /**
     * USB设备
     */
    public static final String CHECK_TYPE_USB = "usb";
    /**
     * 不得存在违规使用USB存储等移动介质
     */
    public static final String CHECK_TYPE_USB_ILLEGAL = "usb_illegal";
    public static final String CHECK_TYPE_USB_ILLEGAL_DESC = "发现非常规USB设备";
    /**
     * 文件信息
     */
    public static final String CHECK_TYPE_FILE = "file";
    /**
     * 不得存在违规文件
     */
    public static final String CHECK_TYPE_FILE_ILLEGAL = "file_illegal";
    /**
     * 系统配置
     */
    public static final String CHECK_TYPE_CONFIG = "system_config";
    /**
     * 已关闭所有的共享目录，只保留IPC这一默认共享
     */
    public static final String CHECK_TYPE_CONFIG_SHARE_DIR = "share_dir";

    public static final String CHECK_TYPE_CONFIG_SHARE_DIR_DESC = "发现使用存储共享";


}
