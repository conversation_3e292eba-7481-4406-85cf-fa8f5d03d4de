package com.superred.supervisor.manager.controller.index;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.manager.model.common.BatchIdsReq;
import com.superred.supervisor.manager.model.vo.index.IndexBusinessStatusResp;
import com.superred.supervisor.manager.model.vo.index.IndexRuntimeStatusResp;
import com.superred.supervisor.manager.model.vo.index.LocalDeviceEventPageReq;
import com.superred.supervisor.manager.model.vo.index.LocalDeviceEventPageResp;
import com.superred.supervisor.manager.service.system.IndexService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Tag(name = "7.1. 首页")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/index/local")
public class SystemIndexController {
    @Autowired
    private IndexService indexService;

    @Operation(summary = "1. 系统运行状态")
    @ApiOperationSupport(order = 1)
    @GetMapping("/runtime/status")
    public R<IndexRuntimeStatusResp> getRuntimeStatus() {
        IndexRuntimeStatusResp indexRuntimeStatusResp = indexService.getRuntimeStatusHandle();
        return R.success(indexRuntimeStatusResp);
    }


    @Operation(summary = "2. 系统业务状态")
    @ApiOperationSupport(order = 2)
    @GetMapping("/business/status")
    public R<IndexBusinessStatusResp> getBusinessDetail() {
        IndexBusinessStatusResp indexBusinessStatusResp = indexService.getBusinessDetailHandle();
        return R.success(indexBusinessStatusResp);
    }

    @Operation(summary = "3. 系统异常告警")
    @ApiOperationSupport(order = 3)
    @PostMapping("/event_log/page")
    public RPage<LocalDeviceEventPageResp> pageLogs(@RequestBody LocalDeviceEventPageReq req) {
        IPage<LocalDeviceEventPageResp> localDeviceEventPageRespIPage = indexService.pageLogsHandle(req);
        return new RPage<>(localDeviceEventPageRespIPage);
    }

    @Operation(summary = "4. 系统异常已读")
    @ApiOperationSupport(order = 4)
    @PostMapping("/event_log/read")
    public R<String> readLogs(@RequestBody BatchIdsReq req) {
        return indexService.readLogsHandle(req);
    }
}
