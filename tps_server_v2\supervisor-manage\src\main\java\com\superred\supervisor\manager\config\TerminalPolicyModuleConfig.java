package com.superred.supervisor.manager.config;

import com.superred.supervisor.manager.model.vo.policy.terminal.TerminalPolicyModuleTreeResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 终端策略模块配置
 * 使用双层Map保存模块对象，结构清晰直观
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Component
public class TerminalPolicyModuleConfig {

    /**
     * 模块信息对象
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModuleInfo {
        private String moduleCode;              // 模块英文名称
        private String moduleName;              // 模块中文名称
        private List<ModuleInfo> children;      // 子模块列表

        // 便捷构造函数 - 叶子节点
        public ModuleInfo(String moduleCode, String moduleName) {
            this.moduleCode = moduleCode;
            this.moduleName = moduleName;
            this.children = null;
        }

        // 便捷构造函数 - 带子节点
        public ModuleInfo(String moduleCode, String moduleName, ModuleInfo... children) {
            this.moduleCode = moduleCode;
            this.moduleName = moduleName;
            this.children = children != null && children.length > 0 ? Arrays.asList(children) : null;
        }
    }

    /**
     * 终端策略模块静态结构
     * 在一个地方维护完整的层级关系，ModuleInfo包含子节点
     */
    private static final ModuleInfo[] TERMINAL_POLICY_MODULES = {

        // ═══════════════════════════════════════════════════════════════════════════════
        //                          攻击窃密检测（三级结构）
        // ═══════════════════════════════════════════════════════════════════════════════
        new ModuleInfo("alarm", "攻击窃密检测",
            new ModuleInfo("blacklist", "黑名单检测策略",
                new ModuleInfo("ip_blacklist",     "IP黑名单检测策略"),
                new ModuleInfo("domain_blacklist", "域名黑名单检测策略")
            )
        ),

        // ═══════════════════════════════════════════════════════════════════════════════
        //                          文件筛选（两级结构）
        // ═══════════════════════════════════════════════════════════════════════════════
        new ModuleInfo("file_filter", "文件筛选",
            new ModuleInfo("keyword_filter", "关键词筛选策略"),
            new ModuleInfo("md5_filter",     "文件MD5筛选策略")
        )
    };

    /**
     * 缓存的模块树
     */
    private static List<TerminalPolicyModuleTreeResp> terminalPolicyModuleTree;

    /**
     * 项目启动时初始化模块树
     */
    @PostConstruct
    public void initModuleTree() {
        terminalPolicyModuleTree = buildModuleTreeFromStaticData();
    }

    /**
     * 获取终端策略模块树
     */
    public static List<TerminalPolicyModuleTreeResp> getModuleTree() {
        return terminalPolicyModuleTree;
    }

    /**
     * 从静态数组构建模块树
     */
    private List<TerminalPolicyModuleTreeResp> buildModuleTreeFromStaticData() {
        List<TerminalPolicyModuleTreeResp> result = new ArrayList<>();

        // 遍历根节点模块数组
        for (ModuleInfo moduleInfo : TERMINAL_POLICY_MODULES) {
            TerminalPolicyModuleTreeResp node = buildTreeNode(moduleInfo);
            result.add(node);
        }

        return result;
    }

    /**
     * 递归构建树节点
     */
    private TerminalPolicyModuleTreeResp buildTreeNode(ModuleInfo moduleInfo) {
        List<TerminalPolicyModuleTreeResp> children = null;

        // 如果有子节点，递归构建
        if (moduleInfo.getChildren() != null && !moduleInfo.getChildren().isEmpty()) {
            children = new ArrayList<>();
            for (ModuleInfo childInfo : moduleInfo.getChildren()) {
                children.add(buildTreeNode(childInfo));
            }
        }

        return TerminalPolicyModuleTreeResp.builder()
                .module(moduleInfo.getModuleCode())
                .label(moduleInfo.getModuleName())
                .children(children)
                .build();
    }
}
