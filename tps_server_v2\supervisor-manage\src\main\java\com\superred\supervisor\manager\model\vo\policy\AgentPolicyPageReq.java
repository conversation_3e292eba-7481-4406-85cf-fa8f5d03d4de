package com.superred.supervisor.manager.model.vo.policy;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:38
 */
@Data

public class AgentPolicyPageReq extends PageReqDTO {

    @Schema(description = "策略名称")
    private String name;

    @Schema(description = "规则类型：keyword_detect 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单")
    private String module;

    @Schema(description = "下发方式reset 全量，add增量，del增量删除")
    private String cmd;

    @Schema(description = "删除策略id列表")
    private List<Long> ids;

    @Schema(description = "下发策略id")
    private Long policyId;

    @Schema(description = "规则id")
    private String ruleId;

}
