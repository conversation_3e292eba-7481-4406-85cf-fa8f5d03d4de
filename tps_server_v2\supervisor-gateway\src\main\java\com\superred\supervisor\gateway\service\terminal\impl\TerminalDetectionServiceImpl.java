package com.superred.supervisor.gateway.service.terminal.impl;

import com.superred.common.core.utils.JsonUtil;
import com.superred.common.minio.core.utils.SimpleMinioUploadClient;
import com.superred.supervisor.common.model.dto.detection.AgentDetectionResultDTO;
import com.superred.supervisor.file.consant.ExportFile;
import com.superred.supervisor.gateway.exception.ApiBaseException;
import com.superred.supervisor.gateway.kafka.producer.AgentDetectionEventPublisher;
import com.superred.supervisor.gateway.service.terminal.TerminalDetectionService;
import com.superred.supervisor.standard.v202505.terminal.detection.FileDescReq;
import com.superred.supervisor.standard.v202505.terminal.detection.TerminalAlarmReq;
import com.superred.supervisor.standard.v202505.terminal.detection.TerminalFileFilterAlertReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;

import static com.superred.supervisor.common.constant.CommonConstant.ALARM_FILE_MINIO_PATH;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/5/29 14:03
 */
@Service
@Slf4j
public class TerminalDetectionServiceImpl implements TerminalDetectionService {

    @Resource
    private SimpleMinioUploadClient simpleMinioUploadClient;

    @Resource
    private AgentDetectionEventPublisher agentDetectionEventPublisher;

    /**
     * 上报终端文件
     *
     * @param req 终端文件过滤告警请求
     * @param type 告警类型
     */
    @Override
    public void reportFileFilterAlter(TerminalFileFilterAlertReq req, String type) {

        ExportFile exportFileType = ExportFile.getExportFileType(type);
        if (exportFileType == null) {
            log.error("上报终端文件失败，告警类型不支持，类型：{}", type);
            throw new ApiBaseException("上报终端文件失败，告警类型不支持");
        }

        AgentDetectionResultDTO<TerminalFileFilterAlertReq> agentDetectionResultDTO = AgentDetectionResultDTO.<TerminalFileFilterAlertReq>builder()
                .exportFile(exportFileType)
                .requestBody(req)
                .build();

        agentDetectionEventPublisher.sendMessage(agentDetectionResultDTO);


    }

    /**
     * 上报终端检测组件异常通信检测数据
     *
     * @param req   终端告警请求
     * @param type 告警类型
     */
    @Override
    public void agentAlarmAlert(TerminalAlarmReq req, String type) {

        ExportFile exportFileType = ExportFile.getExportFileType(type);
        if (exportFileType == null) {
            log.error("上报终端检测组件异常通信检测数据失败，告警类型不支持，类型：{}", type);
            throw new ApiBaseException("上报终端检测组件异常通信检测数据失败，告警类型不支持");
        }

        AgentDetectionResultDTO<TerminalAlarmReq> agentDetectionResultDTO = AgentDetectionResultDTO.<TerminalAlarmReq>builder()
                .exportFile(exportFileType)
                .requestBody(req)
                .build();

        agentDetectionEventPublisher.sendMessage(agentDetectionResultDTO);

    }

    /**
     * 上传攻击文件
     *
     * @param fileDesc            文件描述
     * @param type                文件类型
     * @param singleMultipartFile 单个上传的文件
     */
    @Override
    public void uploadAttackFile(String fileDesc, String type, MultipartFile singleMultipartFile) {

        FileDescReq fileDescReq = JsonUtil.fromJson(fileDesc, FileDescReq.class);
        if (fileDescReq == null) {
            log.error("上传攻击文件失败，文件描述解析失败，类型：{}, {}", type, fileDesc);
            throw new ApiBaseException("上传攻击文件失败，文件描述解析失败");
        }

        try (InputStream inputStream = singleMultipartFile.getInputStream()) {
            simpleMinioUploadClient.uploadFileSync(ALARM_FILE_MINIO_PATH, fileDescReq.getChecksum(), inputStream);
        } catch (Exception e) {
            log.error("上传攻击文件失败，文件描述：{}，类型：{}，异常：{}", fileDesc, type, e.getMessage(), e);
            throw new ApiBaseException("上传攻击文件失败", e);
        }

        log.info("上传攻击文件成功，文件描述：{}，类型：{}", fileDesc, type);

        AgentDetectionResultDTO<FileDescReq> agentDetectionResultDTO = AgentDetectionResultDTO.<FileDescReq>builder()
                .exportFile(ExportFile.ALARM_SOURCE_FILE)
                .requestBody(fileDescReq)
                .minioPath(ALARM_FILE_MINIO_PATH + fileDescReq.getChecksum())
                .fileChecksum(fileDescReq.getChecksum())
                .build();

        // 发送消息到Kafka
        agentDetectionEventPublisher.sendMessage(agentDetectionResultDTO);
    }
}
