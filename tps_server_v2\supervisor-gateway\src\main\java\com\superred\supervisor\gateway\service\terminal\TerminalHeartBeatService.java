package com.superred.supervisor.gateway.service.terminal;


import com.superred.supervisor.standard.v202505.terminal.cmd.TerminalCmdResp;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 终端心跳服务
 *
 * <AUTHOR>
 * @since 2025/5/20 9:58
 */
public interface TerminalHeartBeatService {

    /**
     * 处理终端心跳
     *
     * @return 返回处理结果列表
     */
    List<TerminalCmdResp> handleHeatbeat();

    /**
     * 内置策略更新指令响应
     *
     * @param filename 需要更新的策略文件名
     * @param response HttpServletResponse对象，用于发送响应
     */
    void innerPolicyUpdateFile(String filename, HttpServletResponse response);

    /**
     * 系统软件升级指令响应
     *
     * @param filename 需要更新的文件名
     * @param version  文件版本
     * @param response HttpServletResponse对象，用于发送响应
     */
    void agentUpgradeFile(String filename, String version, HttpServletResponse response);

    /**
     * 处理离线终端
     * 定时任务处理，设定时间间隔为40秒
     * 那么在TIMEOUT_SECONDS+(0~40)秒内没有收到心跳的终端将被视为离线
     */
    void processOfflineTerminal();
}
