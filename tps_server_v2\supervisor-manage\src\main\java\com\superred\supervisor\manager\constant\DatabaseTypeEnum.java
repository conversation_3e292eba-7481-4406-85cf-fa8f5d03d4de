package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2025-04-07 14:12
 */
@Getter
public enum DatabaseTypeEnum {
    DATABASE_1("MYSQL", "MYSQL"),
    DATABASE_2("MicrosoftSQL", "MicrosoftSQL"),
    DATABASE_3("Oracle", "Oracle"),
    DATABASE_4("MongoDB", "MongoDB"),
    DATABASE_5("PostGreSql", "PostGreSql"),
    DATABASE_6("DB2", "DB2"),
    DATABASE_7("REDIS", "REDIS"),
    DATABASE_8("达梦", "达梦"),
    DATABASE_9("人大金仓", "人大金仓"),
    DATABASE_10("神州通用", "神州通用"),
    DATABASE_11("南大通用", "南大通用");

    private final String key;
    private final String value;

    DatabaseTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

}
