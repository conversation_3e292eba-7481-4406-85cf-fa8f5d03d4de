package com.superred.supervisor.common.repository.policy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.RuleAuditAppBehavior;
import com.superred.supervisor.common.mapper.policy.RuleAuditAppBehaviorMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2025-04-07 10:41
 */
@Slf4j
@Repository
@AllArgsConstructor
public class RuleAuditAppBehaviorRepository extends ServiceImpl<RuleAuditAppBehaviorMapper, RuleAuditAppBehavior> {
}
