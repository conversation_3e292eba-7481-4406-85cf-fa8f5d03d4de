package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 自监管系统运行状态(LocalDeviceStatus) 实体
 *
 * <AUTHOR>
 * @since 2025-03-27 15:31:59
 */
@Data
@TableName("local_device_status")
public class DeviceStatus {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 操作系统
     */
    @TableField("os")
    private String os;

    /**
     * 开机时间,运行时长，单位秒
     */
    @TableField("uptime")
    private Integer uptime;

    /**
     * 主机ip地址
     */
    @TableField("ip")
    private String ip;

    /**
     * 表示CPU使用率，取0-100的数值，多个CPU以列表方式上传。
     physical_id： CPU ID，数值类型;
     cpu_usage：CPU使用率百分比，数值类型，取0-100的数值

     */
    @TableField("cpu")
    private String cpu;

    /**
     * 表示内存利用率，取0-100数值
     */
    @TableField("mem")
    private Integer mem;

    /**
     * 表示数据磁盘整体可用空间，单位GB
     */
    @TableField("disk")
    private Integer disk;

    /**
     * 当检测器由多台服务器组成时，表示服务器的编号
     */
    @TableField("did")
    private Integer did;

    /**
     * 系统运行状态采集
     */
    @TableField("time")
    private Date time;

    @TableField("disk_total")
    private Integer diskTotal;

}

