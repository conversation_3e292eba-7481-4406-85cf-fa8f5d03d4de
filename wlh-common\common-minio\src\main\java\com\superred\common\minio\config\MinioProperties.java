package com.superred.common.minio.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * <AUTHOR>
 * @version 1.0
 * @classname MinioProperties
 * <p>
 * oss 配置信息
 * @date 2020/7/18 06:49
 */
@Data
@EnableConfigurationProperties(MinioProperties.class)
@ConfigurationProperties(prefix = "superred.common.minio")
public class MinioProperties {

    /**
     * 是一个URL，域名，IPv4或者IPv6地址")
     */
    private String endpoint;
    /**
     * "accessKey类似于用户ID，用于唯一标识你的账户"
     */
    private String accessKey;
    /**
     * "secretKey是你账户的密码"
     */
    private String secretKey;

    private String bucket;
}
