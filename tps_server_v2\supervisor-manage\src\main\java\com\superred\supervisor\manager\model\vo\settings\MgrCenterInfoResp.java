package com.superred.supervisor.manager.model.vo.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * <AUTHOR>
 *  上级系统注册
 * @since 2025年03月24日
 */
@Data
public class MgrCenterInfoResp {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @Schema(description = "ip地址,************")
    @NotBlank(message = "IP地址 不可为空")
    @Pattern(regexp = "^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)$",
            message = "IP地址 格式错误")
    private String ip;

    @Schema(description = "上级中心端口")
    @NotNull(message = "端口 不可为空")
    @Min(value = 0, message = "端口 取值范围是0-65535")
    @Max(value = 65535, message = "端口 取值范围是0-65535")
    private Integer port;

    @Schema(description = "注册状态，0（成功），1（失败），2（审核中）")
    private Integer regStatus;

    @Schema(description = "注册状态描述，0（成功），1（需从页面录入），2（审核中）")
    private String regMessage;

    @Schema(description = "注册时间")
    private Date regTime;

}
