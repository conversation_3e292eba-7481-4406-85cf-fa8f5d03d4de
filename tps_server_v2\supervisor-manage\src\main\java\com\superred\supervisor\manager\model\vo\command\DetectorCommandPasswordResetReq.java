package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DetectorCommandPasswordResetReq {
    @Schema(description = "设备编号")
    private List<String> deviceIdList;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "密码")
    private String password;

}
