package com.superred.supervisor.gateway.service.terminal.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import com.superred.supervisor.common.entity.terminal.TerminalSuspectedLog;
import com.superred.supervisor.common.entity.terminal.TerminalSystemAudit;
import com.superred.supervisor.common.entity.terminal.TerminalSystemStatus;
import com.superred.supervisor.common.repository.terminal.TerminalAgentInfoRepository;
import com.superred.supervisor.common.repository.terminal.TerminalSuspectedLogRepository;
import com.superred.supervisor.common.repository.terminal.TerminalSystemAuditRepository;
import com.superred.supervisor.common.repository.terminal.TerminalSystemStatusRepository;
import com.superred.supervisor.gateway.exception.ApiBaseException;
import com.superred.supervisor.gateway.service.terminal.TerminalStatusService;
import com.superred.supervisor.gateway.utils.AgentAuthUtils;
import com.superred.supervisor.standard.v202505.terminal.status.TerminalBusinessStatusReq;
import com.superred.supervisor.standard.v202505.terminal.status.TerminalSystemAuditReq;
import com.superred.supervisor.standard.v202505.terminal.status.TerminalSystemStatusReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 *
 *
 * <AUTHOR>
 * @since 2025/5/29 13:42
 */
@Service
@Slf4j
public class TerminalStatusServiceImpl implements TerminalStatusService {

    @Resource
    private TerminalAgentInfoRepository terminalAgentInfoRepository;

    @Resource
    private TerminalSystemStatusRepository terminalSystemStatusRepository;

    @Resource
    private TerminalSuspectedLogRepository terminalSuspectedLogRepository;

    @Resource
    private TerminalSystemAuditRepository terminalSystemAuditRepository;

    /**
     * 上报终端系统状态
     *
     * @param req 请求参数
     */
    @Override
    public void reportSystemStatus(TerminalSystemStatusReq req) {
        String deviceId = AgentAuthUtils.getDeviceIdFromRequest();
        TerminalAgentInfo terminalAgentInfo = terminalAgentInfoRepository.getById(deviceId);
        if (terminalAgentInfo == null) {
            throw new ApiBaseException("设备不存在");
        }

//        terminalSystemStatusRepository.remove(Wrappers.<TerminalSystemStatus>lambdaQuery().eq(TerminalSystemStatus::getDeviceId, deviceId));

        TerminalSystemStatus agentSystemStatus = new TerminalSystemStatus();
        agentSystemStatus.setDeviceId(deviceId);
        agentSystemStatus.setCpu(JsonUtil.toJson(req.getCpu()));
        agentSystemStatus.setMem(req.getMem());
        agentSystemStatus.setDisk(req.getDisk());
        agentSystemStatus.setDisk(req.getDisk());
        agentSystemStatus.setTime(LocalDateTime.now());
        agentSystemStatus.setPublicIpAddress(JsonUtil.toJson(req.getPublicIpAddress()));
        terminalSystemStatusRepository.save(agentSystemStatus);

    }

    /**
     * 上报终端业务状态
     *
     * @param req 请求参数列表
     */
    @Override
    public void reportBusinessStatus(List<TerminalBusinessStatusReq> req) {
        String deviceId = AgentAuthUtils.getDeviceIdFromRequest();
        TerminalAgentInfo terminalAgentInfo = terminalAgentInfoRepository.getById(deviceId);
        if (terminalAgentInfo == null) {
            throw new ApiBaseException("设备不存在");
        }
        if (CollUtil.isEmpty(req)) {
            log.warn("设备[{}]上报业务状态为空", deviceId);
            return;
        }
        List<TerminalSuspectedLog> addList = new ArrayList<>();
        for (TerminalBusinessStatusReq businessStatusVo : req) {

            List<TerminalBusinessStatusReq.SuspectedInfo> suspected = businessStatusVo.getSuspected();
            if (CollUtil.isEmpty(suspected)) {
                continue;
            }
            for (TerminalBusinessStatusReq.SuspectedInfo suspectedOne : suspected) {
                TerminalSuspectedLog agentSuspectedLog = new TerminalSuspectedLog();
                agentSuspectedLog.setDeviceId(deviceId);
                agentSuspectedLog.setRisk(suspectedOne.getRisk());
                agentSuspectedLog.setEventType(suspectedOne.getEventType());
                agentSuspectedLog.setMsg(suspectedOne.getMsg());
                agentSuspectedLog.setTime(LocalDateTimeUtil.parse(suspectedOne.getTime(), DatePattern.NORM_DATETIME_FORMATTER));
                agentSuspectedLog.setSoftVersion(businessStatusVo.getSoftVersion());
                addList.add(agentSuspectedLog);
            }
        }
        terminalSuspectedLogRepository.saveBatch(addList);
    }

    /**
     * 上报终端系统审计信息
     *
     * @param req 请求参数
     */
    @Override
    public void reportSystemAudit(TerminalSystemAuditReq req) {

        long count = terminalSystemAuditRepository.count(Wrappers.<TerminalSystemAudit>lambdaQuery().eq(TerminalSystemAudit::getMsgId, req.getId()));
        if (count > 0) {
            log.warn("数据已经上报：{}", req.getId());
            return;
        }
        String deviceId = AgentAuthUtils.getDeviceIdFromRequest();
        TerminalSystemAudit audit = new TerminalSystemAudit();
        audit.setMsgId(req.getId());
        audit.setDeviceId(deviceId);
        audit.setUser(req.getUser());
        audit.setTime(LocalDateTimeUtil.parse(req.getTime(), DatePattern.NORM_DATETIME_FORMATTER));
        audit.setEventType(req.getEventType());
        audit.setOptType(req.getOptType());
        audit.setMessage(req.getMessage());
        audit.setIsReport(0);
        terminalSystemAuditRepository.save(audit);
    }
}
