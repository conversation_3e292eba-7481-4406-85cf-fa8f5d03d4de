#!/bin/sh
memTotal=`free -m | grep "Mem:" | awk '{print $2}'`
#memUsage=`free -m | grep "Mem:" | awk '{print int($3/$2*100 + 0.5)}'`
memUsage=`free -m | grep "Mem:" | awk '{print $3}'`
os=`cat /etc/*os-release | grep "NAME" | grep -v "PRETTY_NAME" | awk -F\" '{printf $2}'`
uptime=`cat /proc/uptime | awk '{print int($1+0.5)}'`
ip=`ifconfig 'eth0' | grep inet | grep -v inet6 | grep -v 127.0.0.1 |awk '{print $2}' |tr -d "addr:"`
echo "$memTotal,$memUsage,$os,$uptime,$ip"
