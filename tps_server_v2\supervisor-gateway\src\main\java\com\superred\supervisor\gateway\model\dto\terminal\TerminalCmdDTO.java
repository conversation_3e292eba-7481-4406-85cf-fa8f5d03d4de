package com.superred.supervisor.gateway.model.dto.terminal;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * 终端命令响应
 *
 * <AUTHOR>
 * @since 2025/05/28
 */
@Data
public class TerminalCmdDTO {


    /**
     * 操作执行ID.
     */
    private Long operationExecId;
    /**
     * 系统操作指令/检测策略指令/检查任务指令.
     * 取值：command、policy
     **/
    private String type;

    /**
     * 系统软件更新指令/策略下发类型/系统内置策略更新指令.
     * 取值：update、add、del、reset
     **/
    private String cmd;

    /**
     * 检测策略对应的模块名称.
     **/
    private String module;

    /**
     * 检测策略对应的子模块名称.
     **/
    private String submoduleJson;

    /**
     * 策略/任务对应版本号.
     **/
    private String version;

    /**
     * 策略数量.
     **/
    private Integer num;

    /**
     * 更新参数.
     **/
    private String paramJson;

    /**
     * 指令ID.
     **/
    @JsonProperty("cmd_id")
    private String cmdId;

    /**
     * 新的策略内容/任务内容.
     **/
    private String config;
}
