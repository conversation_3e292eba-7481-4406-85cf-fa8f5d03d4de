package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DetectorCommandDropDataReq {
    @Schema(description = "设备编号")
    private List<String> deviceIdList;

    @Schema(description = "上报模块")
    private List<String> submodules;

    @Schema(description = "是否全部")
    private Integer isAll;

    @Schema(description = "规则列表")
    private List<String> blacklistRuleIds;

    @Schema(description = "规则列表")
    private List<String> keywordsRuleIds;

    @Schema(description = "规则列表")
    private List<String> md5RuleIds;
}
