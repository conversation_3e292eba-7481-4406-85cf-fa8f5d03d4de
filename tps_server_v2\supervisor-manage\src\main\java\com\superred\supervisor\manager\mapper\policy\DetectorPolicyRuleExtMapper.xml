<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.superred.supervisor.manager.mapper.policy.DetectorPolicyRuleExtMapper">

    <resultMap id="PolicyApplyMap" type="com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp">
        <result column="rule_id" property="ruleId"/>
        <result column="policy_id" property="policyId"/>
        <result column="policy_name" property="policyName"/>
        <result column="type" property="policyType"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , rule_id, policy_id
    </sql>

    <select id="selectPolicyApply" resultMap="PolicyApplyMap">
        select
        "detector" as type,
        d.rule_id,
        d.policy_id
        from policy_detector_policy_rule d
        left join policy_detector_policy p on d.policy_id = p.id
        <where>
            <if test="query!=null and query!=''">
                d.rule_id = #{query}
            </if>
        </where>
    </select>

    <delete id="removePolicy">
        delete
        from policy_detector_policy_rule
        where policy_id = #{query}
    </delete>

</mapper>
