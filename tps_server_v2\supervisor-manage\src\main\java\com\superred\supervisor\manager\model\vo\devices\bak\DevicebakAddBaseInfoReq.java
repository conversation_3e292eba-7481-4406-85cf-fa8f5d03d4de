package com.superred.supervisor.manager.model.vo.devices.bak;

import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;


/**
 * evicebak添加基本信息请求
 *
 * <AUTHOR>
 * @since 2025/03/14
 */
@Data

public class DevicebakAddBaseInfoReq {


    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */
    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    @NotBlank(message = "设备编号 不能为空")
    @Size(min = 12, max = 12, message = "设备编号 长度为12位")
    private String deviceId;

    /**
     * 设备类型，最长2个数字
     */
    @Schema(description = "设备类型，最长2个数字")
    @NotBlank(message = "设备类型 不能为空")
    private String deviceType;


    /**
     * 厂商英文名
     */
    @Schema(description = "厂商英文名")
    @NotBlank(message = "厂商名称 不能为空")
    @ByteSize(max = 32, message = "厂商名称 不能超过32个字节")
    private String vendorName;

    /**
     * 产品软件版本号，最长16个字符。如2012.1.5.12
     */
    @Schema(description = "产品软件版本号，最长16个字符。如2012.1.5.12")
    @NotBlank(message = "软件版本号 不能为空")
    @ByteSize(max = 32, message = "软件版本号 不能超过32个字节")
    private String softVersion;

    /**
     * 内存总数，单位MB
     */
    @Schema(description = "内存总数，单位MB")
    @NotNull(message = "内存总数不能为空")
    @Size(max = 10, message = "内存总数 不能超过10个字节")
    private String memTotal;

    /**
     * 监测器部署的客户单位名，如“XX信息中心”
     */
    @Schema(description = "监测器部署的客户单位名，如“XX信息中心”")
    @NotBlank(message = "部署单位 不能为空")
    @ByteSize(max = 64, message = "部署单位 不能超过64个字节")
    private String organs;

    /**
     * 监测器部署的地理位置，如“北京市海淀区复兴路128号区政府接入机房F-12”
     */
    @Schema(description = "监测器部署的地理位置，如“北京市海淀区复兴路128号区政府接入机房F-12”")
    @NotBlank(message = "部署位置 不能为空")
    @ByteSize(max = 128, message = "部署位置 不能超过128个字节")
    private String address;

    /**
     * 行政区域编码，如“100085”
     */
    @Schema(description = "行政区域编码，如“100085”")
    @NotBlank(message = "行政区域 不能为空")
    @ByteSize(max = 128, message = "行政区域 不能超过128个字节")
    private String addressCode;


    /**
     * 设备ca证书序列号
     */
    @Schema(description = "设备ca证书序列号")
    @NotBlank(message = "证书编号 不能为空")
    @ByteSize(max = 64, message = "证书编号 不能超过64个字节")
    private String deviceCa;


}
