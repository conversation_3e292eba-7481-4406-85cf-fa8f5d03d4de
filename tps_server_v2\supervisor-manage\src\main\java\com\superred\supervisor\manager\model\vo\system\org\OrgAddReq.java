package com.superred.supervisor.manager.model.vo.system.org;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @Classname OrgTree
 * @Description 组织树
 * <AUTHOR>
 * @Date 2022-02-19 11:49
 * @Version 1.0
 */
@Data

public class OrgAddReq {

    @Schema(description = "部门名称")
    @NotBlank(message = "部门名称 不能为空")
    @Size(min = 1, max = 20, message = "部门名称 长度限制1-20位")
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5]+$", message = "部门名称 只能包含中文、英文、数字")
    private String name;

    @Schema(description = "排序")
    @Max(value = 999, message = "排序 取值范围1-999")
    @Min(value = 1, message = "排序 取值范围1-999")
    private Integer sort;


    @Schema(description = "上级部门id")
    private Integer parentId;

    @Schema(description = "描述信息")
    @Size(max = 300, message = "描述信息 长度不能超过300")
    private String desc;

    @Schema(description = "IP地址范围")
    @Size(max = 500, message = "IP地址范围 长度不能超过500")
    private String ipRanges;

    @Schema(description = "区域编码")
    @NotBlank(message = "区域编码 不能为空")
    private String regionCode;


}
