package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleAttackPermeate;
import com.superred.supervisor.common.repository.policy.RuleAttackPermeateRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.constant.RuleSourceTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.config.PermeatePolicyConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackPermeatePageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackPermeateReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackPermeateResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleAttackPermeateService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-12 15:58
 */
@Slf4j
@Service("ruleAttackPermeateService")
@AllArgsConstructor
public class RuleAttackPermeateServiceImpl implements RuleAttackPermeateService, RuleService {

    @Resource
    private RuleAttackPermeateRepository ruleAttackPermeateRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    @Override
    public IPage<RuleAttackPermeateResp> page(RuleAttackPermeatePageReq ruleAttackPermeatePageReq) {
        List<String> ruleIdList = this.getRuleIdList(ruleAttackPermeatePageReq);
        LambdaQueryWrapper<RuleAttackPermeate> queryWrapper = new LambdaQueryWrapper<RuleAttackPermeate>()
                .le(StrUtil.isNotBlank(ruleAttackPermeatePageReq.getEndDate()), RuleAttackPermeate::getUpdateTime, ruleAttackPermeatePageReq.getEndDate())
                .ge(StrUtil.isNotEmpty(ruleAttackPermeatePageReq.getStartDate()), RuleAttackPermeate::getUpdateTime, ruleAttackPermeatePageReq.getStartDate())
                .like(StrUtil.isNotEmpty(ruleAttackPermeatePageReq.getRuleId()), RuleAttackPermeate::getRuleId, ruleAttackPermeatePageReq.getRuleId())
                .eq(StrUtil.isNotEmpty(ruleAttackPermeatePageReq.getRisk()), RuleAttackPermeate::getRisk, ruleAttackPermeatePageReq.getRisk())
                .eq(StrUtil.isNotEmpty(ruleAttackPermeatePageReq.getAttackClass()), RuleAttackPermeate::getAttackClass, ruleAttackPermeatePageReq.getAttackClass())
                .eq(StrUtil.isNotEmpty(ruleAttackPermeatePageReq.getIsShare()), RuleAttackPermeate::getIsShare, ruleAttackPermeatePageReq.getIsShare())
                .eq(StrUtil.isNotEmpty(ruleAttackPermeatePageReq.getRuleSource()), RuleAttackPermeate::getRuleSource, ruleAttackPermeatePageReq.getRuleSource())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleAttackPermeate::getRuleId, ruleIdList)
                .ne(RuleAttackPermeate::getRuleSource, RuleSourceTypeEnum.SUPER_POLICY_SHARE.getKey())
                .orderByDesc(RuleAttackPermeate::getUpdateTime);
        Page<RuleAttackPermeate> page = new Page<>(ruleAttackPermeatePageReq.getStart(), ruleAttackPermeatePageReq.getLimit());
        IPage<RuleAttackPermeate> page1 = this.ruleAttackPermeateRepository.page(page, queryWrapper);
        return page1.convert(RuleAttackPermeateResp::fromRuleAttackPermeate);
    }

    @Override
    public RuleAttackPermeateResp getById(Long ruleId) {
        RuleAttackPermeate ruleAttackPermeate = this.ruleAttackPermeateRepository.getById(ruleId);
        return RuleAttackPermeateResp.fromRuleAttackPermeate(ruleAttackPermeate);
    }

    @Override
    public void save(RuleAttackPermeateReq ruleAttackPermeateReq) {
        RuleAttackPermeate ruleAttackPermeate = fromRuleAttackPermeateReq(ruleAttackPermeateReq);
        // 赋值ruleId
        ruleAttackPermeate.setRuleId(this.ruleIdBuilder.buildRuleId().toString());
        this.ruleAttackPermeateRepository.save(ruleAttackPermeate);
    }

    @Override
    public void edit(RuleAttackPermeateReq ruleAttackPermeateReq) {
        RuleAttackPermeate ruleAttackPermeate = fromRuleAttackPermeateReq(ruleAttackPermeateReq);
        this.ruleAttackPermeateRepository.update(ruleAttackPermeate, Wrappers.<RuleAttackPermeate>lambdaUpdate().eq(RuleAttackPermeate::getRuleId, ruleAttackPermeateReq.getRuleId()));
    }

    public static RuleAttackPermeate fromRuleAttackPermeateReq(RuleAttackPermeateReq ruleAttackPermeateReq) {
        return RuleAttackPermeate.builder()
                .ruleId(ruleAttackPermeateReq.getRuleId())
                .ruleName(ruleAttackPermeateReq.getRuleName())
                .storePcap(ruleAttackPermeateReq.getStorePcap())
                .attackClass(ruleAttackPermeateReq.getAttackClass())
                .attackGroup(ruleAttackPermeateReq.getAttackGroup())
                .rule(ruleAttackPermeateReq.getRule())
                .desc(ruleAttackPermeateReq.getDesc())
                .cve(ruleAttackPermeateReq.getCve())
                .vulnerability(ruleAttackPermeateReq.getVulnerability())
                .risk(ruleAttackPermeateReq.getRisk())
                .status(ruleAttackPermeateReq.getStatus())
                .isShare(ruleAttackPermeateReq.getIsShare())
                .ruleSource(ruleAttackPermeateReq.getRuleSource())
                .level(ruleAttackPermeateReq.getLevel())
                .updateTime(ruleAttackPermeateReq.getUpdateTime())
                .createTime(ruleAttackPermeateReq.getCreateTime())
                .ext1(ruleAttackPermeateReq.getExt1())
                .ext2(ruleAttackPermeateReq.getExt2())
                .ext3(ruleAttackPermeateReq.getExt3())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleAttackPermeateRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 验证是否在使用
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleAttackPermeate> list = this.ruleAttackPermeateRepository.list(Wrappers.<RuleAttackPermeate>lambdaQuery()
                .in(RuleAttackPermeate::getRuleId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.equals(PolicyApplyStatusEnum.APPLY.getKey().toString(), item.getStatus())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
                Integer ruleSource = Integer.valueOf(item.getRuleSource());
                if (ruleSource != RuleSourceTypeEnum.LOCAL_POLICY.getKey()) {
                    throw new BaseBusinessException("存在上级策略，上级策略不允许被删除");
                }
            });
        }
    }

    /**
     * 获取规则ID列表
     * @param ruleAttackPermeatePageReq
     * @return
     */
    private List<String> getRuleIdList(RuleAttackPermeatePageReq ruleAttackPermeatePageReq) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleAttackPermeatePageReq.getIssueDeviceType())
                || StrUtil.isBlank(ruleAttackPermeatePageReq.getPolicyId())
                || StrUtil.isBlank(ruleAttackPermeatePageReq.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleAttackPermeatePageReq.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleAttackPermeatePageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleAttackPermeatePageReq.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleAttackPermeatePageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 攻击窃密检测 - 渗透行为检测策略
        return StrUtil.equals(module, PolicyModuleTwoEnum.ATTACK.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 攻击窃密检测 - 渗透行为检测策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleTwoEnum.ATTACK.getKey())
                .moduleStr(PolicyModuleTwoEnum.ATTACK.getValue())
                .moduleParentStr(PolicyModuleOneEnum.ALARM.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 攻击窃密检测 - 渗透行为检测策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleAttackPermeate> list = this.ruleAttackPermeateRepository.list(Wrappers.<RuleAttackPermeate>lambdaQuery()
                .in(RuleAttackPermeate::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<PermeatePolicyConfigDTO> configDTOS = list.stream().map(item -> {
            return PermeatePolicyConfigDTO.getPolicyConfig(item);
        }).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 攻击窃密检测 - 渗透行为检测策略
        this.ruleAttackPermeateRepository.update(Wrappers.<RuleAttackPermeate>lambdaUpdate()
                .in(RuleAttackPermeate::getRuleId, ruleIds)
                .set(RuleAttackPermeate::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 攻击窃密检测 - 渗透行为检测策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleAttackPermeate> list = this.ruleAttackPermeateRepository.list(Wrappers.<RuleAttackPermeate>lambdaQuery()
                .in(RuleAttackPermeate::getRuleId, ruleIdList));
        List<RuleAttackPermeateResp> respList = new ArrayList<>();
        list.forEach(item -> {
            RuleAttackPermeateResp resp = RuleAttackPermeateResp.fromRuleAttackPermeate(item);
            respList.add(resp);
        });
        policyDetail.setPermeateList(respList);
        return policyDetail;
    }
}
