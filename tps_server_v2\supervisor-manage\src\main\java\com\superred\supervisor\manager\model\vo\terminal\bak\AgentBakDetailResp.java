package com.superred.supervisor.manager.model.vo.terminal.bak;

import com.baomidou.mybatisplus.annotation.TableField;
import com.superred.supervisor.common.entity.agent.AgentDeviceInfoBak;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 终端设备信息
 * </p>
 *
 */
@Data

public class AgentBakDetailResp {

    @Schema(description = "前八位为年月日，下划线后自定义")
    private String softVersion;

    @Schema(description = "设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值")
    @TableField("interface")
    private String interfaces;

    @Schema(description = "内存总数，表示整个设备的内存大小，单位MB。")
    private Long memTotal;

    @Schema(description = "CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。	physical_id：CPU ID，数值类型	core：CPU核心数，数值类型；	clock：CPU主频，数值类型精确到小数点后1位	")
    private String cpuInfo;

    @Schema(description = "磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。	size为数值类型，表示磁盘大小，单位GB；	serial为字符串类型，最长64个字节，表示磁盘序列号")
    private String diskInfo;


    @Schema(description = "行政区域编码类型，表示检测器部署所在地的区域编码。")
    private String addressCode;

    @Schema(description = "行政区域")
    private String address;

    @Schema(description = "备注信息")
    private String memo;

    @Schema(description = "终端责任人ID")
    private String userId;

    @Schema(description = "终端责任人姓名")
    private String userName;

    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "主机操作系统")
    private String os;

    @Schema(description = "主机CPU架构")
    private String arch;

    @Schema(description = "单位名称")
    private String company;

    @Schema(description = "单位id")
    private Integer companyId;

    @Schema(description = "所属部门名称")
    private String orgName;

    @Schema(description = "所属部门id")
    private Integer orgId;

    public static AgentBakDetailResp from(AgentDeviceInfoBak agentDeviceInfoBak, String orgName) {
        AgentBakDetailResp agentBakDetailResp = new AgentBakDetailResp();
        agentBakDetailResp.setSoftVersion(agentDeviceInfoBak.getSoftVersion());
        agentBakDetailResp.setInterfaces(agentDeviceInfoBak.getInterfaceInfo());
        agentBakDetailResp.setMemTotal(agentDeviceInfoBak.getMemTotal());
        agentBakDetailResp.setCpuInfo(agentDeviceInfoBak.getCpuInfo());
        agentBakDetailResp.setDiskInfo(agentDeviceInfoBak.getDiskInfo());
        agentBakDetailResp.setAddressCode(agentDeviceInfoBak.getAddressCode());
        agentBakDetailResp.setAddress(agentDeviceInfoBak.getAddress());
        agentBakDetailResp.setMemo(agentDeviceInfoBak.getMemo());
        agentBakDetailResp.setUserId(agentDeviceInfoBak.getUserId());
        agentBakDetailResp.setUserName(agentDeviceInfoBak.getUserName());
        agentBakDetailResp.setHostName(agentDeviceInfoBak.getDeviceName());
        agentBakDetailResp.setOs(agentDeviceInfoBak.getOs());
        agentBakDetailResp.setArch(agentDeviceInfoBak.getArch());
        agentBakDetailResp.setCompany(agentDeviceInfoBak.getCompany());
        agentBakDetailResp.setCompanyId(agentDeviceInfoBak.getCompanyId());
        agentBakDetailResp.setOrgName(orgName);
        agentBakDetailResp.setOrgId(agentDeviceInfoBak.getOrgId());

        return agentBakDetailResp;
    }

}
