package com.superred.supervisor.common.repository.system;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.system.SysAuthorizationInfo;
import com.superred.supervisor.common.mapper.system.SysAuthorizationInfoMapper;
import org.springframework.stereotype.Repository;

/**
 * 系统授权信息表 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:18
 */
@Repository
public class SysAuthorizationInfoRepository extends ServiceImpl<SysAuthorizationInfoMapper, SysAuthorizationInfo> {

    public SysAuthorizationInfo getAuthInfo() {

        return this.getOne(Wrappers.<SysAuthorizationInfo>lambdaQuery().last("limit 1"));
    }
}

