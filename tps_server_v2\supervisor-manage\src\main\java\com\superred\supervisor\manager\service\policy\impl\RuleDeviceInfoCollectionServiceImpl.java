package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleDeviceInfoCollection;
import com.superred.supervisor.common.repository.policy.RuleDeviceInfoCollectionRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.constant.RuleSourceTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.config.DeviceInfoCollectionConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleDeviceInfoCollectionPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleDeviceInfoCollectionReq;
import com.superred.supervisor.manager.model.vo.policy.RuleDeviceInfoCollectionResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleDeviceInfoCollectionService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-04-18 14:50
 */
@Slf4j
@Service("ruleDeviceInfoCollectionService")
@AllArgsConstructor
public class RuleDeviceInfoCollectionServiceImpl implements RuleDeviceInfoCollectionService, RuleService {

    @Resource
    private RuleDeviceInfoCollectionRepository ruleDeviceInfoCollectionRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    @Override
    public IPage<RuleDeviceInfoCollectionResp> page(RuleDeviceInfoCollectionPageReq ruleDeviceInfoCollectionPageReq) {
        List<String> ruleIdList = this.getRuleIdList(ruleDeviceInfoCollectionPageReq);
        LambdaQueryWrapper<RuleDeviceInfoCollection> queryWrapper = new LambdaQueryWrapper<RuleDeviceInfoCollection>()
                .le(StrUtil.isNotEmpty(ruleDeviceInfoCollectionPageReq.getEndDate()), RuleDeviceInfoCollection::getUpdateTime, ruleDeviceInfoCollectionPageReq.getEndDate())
                .ge(StrUtil.isNotEmpty(ruleDeviceInfoCollectionPageReq.getStartDate()), RuleDeviceInfoCollection::getUpdateTime, ruleDeviceInfoCollectionPageReq.getStartDate())
                .like(StrUtil.isNotEmpty(ruleDeviceInfoCollectionPageReq.getRuleId()), RuleDeviceInfoCollection::getRuleId, ruleDeviceInfoCollectionPageReq.getRuleId())
                .eq(StrUtil.isNotEmpty(ruleDeviceInfoCollectionPageReq.getStatus()), RuleDeviceInfoCollection::getStatus, ruleDeviceInfoCollectionPageReq.getStatus())
                .eq(StrUtil.isNotEmpty(ruleDeviceInfoCollectionPageReq.getIsShare()), RuleDeviceInfoCollection::getIsShare, ruleDeviceInfoCollectionPageReq.getIsShare())
                .eq(StrUtil.isNotEmpty(ruleDeviceInfoCollectionPageReq.getRuleSource()), RuleDeviceInfoCollection::getRuleSource, ruleDeviceInfoCollectionPageReq.getRuleSource())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleDeviceInfoCollection::getRuleId, ruleIdList)
                .ne(RuleDeviceInfoCollection::getRuleSource, RuleSourceTypeEnum.SUPER_POLICY_SHARE.getKey())
                .orderByDesc(RuleDeviceInfoCollection::getUpdateTime);
        Page<RuleDeviceInfoCollection> page = new Page<>(ruleDeviceInfoCollectionPageReq.getStart(), ruleDeviceInfoCollectionPageReq.getLimit());
        IPage<RuleDeviceInfoCollection> page1 = this.ruleDeviceInfoCollectionRepository.page(page, queryWrapper);
        return page1.convert(RuleDeviceInfoCollectionResp::fromRuleDeviceInfoCollection);
    }

    @Override
    public RuleDeviceInfoCollectionResp getById(Long ruleId) {
        RuleDeviceInfoCollection ruleDeviceInfoCollection = this.ruleDeviceInfoCollectionRepository.getById(ruleId);
        if (ruleDeviceInfoCollection == null) {
            throw new BaseBusinessException("策略不存在");
        }
        return RuleDeviceInfoCollectionResp.fromRuleDeviceInfoCollection(ruleDeviceInfoCollection);
    }

    @Override
    public void save(RuleDeviceInfoCollectionReq ruleDeviceInfoCollectionReq) {
        RuleDeviceInfoCollection ruleDeviceInfoCollection = fromRuleDeviceInfoCollectionReq(ruleDeviceInfoCollectionReq);
        // 赋值ruleId
        ruleDeviceInfoCollection.setRuleId(this.ruleIdBuilder.buildRuleId().toString());
        this.ruleDeviceInfoCollectionRepository.save(ruleDeviceInfoCollection);
    }

    @Override
    public void edit(RuleDeviceInfoCollectionReq ruleDeviceInfoCollectionReq) {
        // 验证
        this.validateId(ruleDeviceInfoCollectionReq);
        RuleDeviceInfoCollection ruleDeviceInfoCollection = fromRuleDeviceInfoCollectionReq(ruleDeviceInfoCollectionReq);
        this.ruleDeviceInfoCollectionRepository.updateById(ruleDeviceInfoCollection);
    }

    public static RuleDeviceInfoCollection fromRuleDeviceInfoCollectionReq(RuleDeviceInfoCollectionReq ruleDeviceInfoCollectionReq) {
        return RuleDeviceInfoCollection.builder()
                .ruleId(ruleDeviceInfoCollectionReq.getRuleId())
                .ruleName(ruleDeviceInfoCollectionReq.getRuleName())
                .ruleContent(ruleDeviceInfoCollectionReq.getRuleContent())
                .ruleDesc(ruleDeviceInfoCollectionReq.getRuleDesc())
                .status(ruleDeviceInfoCollectionReq.getStatus())
                .isShare(ruleDeviceInfoCollectionReq.getIsShare())
                .ruleSource(ruleDeviceInfoCollectionReq.getRuleSource())
                .level(ruleDeviceInfoCollectionReq.getLevel())
                .updateTime(ruleDeviceInfoCollectionReq.getUpdateTime())
                .createTime(ruleDeviceInfoCollectionReq.getCreateTime())
                .ext1(ruleDeviceInfoCollectionReq.getExt1())
                .ext2(ruleDeviceInfoCollectionReq.getExt2())
                .ext3(ruleDeviceInfoCollectionReq.getExt3())
                .upRuleId(ruleDeviceInfoCollectionReq.getUpRuleId())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleDeviceInfoCollectionRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 校验是否在使用
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleDeviceInfoCollection> list = this.ruleDeviceInfoCollectionRepository.list(Wrappers.<RuleDeviceInfoCollection>lambdaQuery()
                .in(RuleDeviceInfoCollection::getRuleId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.equals(PolicyApplyStatusEnum.APPLY.getKey().toString(), item.getStatus())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
                Integer ruleSource = Integer.valueOf(item.getRuleSource());
                if (ruleSource != RuleSourceTypeEnum.LOCAL_POLICY.getKey()) {
                    throw new BaseBusinessException("存在上级策略，上级策略不允许被删除");
                }
            });
        }
    }

    /**
     * 校验ID
     * @param ruleDeviceInfoCollectionReq
     */
    private void validateId(RuleDeviceInfoCollectionReq ruleDeviceInfoCollectionReq) {
        if (StrUtil.isBlank(ruleDeviceInfoCollectionReq.getRuleId())) {
            throw new BaseBusinessException("策略ID不可为空");
        }
    }

    /**
     * 获取规则ID
     * @param ruleDeviceInfoCollectionPageReq
     * @return
     */
    private List<String> getRuleIdList(RuleDeviceInfoCollectionPageReq ruleDeviceInfoCollectionPageReq) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleDeviceInfoCollectionPageReq.getIssueDeviceType())
                || StrUtil.isBlank(ruleDeviceInfoCollectionPageReq.getPolicyId())
                || StrUtil.isBlank(ruleDeviceInfoCollectionPageReq.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleDeviceInfoCollectionPageReq.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleDeviceInfoCollectionPageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleDeviceInfoCollectionPageReq.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleDeviceInfoCollectionPageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 设备信息采集策略
        return StrUtil.equals(module, PolicyModuleOneEnum.DEVICE_INFO.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 设备信息采集策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleOneEnum.DEVICE_INFO.getKey())
                .moduleStr(PolicyModuleOneEnum.DEVICE_INFO.getValue())
                .moduleParentStr(PolicyModuleOneEnum.DEVICE_INFO.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 设备信息采集策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleDeviceInfoCollection> list = this.ruleDeviceInfoCollectionRepository.list(Wrappers.<RuleDeviceInfoCollection>lambdaQuery()
                .in(RuleDeviceInfoCollection::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<DeviceInfoCollectionConfigDTO> configDTOS = list.stream().map(item -> DeviceInfoCollectionConfigDTO.getPolicyConfig(item)).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 设备信息采集策略
        this.ruleDeviceInfoCollectionRepository.update(Wrappers.<RuleDeviceInfoCollection>lambdaUpdate()
                .in(RuleDeviceInfoCollection::getRuleId, ruleIds)
                .set(RuleDeviceInfoCollection::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 设备信息采集策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleDeviceInfoCollection> list = this.ruleDeviceInfoCollectionRepository.list(Wrappers.<RuleDeviceInfoCollection>lambdaQuery()
                .in(RuleDeviceInfoCollection::getRuleId, ruleIdList));
        List<RuleDeviceInfoCollectionResp> respList = list.stream().map(item -> RuleDeviceInfoCollectionResp.fromRuleDeviceInfoCollection(item)).collect(Collectors.toList());
        policyDetail.setDeviceInfoCollectionRespList(respList);
        return policyDetail;
    }


}
