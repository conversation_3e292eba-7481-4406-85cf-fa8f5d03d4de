package com.superred.supervisor.manager.model.vo.policy;

import com.superred.supervisor.common.entity.policy.RuleDeviceInfoCollection;
import com.superred.supervisor.manager.constant.DeviceInfoCollectionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-04-18 14:37
 */
@Data
@Builder

public class RuleDeviceInfoCollectionResp {

    private static final long serialVersionUID = 1L;

    @Schema(description = "策略ID")
    private String ruleId;

    @Schema(description = "策略名称")
    private String ruleName;

    @Schema(description = "策略内容")
    private String ruleContent;

    @Schema(description = "策略内容")
    private String ruleContentStr;

    @Schema(description = "策略描述")
    private String ruleDesc;

    @Schema(description = "规则应用状态，0未应用，1已应用")
    private String status;

    @Schema(description = "是否共享")
    private String isShare;

    @Schema(description = "策略来源 1 本级 2上级")
    private String ruleSource;

    @Schema(description = "平台级别")
    private String level;

    @Schema(description = "规则更新时间")
    private String updateTime;

    @Schema(description = "策略创建时间")
    private String createTime;

    @Schema(description = "扩展字段1")
    private Long ext1;

    @Schema(description = "扩展字段2")
    private String ext2;

    @Schema(description = "扩展字段3")
    private String ext3;

    @Schema(description = "上级共享策略ID")
    private String upRuleId;

    public static RuleDeviceInfoCollectionResp fromRuleDeviceInfoCollection(RuleDeviceInfoCollection ruleDeviceInfoCollection) {
        return RuleDeviceInfoCollectionResp.builder()
                .ruleId(ruleDeviceInfoCollection.getRuleId())
                .ruleName(ruleDeviceInfoCollection.getRuleName())
                .ruleContent(ruleDeviceInfoCollection.getRuleContent())
                .ruleContentStr(DeviceInfoCollectionEnum.getValueByKey(ruleDeviceInfoCollection.getRuleContent()))
                .ruleDesc(ruleDeviceInfoCollection.getRuleDesc())
                .status(ruleDeviceInfoCollection.getStatus())
                .isShare(ruleDeviceInfoCollection.getIsShare())
                .ruleSource(ruleDeviceInfoCollection.getRuleSource())
                .level(ruleDeviceInfoCollection.getLevel())
                .updateTime(ruleDeviceInfoCollection.getUpdateTime())
                .createTime(ruleDeviceInfoCollection.getCreateTime())
                .ext1(ruleDeviceInfoCollection.getExt1())
                .ext2(ruleDeviceInfoCollection.getExt2())
                .ext3(ruleDeviceInfoCollection.getExt3())
                .upRuleId(ruleDeviceInfoCollection.getUpRuleId())
                .build();
    }
}
