package com.superred.supervisor.manager.model.dto.command;

import lombok.Getter;

/**
 * <p> 一级模块名
 *
 * <AUTHOR>
 * @since 2025/3/14 15:59
 **/
@Getter
public enum ModuleStartAndStopType {
    ALARM("alarm", "攻击窃密检测", 1),
    SENSITIVE("sensitive", "涉密敏感信息检测", 2),
    FILE_FILTER("file_filter", "文件采集筛选", 3),
    NET_AUDIT("net_audit", "网络行为审计", 4),
    OBJECT_LISTEN("object_listen", "目标流量审计", 5),
    ACTIVE_OBJECT_AUDIT("active_object_audit", "活动对象审计", 6);

    private final String key;
    private final String desc;
    private final Integer sort;

    ModuleStartAndStopType(String key, String desc, Integer sort) {
        this.key = key;
        this.desc = desc;
        this.sort = sort;
    }

    public static ModuleStartAndStopType getModuleStartAndStopTypeByKey(String key) {
        for (ModuleStartAndStopType type : ModuleStartAndStopType.values()) {
            if (type.getKey().equals(key)) {
                return type;
            }
        }
        return null;
    }
}
