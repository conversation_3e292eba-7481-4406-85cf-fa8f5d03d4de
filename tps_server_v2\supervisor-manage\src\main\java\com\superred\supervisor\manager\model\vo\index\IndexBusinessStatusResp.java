package com.superred.supervisor.manager.model.vo.index;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 代理商业务状况
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Data

@Builder
public class IndexBusinessStatusResp {


    @Schema(description = "检测器总数")
    private Integer countDetectorTotal;

    @Schema(description = "检测器在线数")
    private Integer countDetectorOnline;

    @Schema(description = "检测器离线数")
    private Integer countDetectorOffline;


    @Schema(description = "终端总数")
    private Integer countAgentTotal;

    @Schema(description = "终端在线数")
    private Integer countAgentOnline;

    @Schema(description = "终端离线数")
    private Integer countAgentOffline;


    @Schema(description = "策略总数")
    private Integer countPolicyTotal;

    @Schema(description = "策略成功数")
    private Integer countPolicySuccess;

    @Schema(description = "策略失败数")
    private Integer countPolicyFail;


    @Schema(description = "策略未下发")
    private Integer countPolicyPending;

    @Schema(description = "命令总数")
    private Integer countCommandTotal;

    @Schema(description = "命令成功数")
    private Integer countCommandSuccess;

    @Schema(description = "命令失败数")
    private Integer countCommandFail;

}
