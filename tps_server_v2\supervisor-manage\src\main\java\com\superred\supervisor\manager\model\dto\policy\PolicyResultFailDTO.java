package com.superred.supervisor.manager.model.dto.policy;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;


/**
 * <AUTHOR>
 * @create 2025-04-03 10:14
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PolicyResultFailDTO {

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 错误内容
     */
    private String msg;

}
