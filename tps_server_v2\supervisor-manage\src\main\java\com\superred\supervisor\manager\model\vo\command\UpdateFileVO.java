package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class UpdateFileVO {
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 命令类型
     * inner_policy_update: 内置策略
     * update: 系统固件
     */
    @Schema(description = "命令类型")
    private String cmd;

    /**
     * 更新固件文件名
     */
    @Schema(description = "更新固件文件名")
    private String filename;

    /**
     * 更新固件文件校验和
     */
    @Schema(description = "更新固件文件校验和")
    private String md5;

    /**
     * 升级文件路径
     */
    @Schema(description = "升级文件路径")
    private String filePath;

    /**
     * 更新后产品软件版本号
     */
    @Schema(description = "更新后产品软件版本号")
    private String softVersion;

    /**
     * 上传时间
     */
    @Schema(description = "上传时间")
    private LocalDateTime createTime;
}