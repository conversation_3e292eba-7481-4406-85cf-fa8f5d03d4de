package com.superred.supervision.base.constant;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 *  SM敏感信息topic
 * <AUTHOR>
 * @since 2023/2/9 15:01
 */
@Data
@Component
public class SensitiveTopic {

    private String msgSensitiveFile = "msg_sensitive_file";

    private String fileDescSensitiveFile = "filedesc_sensitive_file";

    //C.3.8.3.1.1 告警信息上报-关键词检测
    private String msgKeywordFile = "msg_keyword_file";

    //C.3.8.3.1.2 告警文件上报-关键词检测
    private String fileDescKeywordFile = "filedesc_keyword_file";

    //C.3.8.1.1.3.1 扩展告警上报
    private String msgSensitiveExtended = "msg_sensitive_extended";

    //C.3.8.1.1.3.2 扩展告警文件上报
    private String fileDescSensitiveExtended = "filedesc_sensitive_extended";

    private String msgStyleFile = "msg_style_file";

    private String fileDescStyleFile = "filedesc_style_file";

    private String msgCoverFile = "msg_cover_file";

    private String fileDescCoverFile = "filedesc_cover_file";

    private String msgSealFile = "msg_seal_file";

    private String fileDescSealFile = "filedesc_seal_file";

    private String msgFileHash = "msg_file_hash";

    private String fileDescFileHash = "filedesc_seal_file";


}
