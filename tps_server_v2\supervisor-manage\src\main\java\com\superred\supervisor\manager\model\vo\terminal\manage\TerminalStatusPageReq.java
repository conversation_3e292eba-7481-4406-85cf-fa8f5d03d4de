package com.superred.supervisor.manager.model.vo.terminal.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 请求
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@EqualsAndHashCode(callSuper = true)
@Data

public class TerminalStatusPageReq extends PageReqDTO {

    @Schema(description = "终端状态 0 在线，1 审核失败，2 待审核，3 禁用 4 离线, 5 注销")
    private Integer status;

    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "终端责任人")
    private String userName;

    @Schema(description = "软件版本号")
    private String softVersion;

    @Schema(description = "终端在线状态 1 在线 2 离线 3 已卸载")
    private Integer connectionStatus;

    @Schema(description = "终端激活状态 0未激活，1激活")
    private Integer activateStatus;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastHeartbeatTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastHeartbeatTimeEnd;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUpgradeTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUpgradeTimeEnd;
}
