package com.superred.supervision.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervision.db.entity.DeviceInterface;
import com.superred.supervision.db.mapper.DeviceInterfaceMapper;
import com.superred.supervision.db.service.DeviceInterfaceService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 检测器网卡信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Service
public class DeviceInterfaceServiceImpl extends ServiceImpl<DeviceInterfaceMapper, DeviceInterface> implements DeviceInterfaceService {

}
