package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <AUTHOR>
 *  证书管理
 * @since 2025年03月13日
 */
@Data
public class CertificateResp {


    @Schema(description = "id")
    private Integer id;

    @Schema(description = "证书名称")
    private String certName;
    /**
     * 01 监测器 02 自监管  03 管理系统
     */
    @Schema(description = " 1监测器  2管理系统")
    private Integer type;
    /**
     * ca证书
     */
    @Schema(description = "ca证书")
    private String ca;

    /**
     * 签名私钥
     */
    @Schema(description = "加密私钥")
    private String encKey;

    /**
     * 签名私钥
     */
    @Schema(description = "加密证书")
    private String encPem;


    /**
     * 签名私钥
     */
    @Schema(description = "签名私钥")
    private String signKey;
    /**
     * 签名证书
     */
    @Schema(description = "签名证书")
    private String signPem;
    /**
     * 1 已导入  2 未导入
     */
    @Schema(description = "0已开启  1未开启")
    private Integer status;


}
