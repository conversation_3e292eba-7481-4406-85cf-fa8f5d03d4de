package com.superred.supervisor.manager.controller.policy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.constant.RiskSoftwareEnum;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyEnumResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.model.vo.policy.RuleRiskSoftwarePageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleRiskSoftwareReq;
import com.superred.supervisor.manager.model.vo.policy.RuleRiskSoftwareResp;
import com.superred.supervisor.manager.service.policy.RuleRiskSoftwareService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-28 15:27
 */
@Tag(name = "4.11. 风险软件使用策略")
@RestController
@RequestMapping("/rule/risk_software")
@Slf4j
@Validated
public class RuleRiskSoftwareController {


    @Resource
    private RuleRiskSoftwareService ruleRiskSoftwareService;

    @Operation(summary = "1 分页")
    @GetMapping("/page")
    public RPage<RuleRiskSoftwareResp> page(RuleRiskSoftwarePageReq ruleRiskSoftwarePageReq) {
        IPage<RuleRiskSoftwareResp> page = this.ruleRiskSoftwareService.page(ruleRiskSoftwarePageReq);
        return new RPage<>(page);
    }

    @Operation(summary = "2 查详情")
    @GetMapping("/{id}")
    public R<RuleRiskSoftwareResp> getById(@PathVariable("id") Long id) {
        RuleRiskSoftwareResp resp = this.ruleRiskSoftwareService.getById(id);
        return R.success(resp);
    }

    @Operation(summary = "3 新增")
    @PostMapping("/save")
    @SysLogAnn(module = LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY, operateType = OperateTypeConstants.ADD, desc = OperateTypeConstants.ADD + LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY)
    public R save(@Valid @RequestBody RuleRiskSoftwareReq RuleRiskSoftwareReq) {
        this.ruleRiskSoftwareService.save(RuleRiskSoftwareReq);
        return R.success();
    }

    @Operation(summary = "4 编辑")
    @SysLogAnn(module = LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY, operateType = OperateTypeConstants.MODIFY, desc = OperateTypeConstants.MODIFY + LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY)
    @PostMapping("/edit")
    public R edit(@Valid @RequestBody RuleRiskSoftwareReq RuleRiskSoftwareReq) {
        this.ruleRiskSoftwareService.edit(RuleRiskSoftwareReq);
        return R.success();
    }

    @Operation(summary = "5 删除")
    @PostMapping("/del")
    @SysLogAnn(module = LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY, operateType = OperateTypeConstants.DELETE, desc = OperateTypeConstants.DELETE + LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY)
    public R del(@Valid @RequestBody PolicyBatchIdsReq batchIdsReq) {
        this.ruleRiskSoftwareService.del(batchIdsReq);
        return R.success();
    }

    @Operation(summary = "6 查看策略应用策略情况")
    @PostMapping("/policy/{ruleId}")
    public R<List<RulePolicyApplyResp>> policyApply(@PathVariable("ruleId") Long ruleId) {
        List<RulePolicyApplyResp> result = this.ruleRiskSoftwareService.policyApply(ruleId);
        return R.success(result);
    }

    @Operation(summary = "7 导出")
    @PostMapping("/export")
    @SysLogAnn(module = LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY, operateType = OperateTypeConstants.EXPORT, desc = OperateTypeConstants.EXPORT + LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY)
    public void export(HttpServletResponse response, @RequestBody RuleRiskSoftwarePageReq ruleRiskSoftwarePageReq) throws IOException {

        // do something
    }

    @Operation(summary = "8 导入")
    @PostMapping("/import")
    @SysLogAnn(module = LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY, operateType = OperateTypeConstants.IMPORT, desc = OperateTypeConstants.IMPORT + LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY)
    public R<String> importFile(@RequestParam("file") MultipartFile file) {
        return null;
    }

    @Operation(summary = "9 下载模板")
    @PostMapping("/download")
    @SysLogAnn(module = LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY, operateType = OperateTypeConstants.DOWNLOAD, desc = LogTypeConstants.RISK_SOFTWARE_USAGE_STRATEGY + "下载模板")
    public void download(HttpServletResponse response) throws IOException {
        //        String filePath = "template/文件MD5策略模板.xlsx";
        //        String fileName = "文件MD5策略模板.xlsx";
        //        ClassPathResource classpathResource = new ClassPathResource(filePath);
        //        InputStream inputStream = classpathResource.getInputStream();
        //        FileUtils.downloadFileExcel(response, inputStream, fileName);

    }

    @Operation(summary = "10 获取策略内容下拉")
    @GetMapping("/list")
    public R<List<PolicyEnumResp>> list() {
        List<RiskSoftwareEnum> enumList = Arrays.asList(RiskSoftwareEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }

}
