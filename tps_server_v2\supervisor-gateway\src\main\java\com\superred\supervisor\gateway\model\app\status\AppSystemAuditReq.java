package com.superred.supervisor.gateway.model.app.status;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 系统审计日志 实体
 *
 * <AUTHOR>
 */
@Data
@ToString
public class AppSystemAuditReq {

    /**
     * 日志ID，不得使用除字母、数字、下划线"_"以外的其他字符，并且保证ID的唯一性不得重复。最长20个字节。
     */
    private String id;


    /**
     * 操作用户
     */
    @JsonProperty("user")
    private String user;

    /**
     * 事件时间
     */
    @JsonProperty("time")
    private String time;

    /**
     * 日志类型。
     远程系统命令，远程策略更新，本地用户操作，本地系统配置，本地策略配置，其他日志类型
     */
    @JsonProperty("event_type")
    private String eventType;

    /**
     * 操作类型。
     关机、重启，启动模块、停止模块，更新内置策略，升级固件；添加策略、删除策略、重置策略；用户登陆、用户注销、关机、重启、恢复出厂设置，添加用户、删除用户，导出数据，离线升级；网络配置、路由配置、管理机IP配置、管理系统IP配置、监测器部署信息配置；添加关键词、删除关键词，其他操作类型
     */
    @JsonProperty("opt_type")
    private String optType;

    /**
     * 日志详情
     */
    @JsonProperty("message")
    private String message;


}

