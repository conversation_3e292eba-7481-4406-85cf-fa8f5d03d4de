variables:
  GIT_SSL_NO_VERIFY: "1"
  CHANGELOG_PUSH_BRANCH: "master"
  DB_SCHEMA_NAME: "supervision_manage"
  MYSQL_DATABASE: "supervision_manage"

stages:
  - detect

detect:maven-compile-test:
  #执行代码编译、测试、代码质量检查
  stage: detect
  only:
    - /^issue.*$/
    - /^feat.*$/
    - /^fix.*$/
    - /^style.*$/
  script:
    - mvn clean test

detect:db-mysql-schema-check:
  stage: detect
  only:
    - /^issue.*$/
    - /^feat.*$/
    - /^fix.*$/
    - /^style.*$/
  variables:
    MYSQL_ROOT_PASSWORD: rootPassword
    MYSQL_DATABASE: ${DB_SCHEMA_NAME}
    FLYWAY_DRIVER: com.mysql.cj.jdbc.Driver
    FLYWAY_URL: ***********************/${MYSQL_DATABASE}?useUnicode=true&characterEncoding=utf8&useSSL=false
    FLYWAY_USER: root
    FLYWAY_PASSWORD: rootPassword
    FLYWAY_CONNECT_RETRIES: 1
    FLYWAY_BASELINE_ON_MIGRATE: "true"
    FLYWAY_PLACEHOLDER_REPLACEMENT: "false"
    FLYWAY_LOCATIONS: 'filesystem:supervisor-manage/src/main/resources/db/migration/mysql'
  services:
    - name: swr.ap-southeast-1.myhuaweicloud.com/library/mysql:5.7
      alias: mysql
      command: ["--lower_case_table_names=1", "--character-set-server=utf8mb4"]
  image:
    name: flyway/flyway:*******
    entrypoint: [""]
  script:
    - /flyway/flyway info
    - /flyway/flyway migrate
    - /flyway/flyway clean
  tags:
    - docker
