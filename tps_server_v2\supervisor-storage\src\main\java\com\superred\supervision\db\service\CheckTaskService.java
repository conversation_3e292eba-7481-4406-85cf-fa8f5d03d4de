package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.base.vo.check.CheckReportVo;
import com.superred.supervision.db.entity.CheckTask;
import com.superred.supervision.db.vo.agent.TaskVo;

/**
 * <p>
 * 终端检测任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
public interface CheckTaskService extends IService<CheckTask> {
    /**
     * 处理终端任务结果
     * @param taskVo 任务信息
     */
    void process(TaskVo taskVo);

    /**
     * 处理终端上报的报告
     * @param checkReportVo 产品报告
     * @param deviceId
     * @param checksum
     */
    void processFile(CheckReportVo checkReportVo, String deviceId, String checksum);

    /**
     * 修改任务
     * @param taskId
     * @param deviceId
     * @param checksum
     * @param code
     */
    void updateTaskProgress(String taskId, String deviceId, String checksum, Integer code);
}
