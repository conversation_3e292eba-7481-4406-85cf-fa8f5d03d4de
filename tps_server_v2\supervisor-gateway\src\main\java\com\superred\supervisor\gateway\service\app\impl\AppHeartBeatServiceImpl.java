package com.superred.supervisor.gateway.service.app.impl;

import com.superred.supervisor.gateway.model.app.cmd.AppCmdResp;
import com.superred.supervisor.gateway.service.app.AppHeartBeatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/6/30 15:31
 */
@Service
@Slf4j
public class AppHeartBeatServiceImpl implements AppHeartBeatService {
    /**
     * 处理终端心跳
     *
     * @return 返回处理结果列表
     */
    @Override
    public List<AppCmdResp> handleHeatbeat() {


        //todo 处理终端心跳逻辑, 需要重新设计表结构
        return Collections.emptyList();
    }
}
