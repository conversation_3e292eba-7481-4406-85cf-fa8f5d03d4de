package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * 日志风险级别.
 * 1紧急、2重要、3一般、4信息
 *
 * <AUTHOR>
 **/
@Getter
public enum LevelEnum {

    URGENT(1, "紧急"), IMPORTANT(2, "重要"), COMMONLY(3, "一般"), INFORMATION(4, "信息");

    private final int key;
    private final String value;

    LevelEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }


}
