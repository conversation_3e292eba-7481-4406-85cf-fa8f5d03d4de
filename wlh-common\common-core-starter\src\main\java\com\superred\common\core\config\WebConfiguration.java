package com.superred.common.core.config;

import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.superred.common.core.exception.GlobalExceptionHandler;
import com.superred.common.core.filter.GzipDecompressFilter;
import com.superred.common.core.filter.RequestLoggingFilter;
import com.superred.common.core.filter.SecurityHeaderFilter;
import com.superred.common.core.utils.SpringContextHolder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;


/**
 * springmvc web 配置
 *
 * <AUTHOR>
 * @date 2023/12/12
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    private static final String DEFAULT_DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd";
    private static final String DEFAULT_TIME_PATTERN = "HH:mm:ss";


    /**
     *
     *  1. 需要弄懂springboot 自动装配objectMapper的原理
     *  2. 统一使用 Jackson2ObjectMapperBuilderCustomizer 来定制 ObjectMapper , 确保springboot jackson 默认的行为，以及spring.jackson.** 的配置都能生效
     *  3. 如果其他地方需要定制 ObjectMapper , 也应该使用 Jackson2ObjectMapperBuilderCustomizer 来定制
     *  4. （重要！！）如果直接使用Jackson2ObjectMapperBuilder 来创建 ObjectMapper , 那么就会覆盖所有的默认配置和其他的定制
     *
     * @see org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration 中 class StandardJackson2ObjectMapperBuilderCustomizer
     * @return Jackson2ObjectMapperBuilderCustomizer
     */
    @Bean
    public CommonJackson2ObjectMapperBuilderCustomizer commonJackson2ObjectMapperBuilderCustomizer() {
        return new CommonJackson2ObjectMapperBuilderCustomizer();

    }


    /**
     * xss 过滤器
     *
     * @return {@link FilterRegistrationBean }<{@link SecurityHeaderFilter }>
     */
    @Bean
    @ConditionalOnProperty(
            prefix = "superred.common.starter",
            name = "enable-security-header-filter",
            havingValue = "true",
            matchIfMissing = true
    )// 默认注册
    public FilterRegistrationBean<SecurityHeaderFilter> xssFilter() {
        FilterRegistrationBean<SecurityHeaderFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new SecurityHeaderFilter());
        registrationBean.setName("SecurityHeaderFilter");
        registrationBean.setOrder(1);
        return registrationBean;
    }

    /**
     * gZip过滤器
     *
     * @return {@link FilterRegistrationBean }<{@link GzipDecompressFilter }>
     */
    @Bean
    public FilterRegistrationBean<GzipDecompressFilter> gzipFilter() {
        FilterRegistrationBean<GzipDecompressFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new GzipDecompressFilter());
        registrationBean.setName("GzipDecompressFilter");
        registrationBean.setOrder(2);
        return registrationBean;
    }


    /**
     * gZip过滤器
     *
     * @return {@link FilterRegistrationBean }<{@link GzipDecompressFilter }>
     */
    @Bean
    @ConditionalOnProperty(
            prefix = "superred.common.starter",
            name = "enable-logging-filter",
            havingValue = "true",
            matchIfMissing = false
    )// 默认不注册
    public FilterRegistrationBean<RequestLoggingFilter> loggingFilter() {
        FilterRegistrationBean<RequestLoggingFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new RequestLoggingFilter());
        registrationBean.setName("RequestLoggingFilter");
        registrationBean.setOrder(3);
        return registrationBean;
    }

    /**
     * 全局异常处理
     *
     * @return {@link GlobalExceptionHandler }
     */
    @Bean
    @ConditionalOnProperty(
            prefix = "superred.common.starter",
            name = "enable-default-exception-handler",
            havingValue = "true",
            matchIfMissing = true
    )// 默认注册
    public GlobalExceptionHandler globalExceptionHandler() {
        return new GlobalExceptionHandler();
    }


    @Bean
    public SpringContextHolder springContextHolder() {
        return new SpringContextHolder();
    }

    public static class CommonJackson2ObjectMapperBuilderCustomizer implements Jackson2ObjectMapperBuilderCustomizer, Ordered {

        @Override
        public void customize(Jackson2ObjectMapperBuilder jacksonObjectMapperBuilder) {
            // 设置全局的日期格式
            jacksonObjectMapperBuilder.failOnUnknownProperties(false)
                    .serializerByType(Long.class, ToStringSerializer.instance)
                    .serializerByType(Long.TYPE, ToStringSerializer.instance)
                    .serializerByType(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DEFAULT_DATETIME_PATTERN)))
                    .serializerByType(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern(DEFAULT_DATE_PATTERN)))
                    .serializerByType(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern(DEFAULT_TIME_PATTERN)))
                    .serializerByType(Date.class, new DateSerializer(false, new SimpleDateFormat(DEFAULT_DATETIME_PATTERN)))
                    .deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(DEFAULT_DATETIME_PATTERN)))
                    .deserializerByType(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern(DEFAULT_DATE_PATTERN)))
                    .deserializerByType(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern(DEFAULT_TIME_PATTERN)))
                    .deserializerByType(Date.class, new DateDeserializers.DateDeserializer(DateDeserializers.DateDeserializer.instance, new SimpleDateFormat(DEFAULT_DATETIME_PATTERN), DEFAULT_DATETIME_PATTERN));
        }


        @Override
        public int getOrder() {
            return 1;
        }
    }
}
