package com.superred.supervisor.manager.common.enums.operation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 1. 按部门 2.按设备 3.按ip地址段
 *
 * <AUTHOR>
 * @since 2025/7/25 10:27
 */
@Getter
@AllArgsConstructor
public enum OpetationPublishType {

    DEPARTMENT(1, "按部门"),
    DEVICE(2, "按设备"),
    IP_RANGE(3, "按IP地址段");

    private final int code;
    private final String description;

    public static OpetationPublishType fromCode(int code) {
        for (OpetationPublishType type : OpetationPublishType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}
