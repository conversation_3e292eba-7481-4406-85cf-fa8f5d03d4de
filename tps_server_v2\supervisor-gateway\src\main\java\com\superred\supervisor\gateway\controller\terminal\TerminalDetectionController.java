package com.superred.supervisor.gateway.controller.terminal;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.supervisor.common.constant.CommonConstant;
import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.service.terminal.TerminalDetectionService;
import com.superred.supervisor.gateway.utils.WebExtUtils;
import com.superred.supervisor.standard.v202505.terminal.detection.TerminalAlarmReq;
import com.superred.supervisor.standard.v202505.terminal.detection.TerminalFileFilterAlertReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.Pattern;

/**
 * 检测数据上报
 *
 * <AUTHOR>
 * @since 2025/5/29 14:00
 */
@Tag(name = "1.4 检测数据上报(2025-02)")
@RestController
@Slf4j
@RequestMapping("/C2/")
public class TerminalDetectionController {

    @Resource
    private TerminalDetectionService terminalDetectionService;


    /**
     *  文件筛选数据上报
     *
     * @return {@link ApiResponse }
     */
    @PostMapping("/file_filter/{type}/alert")
    @Operation(summary = "C.3.7.1 文件筛选数据上报")
    @ApiOperationSupport(order = 1)
    public ApiResponse<String> reportFileFilterAlter(
            @RequestBody TerminalFileFilterAlertReq req,
            @PathVariable @Pattern(regexp = "^(keyword|md5|security_classification|secret_level)_filter$", message = "告警类型不支持") String type) {


        terminalDetectionService.reportFileFilterAlter(req, type);
        return ApiResponse.success();
    }


    /**
     * C.3.7.2 终端检测组件异常通信检测数据上报
     *
     **/
    @PostMapping("/alarm/{type}/alert")
    @Operation(summary = "C.3.7.2 终端检测组件异常通信检测数据上报")
    @ApiOperationSupport(order = 2)
    public ApiResponse<String> alarmAlert(
            @RequestBody TerminalAlarmReq req,
            @PathVariable @Pattern(regexp = "^(ip|domain)_blacklist$", message = "告警类型不支持") String type) {


        terminalDetectionService.agentAlarmAlert(req, type);
        return ApiResponse.success();
    }


    /**
     * C.3.7.3 终端检测组件异常通信检测数据上报
     *
     **/
    @PostMapping("/alarm/{type}/attack_file")
    @Operation(summary = "C.3.7.3 终端检测组件异常通信检测数据上报")
    @ApiOperationSupport(order = 3)
    public void agentAttackFile(
            @RequestHeader(CommonConstant.FILE_DESC_HEADER) String fileDesc,
            @PathVariable @Pattern(regexp = "^(ip|domain)_blacklist$", message = "告警类型不支持") String type) {

        MultipartFile singleMultipartFile = WebExtUtils.getSingleMultipartFile();


        terminalDetectionService.uploadAttackFile(fileDesc, type, singleMultipartFile);

    }
}
