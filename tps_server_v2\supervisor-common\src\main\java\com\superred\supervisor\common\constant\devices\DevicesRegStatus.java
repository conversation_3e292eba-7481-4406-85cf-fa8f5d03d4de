package com.superred.supervisor.common.constant.devices;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 注册状态，0 审核通过 1  审核不通过 2 待审核 3 禁用 4 注销
 *
 * <AUTHOR>
 * @since 2025/6/30 10:34
 */
@Getter
@AllArgsConstructor
public enum DevicesRegStatus {

    PASS(0, "审核通过"),
    REJECT(1, "审核不通过"),
    PENDING(2, "待审核"),
    DISABLED(3, "禁用"),
    CANCELED(4, "注销");

    @EnumValue
    private final Integer value;

    private final String desc;

    public static DevicesRegStatus of(Integer code) {
        for (DevicesRegStatus status : DevicesRegStatus.values()) {
            if (status.getValue().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
