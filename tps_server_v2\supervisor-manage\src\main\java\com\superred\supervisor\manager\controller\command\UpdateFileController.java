package com.superred.supervisor.manager.controller.command;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.annotation.role.IgnoreAuth;
import com.superred.supervisor.common.entity.command.UpdateFile;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.command.AgentInnerPolicyDTO;
import com.superred.supervisor.manager.model.vo.command.AgentInnerPolicyVO;
import com.superred.supervisor.manager.model.vo.command.CertListReq;
import com.superred.supervisor.manager.model.vo.command.CertUploadResp;
import com.superred.supervisor.manager.model.vo.command.UpdateFileListReq;
import com.superred.supervisor.manager.model.vo.command.UpdateFileVO;
import com.superred.supervisor.manager.service.command.UpdateFileService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 系统升级文件
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "8.2 系统升级文件")
@RestController
@AllArgsConstructor
@RequestMapping("/update_file/")
@IgnoreAuth
public class UpdateFileController {

    @Resource
    private UpdateFileService updateFileService;


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1.上传系统软件升级包")
    @PostMapping("/software_update/upload")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_UPGRADE_FILE_MANAGEMENT, operateType = OperateTypeConstants.UPLOAD, desc = "上传系统软件升级包")
    public R<String> softwareUpdateUpload(@RequestParam("file") MultipartFile file) {
        updateFileService.softwareUpdateUpload(file);
        return R.success();
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2.上传内置策略更新包")
    @PostMapping("/inner_policy_update/upload")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_UPGRADE_FILE_MANAGEMENT, operateType = OperateTypeConstants.UPLOAD, desc = "上传内置策略更新包")
    public R<String> innerPolicyUpdateUpload(@RequestParam("file") MultipartFile file) {
        updateFileService.innerPolicyUpdateUpload(file);
        return R.success();
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2.查询升级文件更新包")
    @GetMapping("/list")
    public R<List<UpdateFileVO>> list(UpdateFileListReq req) {
        List<UpdateFile> list = updateFileService.list(req);
        if (CollUtil.isEmpty(list)) {
            return R.success(Collections.emptyList());
        }
        return R.success(BeanUtil.copyToList(list, UpdateFileVO.class));
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "3.删除升级组件/内置规则更新包")
    @PostMapping("/cmd/delete")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_UPGRADE_FILE_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除升级组件/内置规则更新包")
    public R<String> cmdDelete(@RequestBody List<Integer> idList) {

        updateFileService.cmdDelete(idList);
        return R.success();
    }

    @ApiOperationSupport(order = 4)
    @Operation(summary = "4.上传通信证书文件")
    @PostMapping("/cert/upload")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_UPGRADE_FILE_MANAGEMENT, operateType = OperateTypeConstants.UPLOAD, desc = "上传通信证书文件")
    public R<CertUploadResp> communicationCertUpload(@RequestParam("file") MultipartFile file) {

        CertUploadResp resp = updateFileService.certUpload(file);
        return R.success(resp);
    }

    @ApiOperationSupport(order = 5)
    @Operation(summary = "5.查询通信证书")
    @GetMapping("/cert/list")
    public R<List<CertUploadResp>> certList(CertListReq req) {
        List<CertUploadResp> list = updateFileService.certList(req);
        return R.success(list);
    }

    @ApiOperationSupport(order = 6)
    @Operation(summary = "6.删除通信证书")
    @PostMapping("/cert/delete")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_UPGRADE_FILE_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除通信证书")
    public R<String> certDelete(@RequestBody CertListReq req) {

        updateFileService.certDelete(req);
        return R.success();
    }

    @ApiOperationSupport(order = 7)
    @Operation(summary = "7.上传终端内置策略文件")
    @PostMapping("/agent/inner_policy/upload")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_UPGRADE_FILE_MANAGEMENT, operateType = OperateTypeConstants.UPLOAD, desc = "上传终端内置策略文件")
    public R<AgentInnerPolicyVO> agentInnerPolicyUpload(@RequestParam("file") MultipartFile file) {

        AgentInnerPolicyVO resp = updateFileService.agentInnerPolicyUpload(file);
        return R.success(resp);
    }

    @ApiOperationSupport(order = 8)
    @Operation(summary = "8.查询终端内置策略列表")
    @GetMapping("/agent/inner_policy/list")
    public R<List<AgentInnerPolicyVO>> agentInnerPolicyList(AgentInnerPolicyDTO req) {

        List<AgentInnerPolicyVO> resp = updateFileService.agentInnerPolicyList(req);
        return R.success(resp);
    }

    @ApiOperationSupport(order = 9)
    @Operation(summary = "9.删除终端内置策略")
    @GetMapping("/agent/inner_policy/delete")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_UPGRADE_FILE_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除终端内置策略")
    public R<String> agentInnerPolicyDelete(AgentInnerPolicyDTO req) {

        updateFileService.agentInnerPolicyDelete(req);
        return R.success();
    }

}
