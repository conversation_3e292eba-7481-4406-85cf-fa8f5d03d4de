#!/bin/sh

DB_NAME=$1
MARIADB_PASSWORD=$2

if [ $# -lt 1 ]; then
    echo "缺少参数" >&2
    echo ""
    exit 1
fi

mysql -h 127.0.0.1 -u superred -p$MARIADB_PASSWORD -e "DROP DATABASE IF EXISTS $DB_NAME;"
if [ $? -ne 0 ]; then
  exit 1
else
  echo ""
fi
mysql -h 127.0.0.1 -u superred -p$MARIADB_PASSWORD -e "CREATE DATABASE $DB_NAME DEFAULT CHARSET utf8mb4 COLLATE utf8mb4_general_ci;"
if [ $? -ne 0 ]; then
  exit 1
else
  echo "创建数据库成功：$DB_NAME"
  echo ""
fi

mysql -h 127.0.0.1 -u superred -p$MARIADB_PASSWORD ${DB_NAME} < /opt/wlh/supervisor/bin/db/create_local_device.sql

if [ $? -ne 0 ]; then
  exit 1
else
  echo "创建表成功：local_device_info"
  echo ""
fi