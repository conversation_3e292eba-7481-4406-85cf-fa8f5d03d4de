package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月14日
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClientInstallPackageUploadReq implements Serializable {

    private static final long serialVersionUID = 5818938988517242707L;

    /**
     * 文件
     */
    @Schema(description = "上传文件")
    @NotEmpty(message = "请选择文件！")
    private MultipartFile file;


    @Schema(description = "安装包描述")
    private String remark;


    @Schema(description = "安装包启用/停用状态  0停用  1启用")
    private Integer isPublish;


}
