package com.superred.supervisor.manager.model.dto.system;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户查询DTO
 */
@Data
public class UserQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Integer id;

    /**
     * 账号
     */
    @Schema(description = "账号")
    private String username;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String realName;

    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    private Integer orgId;

    /**
     * 组织机构ids
     */
    @Schema(description = "组织机构ids")
    private List<Integer> orgIds;

    /**
     * 角色ID
     */
    @Schema(description = "查询角色ids")
    private List<Integer> role;
}
