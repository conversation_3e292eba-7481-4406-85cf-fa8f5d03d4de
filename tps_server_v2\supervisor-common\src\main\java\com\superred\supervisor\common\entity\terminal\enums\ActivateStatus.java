package com.superred.supervisor.common.entity.terminal.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 激活状态， 1 未激活 2 已激活
 *
 * <AUTHOR>
 * @since 2025/3/18 19:43
 */
@Getter
@AllArgsConstructor
public enum ActivateStatus implements IEnum<Integer> {
    /**
     * 未激活
     */
    NOT_ACTIVE(1, "未激活"),
    /**
     * 激活
     */
    ACTIVE(2, "已激活");

    private final Integer value;


    private final String desc;

}
