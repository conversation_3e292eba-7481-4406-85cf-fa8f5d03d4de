package com.superred.supervisor.common.constant.devices;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 在线状态， 1 在线 2 离线 3 已卸载
 *
 * <AUTHOR>
 * @since 2025/6/30 10:57
 */
@Getter
@AllArgsConstructor
public enum DevicesConnectStatus {

    ONLINE(1, "在线"),
    OFFLINE(2, "离线"),
    UNINSTALLED(3, "已卸载");

    @EnumValue
    private final Integer value;
    private final String desc;

    public static DevicesConnectStatus of(Integer code) {
        for (DevicesConnectStatus status : DevicesConnectStatus.values()) {
            if (status.getValue().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
