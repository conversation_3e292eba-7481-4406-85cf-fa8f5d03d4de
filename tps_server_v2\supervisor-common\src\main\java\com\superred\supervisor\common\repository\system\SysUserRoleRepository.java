package com.superred.supervisor.common.repository.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.system.SysUserRole;
import com.superred.supervisor.common.mapper.system.SysUserRoleMapper;
import org.springframework.stereotype.Repository;

/**
 * 用户角色表 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:23
 */
@Repository
public class SysUserRoleRepository extends ServiceImpl<SysUserRoleMapper, SysUserRole> {

    public void removeRoleByUserId(Integer id) {
        LambdaQueryWrapper<SysUserRole> del = Wrappers.lambdaQuery(SysUserRole.class).eq(SysUserRole::getUserId, id);
        this.remove(del);
    }
}

