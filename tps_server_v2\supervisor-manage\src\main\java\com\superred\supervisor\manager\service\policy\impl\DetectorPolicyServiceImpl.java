package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleThreeEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.constant.policy.PolicyIssueStatusEnum;
import com.superred.supervisor.common.constant.policy.PolicyIssueTypeEnum;
import com.superred.supervisor.common.entity.devices.DeviceInfo;
import com.superred.supervisor.common.entity.policy.DetectorPolicy;
import com.superred.supervisor.common.entity.policy.DetectorPolicyDevice;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.DeviceRule;
import com.superred.supervisor.common.entity.policy.IssuePolicy;
import com.superred.supervisor.common.repository.devices.DeviceInfoRepository;
import com.superred.supervisor.common.repository.policy.DetectorPolicyDeviceRepository;
import com.superred.supervisor.common.repository.policy.DetectorPolicyRepository;
import com.superred.supervisor.common.repository.policy.DetectorPolicyRuleRepository;
import com.superred.supervisor.common.repository.policy.DeviceRuleRepository;
import com.superred.supervisor.common.repository.policy.IssuePolicyRepository;
import com.superred.supervisor.manager.constant.IssueTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.DetectorPolicyDeviceDTO;
import com.superred.supervisor.manager.model.dto.policy.DetectorPolicyRuleDTO;
import com.superred.supervisor.manager.model.dto.policy.PolicyResultFailDTO;
import com.superred.supervisor.manager.model.vo.policy.DetectorPolicyIssueReq;
import com.superred.supervisor.manager.model.vo.policy.DetectorPolicyPageReq;
import com.superred.supervisor.manager.model.vo.policy.DetectorPolicyReq;
import com.superred.supervisor.manager.model.vo.policy.DetectorPolicyResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyHistoryVersionResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyIssueDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyIssueDeviceResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyTreeResp;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.DetectorPolicyService;
import com.superred.supervisor.manager.service.policy.RuleService;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-13 15:18
 */
@Slf4j
@Service("detectorPolicyService")
@AllArgsConstructor
public class DetectorPolicyServiceImpl implements DetectorPolicyService {

    @Resource
    private DetectorPolicyRepository detectorPolicyRepository;
    @Resource
    private DetectorPolicyDeviceRepository detectorPolicyDeviceRepository;
    @Resource
    private DetectorPolicyRuleRepository detectorPolicyRuleRepository;
    @Resource
    private IssuePolicyRepository issuePolicyRepository;
    @Resource
    private DeviceRuleRepository deviceRuleRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;
    @Resource
    private DeviceInfoRepository deviceInfoRepository;
    @Resource
    private List<RuleService> ruleServiceList;

    @Override
    public IPage<DetectorPolicyResp> page(DetectorPolicyPageReq detectorPolicyPageReq) {
        // 查询
        LambdaQueryWrapper<DetectorPolicy> queryWrapper = new LambdaQueryWrapper<DetectorPolicy>()
                .eq(StrUtil.isNotBlank(detectorPolicyPageReq.getName()), DetectorPolicy::getName, detectorPolicyPageReq.getName())
                .eq(StrUtil.isNotBlank(detectorPolicyPageReq.getModule()), DetectorPolicy::getModule, detectorPolicyPageReq.getModule())
                .eq(StrUtil.isNotBlank(detectorPolicyPageReq.getCmd()), DetectorPolicy::getCmd, detectorPolicyPageReq.getCmd())
                .orderByDesc(DetectorPolicy::getCreateTime);
        Page<DetectorPolicy> page = new Page<>(detectorPolicyPageReq.getStart(), detectorPolicyPageReq.getLimit());
        IPage<DetectorPolicy> page1 = this.detectorPolicyRepository.page(page, queryWrapper);
        return page1.convert(item -> {
            DetectorPolicyResp detectorPolicyResp = DetectorPolicyResp.fromDetectorPolicy(item);
            long issueCount = this.issuePolicyRepository.count(Wrappers.<IssuePolicy>lambdaQuery()
                    .eq(IssuePolicy::getVersion, item.getVersion())
                    .eq(IssuePolicy::getStatus, PolicyIssueStatusEnum.ISSUE.getKey()));
            long unIssueCount = this.issuePolicyRepository.count(Wrappers.<IssuePolicy>lambdaQuery()
                    .eq(IssuePolicy::getVersion, item.getVersion())
                    .eq(IssuePolicy::getStatus, PolicyIssueStatusEnum.UN_ISSUE.getKey()));
            long num = this.detectorPolicyRuleRepository.count(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, item.getId()));
            detectorPolicyResp.setIssueCount((int) issueCount);
            detectorPolicyResp.setAllCount((int) (issueCount + unIssueCount));
            detectorPolicyResp.setNum((int) num);
            RuleService ruleService = this.ruleServiceList.stream().filter(item1 -> item1.isSupported(detectorPolicyResp.getModule())).findFirst().orElse(null);
            PolicyModuleResp module = ruleService.getModule();
            detectorPolicyResp.setModuleParentStr(module.getModuleParentStr());
            detectorPolicyResp.setModuleStr(module.getModuleStr());
            return detectorPolicyResp;
        });
    }

    @Override
    public DetectorPolicyResp getById(Long policyId) {
        DetectorPolicy detectorPolicy = this.detectorPolicyRepository.getById(policyId);
        DetectorPolicyResp detectorPolicyResp = DetectorPolicyResp.fromDetectorPolicy(detectorPolicy);
        if (detectorPolicyResp != null) {
            // 绑定监测器
            List<DetectorPolicyDevice> detectorPolicyDeviceList = this.detectorPolicyDeviceRepository.list(Wrappers.<DetectorPolicyDevice>lambdaQuery()
                    .eq(DetectorPolicyDevice::getPolicyId, policyId));
            detectorPolicyResp.setDeviceList(DetectorPolicyDeviceDTO.fromDetectorPolicyDevice(detectorPolicyDeviceList));
            // 绑定规则
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, policyId));
            detectorPolicyResp.setRuleList(DetectorPolicyRuleDTO.fromDetectorPolicyRule(detectorPolicyRuleList));
            // 查询具体规则信息
            List<Long> ruleIdList = detectorPolicyRuleList.stream().map(DetectorPolicyRule::getRuleId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ruleIdList)) {
                // 需要补充
                this.getRuleDetail(ruleIdList, detectorPolicy.getModule(), detectorPolicyResp);
            }
        }
        return detectorPolicyResp;
    }

    @Override
    public IPage<PolicyIssueDetailResp> detail(DetectorPolicyPageReq detectorPolicyPageReq) {
        Page<DetectorPolicyRule> page = new Page<>(detectorPolicyPageReq.getStart(), detectorPolicyPageReq.getLimit());
        Page<DetectorPolicyRule> policyRulePage = this.detectorPolicyRuleRepository.page(page, Wrappers.<DetectorPolicyRule>lambdaQuery()
                .eq(DetectorPolicyRule::getPolicyId, detectorPolicyPageReq.getPolicyId())
                .eq(StrUtil.isNotBlank(detectorPolicyPageReq.getRuleId()), DetectorPolicyRule::getRuleId, detectorPolicyPageReq.getRuleId()));
        // 查询设备列表
        List<DeviceInfo> deviceInfos = this.deviceInfoRepository.list(Wrappers.emptyWrapper());
        deviceInfos = CollectionUtil.isNotEmpty(deviceInfos) ? deviceInfos : new ArrayList<>();
        Map<String, DeviceInfo> deviceMap = deviceInfos.stream().collect(Collectors.toMap(DeviceInfo::getDeviceId, item -> item));
        // 查询策略下发记录表
        List<IssuePolicy> policyIssue = this.issuePolicyRepository.list(Wrappers.<IssuePolicy>lambdaQuery()
                .eq(IssuePolicy::getPolicyId, detectorPolicyPageReq.getPolicyId()));

        return policyRulePage.convert(policyRule -> {
            String ruleId = policyRule.getRuleId().toString();
            PolicyIssueDetailResp policyIssueDetailResp = new PolicyIssueDetailResp();
            List<PolicyIssueDeviceResp> successDeviceList = new ArrayList<>();
            List<PolicyIssueDeviceResp> failDeviceList = new ArrayList<>();
            List<PolicyIssueDeviceResp> issueDeviceList = new ArrayList<>();
            for (IssuePolicy issue : policyIssue) {
                String deviceId = issue.getDeviceId();
                String organs = "";
                DeviceInfo deviceInfo = deviceMap.get(deviceId);
                if (deviceInfo != null) {
                    organs = deviceInfo.getOrgans();
                }
                PolicyIssueDeviceResp policyIssueDeviceResp = new PolicyIssueDeviceResp();
                policyIssueDeviceResp.setDeviceId(deviceId);
                policyIssueDeviceResp.setDeviceType(PolicyDeviceTypeEnum.JCQ.getValue());
                policyIssueDeviceResp.setOrgans(organs);
                issueDeviceList.add(policyIssueDeviceResp);
                if (StrUtil.isNotBlank(issue.getSuccess())) {
                    List<String> successIdList = JsonUtil.fromJson(issue.getSuccess(), new TypeReference<List<String>>() {
                    });
                    String ruleIdFilter = successIdList.stream().filter(item -> item.equals(ruleId)).findFirst().orElse(null);
                    if (StrUtil.isNotBlank(ruleIdFilter)) {
                        successDeviceList.add(policyIssueDeviceResp);
                    }
                }
                if (StrUtil.isNotBlank(issue.getFail())) {
                    List<PolicyResultFailDTO> policyResultFailDTOS = JsonUtil.fromJson(issue.getFail(), new TypeReference<List<PolicyResultFailDTO>>() {
                    });
                    PolicyResultFailDTO policyResultFailDTO = policyResultFailDTOS.stream().filter(item -> item.getRuleId().equals(ruleId)).findFirst().orElse(null);
                    if (policyResultFailDTO != null) {
                        policyIssueDeviceResp.setMessage(policyResultFailDTO.getMsg());
                        failDeviceList.add(policyIssueDeviceResp);
                    }
                }
            }
            policyIssueDetailResp.setRuleId(ruleId);
            policyIssueDetailResp.setIssueDeviceAmount(policyIssue.size());
            policyIssueDetailResp.setIssueDeviceList(issueDeviceList);
            policyIssueDetailResp.setSuccessDeviceAmount(successDeviceList.size());
            policyIssueDetailResp.setFailDeviceAmount(failDeviceList.size());
            policyIssueDetailResp.setSuccessDeviceList(successDeviceList);
            policyIssueDetailResp.setFailDeviceList(failDeviceList);
            return policyIssueDetailResp;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(DetectorPolicyReq detectorPolicyReq) {
        // 验证重复
        this.validateSaveRepeat(detectorPolicyReq);
        // 新增策略表
        DetectorPolicy detectorPolicy = fromDetectorPolicyReq(detectorPolicyReq);
        detectorPolicy.setVersion(this.ruleIdBuilder.buildPolicyVersion());
        detectorPolicy.setNum(detectorPolicyReq.getRuleIds().size());
        this.detectorPolicyRepository.save(detectorPolicy);
        // 修改策略应用状态 + 添加关系表
        if (CollectionUtil.isNotEmpty(detectorPolicyReq.getRuleIds())) {
            this.updatePolicyStatus(detectorPolicyReq.getRuleIds(), detectorPolicy.getId(), detectorPolicyReq.getModule());
        }
        // 进行下发
        if (CollectionUtil.isNotEmpty(detectorPolicyReq.getDeviceIds())) {
            DetectorPolicyIssueReq detectorPolicyIssueReq = new DetectorPolicyIssueReq();
            detectorPolicyIssueReq.setPolicyId(detectorPolicy.getId());
            detectorPolicyIssueReq.setModule(detectorPolicy.getModule());
            detectorPolicyIssueReq.setDeviceIds(detectorPolicyReq.getDeviceIds());
            this.issue(detectorPolicyIssueReq);
        }
    }

    public static DetectorPolicy fromDetectorPolicyReq(DetectorPolicyReq detectorPolicyReq) {
        return DetectorPolicy.builder()
                .name(detectorPolicyReq.getName())
                .module(detectorPolicyReq.getModule())
                .version(detectorPolicyReq.getVersion())
                .num(detectorPolicyReq.getNum())
                .createTime(detectorPolicyReq.getCreateTime())
                .description(detectorPolicyReq.getDescription())
                .issuedStatus(detectorPolicyReq.getIssuedStatus())
                .issuedTime(detectorPolicyReq.getIssuedTime())
                .isDefault(detectorPolicyReq.getIsDefault())
                .cmd(detectorPolicyReq.getCmd())
                .build();
    }

    @Override
    public void edit(DetectorPolicyReq detectorPolicyReq) {
        // 验证重复
        this.validateEditRepeat(detectorPolicyReq);
        // 更新策略表
        this.detectorPolicyRepository.update(Wrappers.<DetectorPolicy>lambdaUpdate()
                .eq(DetectorPolicy::getId, detectorPolicyReq.getId())
                .set(DetectorPolicy::getNum, detectorPolicyReq.getRuleIds().size()));
        // 删除之前的关系表
        this.detectorPolicyRuleRepository.remove(Wrappers.<DetectorPolicyRule>lambdaQuery()
                .eq(DetectorPolicyRule::getPolicyId, detectorPolicyReq.getId()));
        // 修改策略应用状态 + 添加关系表
        if (CollectionUtil.isNotEmpty(detectorPolicyReq.getRuleIds())) {
            // 需要补逻辑
            this.updatePolicyStatus(detectorPolicyReq.getRuleIds(), detectorPolicyReq.getId(), detectorPolicyReq.getModule());
        }
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 策略删除，将原生效范围的设备策略改为默认策略，删除策略记录级策略和规则的关系表
        // 判断该策略是否存在生效范围，如果存在则不能删除
        List<DetectorPolicyDevice> list = this.detectorPolicyDeviceRepository.list(Wrappers.<DetectorPolicyDevice>lambdaQuery()
                .in(DetectorPolicyDevice::getPolicyId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("存在正在生效的检测器，请检查策略是否生效");
        }
        this.detectorPolicyRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public void issue(DetectorPolicyIssueReq detectorPolicyIssueReq) {
        DetectorPolicy detectorPolicy = this.detectorPolicyRepository.getById(detectorPolicyIssueReq.getPolicyId());
        if (detectorPolicy.getIssuedStatus() == PolicyIssueStatusEnum.ISSUE.getKey()) {
            throw new BaseBusinessException("该策略已经下发，不可重复下发");
        }
        // 修改下发状态下发时间
        DetectorPolicy updateBean = DetectorPolicy.builder()
                .id(detectorPolicyIssueReq.getPolicyId())
                .issuedStatus(PolicyIssueStatusEnum.ISSUE.getKey())
                .issuedTime(DateUtil.date())
                .build();
        this.detectorPolicyRepository.updateById(updateBean);
        // 下发给监测器的策略详情,需要参照标准变动
        List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                .eq(DetectorPolicyRule::getPolicyId, detectorPolicy.getId())
                .eq(DetectorPolicyRule::getModule, detectorPolicy.getModule()));
        List<Long> ruleIds = detectorPolicyRuleList.stream().map(DetectorPolicyRule::getRuleId).collect(Collectors.toList());
        String config = this.getRuleConfig(ruleIds, detectorPolicy.getModule());
        List<IssuePolicy> issuePolicyList = new ArrayList<>();
        for (String deviceId : detectorPolicyIssueReq.getDeviceIds()) {
            // 更新检测器策略
            this.updateDeviceRule(deviceId, detectorPolicy.getCmd(), detectorPolicy.getModule(), ruleIds);
            IssuePolicy issuePolicy = IssuePolicy.builder()
                    .id(null)
                    .deviceId(deviceId)
                    .type(IssueTypeEnum.POLICY.getKey())
                    .module(detectorPolicy.getModule())
                    .version(detectorPolicy.getVersion())
                    .cmd(detectorPolicy.getCmd())
                    .num(detectorPolicy.getNum())
                    .config(config)
                    .policyId(detectorPolicyIssueReq.getPolicyId())
                    .build();
            issuePolicyList.add(issuePolicy);
        }
        this.issuePolicyRepository.saveBatch(issuePolicyList);
    }

    /**
     * 更新检测器策略
     */
    private void updateDeviceRule(String deviceId, String cmd, String module, List<Long> ruleIds) {
        PolicyIssueTypeEnum policyIssueTypeEnum = PolicyIssueTypeEnum.getByKey(cmd);
        if (policyIssueTypeEnum == null) {
            throw new BaseBusinessException("下发命令错误");
        }
        switch (policyIssueTypeEnum) {
            case CMD_ADD:
                //判断每一个规则是否已经存在关系中，如果没有存在则添加到数据库中
                this.cmdAdd(deviceId, module, ruleIds);
                break;
            case CMD_RESET:
                //先删除原来的规则，然后全量的更新新的规则
                this.cmdReset(deviceId, module, ruleIds);
                break;
            case CMD_DEL:
                //根据规则id设备id和模块删除对应的规则
                this.cmdDel(deviceId, module, ruleIds);
                break;
            default:
                throw new BaseBusinessException("下发命令错误");
        }
    }

    private void cmdDel(String deviceId, String module, List<Long> ruleIds) {
        this.deviceRuleRepository.remove(Wrappers.<DeviceRule>lambdaQuery()
                .eq(DeviceRule::getDeviceId, deviceId)
                .in(DeviceRule::getRuleId, ruleIds)
                .eq(DeviceRule::getModule, module));
    }

    private void cmdReset(String deviceId, String module, List<Long> ruleIds) {
        this.deviceRuleRepository.getBaseMapper().delete(Wrappers.<DeviceRule>lambdaQuery()
                .eq(DeviceRule::getDeviceId, deviceId)
                .eq(DeviceRule::getModule, module));
        List<DeviceRule> deviceRuleList = new ArrayList<>();
        for (Long ruleId : ruleIds) {
            DeviceRule deviceRule = DeviceRule.builder()
                    .deviceId(deviceId)
                    .ruleId(ruleId)
                    .module(module)
                    .build();
            deviceRuleList.add(deviceRule);
        }
        this.deviceRuleRepository.saveBatch(deviceRuleList);
    }

    private void cmdAdd(String deviceId, String module, List<Long> ruleIds) {
        List<DeviceRule> deviceRuleList = new ArrayList<>();
        List<DeviceRule> queryList = this.deviceRuleRepository.list(Wrappers.<DeviceRule>lambdaQuery()
                .eq(DeviceRule::getDeviceId, deviceId)
                .in(DeviceRule::getRuleId, ruleIds)
                .eq(DeviceRule::getModule, module));
        List<Long> queryRuleIdList = queryList.stream().map(DeviceRule::getRuleId).collect(Collectors.toList());
        for (Long ruleId : ruleIds) {
            if (queryRuleIdList.contains(ruleId)) {
                continue;
            }
            DeviceRule deviceRule = DeviceRule.builder()
                    .deviceId(deviceId)
                    .ruleId(ruleId)
                    .module(module)
                    .build();
            deviceRuleList.add(deviceRule);
        }
        this.deviceRuleRepository.saveBatch(deviceRuleList);
    }

    /**
     * 获取加密config字段值
     *
     * @param ruleIds
     * @param module
     * @return
     */
    private String getRuleConfig(List<Long> ruleIds, String module) {
        RuleService ruleService = this.getRuleServiceByModule(module);
        String config = ruleService.getRuleConfig(ruleIds);
        return PolicyUtils.base64Encode(config);
    }

    @Override
    public List<PolicyTreeResp> getPolicyRulesDetector() {
        List<PolicyTreeResp> oneList = new ArrayList<>();
        List<PolicyModuleOneEnum> oneEnumList = PolicyModuleOneEnum.get(PolicyDeviceTypeEnum.JCQ.getKey());
        oneEnumList.forEach(item -> {
            List<PolicyTreeResp> twoList = null;
            List<PolicyModuleTwoEnum> twoEnumList = PolicyModuleTwoEnum.getTwoByParent(item, PolicyDeviceTypeEnum.JCQ.getKey());
            if (CollectionUtil.isNotEmpty(twoEnumList)) {
                twoList = new ArrayList<>();
                for (PolicyModuleTwoEnum twoItem : twoEnumList) {
                    List<PolicyTreeResp> threeList = null;
                    List<PolicyModuleThreeEnum> threeEnumList = PolicyModuleThreeEnum.getThreeByParent(twoItem, PolicyDeviceTypeEnum.JCQ.getKey());
                    if (CollectionUtil.isNotEmpty(threeEnumList)) {
                        threeList = new ArrayList<>();
                        for (PolicyModuleThreeEnum threeItem : threeEnumList) {
                            PolicyTreeResp policyTreeResp = PolicyTreeResp.builder()
                                    .module(threeItem.getKey())
                                    .label(threeItem.getValue())
                                    .children(null)
                                    .build();
                            threeList.add(policyTreeResp);
                        }
                    }
                    PolicyTreeResp policyTreeResp = PolicyTreeResp.builder()
                            .module(twoItem.getKey())
                            .label(twoItem.getValue())
                            .children(threeList)
                            .build();
                    twoList.add(policyTreeResp);
                }
            }
            PolicyTreeResp policyTreeResp = PolicyTreeResp.builder()
                    .module(item.getKey())
                    .label(item.getValue())
                    .children(twoList)
                    .build();
            oneList.add(policyTreeResp);
        });
        return oneList;
    }

    @Override
    public List<PolicyHistoryVersionResp> getPolicyByModule(String module) {
        List<IssuePolicy> issuePolicyList = this.issuePolicyRepository.list(Wrappers.<IssuePolicy>lambdaQuery()
                .eq(IssuePolicy::getType, IssueTypeEnum.POLICY.getKey())
                .eq(IssuePolicy::getModule, module));
        if (CollectionUtil.isEmpty(issuePolicyList)) {
            return new ArrayList<>();
        }
        List<IssuePolicy> distinctList = issuePolicyList.stream()
                .collect(Collectors.toMap(IssuePolicy::getVersion, p -> p, (p1, p2) -> p1))
                .values()
                .stream()
                .collect(Collectors.toList());
        List<PolicyHistoryVersionResp> list = new ArrayList<>(distinctList.size());
        distinctList.forEach(item -> {
            list.add(PolicyHistoryVersionResp.builder()
                    .module(item.getModule())
                    .version(item.getVersion())
                    .policyId(item.getPolicyId())
                    .build());
        });
        return list;
    }

    /**
     * 修改策略应用状态 + 添加关系表
     *
     * @param ruleIds
     * @param policyId
     * @param module
     */
    private void updatePolicyStatus(List<Long> ruleIds, Long policyId, String module) {
        List<DetectorPolicyRule> detectorPolicyRuleList = new ArrayList<>();
        ruleIds.forEach(item -> {
            DetectorPolicyRule detectorPolicyRule = new DetectorPolicyRule();
            detectorPolicyRule.setPolicyId(policyId);
            detectorPolicyRule.setRuleId(item);
            detectorPolicyRule.setModule(module);
            detectorPolicyRuleList.add(detectorPolicyRule);
        });
        // 添加关系表
        this.detectorPolicyRuleRepository.saveBatch(detectorPolicyRuleList);
        // 更新规则状态
        RuleService ruleService = this.getRuleServiceByModule(module);
        ruleService.updateStatus(ruleIds);
    }

    /**
     * 查询具体规则信息
     *
     * @param ruleIdList
     * @param module
     * @param detectorPolicyResp
     */
    private void getRuleDetail(List<Long> ruleIdList, String module, DetectorPolicyResp detectorPolicyResp) {
        RuleService ruleService = this.getRuleServiceByModule(module);
        PolicyDetailResp policyDetail = ruleService.getDetailByRuleId(ruleIdList);
        detectorPolicyResp.setPolicyDetail(policyDetail);
    }


    /**
     * 根据模块获取对应的service
     *
     * @param module
     * @return
     */
    private RuleService getRuleServiceByModule(String module) {
        RuleService ruleService = this.ruleServiceList.stream().filter(item -> item.isSupported(module)).findFirst().orElse(null);
        if (ruleService == null) {
            throw new BaseBusinessException("模块不存在");
        }
        return ruleService;
    }

    /**
     * 验证重复
     *
     * @param detectorPolicyReq
     */
    private void validateEditRepeat(DetectorPolicyReq detectorPolicyReq) {
        List<DetectorPolicy> list = this.detectorPolicyRepository.list(Wrappers.<DetectorPolicy>lambdaQuery()
                .eq(DetectorPolicy::getName, detectorPolicyReq.getName())
                .eq(DetectorPolicy::getModule, detectorPolicyReq.getModule())
                .ne(DetectorPolicy::getId, detectorPolicyReq.getId()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("数据已存在，请检查策略名称是否重复");
        }
    }

    /**
     * 验证重复
     *
     * @param detectorPolicyReq
     */
    private void validateSaveRepeat(DetectorPolicyReq detectorPolicyReq) {
        List<DetectorPolicy> list = this.detectorPolicyRepository.list(Wrappers.<DetectorPolicy>lambdaQuery()
                .eq(DetectorPolicy::getName, detectorPolicyReq.getName())
                .eq(DetectorPolicy::getModule, detectorPolicyReq.getModule()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("数据已存在，请检查策略名称是否重复");
        }
    }

}
