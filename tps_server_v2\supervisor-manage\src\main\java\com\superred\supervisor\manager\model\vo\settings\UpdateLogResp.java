package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月14日
 */
@Data
public class UpdateLogResp {

    private Integer id;

    @Schema(description = "升级记录标识")
    private String updateId;

    @Schema(description = "升级包名称")
    private String fileName;

    @Schema(description = "升级版本")
    private String version;

    @Schema(description = "升级时间")
    private Date updateTime;

    @Schema(description = "升级内容描述")
    private String updateDesc;

    @Schema(description = "升级结果")
    private String updateResult;

    @Schema(description = "错误信息")
    private String errorInfo;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "升级起始时间")
    private Date updateStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "升级截止时间")
    private Date updateEndTime;
}
