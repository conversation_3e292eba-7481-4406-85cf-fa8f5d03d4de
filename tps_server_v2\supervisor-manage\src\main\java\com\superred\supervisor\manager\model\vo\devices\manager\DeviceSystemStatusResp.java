package com.superred.supervisor.manager.model.vo.devices.manager;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备系统状态响应
 *
 * <AUTHOR>
 * @since 2025/03/14
 */
@Data

@Builder
public class DeviceSystemStatusResp {

    /**
     * 内存利用率 0-100
     */
    @Schema(description = "内存利用率 0-100")
    private Integer mem;

    /**
     * 磁盘整体可用空间(GB)
     */
    @Schema(description = "磁盘整体可用空间(GB)")
    private Integer disk;

    /**
     * 运行状态采集时间
     */
    @Schema(description = "运行状态采集时间")
    private LocalDateTime time;
    /**
     * 服务器的编号
     */
    @Schema(description = "服务器的编号")
    private Integer did;

}
