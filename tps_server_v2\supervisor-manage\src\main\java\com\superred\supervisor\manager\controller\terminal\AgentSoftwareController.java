package com.superred.supervisor.manager.controller.terminal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.annotation.role.IgnoreAuth;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.dto.agent.AgentSoftDTO;
import com.superred.supervisor.manager.model.vo.terminal.AgentSoftwareVO;
import com.superred.supervisor.manager.service.terminal.AgentSoftwareService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 终端软件
 *
 * <AUTHOR>
 */
@Tag(name = "6.3终端软件")
@RestController
@RequestMapping("/agent_software/")
@IgnoreAuth
public class AgentSoftwareController {
    @Resource
    private AgentSoftwareService agentSoftwareService;

    @ApiOperationSupport(order = 1)
    @Operation(summary = "1.上传系统软件升级包")
    @PostMapping("/upload")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_SOFTWARE, operateType = OperateTypeConstants.UPLOAD, desc = "上传系统软件升级包")
    public R<String> upload(@RequestParam("file") MultipartFile file, @RequestParam("softType") Short softType) {
        agentSoftwareService.upload(file, softType);
        return R.success();
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2.获取终端软件包分页数据")
    @GetMapping("/page")
    public RPage<AgentSoftwareVO> getPage(AgentSoftDTO agentSoftDTO) {

        IPage<AgentSoftwareVO> page = agentSoftwareService.page(agentSoftDTO);
        return new RPage<>(page);
    }
}
