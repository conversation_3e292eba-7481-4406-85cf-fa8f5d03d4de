package com.superred.supervisor.manager.model.vo.terminal.bak;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * agent审核分页查询请求
 */
@EqualsAndHashCode(callSuper = true)
@Data

public class AgentBakPageReq extends PageReqDTO {

    @Schema(description = "终端名称")
    private String hostName;


}
