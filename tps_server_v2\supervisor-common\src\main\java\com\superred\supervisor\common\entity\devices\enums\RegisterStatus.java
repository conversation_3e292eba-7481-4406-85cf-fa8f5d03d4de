package com.superred.supervisor.common.entity.devices.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 注册状态，0（成功），1（失败），2（审核中） 3离线；4无效；5已删除 6在线
 *
 * <AUTHOR>
 * @since 2025/3/19 9:22
 */
@Getter
@AllArgsConstructor
public enum RegisterStatus implements IEnum<Integer> {
    SUCCESS(0, "成功"),
    FAIL(1, "失败"),
    AUDITING(2, "审核中"),
    OFFLINE(3, "离线"),
    INVALID(4, "无效"),
    DELETED(5, "已删除"),
    ONLINE(6, "在线");

    private final Integer value;
    private final String desc;


    public static RegisterStatus getByValue(Integer value) {
        for (RegisterStatus registerStatus : RegisterStatus.values()) {
            if (registerStatus.getValue().equals(value)) {
                return registerStatus;
            }
        }
        return null;
    }
}
