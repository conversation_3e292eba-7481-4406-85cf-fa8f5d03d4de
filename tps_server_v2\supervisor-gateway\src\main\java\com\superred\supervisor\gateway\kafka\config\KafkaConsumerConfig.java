package com.superred.supervisor.gateway.kafka.config;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.ConcurrentKafkaListenerContainerFactoryConfigurer;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.Map;


/**
 * kafkaConsumer配置
 *
 * @author: hailong.qu
 * @since: 2025/3/28 10:16
 */
@Configuration
@EnableKafka
@Slf4j
public class KafkaConsumerConfig {

    private static final int CORE_POOL_SIZE = 2;
    private static final int MAX_POOL_SIZE = 4;
    private static final int QUEUE_CAPACITY = 32;

    @Value("${spring.kafka.consumer.properties.sasl.jaas.config:}")
    private String consumerSaslJaasConfig;
    @Value("${spring.kafka.consumer.properties.security.protocol:}")
    private String consumerSecurityProtocol;
    @Value("${spring.kafka.consumer.properties.sasl.mechanism:}")
    private String consumerSaslMechanism;

    @Value("${spring.kafka.file-content.max-poll-records:5}")
    private Integer fileContentMaxPollRecords;

    @Resource
    private KafkaProperties properties;


    private ThreadPoolTaskExecutor alarmTaskExecutor(String prefix) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(KafkaConsumerConfig.CORE_POOL_SIZE);
        executor.setMaxPoolSize(KafkaConsumerConfig.MAX_POOL_SIZE);
        executor.setQueueCapacity(KafkaConsumerConfig.QUEUE_CAPACITY);
        executor.setThreadNamePrefix(prefix);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }

    @Bean("fileContentKafkaListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<?, ?> fileContentKafkaListenerContainerFactory(
            ConcurrentKafkaListenerContainerFactoryConfigurer configurer) {


        log.warn("------------------ ::: fileContentKafkaListenerContainerFactory:: bootstrapAddress: {}", properties.getBootstrapServers());

        ConcurrentKafkaListenerContainerFactory<Object, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.getContainerProperties().setConsumerTaskExecutor(alarmTaskExecutor("FILE-CONTENT-"));
        factory.setConcurrency(1);

        Map<String, Object> consumerProperties = properties.buildConsumerProperties();
        consumerProperties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);
        consumerProperties.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, fileContentMaxPollRecords);

        if (StrUtil.isNotBlank(consumerSecurityProtocol)) {
            consumerProperties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, consumerSecurityProtocol);
            consumerProperties.put(SaslConfigs.SASL_MECHANISM, consumerSaslMechanism);
            consumerProperties.put(SaslConfigs.SASL_JAAS_CONFIG, consumerSaslJaasConfig);
        }
        DefaultKafkaConsumerFactory<Object, Object> consumerFactory = new DefaultKafkaConsumerFactory<>(consumerProperties);
        configurer.configure(factory, consumerFactory);
        // 没有异常的情况下，自动提交更加高效
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.RECORD);
        return factory;
    }


}
