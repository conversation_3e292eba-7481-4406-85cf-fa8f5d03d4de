package com.superred.supervision.base.vo.data;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/4/27 17:52
 */
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FileCommonVo {
    /**
     * 告警id
     */
    private String id;

    private String deviceId;

    private Long ruleId;

    private String time;

    private String module;

    private String alarmDeviceModuleChecksum;

    private String subModule;

    private Integer type;

}
