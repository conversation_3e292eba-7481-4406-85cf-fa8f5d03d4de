package com.superred.supervisor.manager.controller.devices;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.manager.model.vo.devices.bak.DeviceBakPageReq;
import com.superred.supervisor.manager.model.vo.devices.bak.DeviceBakPageResp;
import com.superred.supervisor.manager.model.vo.devices.bak.DevicebakAddReq;
import com.superred.supervisor.manager.model.vo.devices.bak.DevicebakDetailResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


/**
 * 设备报备控制器
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Tag(name = "3.1. 设备报备")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/device_bak")
public class DeviceBakController {


    @Operation(summary = "1. 分页查询")
    @ApiOperationSupport(order = 1)
    @GetMapping("/page")
    public RPage<DeviceBakPageResp> getDeviceBakPage(@RequestBody DeviceBakPageReq req) {

        return new RPage<>(new Page<>());
    }


    @Operation(summary = "2. 新增设备报备")
    @ApiOperationSupport(order = 2)
    //    @SysLogAnn(module = Constants.EVENT_TYPE_LOCAL_USER, action = "新增设备报备", operateType = NEW_INFORMATION_ADDED)
    @PostMapping("/add")
    public R<Integer> save(@Valid @RequestBody DevicebakAddReq req) {

        return R.success(null);
    }


    @Operation(summary = "3. 通过id查询设备报备")
    @ApiOperationSupport(order = 3)
    @GetMapping("/{id}")
    public R<DevicebakDetailResp> getById(@PathVariable("id") Long id) {
        return R.success(null);
    }


    @Operation(summary = "4. 修改设备报备")
    @ApiOperationSupport(order = 4)
    //    @SysLogAnn(module = Constants.EVENT_TYPE_LOCAL_USER, action = "修改设备报备", operateType = MODIFYING_INFORMATION)
    @PostMapping("/update/{id}")
    public R updateById(@Valid @RequestBody DevicebakAddReq req, @PathVariable Integer id) {

        return R.success(null);
    }


    @Operation(summary = "5. 通过id删除设备报备")
    @ApiOperationSupport(order = 5)
    //    @SysLogAnn(module = Constants.EVENT_TYPE_LOCAL_USER, action = "通过id删除设备报备", operateType = DELETE_INFORMATION)
    @PostMapping("/delete/{id}")
    public R<Boolean> removeById(@PathVariable Long id) {
        return R.success(null);
    }


    @Operation(summary = "6. 下载设备报备导入模板")
    @ApiOperationSupport(order = 6)
    @GetMapping("/download/template")
    public void downloadModule(HttpServletResponse response) {
        //        String path = "excel" + File.separator + "deviceBak" + "." + Constants.XLSX;
        //        ClassPathResource resource = new ClassPathResource(path);
        //        InputStream inputStream = resource.getInputStream();
        //        FileUtils.downloadFile(response, inputStream, "设备报备模板");
    }

    @Operation(summary = "7. 导出设备报备")
    @ApiOperationSupport(order = 7)
    @PostMapping(value = "/export")
    public void export(HttpServletResponse response) {

        // 导出
    }

    @Operation(summary = "8. 导入设备报备")
    @ApiOperationSupport(order = 8)
    @PostMapping(value = "/import")
    public R<Boolean> importExcel(@RequestParam("excelFile") MultipartFile file) {

        return R.success(null);
    }
}
