package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * mem信息(LocalMemInfo) 实体
 *
 * <AUTHOR>
 * @since 2025-03-27 14:55:41
 */
@Data
@TableName("local_mem_info")
public class MemInfo {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 内存总数，表示整个设备的内存大小，单位MB。
     */
    @TableField("mem_total")
    private Integer memTotal;

    /**
     * 已使用内存总数，单位MB。
     */
    @TableField("mem_used")
    private Integer memUsed;

    /**
     * 当由多台服务器组成时，表示服务器的编号
     */
    @TableField("did")
    private Integer did;

}

