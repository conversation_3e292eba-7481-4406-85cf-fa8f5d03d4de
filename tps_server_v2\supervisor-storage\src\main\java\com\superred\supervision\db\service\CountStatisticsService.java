package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.db.entity.CountStatistics;
import com.superred.supervision.db.vo.statistics.CountStatisticsVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
public interface CountStatisticsService extends IService<CountStatistics> {
    /**
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据
     */
    List<CountStatisticsVo> findByTime(String startTime, String endTime, Integer isReport);

    void deleteByTime(String startTime, String endTime, Integer isReport);
}
