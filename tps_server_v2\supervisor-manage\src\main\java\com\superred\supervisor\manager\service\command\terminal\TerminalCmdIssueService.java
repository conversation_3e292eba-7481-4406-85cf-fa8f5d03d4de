package com.superred.supervisor.manager.service.command.terminal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageResp;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalCmdRecordPageReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdRecordPageResp;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalUninstallCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalModuleSwitchCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalInnerPolicyUpdateCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalSoftUpgradeCmdReq;

/**
 * 终端指令下发服务
 *
 * <AUTHOR>
 * @since 2025/7/24 17:44
 */
public interface TerminalCmdIssueService {

    /**
     * 终端软升级指令下发
     *
     * @param req 终端软升级请求参数
     * @return 下发结果
     */
    String softUpdate(TerminalSoftUpgradeCmdReq req);

    /**
     * 终端内置策略更新指令下发
     *
     * @param req 终端内置策略更新请求参数
     * @return 下发结果
     */
    String innerPolicyUpdate(TerminalInnerPolicyUpdateCmdReq req);

    /**
     * 终端卸载指令下发
     *
     * @param req 终端卸载请求参数
     * @return 下发结果
     */
    String uninstall(TerminalUninstallCmdReq req);

    /**
     * 终端模块开关指令下发
     *
     * @param req 终端模块开关请求参数
     * @return 下发结果
     */
    String moduleSwitch(TerminalModuleSwitchCmdReq req);

    /**
     * 终端指令下发记录
     *
     * @param req 查询请求参数
     * @return 分页结果
     */
    IPage<TerminalCmdRecordPageResp> recordPage(TerminalCmdRecordPageReq req);

    /**
     * 终端指令执行详情
     *
     * @param req 查询请求参数
     * @return 分页结果
     */
    IPage<TerminalCmdExecPageResp> terminalExecPage(TerminalCmdExecPageReq req);
}
