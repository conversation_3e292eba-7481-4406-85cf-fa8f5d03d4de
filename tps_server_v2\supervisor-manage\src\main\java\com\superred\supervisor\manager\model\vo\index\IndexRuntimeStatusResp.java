package com.superred.supervisor.manager.model.vo.index;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 代理商业务状况
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Data

@Builder
public class IndexRuntimeStatusResp {

    @Schema(description = "内存总数，表示整个设备的内存大小，单位MB。")
    private Double memTotal;

    @Schema(description = "内存使用率")
    private Integer memRate;

    @Schema(description = "内存使用情况")
    private Double memUsed;

    @Schema(description = "内存剩余情况")
    private Double memFree;


    @Schema(description = "系统cpu使用率")
    private Double cpuRate;


    @Schema(description = "磁盘总大小G")
    private Integer diskTotal;

    @Schema(description = "磁盘使用率")
    private Integer diskRate;

    @Schema(description = "磁盘使用情况")
    private Integer diskUsed;

    @Schema(description = "磁盘剩余情况")
    private Integer diskFree;

    @Schema(description = "系统运行时间")
    private Double uptime;
}
