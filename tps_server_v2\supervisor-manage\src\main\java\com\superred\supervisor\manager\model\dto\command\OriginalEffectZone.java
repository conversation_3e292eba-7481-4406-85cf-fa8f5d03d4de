package com.superred.supervisor.manager.model.dto.command;

import lombok.Data;

import java.util.List;


/**
 * 指令下发生效范围数据,解析前
 *
 * <AUTHOR>
 * @since 2023/10/10
 **/
@Data
public class OriginalEffectZone {

    /**
     * 行业监管还是行政监管  1 行政监管 2 行业监管
     */
    private Integer supervisionType;

    /**
     * 生效范围
     */
    private List<EffectZone> effectZones;

    /**
     * 需要过滤的设备枚举值  目前 1 表示过滤下级的自监管
     */
    private Integer filterDeviceType;
}
