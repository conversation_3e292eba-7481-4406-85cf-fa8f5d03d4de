package com.superred.supervisor.manager.mapper.setting;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.supervisor.common.entity.settings.LocalDeviceSuspectedLog;
import com.superred.supervisor.manager.model.vo.index.LocalDeviceEventPageReq;
import com.superred.supervisor.manager.model.vo.index.LocalDeviceEventPageResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 系统异常日志(LocalDeviceSuspectedLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-27 15:31:59
 */
@Mapper
public interface LocalDeviceSuspectedLogExtMapper extends BaseMapper<LocalDeviceSuspectedLog> {
    IPage<LocalDeviceEventPageResp> pageLocalDeviceSuspectedLog(Page<LocalDeviceEventPageResp> objectPage, @Param("req") LocalDeviceEventPageReq req);
}

