package com.superred.supervisor.manager.model.vo.devices.manager;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 设备状态页面请求
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@EqualsAndHashCode(callSuper = true)
@Data

public class DeviceEventlogReq extends PageReqDTO {


    @Schema(description = "设备编号")
    @NotBlank(message = "设备编号不能为空")
    private String deviceId;


}
