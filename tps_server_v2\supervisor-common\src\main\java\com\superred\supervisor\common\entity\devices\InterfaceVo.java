package com.superred.supervisor.common.entity.devices;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022/6/14 15:50
 * 设备配置信息
 **/
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class InterfaceVo {


    /**
     * ip地址
     */
    private String ip;
    /**
     * 子网掩码
     */
    private String netmask;
    /**
     * 网关地址
     */
    private String gateway;
    /**
     * mac地址
     */
    private String mac;

    /**
     * 是否是管理IP
     */
    private Boolean manage;
}
