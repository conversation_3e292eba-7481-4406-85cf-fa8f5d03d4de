package com.superred.supervisor.manager.model.vo.devices;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DeviceContact
 * <p> 联系人信息
 * @since 2022-08-12 10:09:58
 **/
@Data

public class DeviceContactResp {

    /**
     *
     */
    @Schema(description = "")
    private Integer id;
    /**
     * 联系人姓名，“张三”
     */
    @Schema(description = "联系人姓名，“张三”")

    private String name;

    /**
     * 联系人邮件地址，“<EMAIL>”
     */
    @Schema(description = "联系人邮件地址，“<EMAIL>”")
    private String email;

    /**
     * 联系人电话，“13811223344”
     */

    @Schema(description = "联系人电话，“13811223344”")
    private String phone;

    /**
     * 联系人职务，“处长”
     */
    @Schema(description = "联系人职务，“处长”")
    private String position;

}
