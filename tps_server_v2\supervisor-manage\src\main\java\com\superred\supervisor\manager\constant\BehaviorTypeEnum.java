package com.superred.supervisor.manager.constant;

/**
 * BehaviorEnum. 行为类型. 1.违规行为 ； 2.异常行为; 3 一般行为
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021-07-13 16:59
 **/
public enum BehaviorTypeEnum {

    COMMONLY(1, "一般行为"), ANOMAL(2, "异常行为"), ILLEGAL(3, "违规行为");

    private final Integer key;
    private final String value;

    BehaviorTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    /**
     * 根据key获取行为类型.
     *
     * <AUTHOR>
     * @since 2021-07-14 13:46
     **/
    public static String getValue(int key) {
        BehaviorTypeEnum[] values = values();
        for (BehaviorTypeEnum tmp : values) {
            if (tmp.getKey().equals(key)) {
                return tmp.getValue();
            }
        }
        return null;
    }

    public final Integer getKey() {
        return this.key;
    }

    public final String getValue() {
        return this.value;
    }

}
