package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.base.vo.command.CommandResultVo;
import com.superred.supervision.db.entity.IssueCommand;

/**
 * <p>
 * 命令下发表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
public interface IssueCommandService extends IService<IssueCommand> {
    /**
     * 保存指令上报结果
     * @param deviceId 设备id
     * @param commandResultVo 指令结果
     */
    void saveResult(String deviceId, CommandResultVo commandResultVo);

}
