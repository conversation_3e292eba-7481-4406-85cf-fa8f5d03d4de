package com.superred.supervisor.manager.model.vo.system.staff;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023-08-24 16:30
 */
@Data

public class StaffAddReq {


    @Schema(description = "人员姓名")
    @NotBlank(message = "人员姓名不能为空")
    private String staffName;

    @Schema(description = "身份证号")
//    @Sensitive(type = SensitiveTypeEnum.CUSTOM,beginNoMask = 2,endNoMask = 4,maskStr = "*",encrypt = true)
    @NotBlank(message = "身份证号不能为空")
    private String idCardNum;


    @Schema(description = "所属单位")
    @NotBlank(message = "所属单位不能为空")
    private String unitName;

    @Schema(description = "所属部门id")
    @NotNull(message = "所属部门id不能为空")
    private Integer orgId;


    @Schema(description = "联系方式")
//    @Sensitive(type = SensitiveTypeEnum.CUSTOM,beginNoMask = 2,endNoMask = 2,maskStr = "*",encrypt = true)
    private String contactInfo;

    @Schema(description = "邮箱")
//    @Sensitive(type = SensitiveTypeEnum.CUSTOM,beginNoMask = 2,endNoMask = 4,maskStr = "*",encrypt = true)
    private String email;


}
