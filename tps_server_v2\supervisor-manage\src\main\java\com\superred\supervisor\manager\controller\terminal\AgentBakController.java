package com.superred.supervisor.manager.controller.terminal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.terminal.bak.AgentBakAddReq;
import com.superred.supervisor.manager.model.vo.terminal.bak.AgentBakDetailResp;
import com.superred.supervisor.manager.model.vo.terminal.bak.AgentBakPageReq;
import com.superred.supervisor.manager.model.vo.terminal.bak.AgentBakPageResp;
import com.superred.supervisor.manager.service.terminal.AgentDeviceInfoBakService;
import com.superred.supervisor.manager.utils.FileDownloadUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


/**
 * 终端设备报备
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Tag(name = "6.1. 终端设备报备")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agent_bak")
public class AgentBakController {

    @Autowired
    private AgentDeviceInfoBakService agentDeviceInfoBakService;

    @Operation(summary = "1. 分页查询")
    @ApiOperationSupport(order = 1)
    @PostMapping("/list")
    public RPage<AgentBakPageResp> getDeviceBakPage(@RequestBody AgentBakPageReq req) {
        IPage<AgentBakPageResp> page = agentDeviceInfoBakService.getDeviceBakPage(req);
        return new RPage<>(page);
    }


    @Operation(summary = "2. 新增设备报备")
    @ApiOperationSupport(order = 2)
    @SysLogAnn(module = LogTypeConstants.TERMINAL_DEVICE_REPORTING_MANAGEMENT, operateType = OperateTypeConstants.ADD, desc = "新增设备报备")
    @PostMapping("/add")
    public R<Boolean> save(@Valid @RequestBody AgentBakAddReq req) {
        return R.success(agentDeviceInfoBakService.save(req));
    }


    @Operation(summary = "3. 通过id查询设备报备")
    @ApiOperationSupport(order = 3)
    @GetMapping("/{id}")
    public R<AgentBakDetailResp> getById(@PathVariable("id") Long id) {
        return R.success(agentDeviceInfoBakService.getById(id));
    }


    @Operation(summary = "4. 修改设备报备")
    @ApiOperationSupport(order = 4)
    @SysLogAnn(module = LogTypeConstants.TERMINAL_DEVICE_REPORTING_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "修改设备报备")
    @PostMapping("/update/{id}")
    public R updateById(@Valid @RequestBody AgentBakAddReq req, @PathVariable Integer id) {
        agentDeviceInfoBakService.updateById(req, id);
        return R.success();
    }


    @Operation(summary = "5. 通过id删除设备报备")
    @ApiOperationSupport(order = 5)
    @SysLogAnn(module = LogTypeConstants.TERMINAL_DEVICE_REPORTING_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除设备报备")
    @PostMapping("/delete/{id}")
    public R<Boolean> removeById(@PathVariable Long id) {
        return R.success(agentDeviceInfoBakService.removeById(id));
    }


    @Operation(summary = "6. 下载设备报备导入模板")
    @ApiOperationSupport(order = 6)
    @GetMapping("/download/template")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_DEVICE_REPORTING_MANAGEMENT, operateType = OperateTypeConstants.DOWNLOAD, desc = "下载设备报备导入模板")
    public void downloadModule(HttpServletResponse response) {
        FileDownloadUtils.downloadClassPathFile(response, "template/设备报备导入模板.xlsx", "设备报备导入模板.xlsx");
    }

    @Operation(summary = "7. 导出设备报备")
    @ApiOperationSupport(order = 7)
    @PostMapping(value = "/export")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_DEVICE_REPORTING_MANAGEMENT, operateType = OperateTypeConstants.EXPORT, desc = "导出设备报备")
    public void export(HttpServletResponse response) {
        agentDeviceInfoBakService.export(response);
    }

    @Operation(summary = "8. 导入设备报备")
    @ApiOperationSupport(order = 8)
    @PostMapping(value = "/import")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_DEVICE_REPORTING_MANAGEMENT, operateType = OperateTypeConstants.IMPORT, desc = "导入设备报备")
    public R importExcel(@RequestParam("excelFile") MultipartFile file) {
        agentDeviceInfoBakService.importExcel(file);
        return R.success();
    }
}
