package com.superred.supervisor.manager.controller.login;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.annotation.role.IgnoreAuth;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.auth.CaptchaResp;
import com.superred.supervisor.manager.model.auth.KeyPairResp;
import com.superred.supervisor.manager.model.vo.login.LoginReq;
import com.superred.supervisor.manager.model.vo.login.LoginResp;
import com.superred.supervisor.manager.model.vo.login.UkeyLoginReq;
import com.superred.supervisor.manager.model.vo.login.UkeyLoginResp;
import com.superred.supervisor.manager.model.vo.login.UserInfoResp;
import com.superred.supervisor.manager.model.vo.system.MenuTreeResp;
import com.superred.supervisor.manager.service.login.LoginService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 系统登录相关接口
 *
 * <AUTHOR>
 * @since 2025/3/6 15:50
 */
@Tag(name = "1.0 .系统登录")
@RestController
@Slf4j
@Validated
public class SystemLoginController {

    @Resource
    private LoginService loginService;


    @Operation(summary = "0. 获取验证码")
    @ApiOperationSupport(order = 1)
    @GetMapping("/auth/image")
    @IgnoreAuth
    public R<CaptchaResp> createCode() {
        CaptchaResp resp = loginService.createImageCode();

        return R.success(resp);
    }


    @Operation(summary = "0. 获取SM2加密密钥对")
    @ApiOperationSupport(order = 2)
    @GetMapping("/auth/keypair")
    @IgnoreAuth
    public R<KeyPairResp> createSM2KeyPair() {
        KeyPairResp resp = loginService.createSM2KeyPair();

        return R.success(resp);
    }


    @Operation(summary = "1. 用户登录")
    @ApiOperationSupport(order = 3)
    @PostMapping("/auth/user/login")
    @IgnoreAuth
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.LOGON, desc = "用户登录")
    public R<LoginResp> login(@RequestBody @Valid LoginReq req) {

        LoginResp loginResp = loginService.login(req);
        return R.success(loginResp);
    }

    @Operation(summary = "2. 用户登出")
    @ApiOperationSupport(order = 4)
    @PostMapping("/auth/user/logout")
    @IgnoreAuth
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.LOGOUT, desc = "用户登出")
    public R<String> logout() {
        loginService.logout();
        return R.success().msg("退出成功！");
    }


    @Operation(summary = "3. 登录用户信息")
    @ApiOperationSupport(order = 5)
    @GetMapping("/auth/user/info")
    public R<UserInfoResp> userInfo() {
        UserInfoResp resp = loginService.getUserInfo();
        return R.success(resp);
    }


    @Operation(summary = "4. 获取登录用户菜单")
    @ApiOperationSupport(order = 6)
    @GetMapping("/auth/user/menu")
    public R<List<MenuTreeResp>> getLoginUserMenu() {
        List<MenuTreeResp> resps = loginService.getLoginUserMenu();
        return R.success(resps);
    }


    @ApiOperationSupport(order = 7)
    @Operation(summary = "5. 获取随机数")
    @PostMapping("/get_random_number")
    @IgnoreAuth
    public R<UkeyLoginResp> getRandomNumber(@RequestBody UkeyLoginReq req) {
        UkeyLoginResp resp = loginService.getRandomNumber(req);

        return R.success(resp);

    }

    @Operation(summary = "6. ukey验签")
    @PostMapping("/verify_sign")
    @IgnoreAuth
    public R<Boolean> verifySign(@RequestBody UkeyLoginReq ukeyLoginDTO) {
/*        SysUkey ukey = sysUkeyService.getOne(Wrappers.<SysUkey>lambdaQuery().eq(SysUkey::getSerialNumber, ukeyLoginDTO.getSerialNumber()));
        if (!this.verify(ukey.getPublicKey(), ukey.getSerialNumber(), ukeyLoginDTO.getSign())) {
            return R.failed("登录失败，验证不通过！");
        }*/
        return R.success(true);
    }


    @PostMapping("/auth/check_token/expire")
    @Operation(summary = "7. 校验token是否有效")
    public R<String> validToken() {
        return R.success();
    }

}
