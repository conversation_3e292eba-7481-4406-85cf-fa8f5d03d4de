package com.superred.supervisor.manager.model.vo.system;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月14日
 */

@Data
public class LogSettingReq {

    @Schema(description = "日志存储空间设置 10G")
    private Integer syscfgStorageLogSize;

    @Schema(description = "存储空间告警提醒 1开启  0关闭")
    private Integer syscfgStorageOnAlert;

    @Schema(description = "存储空间告警阈值% ")
    private Integer syscfgStorageTotalRate;

    @Schema(description = "日志保存时间 单位月 ")
    private Integer syscfgPolicyLogSaveMonth;

    @Schema(description = "日志存储策略  0覆盖   1转存 ")
    private Integer syscfgPolicyLogType;

    @Schema(description = "数据转存路径")
    private String syscfgPolicyLogSavePath;
}
