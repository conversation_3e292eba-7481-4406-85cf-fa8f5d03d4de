package com.superred.supervisor.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.common.entity.system.enums.RegionLevel;
import lombok.Data;

/**
 * 行政区划分 实体
 *
 * <AUTHOR>
 * @since 2025-03-17 15:53:19
 */
@Data
@TableName("p_sys_region")
public class SysRegion {


    /**
     * 区域编码
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 父ID
     */
    @TableField("parentid")
    private Integer parentId;

    /**
     * 简称
     */
    @TableField("shortname")
    private String shortName;

    /**
     * 0中国 1省 2市 3区县
     */
    @TableField("leveltype")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private RegionLevel levelType;

    /**
     * 省级编码
     */
    @TableField("citycode")
    private String cityCode;

    /**
     * 邮政编码
     */
    @TableField("zipcode")
    private String zipcode;

    /**
     * 经度
     */
    @TableField("lng")
    private String lng;

    /**
     * 纬度
     */
    @TableField("lat")
    private String lat;

    /**
     * 拼音
     */
    @TableField("pinyin")
    private String pinyin;

    /**
     * 状态 暂时没用
     */
    @TableField("status")
    private String status;

    /**
     * 区域路径,用于查询：/510000/511322
     */
    @TableField("region_path")
    private String regionPath;

}

