package com.superred.supervision.base.vo.audit;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022/6/24 10:54
 **/
@Data
@ToString
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AuditVo {

    private String id;

    private String user;

    private String time;

    private String eventType;

    private String optType;

    private String message;
}
