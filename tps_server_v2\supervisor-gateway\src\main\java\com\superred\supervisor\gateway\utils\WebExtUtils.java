package com.superred.supervisor.gateway.utils;

import cn.hutool.core.net.URLEncodeUtil;
import com.superred.common.core.utils.WebUtils;
import com.superred.supervisor.gateway.exception.ApiBaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.Charset;
import java.util.Collection;


/**
 * 网页工具类
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Slf4j
public final class WebExtUtils {

    private WebExtUtils() {
    }

    public static HttpServletRequest getRequest() {
        return WebUtils.getRequest();
    }

    public static HttpServletResponse getResponse() {
        return WebUtils.getResponse();
    }

    public static void setDownloadHeader(HttpServletResponse response, String fileName) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;" + URLEncodeUtil.encode(fileName, Charset.defaultCharset()));

    }


    public static String getSessionId() {

        HttpServletRequest request = getRequest();
        if (request == null) {
            throw new ApiBaseException("获取HttpServletRequest失败");
        }
        return request.getSession().getId();
    }

    public static String getCookie(String cookieName) {
        HttpServletRequest request = getRequest();
        if (request == null) {
            throw new ApiBaseException("获取HttpServletRequest失败");
        }

        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equalsIgnoreCase(cookieName)) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }


    public static MultipartFile getSingleMultipartFile() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            throw new ApiBaseException("获取HttpServletRequest失败");
        }
        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            final Collection<MultipartFile> values = multipartRequest.getFileMap().values();
            return values.stream().findFirst()
                    .orElseThrow(() -> new ApiBaseException("请求中没有包含MultipartFile数据，请检查请求格式"));
        }
        throw new ApiBaseException("请求中没有包含MultipartFile数据，请检查请求格式");
    }
}
