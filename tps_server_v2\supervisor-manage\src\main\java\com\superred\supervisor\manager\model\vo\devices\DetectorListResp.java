package com.superred.supervisor.manager.model.vo.devices;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.superred.supervisor.common.entity.devices.DeviceInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 检测器设备信息
 */
@Data
public class DetectorListResp {


    @Schema(description = "设备编号")
    private String deviceId;

    @Schema(description = "全数字组成的最长为2个字节的字符串，检测器为“01”")
    private String deviceType;

    @Schema(description = "前八位为年月日，下划线后自定义")
    private String softVersion;

    @Schema(description = "厂商英文名")
    private String vendorName;

    @Schema(description = "设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值")
    @TableField("interface")
    private String interfaces;

    @Schema(description = "内存总数，表示整个设备的内存大小，单位MB。")
    private Long memTotal;

    @Schema(description = "CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。	physical_id：CPU ID，数值类型	core：CPU核心数，数值类型；	clock：CPU主频，数值类型精确到小数点后1位	")
    private String cpuInfo;

    @Schema(description = "磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。	size为数值类型，表示磁盘大小，单位GB；	serial为字符串类型，最长64个字节，表示磁盘序列号")
    private String diskInfo;

    @Schema(description = "检测器部署的单位名")
    private String organs;

    @Schema(description = "检测器部署的地理位置")
    private String address;

    @Schema(description = "行政区域编码类型，表示检测器部署所在地的区域编码。")
    private String addressCode;

    @Schema(description = "单位联系人信息。	name表示联系人，最长为64个字节的字符串；	email表示联系人邮件地址，最长为64个字节的字符串；	phone表示联系人电话，最长为32字节，；	position表示联系人职务，最长为64个字节的字符串；	")
    private String contact;

    @Schema(description = "注册时间")
    private LocalDateTime registerTime;

    @Schema(description = "注册状态，0（成功），1（失败），2（审核中）3（禁止）")
    private Integer registerStatus;

    @Schema(description = "注册状态描述")
    private String registerMessage;

    @Schema(description = "审核时间")
    private LocalDateTime verifyTime;

    @Schema(description = "备注信息")
    private String memo;

    @Schema(description = "开机时间,运行时长，单位秒")
    private Integer uptime;

    @Schema(description = "心跳时间")
    private LocalDateTime heartbeatTime;

    @Schema(description = "业务状态时间")
    private LocalDateTime businessTime;

    @Schema(description = "上报类型")
    private String reportType;

    @Schema(description = "是否在线：0离线，1在线")
    @TableField(exist = false)
    private Integer isOnline;

    @Schema(description = "是否在线：0离线，1在线")
    @TableField(exist = false)
    private Integer status;

    public static DetectorListResp from(DeviceInfo deviceInfo) {
        return BeanUtil.copyProperties(deviceInfo, DetectorListResp.class);
    }
}