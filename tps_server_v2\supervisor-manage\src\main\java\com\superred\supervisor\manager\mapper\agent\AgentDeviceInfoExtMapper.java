package com.superred.supervisor.manager.mapper.agent;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.supervisor.common.entity.agent.AgentDeviceInfo;
import com.superred.supervisor.manager.model.vo.terminal.TerminalAuditPageReq;
import com.superred.supervisor.manager.model.vo.terminal.TerminalAuditPageResp;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalCountByStatusResp;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalStatusPageReq;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalStatusPageResp;
import org.apache.ibatis.annotations.Param;

/**
 * 终端设备信息表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-18 13:38:00
 */
public interface AgentDeviceInfoExtMapper extends BaseMapper<AgentDeviceInfo> {

    IPage<TerminalAuditPageResp> pageAuditAgentInfo(Page<TerminalAuditPageResp> objectPage, @Param("req") TerminalAuditPageReq req);

    IPage<TerminalStatusPageResp> pageAgentStatus(Page<TerminalStatusPageResp> objectPage, @Param("req") TerminalStatusPageReq req);

    TerminalCountByStatusResp countByStatus(Integer intervalMinutes
    );
}

