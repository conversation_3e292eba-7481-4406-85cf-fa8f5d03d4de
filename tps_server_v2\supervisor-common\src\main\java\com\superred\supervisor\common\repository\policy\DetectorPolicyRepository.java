package com.superred.supervisor.common.repository.policy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.DetectorPolicy;
import com.superred.supervisor.common.mapper.policy.DetectorPolicyMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2025-03-13 15:16
 */
@Slf4j
@Repository
@AllArgsConstructor
public class DetectorPolicyRepository extends ServiceImpl<DetectorPolicyMapper, DetectorPolicy> {
}
