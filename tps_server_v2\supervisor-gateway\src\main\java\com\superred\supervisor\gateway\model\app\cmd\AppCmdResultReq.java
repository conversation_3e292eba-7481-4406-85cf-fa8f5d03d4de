package com.superred.supervisor.gateway.model.app.cmd;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 D.3.5.1 指令响应上报
 *
 * <AUTHOR>
 * @since 2025/05/28
 */
@Data
public class AppCmdResultReq {


    /**
     * 上报时间.
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

    /**
     * 指令类型
     **/
    private String type;

    /**
     * 指令名称
     **/
    private String cmd;

    /**
     * 指令ID.
     **/
    @JsonProperty("cmd_id")
    private String cmdId;

    /**
     * 执行结果.0:成功；1：失败
     **/
    private Integer result;

    /**
     * 执行结果描述.
     **/
    private String message;

    /**
     * 根据不同的指令类
     * 型，定制不同的详
     * 情内容.
     **/
    private List<String> detail;

}
