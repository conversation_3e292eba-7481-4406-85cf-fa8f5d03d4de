package com.superred.supervisor.manager.exception;


import com.superred.common.core.constant.ResultStatus;
import com.superred.common.core.exception.UnAuthorizedException;
import com.superred.common.core.model.R;
import com.superred.supervisor.manager.model.auth.LicenseInvalidDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.validation.ConstraintViolationException;

/**
 * common exception handler, handle the follow exceptions:
 *
 * <AUTHOR>
 * {@link RuntimeException}
 * {@link Throwable}
 * {@link MissingServletRequestParameterException}
 * {@link HttpMessageNotReadableException}
 * {@link NoHandlerFoundException}
 * {@link MethodArgumentNotValidException}
 * {@link BindException}
 * {@link ConstraintViolationException}
 * @since 2021/8/20 3:06 下午
 */
@Slf4j
@RestControllerAdvice
public class CustomExceptionHandler {


    @ExceptionHandler(UnAuthorizedException.class)
    public R<Void> handleUnAuthorizedException(UnAuthorizedException e) {
        log.error("UnAuthorizedException==>{}", e.getMessage(), e);

        return R.failure(ResultStatus.UNAUTHORIZED.getResultCode(), e.getMessage());
    }


    /**
     *  授权文件异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(LicInvalidException.class)
    @ResponseStatus(HttpStatus.I_AM_A_TEAPOT)
    public R<LicenseInvalidDTO> validRException(LicInvalidException ex) {
        log.info("LicInvalidException==>{}", ex.getMessage(), ex);
        LicenseInvalidDTO licenseInvalidDTO = ex.getLicenseInvalidDTO();

        R<LicenseInvalidDTO> success = R.success(licenseInvalidDTO);
        success.setCode(licenseInvalidDTO.getCode());
        success.setMsg(licenseInvalidDTO.getLicenseMsg());
        return success;
    }

}
