package com.superred.common.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * web 工具
 *
 * <AUTHOR>
 * @since 2025/6/24 13:50
 */
@Slf4j
public final class WebUtils {
    private WebUtils() {
    }

    public static boolean isAuthIgnoredUrl() {

        HttpServletRequest request = getRequest();
        if (request == null) {
            return true;
        }

        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
            return true;
        }
        return isSwaggerOrErrorRequest(request.getRequestURI());
    }

    private static boolean isSwaggerOrErrorRequest(String requestURI) {
        return requestURI.contains("swagger") ||
                requestURI.contains("error") ||
                requestURI.contains("actuator") ||
                requestURI.contains("v3/api-docs");
    }


    public static HttpServletRequest getRequest() {
        try {
            RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        } catch (Exception e) {
            log.error("获取HttpServletRequest失败", e);
            return null;
        }
    }

    public static HttpServletResponse getResponse() {
        try {
            RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
            return ((ServletRequestAttributes) requestAttributes).getResponse();
        } catch (Exception e) {
            log.error("获取HttpServletResponse失败", e);
            return null;
        }
    }

    /**
     * 获取客户端真实 IP 地址
     *
     * @return 客户端 IP 地址
     */
    @SuppressWarnings("PMD.NPathComplexity")
    public static String getClientIp() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return "unknown";
        }
        String ip = request.getHeader("X-Forwarded-For");

        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            // X-Forwarded-For 可能会包含多个 IP，取第一个
            return ip.split(",")[0].trim();
        }

        ip = request.getHeader("Proxy-Client-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("WL-Proxy-Client-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("HTTP_CLIENT_IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        // 最后从 request.getRemoteAddr() 获取 IP
        return request.getRemoteAddr();
    }
}
