DROP TABLE IF EXISTS `local_device_info`;
CREATE TABLE `local_device_info`
(
    `device_id`         varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '设备编号',
    `device_type`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '全数字组成的最长为2个字节的字符串，检测器为“01”',
    `mem_total`         int                                                           NULL DEFAULT NULL COMMENT '内存总数，表示整个设备的内存大小，单位MB。',
    `interface`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '设备配置信息，表示包括配置的 IP 地址、子网掩码、MAC 地址、网关地址、是否为管理口。\r\nip 为单一IP 地址类型，\r\nnetmask 为 IP 子 网 类 型 ， \r\ngateway 为单一 IP 地址类型，\r\nmac 为 MAC 地址类型，\r\nmanage 为布尔值',
    `cpu_info`          text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT 'CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。\r\nphysical_id：CPU ID，数值类型\r\ncore：CPU核心数，数值类型；\r\nclock：CPU主频，数值类型精确到小数点后1位\r\n',
    `disk_info`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。\r\nsize为数值类型，表示磁盘大小，单位GB；\r\nserial为字符串类型，最长64个字节，表示磁盘序列号',
    `soft_version`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '软件版本，前八位为年月日，下划线后自定义',
    `vendor_name`       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT '' COMMENT '厂商英文名',
    `organs`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检测器部署的单位名',
    `address`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检测器部署的地理位置',
    `address_code`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行政区域编码类型，表示检测器部署所在地的区域编码。',
    `contact`           text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci         NULL COMMENT '单位联系人信息。\r\nname表示联系人，最长为64个字节的字符串；\r\nemail表示联系人邮件地址，最长为64个字节的字符串；\r\nphone表示联系人电话，最长为32字节，；\r\nposition表示联系人职务，最长为64个字节的字符串；\r\n',
    `register_time`     timestamp                                                     NULL DEFAULT current_timestamp ON UPDATE CURRENT_TIMESTAMP COMMENT '注册时间',
    `register_status`   tinyint                                                       NULL DEFAULT -1 COMMENT '注册状态，0（成功），1（失败），2（审核中）',
    `register_message`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '注册状态描述',
    `verify_time`       timestamp                                                     NULL DEFAULT NULL COMMENT '审核时间',
    `memo`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注信息',
    `uptime`            int UNSIGNED                                                  NULL DEFAULT 0 COMMENT '开机时间,运行时长，单位秒',
    `heartbeat_time`    timestamp                                                     NULL DEFAULT NULL COMMENT '心跳时间',
    `business_time`     timestamp                                                     NULL DEFAULT NULL COMMENT '业务状态时间',
    `platform_type`     varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '平台类型 001 国家级 010 省级 011地市级 100 区县级 ',
    `address_type`      tinyint                                                       NULL DEFAULT NULL COMMENT '1行政监管，2行业监管',
    `public_ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部署单位的公网地址',
    PRIMARY KEY (`device_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '自监管设备信息'
  ROW_FORMAT = DYNAMIC;