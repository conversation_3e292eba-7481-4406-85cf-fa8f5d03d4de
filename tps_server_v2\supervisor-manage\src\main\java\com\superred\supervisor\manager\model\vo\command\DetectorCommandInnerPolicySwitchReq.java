package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/03/14 11:21
 */
@Data
public class DetectorCommandInnerPolicySwitchReq {

    @Schema(description = "策略ID")
    @NotBlank(message = "策略ID不能为空")
    private String policyId;

    @Schema(description = "策略Module")
    @NotBlank(message = "策略Module不能为空")
    private String policyModule;

    @Schema(description = "厂商ID")
    @NotBlank(message = "厂商ID不能为空")
    private String venderId;

    @Schema(description = "启停状态")
    @NotNull(message = "启停状态不能为空")
    private Integer enable;

    @Schema(description = "设备编号")
    @NotEmpty(message = "设备编号不能为空")
    private List<String> deviceIdList;
}
