#!/bin/sh

cpuInfo=$(cat /proc/cpuinfo | grep "physical id" | sort | awk -F ":" '{count[$2]++;} END {for(i in count) {print i "," count[i]}}')
if [ -z "$cpuInfo" ]; then
	cpuInfo=$(lscpu | grep "NUMA" |grep "CPU" | awk  '{print substr($2,3,1) "," (substr($4, index($4,"-")+1,2)+1)/(substr($2,3,1)+1)}')
fi

cpuFreq=$(dmidecode -t processor | grep "Max Speed" | head -n 1 | awk '{printf "%.1f",$3/1000}')

for node in ${cpuInfo[@]}; do
    physicalId=$(echo "$node" | awk -F "," '{print $1}')
    cpuFree=$(mpstat -P "${physicalId}" | awk 'NR==4{print $12}')
    cpuUsed=$(awk -v x=100 -v y=$cpuFree 'BEGIN{printf "%.2f\n",x-y}')
    echo "$node,$cpuFreq,$cpuUsed"
done

