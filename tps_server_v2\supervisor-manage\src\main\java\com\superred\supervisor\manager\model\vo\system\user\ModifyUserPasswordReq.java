package com.superred.supervisor.manager.model.vo.system.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 *  修改密码实体
 * @since 2025年03月12日
 */
@Data
public class ModifyUserPasswordReq implements Serializable {
    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 账号
     */
    @Schema(description = "账号")
    @NotBlank(message = "用户名 不可为空")
    @Size(min = 1, max = 20, message = "用户名 长度限制1-20位")
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5]+$", message = "用户名 只能包含中文、英文、数字")
    private String username;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    @NotBlank(message = "姓名 不可为空")
    @Size(min = 1, max = 20, message = "姓名 长度限制1-20位")
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5]+$", message = "姓名 只能包含中文、英文、数字")
    private String realName;

    /**
     * 密码
     */
    @Schema(description = "原密码")
    private String password;

    /**
     * 新密码
     */
    @Schema(description = "新密码")
    private String newpassword;

    /**
     * 确认密码
     */
    @Schema(description = "确认密码")
    private String newpassword1;

    /**
     * 验证码key
     */
    @Schema(description = "SM2密钥keyId")
    @NotBlank(message = "keyId不能为空")
    private String keyId;
}
