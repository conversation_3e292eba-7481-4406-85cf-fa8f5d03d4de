package com.superred.supervisor.gateway.controller.app;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.model.app.cmd.AppCmdResp;
import com.superred.supervisor.gateway.model.app.cmd.AppCmdResultReq;
import com.superred.supervisor.gateway.model.app.cmd.AppPolicyResultReq;
import com.superred.supervisor.gateway.service.app.AppHeartBeatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 指令与策略接收
 *
 * <AUTHOR>
 * @since 2025/6/27 16:03
 */
@Tag(name = "D.3.4 指令与策略接收(2025-05)")
@RestController
@Slf4j
@RequestMapping("/A2/")
public class AppCommandController {

    @Resource
    private AppHeartBeatService appHeartBeatService;


    /**
     *  D.3.4.1 心跳.指令和策略获取接口
     *  每分钟一次
     *
     **/
    @PostMapping("/heartbeat")
    @Operation(summary = "C.3.4.1 心跳接口")
    @ApiOperationSupport(order = 1)
    public List<AppCmdResp> heartbeat() {
        return appHeartBeatService.handleHeatbeat();
    }


    /**
     * D.3.5.1 指令响应上报.
     *
     **/
    @PostMapping("/sys_manager/command_result")
    @Operation(summary = "D.3.5.1 指令响应上报")
    @ApiOperationSupport(order = 2)
    public ApiResponse<String> commandResult(@RequestBody AppCmdResultReq req) {

//        CmdResultDTO<AgentCmdResultReq> cmdResultDTO = CmdResultDTO.<AgentCmdResultReq>builder()
//                .cmdResultType(CmdResultType.AGENT_CMD)
//                .requestBody(req)
//                .deviceId(AgentAuthUtils.getDeviceIdFromRequest())
//                .build();
//        cmdResultEventPublisher.sendMessage(JsonUtil.toJson(cmdResultDTO));

        return ApiResponse.success().msg("上报成功");
    }

    /**
     * D.3.5.2 策略响应上报.
     *
     **/
    @PostMapping("/sys_manager/policy_result")
    @Operation(summary = "D.3.5.2 策略响应上报.")
    @ApiOperationSupport(order = 3)
    public ApiResponse<String> policyResult(@RequestBody AppPolicyResultReq req) {

//        CmdResultDTO<AgentPolicyResultReq> cmdResultDTO = CmdResultDTO.<AgentPolicyResultReq>builder()
//                .cmdResultType(CmdResultType.AGENT_POLICY)
//                .deviceId(AgentAuthUtils.getDeviceIdFromRequest())
//                .requestBody(req)
//                .build();
//        cmdResultEventPublisher.sendMessage(JsonUtil.toJson(cmdResultDTO));
        return ApiResponse.success().msg("上报成功");
    }


}
