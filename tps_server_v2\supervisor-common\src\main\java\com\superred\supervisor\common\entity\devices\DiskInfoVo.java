package com.superred.supervisor.common.entity.devices;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022/6/14 15:55
 **/
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class DiskInfoVo {
    /**
     * 磁盘大小
     */
    private Integer size;
    /**
     * 磁盘序列号
     */
    private String serial;


}

