package com.superred.supervisor.manager.model.vo.policy;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.manager.common.annotation.BlankOrPattern;
import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 15:24
 */
@Data

public class DetectorPolicyReq {

    private static final long serialVersionUID = 1L;

    private Long id;

    @Schema(description = "策略名称")
    @NotBlank(message = "策略名称 不能为空")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9!\"#$%&'()*+,\\-./:;<=>?@\\[\\\\\\]^_`{|}~]+$", message = "策略名称 格式错误")
    private String name;

    @Schema(description = "模块所属模块：keyword_detect 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单,file_md5 文件md5")
    private String module;

    @Schema(description = "策略版本")
    private String version;

    @Schema(description = "策略中规则个数")
    private Integer num;

    @Schema(description = "策略生成时间")
    private Date createTime;

    @Schema(description = "描述")
    @ByteSize(max = 128, message = "策略备注 长度不能超过128字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9!\"#$%&'()*+,\\-./:;<=>?@\\[\\\\\\]^_`{|}~]+$", message = "策略名称 格式错误")
    private String description;

    @Schema(description = "下发状态，0未下发，1已下发")
    private Integer issuedStatus;

    @Schema(description = "下发时间")
    private Date issuedTime;

    @Schema(description = "是否是默认策略")
    private Integer isDefault;

    @Schema(description = "下发方式：reset 全量下发，add 增量下发，del 增量删除")
    private String cmd;

    @Schema(description = "规则列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private List<Long> ruleIds;

    @Schema(description = "设备列表")
    private List<String> deviceIds;
}
