package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月14日
 */
@Data
public class SysDataStorageTimeThresholdReq {


    @Schema(description = "检测器和终端保密组件上报的运行状态数据、异常状态数据、审计数据")
    @NotNull
    private Integer detectorTerminalReportedData;

    @Schema(description = "终端检测平台自身的运行状态数据、异常状态数据、审计数据")
    @NotNull
    private Integer platformSelfData;

    @Schema(description = "终端检测平台本地下发的策略数据")
    @NotNull
    private Integer localPolicyData;

}
