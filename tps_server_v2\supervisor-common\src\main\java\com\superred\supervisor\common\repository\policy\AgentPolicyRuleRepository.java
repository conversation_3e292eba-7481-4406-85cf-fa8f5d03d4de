package com.superred.supervisor.common.repository.policy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.mapper.policy.AgentPolicyRuleMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:32
 */
@Slf4j
@Repository
@AllArgsConstructor
public class AgentPolicyRuleRepository extends ServiceImpl<AgentPolicyRuleMapper, AgentPolicyRule> {

}
