#!/bin/bash

CONFIG_FILE="/etc/wlh/supervisor/network_config"
TEMP_CONFIG=$(mktemp)
mkdir -p "$(dirname "$CONFIG_FILE")"

echo "检测物理网卡中..."

# 过滤虚拟网卡（常见前缀）
EXCLUDE_PREFIXES="lo|docker|br-|virbr|vmnet|vboxnet|tap|tun|wg|zt|cni|flannel|kube|veth"

# 获取所有物理网卡
interfaces=$(ip -o link show | awk -F': ' '{print $2}' | grep -Ev "$EXCLUDE_PREFIXES")

if [ -z "$interfaces" ]; then
  echo "未检测到物理网卡，退出。"
  exit 1
fi

echo "请选择每个网卡的类型："
echo "  1: 本地管理网络接口"
echo "  2: 通信服务网络接口"
echo "  0: 忽略此网卡"
echo "  提示: !!! 单网卡设备只配置默认网卡为:2 (通信服务网络接口)"

configured=false
has_comm=false
has_manage=false

for iface in $interfaces; do
  echo ""
  read -p "网卡 $iface 类型（1-2 或 0 忽略）: " type
  case $type in
    1)
      echo "$iface=1" >> "$TEMP_CONFIG"
      configured=true
      has_manage=true
      ;;
    2)
      echo "$iface=2" >> "$TEMP_CONFIG"
      configured=true
      has_comm=true
      ;;
    *)
      echo "$iface=ignored" >> "$TEMP_CONFIG"
      ;;
  esac
done

if [ "$has_comm" = false ]; then
  echo "!!!!!! 未配置[通信服务网络接口]，程序退出。"
  rm -f "$TEMP_CONFIG"
  exit 1
fi

# 写入配置文件
mv "$TEMP_CONFIG" "$CONFIG_FILE"
echo "✅ 配置已保存到 $CONFIG_FILE"

# 绑定通信网卡到 public zone
if [ "$has_comm" = true ]; then
  comm_ifaces=$(grep '=2' "$CONFIG_FILE" | cut -d= -f1)
  for iface in $comm_ifaces; do
    echo "绑定 $iface 到 zone: public"
    nmcli connection modify "$iface" connection.zone public
  done
fi

# 如果有管理口，确保 manage zone 存在并绑定
if [ "$has_manage" = true ]; then
  if ! firewall-cmd --permanent --get-zones | grep -q "^manage$"; then
    echo "创建自定义 zone: manage"
    firewall-cmd --permanent --new-zone=manage
    firewall-cmd --reload
  fi

  manage_ifaces=$(grep '=1' "$CONFIG_FILE" | cut -d= -f1)
  for iface in $manage_ifaces; do
    echo "绑定 $iface 到 zone: manage"
    nmcli connection modify "$iface" connection.zone manage
  done
fi

sudo firewall-cmd --zone=public --add-port=12300/tcp --permanent
sudo firewall-cmd --zone=public --add-port=12400/tcp --permanent
sudo firewall-cmd --zone=public --add-port=10443/tcp --permanent
sudo firewall-cmd --zone=public --add-port=10444/tcp --permanent

# 重新加载防火墙规则
sudo firewall-cmd --reload
echo "✅ 所有网卡 zone 配置已完成。"
