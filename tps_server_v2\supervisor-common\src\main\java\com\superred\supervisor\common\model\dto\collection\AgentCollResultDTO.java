package com.superred.supervisor.common.model.dto.collection;


import com.superred.supervisor.file.consant.ExportFile;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 响应结果异步统一实体
 *
 * <AUTHOR>
 * @since 2025/5/28 16:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentCollResultDTO<Req> {

    public static final String KAFKA_HEADER_NAME = "ExportFile";

    /**
     * 数据源设备ID
     */
    private String srcDevice;

    /**
     * 导出文件的检测平台设备ID
     */
    private String localDeviceId;

    /**
     * 导出数据类型
     */
    private ExportFile exportFile;


    private Req requestBody;


}
