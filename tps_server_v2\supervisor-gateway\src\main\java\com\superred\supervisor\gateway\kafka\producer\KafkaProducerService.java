package com.superred.supervisor.gateway.kafka.producer;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import javax.annotation.Resource;


/**
 * 卡夫卡生产者服务
 *
 * <AUTHOR>
 * @since 2024/05/27
 */
@Slf4j
@Service
public class KafkaProducerService {


    @Resource(name = "commonKafkaTemplate")
    private KafkaTemplate<String, String> kafkaTemplate;


    public void sendMessage(String message, String topic, String headerKey, String headerValue) {
        ProducerRecord<String, String> record = new ProducerRecord<>(topic, message);
        record.headers().add(new RecordHeader(headerKey, headerValue.getBytes()));

        this.doSendRecord(record);
    }


    public void sendMessage(String message, String topic) {
        ProducerRecord<String, String> record = new ProducerRecord<>(topic, message);
        this.doSendRecord(record);
    }

    private void doSendRecord(ProducerRecord<String, String> record) {
        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(record);
        future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {

            @Override
            public void onSuccess(SendResult<String, String> result) {
                log.debug("KafkaProducer:: sendMessage:: SUCCESS: message: {} with offset: {}", record.value(),
                        result.getRecordMetadata().offset());
            }

            @Override
            public void onFailure(Throwable ex) {
                log.error("KafkaProducer :: sendMessage:: ERROR: unable to send message: {} Error: {}",
                        record.value(), ex.getMessage());
            }

        });
    }

    public void sendMessage(String message, String topic, Runnable runnable) {
        ProducerRecord<String, String> record = new ProducerRecord<>(topic, message);

        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(record);
        future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {

            @Override
            public void onSuccess(SendResult<String, String> result) {
                runnable.run();
            }

            @Override
            public void onFailure(Throwable ex) {
                log.error("KafkaProducer :: sendMessage:: ERROR: unable to send message: {} Error: {}",
                        message, ex.getMessage());
            }

        });
    }

}
