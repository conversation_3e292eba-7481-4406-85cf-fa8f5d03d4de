package com.superred.supervisor.manager.model.vo.system.role;

import com.superred.supervisor.common.entity.system.SysRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static com.superred.supervisor.manager.constant.CommonConstants.ADMIN_ROLE_IDS;


/**
 * 系统角色页面
 *
 * <AUTHOR>
 * @since 2025/03/20
 */
@Data

public class SysRolePageResp {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 角色名
     */
    @Schema(description = "角色名")

    private String name;

    /**
     * 角色编码
     */
    @Schema(description = "角色编码")
    private String code;

    /**
     * 角色描述
     */
    @Schema(description = "角色描述")
    private String description;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")

    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime modifiedTime;


    /**
     * 编辑
     */
    @Schema(description = "可编辑状态")
    private Boolean editable;


    public static SysRolePageResp fromSysRole(SysRole sysRole) {

        SysRolePageResp resp = new SysRolePageResp();
        resp.setId(sysRole.getId());
        resp.setName(sysRole.getName());
        resp.setCode(sysRole.getCode());
        resp.setDescription(sysRole.getDescription());
        resp.setCreateTime(sysRole.getCreateTime());
        resp.setModifiedTime(sysRole.getModifiedTime());
        resp.setEditable(!ADMIN_ROLE_IDS.contains(sysRole.getId()));
        return resp;

    }
}
