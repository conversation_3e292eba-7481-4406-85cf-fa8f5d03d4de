package com.superred.supervisor.manager.service.login;


import com.superred.supervisor.manager.model.auth.CaptchaResp;
import com.superred.supervisor.manager.model.auth.KeyPairResp;
import com.superred.supervisor.manager.model.vo.login.LoginReq;
import com.superred.supervisor.manager.model.vo.login.LoginResp;
import com.superred.supervisor.manager.model.vo.login.UkeyLoginReq;
import com.superred.supervisor.manager.model.vo.login.UkeyLoginResp;
import com.superred.supervisor.manager.model.vo.login.UserInfoResp;
import com.superred.supervisor.manager.model.vo.system.MenuTreeResp;

import java.util.List;

/**
 * 用户登录相关逻辑
 *
 * <AUTHOR>
 * @since 2025/3/6 16:25
 */
public interface LoginService {

    /**
     * 用户登录
     *
     * @param req 登录请求
     * @return 登录响应
     */
    LoginResp login(LoginReq req);

    /**
     * 用户登出
     */
    void logout();

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    UserInfoResp getUserInfo();

    /**
     * 创建图片验证码
     *
     * @return 图片验证码
     */
    CaptchaResp createImageCode();


    /**
     * 创建SM2密钥对
     *
     * @return SM2密钥对响应
     */
    KeyPairResp createSM2KeyPair();

    /**
     * ukey登录
     *
     * @param req 登录请求
     * @return 登录响应
     */
    UkeyLoginResp getRandomNumber(UkeyLoginReq req);


    /**
     * 获取登录用户菜单
     *
     * @return 菜单树
     */
    List<MenuTreeResp> getLoginUserMenu();


}
