package com.superred.supervisor.common.repository.agent;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.agent.AgentSystemStatus;
import com.superred.supervisor.common.mapper.agent.AgentSystemStatusMapper;
import org.springframework.stereotype.Repository;

/**
 * 检测器运行状态 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-21 16:54:03
 */
@Repository
public class AgentSystemStatusRepository extends ServiceImpl<AgentSystemStatusMapper, AgentSystemStatus> {

}

