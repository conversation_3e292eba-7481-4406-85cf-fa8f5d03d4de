package com.superred.supervisor.manager.model.vo.index;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 系统异常日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@EqualsAndHashCode(callSuper = true)
@Data

public class LocalDeviceEventPageReq extends PageReqDTO {

    @Schema(description = "是否已读 0未读 1已读")
    private Integer status;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;
}
