package com.superred.supervisor.manager.model.vo.devices;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 设备中央处理器信息
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data

public class DeviceCpuInfoResp {


    /**
     *
     */
    @Schema(description = "")
    @JsonProperty("physical_id")
    private Integer physicalId;


    /**
     * cpu核数，8
     */
    @Schema(description = "CPU核数，8")
    private Integer core;

    /**
     * 主频，“1.8GHz”
     */
    @Schema(description = "主频，“1.8GHz”")
    private Double clock;


}
