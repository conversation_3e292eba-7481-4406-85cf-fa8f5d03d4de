package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 访问控制白名单
 *
 * <AUTHOR>
 * @since 2025年03月13日
 */
@Data
public class IpWhiteListEnableReq {
    @Schema(description = "主键")
    private Integer id;


    @Schema(description = "是否启用")
    @NotNull(message = "应用状态 不可为空")
    private Boolean enabled;


}
