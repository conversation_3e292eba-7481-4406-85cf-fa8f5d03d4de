
package com.superred.common.xss.core;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.deser.std.StringDeserializer;
import com.superred.common.xss.utils.XssHolder;
import com.superred.common.xss.utils.XssUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Objects;

/**
 * Jackson XSS 处理基类
 *
 * <AUTHOR>
 * @since 2025/07/23
 */
@Slf4j
@RequiredArgsConstructor
public class XssCleanDeserializer extends JsonDeserializer<String> {


    private final XssCleaner xssCleaner;


    @Override
    public String deserialize(JsonParser p, DeserializationContext ctx) throws IOException {
        // 先使用默认的 StringDeserializer 反序列化, 没有实现 ContextualDeserializer
        //Jackson 反序列化 String 字段时不会有上下文敏感行为，也不会根据字段上的注解或属性动态调整其解析逻辑
        String raw = StringDeserializer.instance.deserialize(p, ctx);

        // xss 过滤
        String currentName = p.currentName();
        String cleaned = this.clean(currentName, raw);
        if (!StrUtil.equals(raw, cleaned)) {
            log.warn(">>>xss cleaned:  property name:{}, old value : {},  cleaned current value : {}", currentName, raw, cleaned);
        }
        return cleaned;
    }

    private String clean(String name, String text) {
        if (XssHolder.isEnabled() && Objects.isNull(XssHolder.getXssIgnore())) {
            return xssCleaner.clean(text);
        } else if (XssHolder.isEnabled() && Objects.nonNull(XssHolder.getXssIgnore())) {
            XssIgnore xssCleanIgnore = XssHolder.getXssIgnore();
            if (ArrayUtil.contains(xssCleanIgnore.value(), name)) {
                return XssUtil.trim(text, false);
            }
            return xssCleaner.clean(XssUtil.trim(text, false));
        } else {
            return XssUtil.trim(text, false);
        }
    }

}
