package com.superred.supervisor.common.repository.settings;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.settings.AclWhitelist;
import com.superred.supervisor.common.mapper.settings.AclWhitelistMapper;
import org.springframework.stereotype.Repository;

/**
 * 访问控制白名单(PSysAclWhitelist) Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-19 18:26:53
 */
@Repository
public class AclWhitelistRepository extends ServiceImpl<AclWhitelistMapper, AclWhitelist> {


}

