package com.superred.common.minio.core.utils;

import com.superred.common.minio.core.constants.Constant;
import io.minio.BucketExistsArgs;
import io.minio.DownloadObjectArgs;
import io.minio.GetObjectArgs;
import io.minio.GetObjectResponse;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.ListObjectsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import io.minio.Result;
import io.minio.SetBucketPolicyArgs;
import io.minio.StatObjectArgs;
import io.minio.StatObjectResponse;
import io.minio.UploadObjectArgs;
import io.minio.http.Method;
import io.minio.messages.Item;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;


/**
 * minio 工具
 */
@Component
@Slf4j
public class MinioUploadClient {
    @Resource
    private MinioClient minioClient;

    public MinioClient getMinioClient() {
        return minioClient;
    }

    @SneakyThrows
    public void makeBucketIfNotExists(String bucketName, Boolean isPublic) {
        boolean exists = minioClient.bucketExists(BucketExistsArgs.builder()
                .bucket(bucketName)
                .build());
        if (!exists) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            // 设置存储桶策略，允许公共读取
            if (isPublic) {
                //创建公共访问的桶
                // 设置存储桶策略，允许公共读取
                minioClient.setBucketPolicy(
                        SetBucketPolicyArgs.builder()
                                .bucket(bucketName)
                                .config(String.format(Constant.policyBunk, bucketName, bucketName))
                                .build());
            }
        }
    }

    @SneakyThrows
    public StatObjectResponse statObject(String bucketName, String objectName) {
        return minioClient.statObject(StatObjectArgs.builder()
                .bucket(bucketName)
                .object(objectName)
                .build());
    }

    /**
     * @param bucketName 桶名称
     * @param file       上传文件
     * <AUTHOR>
     * @date 2023/10/31 10:43
     */
    @SneakyThrows
    public void uploadFile(String bucketName, String filePath, File file) {
        //检查桶是否存在
        makeBucketIfNotExists(bucketName, true);
        //文件上传
        ExecutorService executor = Executors.newFixedThreadPool(1);
        try {
            executor.execute(() -> {
                try (InputStream in = Files.newInputStream(file.toPath())) {

                    minioClient.putObject(
                            PutObjectArgs.builder()
                                    .bucket(bucketName)
                                    .object(filePath + "/" + file.getName())
                                    .stream(in, file.length(), -1)
                                    .contentType("application/octet-stream")
                                    .build()
                    );
                } catch (Exception e) {
                    log.error("文件上传minio失败：{}", e.getMessage(), e);
                    // Handle the exception accordingly
                }
            });
        } finally {
            executor.shutdown();
        }
    }

    /**
     * 上传文件
     *
     * @param bucketName  桶名
     * @param filePath    上传路径
     * @param inputStream 输入流
     * @param objectSize  对象大小
     * @param fileName    文件名
     */
    @SneakyThrows
    public void uploadFile(String bucketName, String filePath, InputStream inputStream, Long objectSize, String fileName) {
        //检查桶是否存在
        makeBucketIfNotExists(bucketName, true);
        //文件上传
        ExecutorService executor = Executors.newFixedThreadPool(1);
        try {
            executor.execute(() -> {
                try {
                    minioClient.putObject(
                            PutObjectArgs.builder()
                                    .bucket(bucketName)
                                    .object(filePath + "/" + fileName)
                                    .stream(inputStream, objectSize, -1)
                                    .contentType("application/octet-stream")
                                    .build()
                    );
                } catch (Exception e) {
                    log.error("文件上传minio失败：{}", e.getMessage(), e);
                    // Handle the exception accordingly
                } finally {
                    if (inputStream != null) {
                        try {
                            inputStream.close();
                        } catch (IOException e) {
                            log.error("文件上传minio失败：{}", e.getMessage(), e);
                        }
                    }
                }
            });
        } finally {
            executor.shutdown();
        }
    }

    /**
     * 同步上传文件，并且不关闭 inputStream
     *
     * @param bucketName  桶名
     * @param filePath    上传路径
     * @param inputStream 输入流
     * @param objectSize  对象大小
     * @param fileName    文件名
     */
    @SneakyThrows
    public void uploadFileSync(String bucketName, String filePath, InputStream inputStream, Long objectSize, String fileName) {
        //检查桶是否存在
        makeBucketIfNotExists(bucketName, true);
        try {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(filePath + "/" + fileName)
                            .stream(inputStream, objectSize, -1)
                            .contentType("application/octet-stream")
                            .build()
            );
        } catch (Exception e) {
            log.error("文件上传minio失败{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 上传本地文件
     *
     * @param bucketName
     * @param fileFullPath
     */
    public void uploadLocalFile(String bucketName, String fileFullPath) {
        try {
            makeBucketIfNotExists(bucketName, true);
            minioClient.uploadObject(
                    UploadObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileFullPath)
                            .filename(fileFullPath).build()
            );
        } catch (Exception e) {
            log.error("文件上传minio失败：{}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }


    @SneakyThrows
    public InputStream download(String fileMd5, String bucketName) {
        StatObjectResponse statObject = statObject(bucketName, fileMd5);

        if (statObject == null || statObject.size() <= 0) {
            throw new Exception(fileMd5 + "文件不存在");
        }
        return minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(fileMd5)
                        .build());
    }

    @SneakyThrows
    public void downloadFile(String fileMd5, String bucketName, String filePath) {
        try {
            StatObjectResponse statObject = statObject(bucketName, fileMd5);
            if (statObject == null || statObject.size() <= 0) {
                throw new Exception(fileMd5 + "文件不存在");
            }
        } catch (Exception e) {
            log.error("文件不存在");
            throw new Exception(fileMd5 + "文件不存在");
        }

        minioClient.downloadObject(
                DownloadObjectArgs.builder()
                        .bucket(bucketName)
                        .object(fileMd5)
                        .filename(filePath)
                        .build()
        );
    }



    /**
     * 删除
     *
     * @param fileName
     * @return
     * @throws Exception
     */
    public boolean remove(String fileName, String bucketName) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(fileName).build());
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @SneakyThrows
    public String getURL(String fileName, String bucketName) {
        GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                .bucket(bucketName)
                .object(fileName)  // 对象名称
                .expiry(2, TimeUnit.SECONDS) // 该url签名10秒过期
                .method(Method.GET)  // 该url允许的请求方式
                .build();
        // 创建预签名url
        String preSignedObjectUrl = minioClient.getPresignedObjectUrl(args);
        // 设置存储桶策略，允许公共读取
        makeBucketPolicy(bucketName);
        return preSignedObjectUrl;
    }


    public String getUploadSignedURL(String fileName, String bucketName) {
        GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                .bucket(bucketName)
                .object(fileName)
                .expiry(10, TimeUnit.MINUTES)
                .method(Method.PUT)
                .build();
        // 创建预签名url
        String preSignedObjectUrl;
        try {
            preSignedObjectUrl = minioClient.getPresignedObjectUrl(args);
        } catch (Exception e) {
            log.error("获取上传文件url失败{}", e.getMessage());
            throw new RuntimeException(e);
        }
        return preSignedObjectUrl;
    }

    @SneakyThrows
    public InputStream downloadByLog(String fileMd5, String bucketName) {
        // 设置存储桶策略，允许公共读取 因为增加预览功能导致 再次下载需要开发权限
        makeBucketPolicy(bucketName);
        return minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(fileMd5)
                        .build());
    }

    @SneakyThrows
    public void makeBucketPolicy(String bucketName) {
        //创建公共访问的桶
        // 设置存储桶策略，允许公共读取
        minioClient.setBucketPolicy(
                SetBucketPolicyArgs.builder()
                        .bucket(bucketName)
                        .config(String.format(Constant.policyBunk, bucketName, bucketName))
                        .build());
    }

    @SneakyThrows
    public GetObjectResponse getObject(StatObjectResponse statObjectResponse, long startByte, long contentLength) {
        return minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(statObjectResponse.bucket())
                        .object(statObjectResponse.object())
                        .offset(startByte)
                        .length(contentLength)
                        .build());
    }

    /**
     * 遍历bucket下所有文件名
     *
     * @param bucketName 要下载的bucket
     * @param prefix     前缀
     */
    public List<String> listNames(String bucketName, String prefix) {
        List<String> resultList = new ArrayList<>();
        Iterable<Result<Item>> listObjects = new ArrayList<>();
        if (StringUtils.hasLength(prefix)) {
            listObjects = minioClient.listObjects(ListObjectsArgs.builder()
                    .bucket(bucketName)
                    .prefix(prefix)
                    .build());
        } else {
            listObjects = minioClient.listObjects(ListObjectsArgs.builder()
                    .bucket(bucketName)
                    .build());
        }
        try {
            for (Result<Item> result : listObjects) {
                Item item = result.get();
                if (item.isDir()) continue; // 跳过目录

                // 根据扩展名判断是否为图片
                String objectName = item.objectName();
                if (objectName.endsWith(".jpg") || objectName.endsWith(".jpeg") || objectName.endsWith(".png") || objectName.endsWith(".gif")) {
                    resultList.add(objectName);
                }
            }
        } catch (Exception e) {
            log.error("遍历bucket下所有文件名异常!", e.getMessage(), e);
        }
        return resultList;
    }
}
