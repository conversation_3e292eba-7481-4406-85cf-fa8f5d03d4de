package com.superred.supervisor.gateway.model.app.status;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * APP系统状态上报请求
 *
 * <AUTHOR>
 * @since 2025/6/27 9:27
 **/
@Data
public class AppSystemStatusReq {

    /**
     * cpu 使用率 0-100
     */
    private List<CpuUsageReq> cpu;

    /**
     * 内存使用率 0-100
     */
    private Integer mem;

    /**
     * 磁盘使用空间GB
     */
    private Integer disk;

    /**
     *  上报时间
     */
    private String time;

    /**
     * 终端保密组件 服务器编号
     */
    private Integer did;


    /**
     * CPU 使用请求
     * <AUTHOR>
     * @since 2025/07/28
     */
    @Data
    public static class CpuUsageReq {
        /**
         * 物理CPU编号
         */
        @JsonProperty("physical_id")
        private Integer physicalId;
        /**
         * CPU使用率
         */
        @JsonProperty("cpu_usage")
        private Integer cpuUsage;

    }
}


