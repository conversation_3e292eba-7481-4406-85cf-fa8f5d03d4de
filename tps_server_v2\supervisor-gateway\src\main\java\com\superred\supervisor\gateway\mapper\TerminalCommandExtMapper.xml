<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.superred.supervisor.gateway.mapper.TerminalCommandExtMapper">


    <select id="selectOperationsByDeviceId"
            resultType="com.superred.supervisor.gateway.model.dto.terminal.TerminalCmdDTO">
        SELECT
            ope.id operationExecId,
            ope.operation_type type,
            op.cmd,
            op.module,
            op.submodule submoduleJson,
            op.version,
            op.num,
            op.param paramJson,
            op.ref_id cmdId,
            op.config
        FROM
            `op_terminal_operation_exec` ope
                LEFT JOIN op_terminal_operation op ON ope.operation_id = op.id
        WHERE
            ope.device_id = #{deviceId}
    </select>



</mapper>