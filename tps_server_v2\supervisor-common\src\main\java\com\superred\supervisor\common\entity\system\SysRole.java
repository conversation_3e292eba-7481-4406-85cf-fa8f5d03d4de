package com.superred.supervisor.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 角色表 实体
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:21
 */
@Data
@TableName("p_sys_role")
public class SysRole {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 角色名
     */
    @TableField("name")
    private String name;

    /**
     * 角色编码
     */
    @TableField("code")
    private String code;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modified_time")
    private LocalDateTime modifiedTime;

    /**
     * 1 表示删除，0 表示未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 0 表示可编辑, 1 表示不可编辑
     */
    @TableField("is_edit")
    private Integer isEdit;

    /**
     * 数据权限类型
     */
    @TableField("ds_type")
    private String dsType;

    /**
     * 数据权限作用范围
     */
    @TableField("ds_scope")
    private String dsScope;

}

