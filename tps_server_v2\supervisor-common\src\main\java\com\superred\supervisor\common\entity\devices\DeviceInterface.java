package com.superred.supervisor.common.entity.devices;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 检测器网卡流量 实体
 *
 * <AUTHOR>
 * @since 2025-03-18 20:06:46
 */
@Data
@TableName("d_device_interface")
public class DeviceInterface {


    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("device_id")
    private String deviceId;

    /**
     * 采集数据网卡序号，每个网卡具备固定序号
     */
    @TableField("interface_seq")
    private Integer interfaceSeq;

    /**
     * 采集数据网卡描述
     */
    @TableField("interface_flag")
    private String interfaceFlag;

    /**
     * 网卡状态，数值类型，取值为：1（网卡启用）、2（网卡停用）、3（网线掉落）、4（网卡故障）、99（未知）;
     */
    @TableField("interface_stat")
    private Integer interfaceStat;

    /**
     * 表示采集到数据流量，单位:kbps;
     */
    @TableField("interface_flow")
    private Integer interfaceFlow;

    /**
     * 错误或无法处理的报文个数
     */
    @TableField("interface_error")
    private Integer interfaceError;

    /**
     * 丢包个数
     */
    @TableField("interface_drop")
    private Integer interfaceDrop;

    /**
     * 数据采集时长的秒数
     */
    @TableField("duration_time")
    private Long durationTime;

    /**
     * 采集时间
     */
    @TableField("time")
    private LocalDateTime time;

}

