package com.superred.supervisor.manager.repository.terminal;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.supervisor.common.constant.devices.DevicesRegStatus;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import com.superred.supervisor.common.entity.terminal.enums.ActivateStatus;
import com.superred.supervisor.manager.common.enums.operation.OpetationPublishType;
import com.superred.supervisor.manager.mapper.terminal.TerminalAgentInfoExtMapper;
import com.superred.supervisor.manager.model.vo.command.OperationRangeReq;
import com.superred.supervisor.manager.utils.IpV4RangeUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 终端设备信息 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-07-17 17:55:19
 */
@Repository
public class TerminalAgentInfoExtRepository extends ServiceImpl<TerminalAgentInfoExtMapper, TerminalAgentInfo> {


    public List<TerminalAgentInfo> searchActiveTerminalByRangeReq(OperationRangeReq req, boolean throwIfEmpty) {
        List<TerminalAgentInfo> terminalAgentInfos = this.searchActiveTerminalByRangeReq(req);
        if (throwIfEmpty && CollUtil.isEmpty(terminalAgentInfos)) {
            throw new BaseBusinessException("No active terminals found for the given range request.");
        }
        return terminalAgentInfos;
    }

    /**
     * 根据范围查询终端设备信息
     *
     * @param req 查询请求
     * @return 终端设备信息列表
     */
    public List<TerminalAgentInfo> searchActiveTerminalByRangeReq(OperationRangeReq req) {

        OpetationPublishType opetationPublishType = OpetationPublishType.fromCode(req.getPublishType());
        switch (opetationPublishType) {
            case DEPARTMENT:
                return this.selectByDepartmentId(req.getOrgIds());
            case DEVICE:
                return this.selectByIds(req.getDeviceIds());
            case IP_RANGE:
                return this.selectByIpRanges(req.getIpRanges());
            default:
                throw new IllegalArgumentException("Unsupported publish type: " + req.getPublishType());
        }
    }

    private List<TerminalAgentInfo> selectByIpRanges(List<String> ipRanges) {

        if (ipRanges == null || ipRanges.isEmpty()) {
            return Collections.emptyList();
        }
        Set<IpV4RangeUtils.IpLongRange> collect = ipRanges.stream().map(IpV4RangeUtils::parseIpRange).collect(Collectors.toSet());
        if (collect.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TerminalAgentInfo> queryWrapper = Wrappers.<TerminalAgentInfo>lambdaQuery()
                .eq(TerminalAgentInfo::getActivateStatus, ActivateStatus.ACTIVE)
                .eq(TerminalAgentInfo::getRegisterStatus, DevicesRegStatus.PASS);


        queryWrapper.and(wrapper -> {
            for (IpV4RangeUtils.IpLongRange ipLongRange : collect) {
                wrapper.ge(TerminalAgentInfo::getIpLong, ipLongRange.getStart())
                        .le(TerminalAgentInfo::getIpLong, ipLongRange.getEnd());
            }
        });

        return this.list(queryWrapper);
    }

    private List<TerminalAgentInfo> selectByIds(List<String> deviceIds) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TerminalAgentInfo> queryWrapper = Wrappers.<TerminalAgentInfo>lambdaQuery()
                .eq(TerminalAgentInfo::getActivateStatus, ActivateStatus.ACTIVE)
                .eq(TerminalAgentInfo::getRegisterStatus, DevicesRegStatus.PASS)
                .in(TerminalAgentInfo::getDeviceId, deviceIds);

        return this.list(queryWrapper);

    }

    private List<TerminalAgentInfo> selectByDepartmentId(List<Integer> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 不考虑子部门
        LambdaQueryWrapper<TerminalAgentInfo> queryWrapper = Wrappers.<TerminalAgentInfo>lambdaQuery()
                .eq(TerminalAgentInfo::getActivateStatus, ActivateStatus.ACTIVE)
                .eq(TerminalAgentInfo::getRegisterStatus, DevicesRegStatus.PASS)
                .in(TerminalAgentInfo::getOrgId, orgIds);

        return this.list(queryWrapper);
    }


}

