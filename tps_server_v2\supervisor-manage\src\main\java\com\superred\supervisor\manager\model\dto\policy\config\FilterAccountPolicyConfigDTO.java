package com.superred.supervisor.manager.model.dto.policy.config;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleFilterAccount;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-27 21:23
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FilterAccountPolicyConfigDTO {

    /**
     * 策略ID
     */
    private Long ruleId;

    private String ruleContent;

    private String ruleDesc;

    private List<Integer> filterFileType;

    private FileFilterSizeDTO filterFileSize;

    public static FilterAccountPolicyConfigDTO getPolicyConfig(RuleFilterAccount filterAccount) {
        if (filterAccount == null) {
            return null;
        }
        return FilterAccountPolicyConfigDTO.builder()
                .ruleId(Long.parseLong(filterAccount.getRuleId()))
                .ruleContent(PolicyUtils.handleStrNull(filterAccount.getRuleContent()))
                .ruleDesc(PolicyUtils.handleStrNull(filterAccount.getRuleDesc()))
                .filterFileType(PolicyUtils.strToIntList(filterAccount.getFilterFileType()))
                .filterFileSize((filterAccount.getFilterFileSizeMin() == null && filterAccount.getFilterFileSizeMax() == null)
                        ? null : FileFilterSizeDTO.builder()
                        .minSize(filterAccount.getFilterFileSizeMin() == null ? 0 : filterAccount.getFilterFileSizeMin().intValue())
                        .maxSize(filterAccount.getFilterFileSizeMax() == null ? 4194304 : filterAccount.getFilterFileSizeMax().intValue())
                        .build()
                )
                .build();
    }
}
