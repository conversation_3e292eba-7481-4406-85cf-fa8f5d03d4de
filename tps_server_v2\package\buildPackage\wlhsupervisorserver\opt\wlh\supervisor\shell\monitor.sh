#!/bin/bash

DBNAME="own_supervision"
risk=1
msg=""
clusterDid=`cat /opt/wlh/supervisor/lib/tps-server-v2-1.0.0/conf/prod-config.yml | grep "clusterDid"| awk '{print $2}'`

function check_kafka(){
	status=$(systemctl status --no-page kafka.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="kafka 消息队列服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="kafka 消息队列服务异常"
			echo "$msg"
			add_log
			#systemctl restart kafka.service
		else
			echo "kafka 消息队列服务正常"
		fi
	fi
}

function check_zookeepe(){
	status=$(systemctl status --no-page zookeeper.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="zookeeper 消息队列服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="zookeeper 消息队列服务异常"
			echo "$msg"
			add_log
			#systemctl restart zookeeper.service
		else
			echo "zookeeper 消息队列服务正常"
		fi
	fi
}

function check_pometheus(){
	status=$(systemctl status --no-page prometheus.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="prometheus 设备状态信息采集服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="prometheus 设备状态信息采集服务异常"
			echo "$msg"
			add_log
			#systemctl restart prometheus.service
		else
			echo "prometheus 设备状态信息采集服务正常"
		fi
	fi
}

function check_redis(){
	status=$(systemctl status --no-page redis.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="redis 缓存服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="redis 缓存服务异常"
			echo "$msg"
			add_log
			#systemctl restart redis.service
		else
			echo "redis 缓存服务正常"
		fi
	fi
}

function check_minio(){
	status=$(systemctl status --no-page minio.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="minio 文件存储服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="minio 文件存储服务异常"
			echo "$msg"
			add_log
			#systemctl restart minio.service
		else
			echo "minio 文件存储服务正常"
		fi
	fi
}

function check_nginx(){
	status=$(systemctl status --no-page nginx.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="nginx web服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="nginx web服务异常"
			echo "$msg"
			add_log
			#systemctl restart nginx.service
		else
			echo "nginx web服务正常"
		fi
	fi
}

function check_maiadb(){
	status=$(systemctl status --no-page mariadb.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="mariadb 数据库服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="mariadb 数据库服务异常"
			echo "$msg"
			add_log
			#systemctl restart mariadb.service
		else
			echo "mariadb 数据库服务正常"
		fi
	fi
}

function check_business(){
	status=$(systemctl status --no-page business.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="business 业务处置系统服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		result=$(echo $status | grep "active")
    if [ "$result" == "" ]; then
			msg="business 业务处置系统服务异常"
			echo "$msg"
			add_log
			#systemctl restart business.service
		else
			echo "business 业务处置系统服务正常"
		fi
	fi
}

function check_view(){
	status=$(systemctl status --no-page view.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="view 生成报告服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="view 生成报告服务异常"
			echo "$msg"
			add_log
			#systemctl restart view.service
		else
			echo "view 生成报告服务正常"
		fi
	fi
}

function check_data_api(){
	status=$(systemctl status --no-page data-api.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="data-api 数据接收系统服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="data-api 数据接收系统服务异常"
			echo "$msg"
			add_log
			#systemctl restart data-api.service
		else
			echo "data-api 数据接收系统服务正常"
		fi
	fi
}

function check_data_db(){
	status=$(systemctl status --no-page data-db.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="data-db 数据入库系统服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="data-db 数据入库系统服务异常"
			echo "$msg"
			add_log
			#systemctl restart data-db.service
		else
			echo "data-db 数据入库系统服务正常"
		fi
	fi
}

function check_data_report(){
	status=$(systemctl status --no-page data-report.service |grep Active |awk '{print $2}')
	if [ -z "$status" ]; then
		msg="data-report 数据上报系统服务未找到，请手动排查"
		echo "$msg"
		add_log
	else
		if [ "$status" != "active" ]; then
			msg="data-report 数据上报系统服务异常"
			echo "$msg"
			add_log
			#systemctl restart data-report.service
		else
			echo "data-report 数据上报系统服务正常"
		fi
	fi
}

#记录日志
function add_log() {
   #"节点1,data-report 数据上报系统服务异常"
   #="节点${clusterDid}, ${msg}"
   mysql -usuperred -p123456 ${DBNAME} -e "insert into local_device_suspected_log (device_id, event_type,risk , msg, did) values ('180210010001', 2, ${risk},'${msg}', ${clusterDid}); "
}

function main(){
	check_kafka
	check_zookeepe
	check_pometheus
	check_redis
	check_minio
	check_nginx
	check_maiadb
	check_business
	check_data_api
	check_data_db
	check_data_report
	#check_view
}

main