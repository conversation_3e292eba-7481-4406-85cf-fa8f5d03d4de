package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.db.entity.FileCountStatistics;
import com.superred.supervision.db.vo.statistics.CountStatisticsVo;

import java.util.List;

/**
 * <p>
 * 文件统计数量 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
public interface FileCountStatisticsService extends IService<FileCountStatistics> {

    List<CountStatisticsVo> findByTime(String startTime, String endTime, Integer isReport);

    void deleteByTime(String startTime, String endTime, Integer isReport);
}
