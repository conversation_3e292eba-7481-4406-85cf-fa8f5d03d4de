package com.superred.supervisor.gateway.service.app;

import com.superred.supervisor.gateway.model.app.status.AppBusinessStatusReq;
import com.superred.supervisor.gateway.model.app.status.AppSystemAuditReq;
import com.superred.supervisor.gateway.model.app.status.AppSystemStatusReq;

import java.util.List;

/**
 * APP状态上报
 *
 * <AUTHOR>
 * @since 2025/5/29 13:41
 */
public interface AppStatusService {

    /**
     * 上报APP系统状态
     *
     * @param req 请求参数
     */
    void reportSystemStatus(AppSystemStatusReq req);

    /**
     * 上报APP业务状态
     *
     * @param req 请求参数列表
     */
    void reportBusinessStatus(AppBusinessStatusReq req);

    /**
     * 上报APP系统审计信息
     *
     * @param req 请求参数
     * @return ApiRes 返回结果
     */
    void reportSystemAudit(List<AppSystemAuditReq> req);
}
