#!/bin/sh

usage() {
   echo "NAME"
   echo "    dump-mysql.sh"
   echo ""
   echo "DESCRIPTION"
   echo "    备份mysql数据脚本"
   echo "    arg1 : 备份路径"
   echo ""
}

if [ $# -lt 1 ]; then
    echo "缺少参数" >&2
    echo ""
    usage;
    exit 1
fi

# 备份目录
BACKUP_PATH=$1
MARIADB_PASSWORD=$2
# 数据库列表
DB_ARRAY=(supervision_manage)

for dbname in ${DB_ARRAY[@]}; do
  if mysql -h 127.0.0.1 -u superred -p$MARIADB_PASSWORD -e "use $dbname" >/dev/null 2>&1; then
    mysqldump -h 127.0.0.1 -u superred -p$MARIADB_PASSWORD -B $dbname >$BACKUP_PATH/$dbname.sql
    echo "$dbname数据库历史数据已备份到$BACKUP_PATH/$dbname.sql"
  fi
done
echo "备份本地mysql数据完成"