package com.superred.supervisor.manager.service.command;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageResp;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalCmdRecordPageReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdRecordPageResp;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalUninstallCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalInnerPolicyUpdateCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalModuleSwitchCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalSoftUpgradeCmdReq;

/**
 * <AUTHOR>
 */
public interface IssueAgentService {
    void componentUpgrade(TerminalSoftUpgradeCmdReq req);

    void innerPolicyUpdate(TerminalInnerPolicyUpdateCmdReq req);

    void uninstall(TerminalUninstallCmdReq req);

    void moduleSwitch(TerminalModuleSwitchCmdReq req);

    IPage<TerminalCmdRecordPageResp> statisticsPage(TerminalCmdRecordPageReq req);

    IPage<TerminalCmdExecPageResp> deviceDetailPage(TerminalCmdExecPageReq req);
}
