package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 上级中心地址信息(MgrCenterInfo) 实体
 *
 * <AUTHOR>
 * @since 2025-03-24 11:23:29
 */
@Data
@TableName("mgr_center_info")
public class MgrCenterInfo {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 管理中心名称
     */
    @TableField("name")
    private String name;

    /**
     * ip地址,************
     */
    @TableField("ip")
    private String ip;

    /**
     * 上级中心端口
     */
    @TableField("port")
    private Integer port;

    /**
     * master主中心，slave副中心
     */
    @TableField("type")
    private String type;

    /**
     * 平台级别，1省级，2市级，3区县级
     */
    @TableField("level")
    private Integer level;

    /**
     * 上级中心状态，enable启用，disable停用
     */
    @TableField("status")
    private String status;

    /**
     * 注册状态，0（成功），1（失败），2（审核中）
     */
    @TableField("reg_status")
    private Integer regStatus;

    /**
     * 注册状态描述，0（成功），1（需从页面录入），2（审核中）
     */
    @TableField("reg_message")
    private String regMessage;

    /**
     * 注册时间
     */
    @TableField("reg_time")
    private Date regTime;

    /**
     * 审核时间
     */
    @TableField("verify_time")
    private Date verifyTime;

}

