package com.superred.supervisor.common.entity.settings.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 访问控制类型，1：系统访问控制；2：通信访问控制 3 下载服务控制
 *
 * <AUTHOR>
 * @since 2025/7/10 17:00
 */
@Getter
@AllArgsConstructor
public enum IpWhiteListType {

    WEB_MANAGE(1, "系统访问控制"),
    GATEWAY(2, "通信访问控制"),
    DOWNLOAD_SERVICE(3, "下载服务控制");

    @EnumValue
    private final Integer code;
    private final String description;


    public static IpWhiteListType fromCode(Integer code) {
        for (IpWhiteListType type : IpWhiteListType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown IpWhiteListType code: " + code);
    }
}
