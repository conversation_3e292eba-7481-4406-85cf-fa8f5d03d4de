package com.superred.supervisor.common.entity.agent.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 激活状态：0未激活，1激活
 *
 * <AUTHOR>
 * @since 2025/3/18 19:43
 */
@Getter
@AllArgsConstructor
public enum AuthStatus implements IEnum<Integer> {
    /**
     * 未激活
     */
    NOT_ACTIVE(0, "未激活"),
    /**
     * 激活
     */
    ACTIVE(1, "激活");

    private final Integer value;
    private final String desc;

}
