package com.superred.supervisor.common.mybatis;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.SM4;
import com.superred.supervisor.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <p> 数据库字段字符串加解密Handler
 *
 * <AUTHOR>
 * @since 2025/07/09 11:08
 **/
@Slf4j
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(String.class)
public class EncryptAndDecryptHandler extends BaseTypeHandler<String> {

    /**
     * 兼容未加密的数据, 使用版本前缀标识
     */
    public static final String ENC_PREFIX_V1 = "ENC1::";

    private static volatile SM4 SM4_INSTANCE;

    /**
     * 获取SM4实例
     * 1. EncryptAndDecryptHandler，它会被 MyBatis 多次创建（非 Spring Bean，不是单例）；
     * 2. 希望 SM4 只初始化一次；
     * 3. 同时要求：在 Spring 注入系统属性（或读取配置）之后再初始化。
     *
     * @return sm4
     */
    private static SM4 getSm4Instance() {
        if (SM4_INSTANCE == null) {
            synchronized (EncryptAndDecryptHandler.class) {
                if (SM4_INSTANCE == null) {
                    String key = System.getProperty(CommonConstant.DB_SM4_KEY);
                    if (StrUtil.isBlank(key)) {
                        throw new IllegalArgumentException("系统未设置数据库加密密钥，请检查配置项: " + CommonConstant.DB_SM4_KEY);
                    }
                    String iv = System.getProperty(CommonConstant.DB_SM4_IV);
                    if (StrUtil.isBlank(iv)) {
                        throw new IllegalArgumentException("系统未设置数据库加密密钥，请检查配置项: " + CommonConstant.DB_SM4_IV);
                    }
                    SM4_INSTANCE = new SM4(Mode.CBC, Padding.PKCS5Padding,
                            HexUtil.decodeHex(key), HexUtil.decodeHex(iv));
                }
            }
        }
        return SM4_INSTANCE;
    }


    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String param, JdbcType jdbcType) throws SQLException {
        if (CharSequenceUtil.isEmpty(param)) {
            ps.setString(i, null);
            return;
        }
        ps.setString(i, encryptBase64Database(param));

    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {

        return decryptStrDatabase(rs.getString(columnName));

    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {

        return decryptStrDatabase(rs.getString(columnIndex));

    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {

        return decryptStrDatabase(cs.getString(columnIndex));

    }


    /**
     * 数据库字段加密
     *
     * @param value 需要加密的值
     * @return 解密后得值
     */
    private String encryptBase64Database(String value) {
        if (StrUtil.isBlank(value)) {
            return value;
        }

        try {
            return ENC_PREFIX_V1 + EncryptAndDecryptHandler.getSm4Instance().encryptBase64(value);
        } catch (Exception e) {
            log.error("数据库字段加密失败: {}", value, e);
            throw new RuntimeException("数据库字段加密失败", e);
        }
    }

    /**
     * 数据库字段解密
     *
     * @param value 需要解密的值
     * @return 解密后的值
     */
    private String decryptStrDatabase(String value) {
        if (!StrUtil.startWith(value, ENC_PREFIX_V1)) {
            // 如果不是加密的字符串，直接返回原值
            return value;
        }
        try {
            return EncryptAndDecryptHandler.getSm4Instance().decryptStr(value);
        } catch (Exception e) {
            log.error("数据库字段解密失败", e);
            return value;
        }
    }

}
