package com.superred.supervisor.common.entity.command;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 系统升级文件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@TableName("sys_update_file")
public class UpdateFile {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 命令类型
     * inner_policy_update: 内置策略
     * update: 系统固件
     */
    private String cmd;

    /**
     * 更新固件文件名
     */
    private String filename;

    /**
     * 更新固件文件校验和
     */
    private String md5;

    /**
     * 升级文件路径
     */
    private String filePath;

    /**
     * 更新后产品软件版本号
     */
    private String softVersion;

    /**
     * 上传时间
     */
    private LocalDateTime createTime;

}