package com.superred.supervisor.gateway.exception;

import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.constant.auth.AuthActiveEnum;
import com.superred.supervisor.gateway.constant.auth.AuthStatusEnum;
import lombok.Getter;

/**
 * 网关接口统一异常
 *
 * <AUTHOR>
 * @since 2025/5/19 15:26
 */
@Getter
public class ApiUnAuthException extends RuntimeException {

    private final Integer code;
    private String msg;
    private AuthStatusEnum status;
    private AuthActiveEnum active;

    public ApiUnAuthException(String message) {
        super(message);
        this.msg = message;
        this.code = ApiResponse.FAIL_CODE;
    }

    public ApiUnAuthException(AuthStatusEnum status) {
        super(status.getMessage());
        this.code = ApiResponse.FAIL_CODE;
        this.status = status;
        this.active = AuthActiveEnum.UN_ACTIVATED;
    }

    public ApiUnAuthException(AuthActiveEnum active) {
        super(active.getMessage());
        this.code = ApiResponse.FAIL_CODE;
        this.status = AuthStatusEnum.AUTHENTICATED;
        this.active = active;
    }

    public ApiUnAuthException(String message, Throwable throwable) {
        super(message, throwable);
        this.msg = message;
        this.code = ApiResponse.FAIL_CODE;
    }
}
