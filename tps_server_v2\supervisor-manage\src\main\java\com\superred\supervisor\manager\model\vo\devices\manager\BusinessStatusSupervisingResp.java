package com.superred.supervisor.manager.model.vo.devices.manager;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 自监管业务状态
 */
@Data

public class BusinessStatusSupervisingResp {

    /**
     * 接入监测器数量
     */
    @Schema(description = "接入监测器数量")
    private Long detectorNum;
    /**
     * 接入终端组件数量
     */
    @Schema(description = "接入终端组件数量")
    private Long terminalTotalNum;
    /**
     * 本级策略告警数
     */
    @Schema(description = "本级策略告警数")
    private Long localWarningNum;
    /**
     * 本级策略告警文件数
     */
    @Schema(description = "本级策略告警文件数")
    private Long localFileNum;
    /**
     * 接入终端组件在线数量
     */
    @Schema(description = "接入终端组件在线数量")
    private Long terminalOnlineNum;

    /**
     * 监测器业务状态数据采集时间
     */
    @Schema(description = "监测器业务状态数据采集时间")
    private String time;
    /**
     * 上次加电启动开始运行时长，单位为秒
     */
    @Schema(description = "上次加电启动开始运行时长，单位为秒")
    private Long uptime;
}
