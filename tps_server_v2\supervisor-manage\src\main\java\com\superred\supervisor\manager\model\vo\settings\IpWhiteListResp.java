package com.superred.supervisor.manager.model.vo.settings;

import com.superred.supervisor.common.entity.settings.IpWhitelist;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 *  访问控制白名单
 * @since 2025年03月13日
 */
@Data
public class IpWhiteListResp {

    @Schema(description = "主键")
    private Integer id;

    @Schema(description = "名称")
    private String name;


    @Schema(description = "源IP/源网段")
    private String source;


    @Schema(description = "访问控制类型，1：系统访问控制；2：通信访问控制 3 下载服务控制")
    private Integer aclType;

    @Schema(description = "是否启用")
    private Boolean enabled;


    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remarks;


    public static IpWhiteListResp from(IpWhitelist one) {

        IpWhiteListResp resp = new IpWhiteListResp();
        resp.setId(one.getId());
        resp.setName(one.getName());
        resp.setSource(one.getIp());
        resp.setAclType(one.getType().getCode());
        resp.setEnabled(one.getEnabled());
        resp.setRemarks(one.getRemarks());
        return resp;
    }
}
