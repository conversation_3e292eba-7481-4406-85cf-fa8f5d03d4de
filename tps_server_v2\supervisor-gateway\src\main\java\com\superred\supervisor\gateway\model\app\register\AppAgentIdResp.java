package com.superred.supervisor.gateway.model.app.register;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AgentReqInfo.
 * 终端注册组织机构/人员查询.
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppAgentIdResp {


    /**
     * 终端ID
     */
    @JsonProperty("device_id")
    private String deviceId;

}
