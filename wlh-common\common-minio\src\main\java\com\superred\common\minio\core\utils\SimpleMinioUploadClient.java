package com.superred.common.minio.core.utils;

import cn.hutool.core.io.IoUtil;
import com.superred.common.minio.config.MinioProperties;
import com.superred.common.minio.core.constants.Constant;
import io.minio.BucketExistsArgs;
import io.minio.GetObjectArgs;
import io.minio.GetObjectResponse;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import io.minio.SetBucketPolicyArgs;
import io.minio.StatObjectArgs;
import io.minio.StatObjectResponse;
import io.minio.errors.ErrorResponseException;
import io.minio.errors.InsufficientDataException;
import io.minio.errors.InternalException;
import io.minio.errors.InvalidResponseException;
import io.minio.errors.ServerException;
import io.minio.errors.XmlParserException;
import io.minio.http.Method;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;


/**
 * minio 工具
 */
@Component
@Slf4j
public class SimpleMinioUploadClient {

    private final MinioClient minioClient;

    private final String bucketName;

    private static final AtomicBoolean IS_BUCKET_CREATED = new AtomicBoolean(false);

    public SimpleMinioUploadClient(MinioProperties properties, MinioClient minioClient) {
        this.bucketName = properties.getBucket();
        this.minioClient = minioClient;
    }


    public synchronized void makeBucketIfNotExists() {
        if (IS_BUCKET_CREATED.get()) {
            return;
        }
        try {
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build());
            if (!exists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                // 设置存储桶策略，允许公共读取

                //创建公共访问的桶
                // 设置存储桶策略，允许公共读取
                minioClient.setBucketPolicy(
                        SetBucketPolicyArgs.builder()
                                .bucket(bucketName)
                                .config(String.format(Constant.policyBunk, bucketName, bucketName))
                                .build());
            }
        }catch (Exception e){
            throw new RuntimeException("创建存储桶失败: " + bucketName, e);
        }

        IS_BUCKET_CREATED.set(true);
    }


    public StatObjectResponse statObject(String objectName) {
        makeBucketIfNotExists();
        try {
            return minioClient.statObject(StatObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());
        } catch (Exception e) {
            log.error("获取文件状态失败：{}", e.getMessage(), e);
            return null;
        }
    }


    public void uploadFileSync(String objectName, File file) {
        makeBucketIfNotExists();

        try (InputStream in = Files.newInputStream(file.toPath())) {

            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(in, file.length(), -1)
                            .contentType("application/octet-stream")
                            .build()
            );
        } catch (Exception e) {
            log.error("文件上传minio失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 上传文件
     *
     * @param objectName
     * @param inputStream
     */
    public void uploadFileSync(String objectName, InputStream inputStream) {
        makeBucketIfNotExists();

        try {

            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(inputStream, inputStream.available(), -1)
                            .contentType("application/octet-stream")
                            .build()
            );
        } catch (Exception e) {
            log.error("文件上传minio失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 上传文件
     *
     * @param path 路径
     * @param fileName 文件名
     * @param inputStream 输入流
     */
    public void uploadFileSync(String path, String fileName, InputStream inputStream) {
        this.uploadFileSync(path + "/" + fileName, inputStream);
    }


    @SneakyThrows
    public InputStream download(String fileMd5) {
        StatObjectResponse statObject = statObject(fileMd5);

        if (statObject == null || statObject.size() <= 0) {
            throw new Exception(fileMd5 + "文件不存在");
        }
        return minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(fileMd5)
                        .build());
    }

    public byte[] download(String path, String fileName) {
        String objectName = path + "/" + fileName;
        StatObjectResponse statObject = statObject(objectName);

        if (statObject == null || statObject.size() <= 0) {
            throw new RuntimeException(objectName + "文件不存在");
        }
        try {
            GetObjectResponse object = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build());
            return IoUtil.readBytes(object);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 删除
     *
     * @param fileName
     * @return
     * @throws Exception
     */
    public boolean remove(String fileName) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(fileName).build());
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @SneakyThrows
    public String getURL(String fileName) {
        GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                .bucket(bucketName)
                .object(fileName)  // 对象名称
                .expiry(2, TimeUnit.SECONDS) // 该url签名10秒过期
                .method(Method.GET)  // 该url允许的请求方式
                .build();
        // 创建预签名url
        String preSignedObjectUrl = minioClient.getPresignedObjectUrl(args);
        // 设置存储桶策略，允许公共读取
        makeBucketPolicy(bucketName);
        return preSignedObjectUrl;
    }


    public String getUploadSignedURL(String fileName) {
        GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder()
                .bucket(bucketName)
                .object(fileName)
                .expiry(10, TimeUnit.MINUTES)
                .method(Method.PUT)
                .build();
        // 创建预签名url
        String preSignedObjectUrl;
        try {
            preSignedObjectUrl = minioClient.getPresignedObjectUrl(args);
        } catch (Exception e) {
            log.error("获取上传文件url失败{}", e.getMessage());
            throw new RuntimeException(e);
        }
        return preSignedObjectUrl;
    }


    @SneakyThrows
    public void makeBucketPolicy(String bucketName) {
        //创建公共访问的桶
        // 设置存储桶策略，允许公共读取
        minioClient.setBucketPolicy(
                SetBucketPolicyArgs.builder()
                        .bucket(bucketName)
                        .config(String.format(Constant.policyBunk, bucketName, bucketName))
                        .build());
    }

    @SneakyThrows
    public GetObjectResponse getObject(StatObjectResponse statObjectResponse, long startByte, long contentLength) {
        return minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(statObjectResponse.bucket())
                        .object(statObjectResponse.object())
                        .offset(startByte)
                        .length(contentLength)
                        .build());
    }

}
