<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.superred.supervisor.manager.mapper.operation.TerminalCommandExtMapper">
    <select id="groupCountByCmdIds"
            resultType="com.superred.supervisor.manager.model.vo.command.TerminalCmdRecordPageResp">
        SELECT
            ref_id AS cmdId,
            COUNT(*) AS deviceCount,
            COUNT(CASE WHEN exec_status = 2 THEN 1 END) AS successCount,
            COUNT(CASE WHEN exec_status = 3 THEN 1 END) AS failCount,
            COUNT(CASE WHEN exec_status = 1 THEN 1 END) AS issueCount,
            COUNT(CASE WHEN exec_status = 0 THEN 1 END) AS unIssueCount
        FROM
            op_terminal_operation_exec
        WHERE
            ref_id IN
        <foreach collection="cmdIds" item="cmdId" open="(" separator="," close=")">
            #{cmdId}
        </foreach>
        GROUP BY
            ref_id

    </select>
    <select id="terminalExecPage"
            resultType="com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageResp">
        SELECT
            ope.device_id deviceId,
            dt.host_name hostName,
            dt.ip terminalIp,
            po.`name` orgName,
            ope.exec_result result,
            ope.exec_status `status`
        FROM
            op_terminal_operation_exec ope
                LEFT JOIN d_terminal_agent_info dt ON ope.device_id = dt.device_id
                LEFT JOIN p_sys_org po on dt.org_id = po.id
        WHERE
          ope.ref_id = #{req.cmdId}
        <if test="req.result != null">
            AND ope.exec_result = #{req.result}
        </if>
        <if test="req.status != null">
            AND ope.exec_status = #{req.status}
        </if>
        GROUP BY
            ope.result_time,ope.fetch_time


    </select>
</mapper>