package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 *  服务运行状态
 * @since 2025年03月14日
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceStatusResp {


    @Schema(description = "操作系统")
    private String os;

    @Schema(description = "开机时间,运行时长，单位秒")
    private Integer uptime;

    @Schema(description = "主机ip地址")
    private String ip;

    @Schema(description = "表示CPU使用率，取0-100的数值，多个CPU以列表方式上传。	physical_id： CPU ID，数值类型;	cpu_usage：CPU使用率百分比，数值类型，取0-100的数值	")
    private String cpu;

    @Schema(description = "表示内存利用率，取0-100数值")
    private Integer mem;

    @Schema(description = "表示数据磁盘整体已用空间，单位GB")
    private Integer disk;

    @Schema(description = "表示数据磁盘整体可用空间，单位GB")
    private Integer diskTotal;

    @Schema(description = "当检测器由多台服务器组成时，表示服务器的编号")
    private Integer did;

    @Schema(description = "系统运行状态采集")
    private Date time;

    @Schema(description = "磁盘使用率")
    private String diskRate;

    @Schema(description = "cpu使用率")
    private String cpuRate;

    @Schema(description = "系统当前时间")
    private Date now;
}
