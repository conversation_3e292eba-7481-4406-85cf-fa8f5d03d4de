package com.superred.supervisor.manager.model.vo.system.staff;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @create 2023-08-24 16:41
 */
@EqualsAndHashCode(callSuper = true)
@Data

public class StaffPageReq extends PageReqDTO {


    @Schema(description = "部门ID")
    private Integer orgId;

    @Schema(description = "人员姓名")
    private String staffName;

}
