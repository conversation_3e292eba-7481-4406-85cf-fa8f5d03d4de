package com.superred.supervisor.manager.controller.terminal;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalBusinessStatusResp;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalCountByStatusResp;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalDeviceAuthReq;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalRuntimeStatusResp;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalStatusDetailResp;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalStatusPageReq;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalStatusPageResp;
import com.superred.supervisor.manager.service.terminal.TerminalDeviceInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * 终端审核管理
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Tag(name = "6.2. 终端组件状态管理(首页)")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agent")
public class TerminalManageController {



    @Resource
    private TerminalDeviceInfoService terminalDeviceInfoService;


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1. 分页查询")
    @PostMapping("/manage/page")
    public RPage<TerminalStatusPageResp> getDeviceStatusPage(@Valid @RequestBody TerminalStatusPageReq req) {
        IPage<TerminalStatusPageResp> respIPage = terminalDeviceInfoService.getDeviceStatusPage(req);
        return new RPage<>(respIPage);

    }


    @Operation(summary = "2. 终端注册状态")
    @ApiOperationSupport(order = 2)
    @GetMapping("/manage/{deviceId}")
    public R<TerminalStatusDetailResp> getStatusDetail(@PathVariable("deviceId") String deviceId) {
        TerminalStatusDetailResp resp = terminalDeviceInfoService.getStatusDetail(deviceId);
        return R.success(resp);
    }

    @Operation(summary = "3. 终端业务状态")
    @ApiOperationSupport(order = 3)
    @GetMapping("/business/{deviceId}")
    public R<TerminalBusinessStatusResp> getBusinessDetail(@PathVariable("deviceId") String deviceId) {
        TerminalBusinessStatusResp resp = terminalDeviceInfoService.getBusinessDetail(deviceId);
        return R.success(resp);
    }

    @Operation(summary = "4. 终端运行状态")
    @ApiOperationSupport(order = 4)
    @GetMapping("/runtime/{deviceId}")
    public R<TerminalRuntimeStatusResp> getRuntimeDetail(@PathVariable("deviceId") String deviceId) {
        TerminalRuntimeStatusResp resp = terminalDeviceInfoService.getRuntimeDetail(deviceId);
        return R.success(resp);
    }

    @Operation(summary = "5. 通过id删除接入设备")
    @ApiOperationSupport(order = 5)
    @SysLogAnn(module = LogTypeConstants.ACCESS_DEVICE_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除接入设备")
    @PostMapping("/delete/{deviceId}")
    public R<Boolean> removeById(@PathVariable String deviceId) {
        terminalDeviceInfoService.removeById(deviceId);


        return R.success(true);
    }

    @Operation(summary = "6. 终端授权/激活")
    @PostMapping("/active")
    @SysLogAnn(module = LogTypeConstants.ACCESS_DEVICE_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "终端授权/激活")
    public R<Boolean> active(@Valid @RequestBody TerminalDeviceAuthReq req) {

        terminalDeviceInfoService.active(req);
        return R.success(true);

    }

    @Operation(summary = "7. 终端授权/激活撤销")
    @PostMapping("/active/revoke")
    @SysLogAnn(module = LogTypeConstants.ACCESS_DEVICE_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "终端授权/激活撤销")
    public R<Boolean> activeRevoke(@Valid @RequestBody TerminalDeviceAuthReq agentDeviceAuthDTO) {

        terminalDeviceInfoService.activeRevoke(agentDeviceAuthDTO);
        return R.success(true);

    }

    @Operation(summary = "8. 终端状态统计")
    @GetMapping("/count/status")
    public R<TerminalCountByStatusResp> countByStatus() {

        TerminalCountByStatusResp resp = terminalDeviceInfoService.countByStatus();
        return R.success(resp);

    }
}
