package com.superred.supervisor.manager.model.vo.devices.bak;


import com.superred.supervisor.manager.common.annotation.BlankOrPattern;
import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DeviceContact
 * <p> 联系人信息
 * @since 2022-08-12 10:09:58
 **/
@Data
public class DevicebakContactAddReq {


    /**
     * 联系人姓名，“张三”
     */
    @NotBlank(message = "联系人姓名 不能为空")
    @ByteSize(max = 64, message = "联系人姓名 不能超过64个字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]*$", message = "联系人姓名 只能包含中文、英文、数字")
    @Schema(description = "联系人姓名，“张三”")
    private String name;

    /**
     * 联系人邮件地址，“<EMAIL>”
     */
    @Schema(description = "联系人邮件地址，“<EMAIL>”")
    @ByteSize(max = 64, message = "联系人邮箱 不能超过64个字节")
    @BlankOrPattern(regexp = "^([a-z0-9A-Z]+[-|_|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$", message = "联系人邮箱 格式错误")
    private String email;

    /**
     * 联系人电话，“13811223344”
     */
    @NotBlank(message = "电话不能为空")
    @Schema(description = "联系人电话，“13811223344”")
    @ByteSize(max = 32, message = "联系人电话 不能超过32个字节")
    @BlankOrPattern(regexp = "^$|^(13[0-9]|14[5-9]|15[0-3,5-9]|16[2,5,6,7]|17[0-8]|18[0-9]|19[1,3,5-9])\\d{8}$", message = "联系人电话 格式错误")
    private String phone;

    /**
     * 联系人职务，“处长”
     */
    @Schema(description = "联系人职务，“处长”")
    @ByteSize(max = 64, message = "联系人职务 不能超过64个字节")
    private String position;


}
