package com.superred.supervisor.manager.model.vo.policy;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.manager.common.annotation.BlankOrPattern;
import com.superred.supervisor.manager.common.annotation.ByteSize;
import com.superred.supervisor.manager.model.dto.policy.config.FileFilterSizeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-11 17:02
 */
@Data

public class RuleFilterKeywordReq {

    private static final long serialVersionUID = 1L;

    @Schema(description = "策略ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ruleId;

    @Schema(description = "策略类型，0 关键词，1正则表达式")
    private Integer ruleType;

    @Schema(description = "最少命中次数，默认为1")
    @Min(value = 1, message = "命中次数 最小值为1")
    @Max(value = 9, message = "命中次数 最大值为9")
    private Integer minMatchCount;

    @Schema(description = "策略内容")
    private String ruleContent;

    @Schema(description = "告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级")
    private Integer risk;

    @Schema(description = "策略应用状态，0未应用，1已应用")
    private Integer status;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "监测器策略数量")
    @TableField(exist = false)
    private Integer detectorPolicyCount;

    @Schema(description = "终端策略数量")
    @TableField(exist = false)
    private Integer agentPolicyCount;

    @Schema(description = "包含策略")
    private String ruleContain;

    @Schema(description = "策略描述")
    @ByteSize(max = 128, message = "策略描述 长度不可超过128字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9!\"#$%&'()*+,\\-./:;<=>?@\\[\\\\\\]^_`{|}~]+$", message = "策略描述 格式错误")
    private String ruleDesc;

    @Schema(description = "位置策略")
    private String rulePosition;

    @Schema(description = "排除策略")
    private String ruleExclude;

    @Schema(description = "文件过滤类型，1文档 2图片 3文本/网页 4压缩包 5邮件    1,2")
    private String fileFilterType;

    @TableField(fill = FieldFill.UPDATE)
    @Schema(description = "文件最小值")
    @Min(value = 0, message = "文件最小值 最小值为0")
    @Max(value = 4194304, message = "文件最小值 最大值为4194304")
    private Integer fileFilterMinSize;

    @TableField(fill = FieldFill.UPDATE)
    @Schema(description = "文件最大值")
    @Min(value = 0, message = "文件最大值 最小值为0")
    @Max(value = 4194304, message = "文件最大值 最大值为4194304")
    private Integer fileFilterMaxSize;

    private FileFilterSizeDTO fileFilterSize;

    private List<Integer> fileFilterTypes;


}
