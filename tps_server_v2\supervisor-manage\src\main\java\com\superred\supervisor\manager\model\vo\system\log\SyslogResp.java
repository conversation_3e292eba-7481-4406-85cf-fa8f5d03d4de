package com.superred.supervisor.manager.model.vo.system.log;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.common.entity.system.SysRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 *  操作日志返回实体
 * @since 2025年06月27日
 */

@Data
@Schema(description = "操作日志返回实体")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyslogResp {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 日志 uuid
     */
    @Schema(description = "日志 uuid")
    private String uuid;

    /**
     * 管理员角色
     */
    @Schema(description = "管理员角色")
    private String role;

    /**
     * 产品类别
     */
    @Schema(description = "产品类别")
    private String product;

    /**
     * 操作模块
     */
    @Schema(description = "操作模块")
    private String operateModule;

    /**
     * 操作动作
     */
    @Schema(description = "操作动作")
    private String operateType;


    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 行为类型：1.违规行为 ； 2.异常行为;  3 一般行为
     */
    @Schema(description = "行为类型：1.违规行为 ； 2.异常行为;  3 一般行为")
    private String behaviourType;

    /**
     * 日志风险级别：1紧急、2重要、3一般、4信息
     */
    @Schema(description = "日志风险级别：1紧急、2重要、3一般、4信息")
    private String level;

    /**
     * 操作ip
     */
    @Schema(description = "操作ip")
    private String hostIp;

    /**
     * 操作设备id
     */
    @Schema(description = "操作设备id")
    private String hostId;

    /**
     * 日志模板表type
     */
    @Schema(description = "日志模板表type")
    private String moduleType;

    /**
     * 操作人员名字
     */
    @Schema(description = "操作人员名字")
    private Integer userId;

    /**
     * 操作人员名字
     */
    @Schema(description = "操作人员名字")
    private String username;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-dd-MM HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-dd-MM HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;


    /**
     * 角色列表
     */
    @Schema(description = "角色列表")
    private List<SysRole> roleList;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    private String operateDateStr;

    /**
     * 结果
     */
    @Schema(description = "结果")
    private Integer result;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remarks;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;
}
