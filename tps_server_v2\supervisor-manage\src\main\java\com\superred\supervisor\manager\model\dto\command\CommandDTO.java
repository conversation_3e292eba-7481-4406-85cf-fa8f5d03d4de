package com.superred.supervisor.manager.model.dto.command;


import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 指令下发入参
 *
 * <AUTHOR>
 * @since 2023/10/10
 **/
@Data
public class CommandDTO {

    /**
     * 下发类型
     * policy 策略
     * command 指令
     * extend_xx_command  扩展指令
     * extend_xx_policy   扩展策略
     * xx为厂商id
     */
    private String type;

    /**
     * PolicyModuleType 的 key 值
     */
    private String policyModule;

    /**
     * 下发的数据 不分本级下级
     */
    private OriginalEffectZone originalEffectZone;

    /**
     * 只有本级下发设备
     */
    private List<LocalSelectDevice> localSelectDevices;

    /**
     * 策略
     */
    private DistributePolicy policy;

    /**
     * 指令
     */
    private DistributeCommand command;

    /**
     * 下发指令有可能有文件上传
     */
    private Map<String, MultipartFile> multipartFileMap;

    public String getType() {
        return StringUtils.isBlank(type) ? "command" : type;
    }

    public String resolveAuditMessage() {
        // DistributeCommand command = this.getCommand();
        // String cmd = command.getCmd().trim();
        // CommandType cmdType = CommandType.getCmdTypeByValue(cmd);
        // String message = "";
        // List<String> deviceIds = null;
        // List<PartDevice> partDevices = new ArrayList<>();
        // OriginalEffectZone originalEffectZone = this.getOriginalEffectZone();
        // boolean allDevice = false;
        // boolean allDetector = false;
        // boolean allManager = false;
        // boolean allSelfRegulation = false;
        // if (null != originalEffectZone) {
        //     List<EffectZone> effectZoneList = originalEffectZone.getEffectZones();
        //     for (EffectZone effectZone : effectZoneList) {
        //         // 部分设备自监管
        //         List<PartDevice> selfRegulations = effectZone.getSelfRegulations();
        //         // 部分设备监测器
        //         List<PartDevice> detectors = effectZone.getDetectors();
        //         // 部分设备管理系统
        //         List<PartDevice> managers = effectZone.getManagers();
        //         if (!org.springframework.util.CollectionUtils.isEmpty(selfRegulations)) {
        //             partDevices.addAll(selfRegulations);
        //         }
        //         if (!org.springframework.util.CollectionUtils.isEmpty(detectors)) {
        //             partDevices.addAll(detectors);
        //         }
        //         if (!org.springframework.util.CollectionUtils.isEmpty(managers)) {
        //             partDevices.addAll(managers);
        //         }
        //         // 如果选中的是全部设备
        //         if (Objects.equals(effectZone.getSelectType(), EffectZoneType.ALL_DEVICE.getId())) {
        //             allDevice = true;
        //         }
        //         // 如果选中的是全部监测器
        //         if (Objects.equals(effectZone.getSelectType(), EffectZoneType.ALL_DETECTOR.getId())) {
        //             allDetector = true;
        //         }
        //         // 如果选中的是全部管理系统
        //         if (Objects.equals(effectZone.getSelectType(), EffectZoneType.ALL_MANAGER.getId())) {
        //             allManager = true;
        //         }
        //         // 如果选中的是全部自监管
        //         if (Objects.equals(effectZone.getSelectType(), EffectZoneType.ALL_SELF_REGULATION.getId())) {
        //             allSelfRegulation = true;
        //         }
        //     }
        //     if (!org.springframework.util.CollectionUtils.isEmpty(partDevices)) {
        //         deviceIds = getDeviceIds(partDevices);
        //     }
        // }
        // List<LocalSelectDevice> localSelectDeviceList = this.getLocalSelectDevices();
        // if (CollectionUtils.isNotEmpty(localSelectDeviceList)) {
        //     deviceIds = localSelectDeviceList.stream().map(LocalSelectDevice::getDeviceId).collect(Collectors.toList());
        // }
        // if (CollectionUtils.isEmpty(deviceIds)) {
        //     deviceIds = Collections.emptyList();
        // }
        // int deviceCount = deviceIds.size();
        // String deviceId;
        // String type = "";
        // if (CollectionUtils.isNotEmpty(deviceIds)) {
        //     deviceId = deviceIds.get(0);
        //     type = Objects.requireNonNull(DeviceType.getDeviceType(deviceId.substring(6, 8))).getLabel();
        // }
        // // 如果选中的是全部设备
        // if (allDevice) {
        //     message = String.format("%s指令下发成功, 给全部设备", cmdType.getName());
        // }
        // // 如果选中的是全部监测器
        // if (allDetector) {
        //     message = String.format("%s指令下发成功, 给全部监测器", cmdType.getName());
        // }
        // // 如果选中的是全部管理系统
        // if (allManager) {
        //     message = String.format("%s指令下发成功, 给全部管理系统", cmdType.getName());
        // }
        // // 如果选中的是全部自监管
        // if (allSelfRegulation) {
        //     message = String.format("%s指令下发成功, 给全部自监管", cmdType.getName());
        // }
        // if (deviceCount > 5) {
        //     message = String.format("%s指令下发成功, 给%s台%s设备", cmdType.getName(), deviceCount, type);
        // } else {
        //     if (CollectionUtils.isNotEmpty(deviceIds)) {
        //         String deviceMessage = deviceIds.stream()
        //                 .filter(StringUtils::isNotBlank)
        //                 .map(String::valueOf)
        //                 .collect(Collectors.joining("、设备"));
        //         message = String.format("%s指令下发成功, 给%s设备%s", cmdType.getName(), type, deviceMessage);
        //     }
        // }
        // return message;
        return "";
    }

    // private List<String> getDeviceIds(List<PartDevice> list) {
    //     if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
    //         return list.stream().map(PartDevice::getDeviceId).collect(Collectors.toList());
    //     }
    //     return Collections.emptyList();
    // }
}
