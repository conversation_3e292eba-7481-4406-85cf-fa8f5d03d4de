package com.superred.supervisor.manager.controller.settings;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.annotation.role.IgnoreAuth;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.dto.agent.AgentSoftDTO;
import com.superred.supervisor.manager.model.vo.terminal.AgentSoftwareVO;
import com.superred.supervisor.manager.model.vo.settings.ClientInstallCheckCodeReq;
import com.superred.supervisor.manager.model.vo.settings.ClientInstallCheckCodeResp;
import com.superred.supervisor.manager.model.vo.settings.ClientInstallPackageStatusReq;
import com.superred.supervisor.manager.model.vo.settings.ClientInstallPackageTimeReq;
import com.superred.supervisor.manager.model.vo.settings.ClientInstallPackageTimeResp;
import com.superred.supervisor.manager.model.vo.settings.ClientInstallPackageUpdateReq;
import com.superred.supervisor.manager.model.vo.settings.ClientInstallPackageUploadReq;
import com.superred.supervisor.manager.model.vo.settings.ClientInstallValidCodeReq;
import com.superred.supervisor.manager.service.terminal.AgentSoftwareService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 *  终端安装包管理
 * @since 2025年03月14日
 */

@Tag(name = "5.4系统配置-终端保密组件下载管理")
@RestController
@RequestMapping("/install_package")
@Slf4j
public class ClientInstallPackageController {


    @Resource
    private AgentSoftwareService agentSoftwareService;


    @PostMapping(value = "/upload")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "1.安装包文件上传")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_CONFIDENTIALITY_COMPONENT_DOWNLOAD_MANAGEMENT, operateType = OperateTypeConstants.UPLOAD, desc = "安装包文件上传")
    public R<String> upload(@ModelAttribute ClientInstallPackageUploadReq req) {
        agentSoftwareService.upload(req);
        return R.success();

    }


    @PostMapping("/page")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "2.获取安装包列表(分页)")
    @IgnoreAuth
    public RPage<AgentSoftwareVO> getInfoByPage(@Validated @RequestBody AgentSoftDTO agentSoftDTO) {
        IPage<AgentSoftwareVO> page = agentSoftwareService.page(agentSoftDTO);
        return new RPage<>(page);
    }

    @PostMapping(value = "/changePublishStatus")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "3.修改安装包发布状态")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_CONFIDENTIALITY_COMPONENT_DOWNLOAD_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "修改安装包发布状态")
    public R<Boolean> changePublishStatus(@Validated @RequestBody ClientInstallPackageStatusReq request) {
        Boolean b = this.agentSoftwareService.changePublishStatus(request);
        return R.success(b);

    }


    @PostMapping(value = "/update")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "4.修改安装包信息")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_CONFIDENTIALITY_COMPONENT_DOWNLOAD_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "修改安装包信息")
    public R<Boolean> update(@Validated @RequestBody ClientInstallPackageUpdateReq request) {
        Boolean update = this.agentSoftwareService.update(request);
        return R.success(update);
    }


    @GetMapping(value = "/delete")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "4.删除安装包信息")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_CONFIDENTIALITY_COMPONENT_DOWNLOAD_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除安装包信息")
    public R<Boolean> delete(@RequestParam(required = true, value = "id") Integer id) {
        Boolean delete = this.agentSoftwareService.delete(id);
        return R.success(delete);
    }

    @GetMapping(value = "/download")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "5.下载安装包")
    @IgnoreAuth
    @SysLogAnn(module = LogTypeConstants.TERMINAL_CONFIDENTIALITY_COMPONENT_DOWNLOAD_MANAGEMENT, operateType = OperateTypeConstants.DOWNLOAD, desc = "下载安装包")
    public void downLoad(@RequestParam(required = false, value = "id") Integer id, HttpServletResponse response) {
        this.agentSoftwareService.downLoad(id, response);
    }

    @GetMapping(value = "/getInstallTime")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "6.获取下载开启时间设置")
    @IgnoreAuth
    public R<ClientInstallPackageTimeResp> getInstallTime() {
        ClientInstallPackageTimeResp installTime = this.agentSoftwareService.getInstallTime();
        return R.success(installTime);
    }

    @PostMapping(value = "/updateInstallTime")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "7.编辑下载开启时间")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_CONFIDENTIALITY_COMPONENT_DOWNLOAD_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "修改下载开启时间")
    public R<Boolean> updateInstallTime(@Validated @RequestBody ClientInstallPackageTimeReq req) {
        Boolean b = this.agentSoftwareService.updateInstallTime(req);

        return R.success(b);
    }

    @GetMapping(value = "/getCheckCode")
    @ApiOperationSupport(order = 8)
    @Operation(summary = "8.获取下载校验码")
    @IgnoreAuth
    public R<ClientInstallCheckCodeResp> getCheckCode() {
        ClientInstallCheckCodeResp checkCode = this.agentSoftwareService.getCheckCode();
        return R.success(checkCode);
    }


    @GetMapping(value = "/makeCheckCode")
    @ApiOperationSupport(order = 9)
    @Operation(summary = "9.生成新下载校验码")
    public R<String> makeCheckCode() {
        String s = this.agentSoftwareService.makeCheckCode();
        return R.success(s);
    }

    @PostMapping(value = "/install_check_sum/update")
    @ApiOperationSupport(order = 10)
    @Operation(summary = "10.编辑校验码设置")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_CONFIDENTIALITY_COMPONENT_DOWNLOAD_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "修改校验码设置")
    public R<Boolean> updateCheckCode(@Validated @RequestBody ClientInstallCheckCodeReq req) {
        Boolean b = this.agentSoftwareService.updateCheckCode(req);
        return R.success(b);
    }

    @PostMapping(value = "/install_check_sum/valid")
    @ApiOperationSupport(order = 11)
    @Operation(summary = "11.校验下载码")
    public R<Boolean> validCheckCode(@Validated @RequestBody ClientInstallValidCodeReq req) {
        Boolean b = this.agentSoftwareService.validCheckCode(req);
        return R.success(b);
    }
}
