package com.superred.supervisor.manager.model.vo.system;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统安全配置
 *
 * <AUTHOR>
 * @since 2025/3/11 14:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

public class SecurityConfigResp {


    /*
    {"sysPassLength":"20","sysPassShortLength":"10","sysUKeyLogin":"0","sysLoginMaxLockTime":"30","sysLoginMaxTryCount":"5","passCom":"1,2,3","sysOvertime":"30","sysPassChange":"90"}
     */
    @Schema(description = "密码长度")
    private String sysPassLength;

    @Schema(description = "密码最短长度")
    private String sysPassShortLength;

    @Schema(description = "UKey登录")
    private String sysUKeyLogin;

    @Schema(description = "登录最大锁定时间")
    private String sysLoginMaxLockTime;

    @Schema(description = "登录最大尝试次数")
    private String sysLoginMaxTryCount;


    @Schema(description = "密码复杂度")
    private String passCom;

    @Schema(description = "超时时间")
    private String sysOvertime;

    @Schema(description = "密码修改周期")
    private String sysPassChange;

}


