package com.superred.supervisor.gateway.model.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求终端信息
 *
 * <AUTHOR>
 * @since 2025/5/19 17:10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginAgent {

    /**
     * header User-Agent
     */
    private String userAgent;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 版本
     */
    private String softVersion;

    /**
     * 登录的唯一标识
     */
    private String sessionId;
}
