package com.superred.supervisor.gateway.config;


import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * swagger配置
 * 分组 admin  api
 *
 * <AUTHOR>
 * @since 2023/12/12 17:53
 */
@Configuration
public class Knife4jConfiguration {

    @Bean
    public OpenAPI customOpenAPI() {
        // USER-AGENT 不能通过swagger 传递，请使用google插件 modheader
        return new OpenAPI()
                .info(new Info()
                        .title("互联网BM自监管系统API")
                        .version("2025-05版") //对应标准版本
                        .description("客户端接口")
                );
    }

    @Bean
    public GroupedOpenApi clientApiByPackage() {
        return GroupedOpenApi.builder()
                .group("终端组件接口")
                .packagesToScan("com.superred.supervisor.gateway.controller.terminal")
                .build();
    }

    @Bean
    public GroupedOpenApi adminApiByPackage() {
        return GroupedOpenApi.builder()
                .group("应用ZJG组件接口")
                .packagesToScan("com.superred.supervisor.gateway.controller.app")
                .build();
    }
}
