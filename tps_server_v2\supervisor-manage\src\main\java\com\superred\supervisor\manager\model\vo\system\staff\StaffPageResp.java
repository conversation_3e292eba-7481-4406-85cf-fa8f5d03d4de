package com.superred.supervisor.manager.model.vo.system.staff;

import cn.hutool.core.util.DesensitizedUtil;
import com.superred.supervisor.common.entity.system.SysStaff;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-08-24 16:30
 */
@Data

public class StaffPageResp {


    @Schema(description = "主键")
    private Integer id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "人员姓名")
    private String staffName;

    @Schema(description = "身份证号")
//    @Sensitive(type = SensitiveTypeEnum.CUSTOM,beginNoMask = 2,endNoMask = 4,maskStr = "*",encrypt = true)
    private String idCardNum;


    @Schema(description = "所属单位")
    private String unitName;

    @Schema(description = "所属部门id")
    private Integer orgId;

    @Schema(description = "所属部门")
    private String orgName;

    @Schema(description = "联系方式")
//    @Sensitive(type = SensitiveTypeEnum.CUSTOM,beginNoMask = 2,endNoMask = 2,maskStr = "*",encrypt = true)
    private String contactInfo;

    @Schema(description = "邮箱")
//    @Sensitive(type = SensitiveTypeEnum.CUSTOM,beginNoMask = 2,endNoMask = 4,maskStr = "*",encrypt = true)
    private String email;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime modifiedTime;


    public static StaffPageResp fromSysStaff(SysStaff sysStaff) {
        StaffPageResp staffPageResp = new StaffPageResp();
        staffPageResp.setId(sysStaff.getId());
        staffPageResp.setUsername(sysStaff.getUsername());
        staffPageResp.setStaffName(sysStaff.getStaffName());
        staffPageResp.setIdCardNum(DesensitizedUtil.idCardNum(sysStaff.getIdCardNum(), 2, 4));

        staffPageResp.setOrgId(sysStaff.getOrgId());
        staffPageResp.setUnitName("北京万里红科技有限公司");
        staffPageResp.setContactInfo(sysStaff.getContactInfo());
        staffPageResp.setEmail(sysStaff.getEmail());
        staffPageResp.setCreateTime(sysStaff.getCreateTime());
        staffPageResp.setModifiedTime(sysStaff.getModifiedTime());
        return staffPageResp;
    }
}
