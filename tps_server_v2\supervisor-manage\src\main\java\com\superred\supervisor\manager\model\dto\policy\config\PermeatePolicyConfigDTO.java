package com.superred.supervisor.manager.model.dto.policy.config;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleAttackPermeate;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-03-27 20:32
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PermeatePolicyConfigDTO {

    /**
     * 策略ID
     */
    private Long ruleId;

    /**
     * 策略名称
     */
    private String ruleName;

    /**
     * 命中策略后是否进行流的
     * 报文留存 1（on）、2（off）。
     */
    private Integer storePcap;

    /**
     * 策略内容
     */
    private String rule;


    /**
     * 攻击分类 1. 系统漏洞利用 2. 软件漏洞利用 3. 网络钓鱼 4. 网络扫描 5. 密码猜解 6. SQL注入 7. 溢出攻击 8. 代码执行 9. 非授权访问/权限绕过 10. 跨站脚本攻击（XSS） 11. 跨站请求伪造（CSRF） 12. 目录遍历攻击 13. 文件利用 14. Webshell利用 15. Webshell上传 99. 其它
     */
    private Integer attackClass;

    /**
     * 攻击组织
     */
    private String attackGroup;

    /**
     * 渗透行为描述。
     * 用于对策略的
     * 详细描述、威胁
     * 危害、处置建议
     * 等
     */
    private String desc;

    /**
     * 渗透行为利用漏洞编号
     */
    private String cve;

    /**
     * 漏洞名称
     */
    private String vulnerability;

    /**
     * 告警级别，0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）。
     */
    private Integer risk;

    public static PermeatePolicyConfigDTO getPolicyConfig(RuleAttackPermeate permeate) {
        if (permeate == null) {
            return null;
        }
        return PermeatePolicyConfigDTO.builder()
                .ruleId(Long.parseLong(permeate.getRuleId()))
                .ruleName(PolicyUtils.handleStrNull(permeate.getRuleName()))
                .storePcap(PolicyUtils.strToInt(permeate.getStorePcap()))
                .rule(PolicyUtils.handleStrNull(permeate.getRule()))
                .attackClass(PolicyUtils.strToInt(permeate.getAttackClass()))
                .attackGroup(PolicyUtils.handleStrNull(permeate.getAttackGroup()))
                .desc(PolicyUtils.handleStrNull(permeate.getDesc()))
                .cve(PolicyUtils.handleStrNull(permeate.getCve()))
                .vulnerability(PolicyUtils.handleStrNull(permeate.getVulnerability()))
                .risk(PolicyUtils.strToInt(permeate.getRisk()))
                .build();
    }
}
