package com.superred.supervisor.manager.repository.policy;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.manager.mapper.policy.AgentPolicyRuleExtMapper;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:32
 */
@Slf4j
@Repository
@AllArgsConstructor
public class AgentPolicyRuleExtRepository extends ServiceImpl<AgentPolicyRuleExtMapper, AgentPolicyRule> {
    public List<RulePolicyApplyResp> selectPolicyApply(Long ruleId) {
        return this.baseMapper.selectPolicyApply(ruleId);
    }

    public long getRuleCount(Long id) {
        return this.count(Wrappers.<AgentPolicyRule>lambdaQuery()
                .eq(AgentPolicyRule::getPolicyId, id));
    }
}
