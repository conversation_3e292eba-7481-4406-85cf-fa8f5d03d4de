package com.superred.supervisor.manager.model.vo.policy;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.manager.common.annotation.BlankOrPattern;
import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025-03-11 16:07
 */
@Data

public class RuleFilterMd5Req {

    private static final long serialVersionUID = 1L;

    @Schema(description = "策略ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ruleId;

    @Schema(description = "策略内容")
    @NotBlank(message = "策略内容 不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9]+$", message = "策略内容 格式错误")
    private String ruleContent;

    @Schema(description = "策略描述")
    @ByteSize(max = 128, message = "描述 长度不可超过128字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9!\"#$%&'()*+,\\-./:;<=>?@\\[\\\\\\]^_`{|}~]+$", message = "描述 格式错误")
    private String ruleDesc;

    @Schema(description = "告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级")
    private Integer risk;

    @Schema(description = "策略应用状态，0未应用，1已应用")
    private Integer status;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "监测器策略数量")
    @TableField(exist = false)
    private Integer detectorPolicyCount;

}
