package com.superred.supervisor.manager.model.vo.system.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月12日
 */
@Data
public class UserEnableReq implements Serializable {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotNull
    private Integer id;

    /**
     * 启用状态(1-启用，2-停用)
     */
    @Schema(description = "启用状态(1-启用，2-停用)")
    @NotNull
    private Integer enable;

}


