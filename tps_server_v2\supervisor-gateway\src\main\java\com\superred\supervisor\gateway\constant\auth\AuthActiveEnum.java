package com.superred.supervisor.gateway.constant.auth;

import lombok.Getter;

/**
 * 激活状态
 *     * activate-status:
 *      * 0：未激活
 *      * 1：已激活
 * <AUTHOR>
 * @since 2025/5/19 18:02
 */
@Getter
public enum AuthActiveEnum {

    /**
     * 未激活
     */
    UN_ACTIVATED("0", "未激活"),

    /**
     * 已激活
     */
    ACTIVATED("1", "已激活");

    private final String code;
    private final String message;

    AuthActiveEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }


    public static final String KEY = "activate-status";
}
