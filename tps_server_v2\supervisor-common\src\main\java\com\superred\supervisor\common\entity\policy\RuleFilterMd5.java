package com.superred.supervisor.common.entity.policy;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件MD5策略
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName(value = "policy_rule_md5", autoResultMap = true)
public class RuleFilterMd5 extends Model<RuleFilterMd5> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 策略ID
     */
    private Long ruleId;

    /**
     * 策略内容
     */
    //@TableField(typeHandler = EncryptAndDecryptHandler.class)
    private String ruleContent;

    /**
     * 策略描述
     */
    private String ruleDesc;

    /**
     * 告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级
     */
    private Integer risk;

    /**
     * 策略应用状态，0未应用，1已应用
     */
    private Integer status;

    /**
     * 更新时间
     */
    private Date updateTime;


}
