package com.superred.supervisor.manager.mapper.command;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.supervisor.common.entity.command.IssueCommand;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface IssueCommandExtMapper extends BaseMapper<IssueCommand> {
    IPage<DetectorCommandStatisticsResp> statisticsPage(Page<IssueCommand> objectPage, @Param("query") DetectorCommandStatisticsReq req);
}
