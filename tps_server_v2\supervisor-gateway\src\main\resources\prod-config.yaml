spring:
  redis:
    database: 0
    host: 127.0.0.1
    password: 123456
    port: 6379
    timeout: 5000ms
  datasource:
    url: *************************************************************************************************************************
    username: superred
    password: 123456
  kafka:
    bootstrap-servers: 127.0.0.1:9092

logging:
  level:
    root: info
    com.superred: debug
    com.superred.common.core.filter.RequestLoggingFilter: debug

superred:
  common:
    starter:
      enable-logging-filter: false
    sm4-db:
      key: b778099a3e7b23415c78ddd7491b35c0
      iv: 57d31d0ef5394c443425c3984fe668bf
    minio:
      endpoint: http://127.0.0.1:9000
      accessKey: admin
      secretKey: admin123
      bucket: supervisor
