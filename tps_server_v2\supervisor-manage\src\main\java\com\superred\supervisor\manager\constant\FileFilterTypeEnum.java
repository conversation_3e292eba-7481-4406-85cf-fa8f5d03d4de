package com.superred.supervisor.manager.constant;

import cn.hutool.core.util.StrUtil;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-04-22 17:43
 */
@Getter
public enum FileFilterTypeEnum {
    TYPE_1(1, "文档"),
    TYPE_2(2, "图片"),
    TYPE_3(3, "文本/网页"),
    TYPE_4(4, "压缩包"),
    TYPE_5(5, "邮件");

    private final Integer key;
    private final String value;

    FileFilterTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(String str) {
        String retrurnStr = "";
        if (StrUtil.isBlank(str)) {
            return retrurnStr;
        }
        StringBuffer sb = new StringBuffer();
        List<Integer> keyList = PolicyUtils.strToIntList(str);
        for (FileFilterTypeEnum fileFilterTypeEnum : FileFilterTypeEnum.values()) {
            for (Integer key : keyList) {
                if (fileFilterTypeEnum.getKey().equals(key)) {
                    sb.append(fileFilterTypeEnum.getValue());
                    sb.append(", ");
                }
            }
        }
        retrurnStr = sb.toString();
        if (StrUtil.isNotBlank(retrurnStr)) {
            return retrurnStr.substring(0, retrurnStr.length() - 2);
        }
        return retrurnStr;
    }
}
