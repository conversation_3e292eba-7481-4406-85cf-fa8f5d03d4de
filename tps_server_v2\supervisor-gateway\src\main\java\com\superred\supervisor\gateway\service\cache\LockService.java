package com.superred.supervisor.gateway.service.cache;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 锁服务
 *
 * <AUTHOR>
 * @since 2025/5/19 14:15
 */
@Service
public class LockService {

    private static final String CLIENT_LOCK_PREFIX = "lock-global:agent:";
    private static final String DETECTOR_LOCK_PREFIX = "lock-global:app:";

    private static final String TERMINAL_TASK_LOCK_PREFIX= "lock-global:agent-task:";



    @Resource
    private RedissonClient redissonClient;


    private RLock getGlobalLock(String lockName) {
        return redissonClient.getLock(lockName);
    }


    public RLock getClientLock() {
        return getGlobalLock(CLIENT_LOCK_PREFIX);
    }

    public RLock getDetectorLock() {
        return getGlobalLock(DETECTOR_LOCK_PREFIX);
    }

    public RLock getTerminalTaskLock() {
        return getGlobalLock(TERMINAL_TASK_LOCK_PREFIX);
    }

}
