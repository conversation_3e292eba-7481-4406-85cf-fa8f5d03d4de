package com.superred.supervisor.manager.model.vo.terminal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 终端设备信息
 * </p>
 *
 */
@Data

public class TerminalAuditDetailResp {


    @Schema(description = "设备编号")
    private String deviceId;


    @Schema(description = "前八位为年月日，下划线后自定义")
    private String softVersion;

    @Schema(description = "设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值")
    @TableField("interface")
    private String interfaces;

    @Schema(description = "内存总数，表示整个设备的内存大小，单位MB。")
    private Long memTotal;

    @Schema(description = "CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。	physical_id：CPU ID，数值类型	core：CPU核心数，数值类型；	clock：CPU主频，数值类型精确到小数点后1位	")
    private String cpuInfo;

    @Schema(description = "磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。	size为数值类型，表示磁盘大小，单位GB；	serial为字符串类型，最长64个字节，表示磁盘序列号")
    private String diskInfo;


    @Schema(description = "行政区域编码类型，表示检测器部署所在地的区域编码。")
    private String addressCode;

    @Schema(description = "行政区域：北京市/东城区")
    private String addressCodeStr;

    @Schema(description = "注册时间")
    private LocalDateTime registerTime;


    @Schema(description = "激活时间")
    private LocalDateTime verifyTime;

    @Schema(description = "备注信息")
    private String memo;

    @Schema(description = "心跳时间")
    private LocalDateTime heartbeatTime;


    @Schema(description = "ip")
    private String ip;

    @Schema(description = "mac")
    private String mac;

    @Schema(description = "终端责任人ID")
    private String userId;

    @Schema(description = "终端责任人姓名")
    private String userName;

    @Schema(description = "主机名称")
    private String hostName;

    @Schema(description = "主机操作系统")
    private String os;

    @Schema(description = "主机CPU架构")
    private String arch;


    @Schema(description = "终端状态 0 审核通过 1  审核不通过 2 待审核 3 禁用 4 注销")
    private Integer status;

    @Schema(description = "终端状态 0 在线，1 审核失败，2 待审核，3 禁用 4 离线 5 注销 6已卸载")
    private String statusStr;


    @Schema(description = "单位名称")
    private String company;

    @Schema(description = "所属部门名称")
    private String orgName;

    public static TerminalAuditDetailResp fromAgentDeviceInfo(TerminalAgentInfo agentDeviceInfo) {

        TerminalAuditDetailResp terminalAuditDetailResp = new TerminalAuditDetailResp();
        terminalAuditDetailResp.setDeviceId(agentDeviceInfo.getDeviceId());
        terminalAuditDetailResp.setSoftVersion(agentDeviceInfo.getSoftVersion());
        terminalAuditDetailResp.setInterfaces(agentDeviceInfo.getInterfaces());
        terminalAuditDetailResp.setMemTotal(agentDeviceInfo.getMemTotal());
        terminalAuditDetailResp.setCpuInfo(agentDeviceInfo.getCpuInfo());
        terminalAuditDetailResp.setDiskInfo(agentDeviceInfo.getDiskInfo());

        terminalAuditDetailResp.setRegisterTime(agentDeviceInfo.getRegisterTime());
        terminalAuditDetailResp.setVerifyTime(agentDeviceInfo.getVerifyTime());
        terminalAuditDetailResp.setMemo(agentDeviceInfo.getMemo());
        terminalAuditDetailResp.setHeartbeatTime(agentDeviceInfo.getHeartbeatTime());
        terminalAuditDetailResp.setIp(agentDeviceInfo.getIp());
        terminalAuditDetailResp.setMac(agentDeviceInfo.getMac());
        terminalAuditDetailResp.setUserId(agentDeviceInfo.getUserId());
        terminalAuditDetailResp.setUserName(agentDeviceInfo.getUserName());
        terminalAuditDetailResp.setHostName(agentDeviceInfo.getHostName());
        terminalAuditDetailResp.setOs(agentDeviceInfo.getOs());
        terminalAuditDetailResp.setArch(agentDeviceInfo.getArch());
        terminalAuditDetailResp.setStatus(agentDeviceInfo.getRegisterStatus().getValue());
        terminalAuditDetailResp.setStatusStr(agentDeviceInfo.getRegisterStatus().getDesc());
        return terminalAuditDetailResp;
    }
}
