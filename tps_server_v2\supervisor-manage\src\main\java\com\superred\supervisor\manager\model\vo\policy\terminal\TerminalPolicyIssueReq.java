package com.superred.supervisor.manager.model.vo.policy.terminal;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 终端策略下发请求（参考AgentPolicyReq）
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Data
@Schema(description = "终端策略下发请求")
public class TerminalPolicyIssueReq {

    @Schema(description = "策略名称")
    @NotBlank(message = "策略名称不能为空")
    private String name;

    @Schema(description = "模块所属模块：keyword_detect 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单，file_md5 文件md5")
    @NotBlank(message = "模块类型不能为空")
    private String module;

    @Schema(description = "策略内容")
    private String config;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "下发方式：reset 全量下发，add 增量下发，del 增量删除")
    @NotBlank(message = "下发方式不能为空")
    private String cmd;

    @Schema(description = "规则列表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @NotEmpty(message = "规则列表不能为空")
    private List<Long> ruleIds;

    @Schema(description = "设备列表")
    @NotEmpty(message = "设备列表不能为空")
    private List<String> deviceIds;
}
