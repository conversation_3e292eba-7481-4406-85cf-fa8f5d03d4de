package com.superred.supervisor.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 组织机构表 实体
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:21
 */
@Data
@TableName("p_sys_org")
public class SysOrg {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 组织名称
     */
    @TableField("name")
    private String name;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 父ID
     */
    @TableField("parent_id")
    private Integer parentId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modified_time")
    private LocalDateTime modifiedTime;

    /**
     * 1 表示删除，0 表示未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;


    /**
     * 描述
     */
    @TableField("desrc")
    private String desrc;


    /**
     * 组织机构等级
     */
    @TableField("org_level")
    private Integer orgLevel;

    /**
     * 区域编码
     */
    @TableField("region_code")
    private String regionCode;

    /**
     * 区域名称
     */
    @TableField("region_path")
    private String regionPath;


    /**
     * 区域名称
     */
    @TableField("issue_count")
    private Integer issueCount;


    /**
     * 区域名称
     */
    @TableField("ip_type")
    private Integer ipType;


    /**
     * 区域名称
     */
    @TableField("ip_range")
    private String ipRange;
}

