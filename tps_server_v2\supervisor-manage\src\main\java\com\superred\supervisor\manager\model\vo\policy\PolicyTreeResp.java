package com.superred.supervisor.manager.model.vo.policy;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-26 11:28
 */
@Data
@Builder

public class PolicyTreeResp {

    @Schema(description = "模块")
    String module;

    @Schema(description = "模块名")
    String label;

    @Schema(description = "子节点")
    List<PolicyTreeResp> children;

}
