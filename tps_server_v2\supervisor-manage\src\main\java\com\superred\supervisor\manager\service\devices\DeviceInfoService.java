package com.superred.supervisor.manager.service.devices;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.supervisor.manager.model.vo.devices.DetectorListReq;
import com.superred.supervisor.manager.model.vo.devices.DetectorListResp;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditDetailResp;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditFailedReq;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditPageReq;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditPageResp;
import com.superred.supervisor.manager.model.vo.devices.manager.BusinessStatusDetectorResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceEventlogReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceInterfaceMirrorStatusResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceRemarkEditReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceStateInfoPageResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceStatusPageReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceSuspectedStatusPageResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceSystemStatusResp;
import com.superred.supervisor.manager.model.vo.devices.manager.SystemStatusCpuResp;

import javax.validation.Valid;
import java.util.List;

/**
 * 监测器设备服务
 *
 * <AUTHOR>
 * @since 2025/3/19 9:33
 */
public interface DeviceInfoService {

    /**
     * 分页查询设备审核列表
     *
     * @param pageReq 分页查询请求
     * @return 分页查询结果
     */
    IPage<DeviceAuditPageResp> pageDeviceAudit(@Valid DeviceAuditPageReq pageReq);

    /**
     * 获取设备审核详情
     *
     * @param deviceId 设备ID
     * @return 设备审核详情
     */
    DeviceAuditDetailResp getAuditDetail(String deviceId);

    /**
     * 审核设备
     *
     * @param deviceId 设备ID
     * @param pass     是否通过
     * @param reason   原因
     */
    void checkSuccess(String deviceId);

    /**
     * 审核设备失败
     *
     * @param failedReq 审核失败请求
     */
    void checkFailed(@Valid DeviceAuditFailedReq failedReq);


    /**
     * 分页查询监测器设备状态信息
     *
     * @param req 分页查询请求
     * @return 分页查询结果
     */
    IPage<DeviceStateInfoPageResp> stateInfoPage(@Valid DeviceStatusPageReq req);

    /**
     * 根据设备ID获取监测器设备状态信息
     *
     * @param deviceId 设备ID
     * @return 监测器设备状态信息
     */
    BusinessStatusDetectorResp getDetectorByDeviceId(String deviceId);

    /**
     * 分页查询设备疑似状态日志
     *
     * @param req 分页查询请求
     * @return 分页查询结果
     */
    IPage<DeviceSuspectedStatusPageResp> getDeviceSuspectedLogPage(DeviceEventlogReq req);

    /**
     * 获取设备接口镜像状态
     *
     * @param deviceId 设备ID
     * @return 设备接口镜像状态
     */
    List<DeviceInterfaceMirrorStatusResp> listDeviceInterfaceMirror(String deviceId);

    /**
     * 获取监测器设备系统状态
     *
     * @param deviceId 设备ID
     * @return 监测器设备系统状态
     */
    DeviceSystemStatusResp getSystemStatus(String deviceId);

    /**
     * 获取监测器设备CPU状态
     *
     * @param deviceId 设备ID
     * @return 监测器设备CPU状态
     */
    List<SystemStatusCpuResp> getCpuStatus(String deviceId);

    /**
     * 获取监测器设备备注
     *
     * @param deviceId 设备ID
     * @return 监测器设备备注
     */
    String getRemarksDevice(String deviceId);

    /**
     * 更新监测器设备备注
     *
     * @param deviceBaseInfo 设备基本信息
     */
    void updateRemarksDevice(DeviceRemarkEditReq deviceBaseInfo);

    List<DetectorListResp> detectorList(DetectorListReq req);
}
