package com.superred.supervisor.gateway.service.terminal.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.constant.devices.DevicesAuditType;
import com.superred.supervisor.common.constant.devices.DevicesConnectStatus;
import com.superred.supervisor.common.constant.devices.DevicesRegStatus;
import com.superred.supervisor.common.entity.system.SysOrg;
import com.superred.supervisor.common.entity.system.SysRegion;
import com.superred.supervisor.common.entity.system.SysStaff;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import com.superred.supervisor.common.entity.terminal.enums.ActivateStatus;
import com.superred.supervisor.common.repository.system.SysOrgRepository;
import com.superred.supervisor.common.repository.system.SysRegionRepository;
import com.superred.supervisor.common.repository.system.SysStaffRepository;
import com.superred.supervisor.common.repository.terminal.TerminalAgentInfoRepository;
import com.superred.supervisor.gateway.exception.ApiBaseException;
import com.superred.supervisor.gateway.exception.ApiUnAuthException;
import com.superred.supervisor.gateway.model.auth.LoginAgent;
import com.superred.supervisor.gateway.service.terminal.TerminalRegisterService;
import com.superred.supervisor.gateway.service.cache.AgentCacheService;
import com.superred.supervisor.gateway.service.cache.GatewayCacheService;
import com.superred.supervisor.gateway.service.cache.LockService;
import com.superred.supervisor.gateway.utils.AgentAuthUtils;
import com.superred.supervisor.standard.v202505.terminal.cmd.CheckUpdateReq;
import com.superred.supervisor.standard.v202505.terminal.cmd.CheckUpdateResp;
import com.superred.supervisor.standard.v202505.terminal.register.RegInfoResp;
import com.superred.supervisor.standard.v202505.terminal.register.TerminalIdReq;
import com.superred.supervisor.standard.v202505.terminal.register.TerminalIdResp;
import com.superred.supervisor.standard.v202505.terminal.register.TerminalRegisterReq;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/5/16 16:30
 */
@Service
@Slf4j
public class TerminalRegisterServiceImpl implements TerminalRegisterService {

    @Resource
    private SysOrgRepository sysOrgRepository;

    @Resource
    private SysStaffRepository sysStaffRepository;

    @Resource
    private SysRegionRepository sysRegionRepository;

    @Resource
    private TerminalAgentInfoRepository terminalAgentInfoRepository;

    @Resource
    private LockService lockService;

    @Resource
    private GatewayCacheService gatewayCacheService;

    @Resource
    private AgentCacheService agentCacheService;


    /**
     * 组织机构/人员信息查询接口
     *
     * @return 注册结果
     */
    @Override
    public RegInfoResp regInfo() {
        List<SysOrg> sysOrgs = sysOrgRepository.list();
        List<SysStaff> sysStaffs = sysStaffRepository.list();

        //构建树形结构
        List<RegInfoResp> resps = this.buildTree(sysOrgs, sysStaffs, 0);

        return resps.get(0);
    }




    /**
     * 主机唯一编码查询接口
     *
     * @param req 注册请求
     * @return 注册结果
     */
    @Override
    public TerminalIdResp getComputerClientId(TerminalIdReq req) {

        RLock clientLock = lockService.getClientLock();

        String mac = req.getMac();
        String hdCode = req.getHdCode();
        String md5Str = DigestUtil.md5Hex(mac + hdCode + req.getVendor());

        boolean tryLock = false;
        try {
            tryLock = clientLock.tryLock(3, 5, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new ApiBaseException("系统繁忙，请稍后重试");
            }
            String deviceId = gatewayCacheService.cacheComputerClientId(md5Str);
            if (deviceId == null) {
                deviceId = gatewayCacheService.generateAgentId();
                gatewayCacheService.cacheComputerClientId(md5Str, deviceId);
            }

            return TerminalIdResp.builder()
                    .deviceId(deviceId)
                    .build();

        } catch (Exception e) {
            log.warn("获取锁失败，可能是其他线程已经获取锁，异常信息：{}", e.getMessage());
            throw new ApiBaseException("获取设备ID失败，请稍后重试", e);
        } finally {
            if (tryLock && clientLock.isHeldByCurrentThread()) {
                clientLock.unlock();
            }
        }

    }

    /**
     * 终端注册接口
     *
     * @param req 注册请求
     */
    @Override
    public void regRequest(TerminalRegisterReq req) {

        String deviceId = AgentAuthUtils.getDeviceIdFromHeader();
        if (StrUtil.isEmpty(deviceId)) {
            throw new ApiBaseException("设备ID不能为空");
        }
        if (StrUtil.isNotBlank(req.getMemo()) && req.getMemo().getBytes().length > 128) {
            throw new ApiBaseException("备注长度不可超过128字节");
        }
        TerminalAgentInfo deviceInfo = buildV2(req);
        deviceInfo.setDeviceId(deviceId);
        deviceInfo.setRegisterTime(LocalDateTime.now());
//        deviceInfo.setReportType(type);
//        if (configService.terminalAutoAudit()) {
//            registerType = Constants.REG_STATUS_YES;
//            registerMsg = Constants.AGENT_REG_STATUS_YES_DESC;
//            deviceInfo.setVerifyTime(LocalDateTime.now());
//        }
        deviceInfo.setRegisterStatus(DevicesRegStatus.PENDING);
        deviceInfo.setConnectionStatus(DevicesConnectStatus.ONLINE);
        deviceInfo.setRegisterMessage("注册待审核");
        SysStaff staff = sysStaffRepository.getById(deviceInfo.getUserId());
        if (staff == null) {
            throw new ApiBaseException("用户不存在");
        }
        SysOrg org = sysOrgRepository.getById(staff.getOrgId());
        if (org == null) {
            throw new ApiBaseException("部门不存在");
        }
        SysRegion region = sysRegionRepository.getById(org.getRegionCode());
        if (region == null) {
            throw new ApiBaseException("地址编码不存在");
        }
        deviceInfo.setOrgId(staff.getOrgId());
//        deviceInfo.setAddressCode(String.valueOf(region.getId()));
        deviceInfo.setRegionPath(region.getRegionPath());

        // 查询是否存在
        TerminalAgentInfo oldDeviceInfo = terminalAgentInfoRepository.getById(deviceId);

        if (oldDeviceInfo == null) {
            //fixme 目前没有激活的需求，默认都是激活状态
            deviceInfo.setActivateStatus(ActivateStatus.ACTIVE);
            terminalAgentInfoRepository.save(deviceInfo);
        } else {
            terminalAgentInfoRepository.updateById(deviceInfo);
        }

        //清除登录信息
        agentCacheService.terminalLogout(deviceId);

    }

    private TerminalAgentInfo buildV2(TerminalRegisterReq requestVo) {
        TerminalAgentInfo deviceInfo = new TerminalAgentInfo();
        deviceInfo.setSoftVersion(requestVo.getSoftVersion());
        deviceInfo.setInterfaces(JsonUtil.toJson(requestVo.getInterfaces()));
        deviceInfo.setMemTotal(requestVo.getMemTotal());
        deviceInfo.setCpuInfo(JsonUtil.toJson(requestVo.getCpuInfo()));
        deviceInfo.setDiskInfo(JsonUtil.toJson(requestVo.getDiskInfo()));
        deviceInfo.setUserId(requestVo.getUserId());
        deviceInfo.setUserName(requestVo.getUserName());
        deviceInfo.setHostName(requestVo.getHostName());
        deviceInfo.setOs(requestVo.getOs());
        deviceInfo.setArch(requestVo.getArch());
        deviceInfo.setMemo(requestVo.getMemo());
        deviceInfo.setAuditType(DevicesAuditType.MANUAL);


        if (CollUtil.isNotEmpty(requestVo.getInterfaces())) {
            requestVo.getInterfaces().forEach(item -> {
                if (item.getManage()) {
                    deviceInfo.setIp(item.getIp());
                    deviceInfo.setIpLong(Ipv4Util.ipv4ToLong(item.getIp(), 0L));
                    deviceInfo.setMac(item.getMac());
                }
            });
        }

        return deviceInfo;
    }


    /**
     * 终端认证接口
     *
     * @param req 注册请求
     */
    @Override
    public void authLogin(TerminalRegisterReq req) {

        String deviceId = AgentAuthUtils.getDeviceIdFromHeader();
        if (StrUtil.isEmpty(deviceId)) {
            throw new ApiBaseException("设备ID不能为空");
        }
        TerminalAgentInfo terminalAgentInfo = this.terminalAgentInfoRepository.getById(deviceId);
        if (terminalAgentInfo == null) {
            throw new ApiBaseException("设备不存在");
        }
        if (terminalAgentInfo.getRegisterStatus() != DevicesRegStatus.PASS) {
            log.error("设备未审核通过，deviceId: {}, status: {}", deviceId, terminalAgentInfo.getRegisterStatus());
            throw new ApiUnAuthException("设备审核中");
        }
        //设备激活校验
        //
        TerminalAgentInfo deviceInfo = buildV2(req);
        deviceInfo.setDeviceId(deviceId);
        deviceInfo.setHeartbeatTime(LocalDateTime.now());
        deviceInfo.setConnectionStatus(DevicesConnectStatus.ONLINE);
        terminalAgentInfoRepository.updateById(deviceInfo);


        String sessionId = UUID.randomUUID().toString();
        String userAgent = AgentAuthUtils.getUserAgentFromHeader();
        String softVersion = AgentAuthUtils.getSoftVersion(userAgent);

        LoginAgent loginTerminal = LoginAgent.builder()
                .userAgent(userAgent)
                .sessionId(sessionId)
                .deviceId(deviceId)
                .softVersion(softVersion)
                .build();

        //设置cookie
        AgentAuthUtils.addSessionIdToCookie(sessionId);
        //设置登录信息
        agentCacheService.terminalLogin(deviceId, loginTerminal);
        agentCacheService.evictTerminalAgentInfo(deviceId);
        agentCacheService.cacheTerminalHeartbeat(deviceId);
    }

    /**
     * 终端注销接口
     */
    @Override
    public void regCancel() {
        String deviceId = AgentAuthUtils.getDeviceIdFromRequest();

        TerminalAgentInfo agentDeviceInfo = terminalAgentInfoRepository.getById(deviceId);
        if (agentDeviceInfo == null) {
            throw new ApiBaseException("设备不存在");
        }

        TerminalAgentInfo update = new TerminalAgentInfo();
        update.setDeviceId(deviceId);
        update.setRegisterStatus(DevicesRegStatus.CANCELED);

        this.terminalAgentInfoRepository.updateById(update);

        //清除登录信息
        agentCacheService.terminalLogout(deviceId);
    }

    /**
     * 检查更新接口
     *
     * @param req 更新请求
     * @return 更新响应
     */
    @Override
    public CheckUpdateResp checkUpdate(CheckUpdateReq req) {
        return null;
    }

    /**
     * 终端卸载回调接口
     * 该接口由终端主动调用，表示终端已卸载
     */
    @Override
    public void agentUninstallCallback() {
        String deviceId = AgentAuthUtils.getDeviceIdFromRequest();

        TerminalAgentInfo agentDeviceInfo = terminalAgentInfoRepository.getById(deviceId);
        if (agentDeviceInfo == null) {
            throw new ApiBaseException("设备不存在");
        }
        TerminalAgentInfo update = new TerminalAgentInfo();
        update.setDeviceId(deviceId);
        update.setConnectionStatus(DevicesConnectStatus.UNINSTALLED);
        terminalAgentInfoRepository.updateById(update);

    }

    private List<RegInfoResp> buildTree(List<SysOrg> sysOrgs, List<SysStaff> sysStaffs, int parentId) {
        List<RegInfoResp> tree = new ArrayList<>();
        for (SysOrg sysOrg : sysOrgs) {
            if (sysOrg.getParentId() == parentId) {
                RegInfoResp regInfoResp = new RegInfoResp();
                regInfoResp.setId(String.valueOf(sysOrg.getId()));
                regInfoResp.setPid(String.valueOf(sysOrg.getParentId()));
                regInfoResp.setName(sysOrg.getName());
//                regInfoResp.setCode(sysOrg.getCode());
                regInfoResp.setIsDepartment(true);
                regInfoResp.setChildren(buildTree(sysOrgs, sysStaffs, sysOrg.getId()));
                tree.add(regInfoResp);
            }
        }

        for (SysStaff sysStaff : sysStaffs) {
            if (sysStaff.getOrgId() == parentId) {
                RegInfoResp regInfoResp = new RegInfoResp();
                regInfoResp.setId(String.valueOf(sysStaff.getId()));
                regInfoResp.setName(sysStaff.getStaffName());
                regInfoResp.setPid(String.valueOf(sysStaff.getOrgId()));
//                regInfoResp.setCode(sysStaff.getCode());
                regInfoResp.setIsDepartment(false);
                tree.add(regInfoResp);
            }
        }

        return tree;
    }
}
