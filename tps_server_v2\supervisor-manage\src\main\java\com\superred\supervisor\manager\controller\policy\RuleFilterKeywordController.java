package com.superred.supervisor.manager.controller.policy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterKeywordPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterKeywordReq;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterKeywordResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.service.policy.RuleFilterKeywordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-11 16:46
 */
@Tag(name = "4.9. 文件筛选策略 - 关键词策略")
@RestController
@RequestMapping("/rule/file_filter/keyword")
@Slf4j
@Validated
public class RuleFilterKeywordController {

    @Resource
    private RuleFilterKeywordService ruleFilterKeywordService;

    @Operation(summary = "1 分页")
    @GetMapping("/page")
    public RPage<RuleFilterKeywordResp> page(RuleFilterKeywordPageReq ruleFilterKeywordPageReq) {
        IPage<RuleFilterKeywordResp> page = this.ruleFilterKeywordService.page(ruleFilterKeywordPageReq);
        return new RPage<>(page);
    }

    @Operation(summary = "2 查详情")
    @GetMapping("/{id}")
    public R<RuleFilterKeywordResp> getById(@PathVariable("id") Integer id) {
        RuleFilterKeywordResp resp = this.ruleFilterKeywordService.getById(id);
        return R.success(resp);
    }

    @Operation(summary = "3 新增")
    @PostMapping("/save")
    @SysLogAnn(module = LogTypeConstants.KEYWORD_STRATEGY, operateType = OperateTypeConstants.ADD, desc = OperateTypeConstants.ADD + LogTypeConstants.KEYWORD_STRATEGY)
    public R save(@Valid @RequestBody RuleFilterKeywordReq ruleFilterKeywordReq) {
        this.ruleFilterKeywordService.save(ruleFilterKeywordReq);
        return R.success();
    }

    @Operation(summary = "4 编辑")
    @SysLogAnn(module = LogTypeConstants.KEYWORD_STRATEGY, operateType = OperateTypeConstants.MODIFY, desc = OperateTypeConstants.MODIFY + LogTypeConstants.KEYWORD_STRATEGY)
    @PostMapping("/edit")
    public R edit(@Valid @RequestBody RuleFilterKeywordReq ruleFilterKeywordReq) {
        this.ruleFilterKeywordService.edit(ruleFilterKeywordReq);
        return R.success();
    }

    @Operation(summary = "5 删除")
    @PostMapping("/del")
    @SysLogAnn(module = LogTypeConstants.KEYWORD_STRATEGY, operateType = OperateTypeConstants.DELETE, desc = OperateTypeConstants.DELETE + LogTypeConstants.KEYWORD_STRATEGY)
    public R del(@Valid @RequestBody PolicyBatchIdsReq batchIdsReq) {
        this.ruleFilterKeywordService.del(batchIdsReq);
        return R.success();
    }

    @Operation(summary = "6 查看规则应用策略情况")
    @PostMapping("/policy/{ruleId}")
    public R<List<RulePolicyApplyResp>> policyApply(@PathVariable("ruleId") Long ruleId) {
        List<RulePolicyApplyResp> result = this.ruleFilterKeywordService.policyApply(ruleId);
        return R.success(result);
    }

    @Operation(summary = "7 导出")
    @PostMapping("/export")
    @SysLogAnn(module = LogTypeConstants.KEYWORD_STRATEGY, operateType = OperateTypeConstants.EXPORT, desc = OperateTypeConstants.EXPORT + LogTypeConstants.KEYWORD_STRATEGY)
    public void export(HttpServletResponse response, @RequestBody RuleFilterKeywordPageReq ruleFilterKeywordPageReq) {

        // do something
    }

    @Operation(summary = "8 导入")
    @PostMapping("/import")
    @SysLogAnn(module = LogTypeConstants.KEYWORD_STRATEGY, operateType = OperateTypeConstants.IMPORT, desc = OperateTypeConstants.IMPORT + LogTypeConstants.KEYWORD_STRATEGY)
    public R importFile(@RequestParam("file") MultipartFile file) {
        return null;
    }

    @Operation(summary = "9 下载模板")
    @PostMapping("/download")
    @SysLogAnn(module = LogTypeConstants.KEYWORD_STRATEGY, operateType = OperateTypeConstants.DOWNLOAD, desc = LogTypeConstants.KEYWORD_STRATEGY + "下载模板")
    public void download(HttpServletResponse response) throws IOException {
        //        String filePath = "template/关键词策略模板.xlsx";
        //        String fileName = "关键词策略模板.xlsx";
        //        ClassPathResource classpathResource = new ClassPathResource(filePath);
        //        InputStream inputStream = classpathResource.getInputStream();
        //        FileUtils.downloadFileExcel(response,inputStream,fileName);

    }
}
