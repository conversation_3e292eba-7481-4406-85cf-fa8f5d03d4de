package com.superred.supervisor.manager.common.enums.command;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <p> 指令枚举
 *
 * <AUTHOR>
 * @since 2025/03/13 17:03
 **/
@Getter
public enum CommandType {

    SHUTDOWN("shutdown", "关机"),
    REBOOT("reboot", "重启"),
    STOPM_INNER("stopm_inner", "内置模块停止"),
    STARTM_INNER("startm_inner", "内置模块开启"),
    STARTM("startm", "模块开启"),
    STOPM("stopm", "模块停止"),

    SYNC_TIME("sync_time", "时间同步"),
    UPDATE("update", "系统软件升级"),
    VERSION_CHECK("version_check", "版本一致性检查"),
    VERSION_CHECK_METHOD_LS("ls", "版本一致性检查(读取目录文件列表)"),
    VERSION_CHECK_METHOD_GET_FILE("get_file", "版本一致性检查(读取文件内容)"),
    VERSION_CHECK_METHOD_MD5SUM("md5sum", "版本一致性检查(判断文件MD5)"),
    INNER_POLICY_UPDATE("inner_policy_update", "内置策略更新"),
    CTRL_INNER_POLICY("ctrl_inner_policy", "内置策略启停"),
    PASSWD("passwd", "用户密码重置"),
    DROP_DATA("dropdata", "积压数据删除"),
    REPORT_POLICY("report_policy", "策略上报"),
    WARNING_DISPOSE("warning_dispose", "威胁预警处置"),
    DEVICE_REPORT("device_report", "接入设备信息上报"),
    CERT_UPDATE("cert_update", "通信证书更新"),

    UNKNOWN("unknown", "未知指令");

    private final String value;
    private final String name;

    CommandType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static CommandType getCmdTypeByValue(String value) {
        for (CommandType commandType : CommandType.values()) {
            if (commandType.getValue().equalsIgnoreCase(value)) {
                return commandType;
            }
        }
        return CommandType.UNKNOWN;
    }

    /**
     * 终端软件升级指令参数
     * <AUTHOR>
     * @since 2025/07/25
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UpdateCmdParam {

        private String filename;

        private String md5;

        @JsonProperty("soft_version")
        private String softVersion;

        @JsonProperty("update_method")
        @JsonIgnore
        @Deprecated
        private Integer updateMethod;
    }

    /**
     * 内部策略更新参数
     * <AUTHOR>
     * @since 2025/07/25
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InnerPolicyUpdateParam {


        private String filename;

        private String md5;

    }
}
