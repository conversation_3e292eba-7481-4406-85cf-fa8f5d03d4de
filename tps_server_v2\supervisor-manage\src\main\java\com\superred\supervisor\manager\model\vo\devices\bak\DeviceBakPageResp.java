package com.superred.supervisor.manager.model.vo.devices.bak;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 设备报备页面对应
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data
public class DeviceBakPageResp {


    /**
     * id
     */
    private Long id;

    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */
    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    private String deviceId;

    /**
     * 设备类型
     */
    @Schema(description = "设备类型")
    private String deviceType;

    /**
     * 证书编号
     */
    @Schema(description = "证书编号")
    private String deviceCa;


    /**
     * 产品软件版本号，最长16个字符。如2012.1.5.12
     */
    @Schema(description = "产品软件版本号，最长16个字符。如2012.1.5.12")
    private String softVersion;


    /**
     * 内存总数，单位MB
     */
    @Schema(description = "内存总数，单位MB")
    private Integer memTotal;

    /**
     * 监测器部署的客户单位名，如“XX信息中心”
     */
    @Schema(description = "监测器部署的客户单位名，如“XX信息中心”")
    private String organs;

    /**
     * 监测器部署的地理位置，如“北京市海淀区复兴路128号区政府接入机房F-12”
     */
    @Schema(description = "监测器部署的地理位置，如“北京市海淀区复兴路128号区政府接入机房F-12”")
    private String address;


    /**
     * 报备时间
     */
    @Schema(description = "报备时间")
    private LocalDateTime addTime;


}
