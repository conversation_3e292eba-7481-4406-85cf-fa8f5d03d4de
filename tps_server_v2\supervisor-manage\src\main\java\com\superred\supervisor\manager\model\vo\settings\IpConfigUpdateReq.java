package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;


/**
 * <AUTHOR>
 *  公网ip
 * @since 2025年03月13日
 */
@Data
public class IpConfigUpdateReq {

    @Schema(description = "主键")
    @NotNull
    private Integer id;

    @Schema(description = "公网ip出口")
    @NotNull(message = "ip 不能为空")
    @Pattern(regexp = "^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)$",
            message = "ip 格式错误")
    private String ip;

    @Schema(description = "备注")
    @Size(max = 300, message = "备注 长度不能超过300位")
    private String remark;


}
