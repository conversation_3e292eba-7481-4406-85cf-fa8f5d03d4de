package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 *  系统维护记录
 * @since 2025年03月14日
 */
@Data
public class MaintenanceLogReq {


    @Schema(description = "操作类型")
    private String operateType;

    @Schema(description = "操作结果")
    private String operateResult;

    @Schema(description = "错误信息")
    private String errorInfo;
}
