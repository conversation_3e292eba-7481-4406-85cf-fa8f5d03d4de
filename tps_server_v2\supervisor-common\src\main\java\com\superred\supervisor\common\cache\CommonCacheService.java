package com.superred.supervisor.common.cache;

import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.function.Supplier;

/**
 * 公共缓存服务
 *
 * <AUTHOR>
 * @since 2025/7/18 14:24
 */
@Service
public class CommonCacheService {

    @Resource
    private RedissonClient redissonClient;


    public <T> T getOrLoad(String key, Supplier<T> loader, Duration ttl) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        T value = bucket.get();
        if (value != null) {
            return value;
        }
        value = loader.get();
        if (value != null) {
            bucket.set(value, ttl);
        }
        return value;
    }

    public <T> T getOrLoad(String key, Supplier<T> loader, Duration ttl, T defaultValue) {
        T value = this.getOrLoad(key, loader, ttl);
        if (value == null) {
            return defaultValue;
        }
        return value;
    }

    public <T> T get(String key) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    public <T> void set(String key, T value, Duration ttl) {
        redissonClient.getBucket(key).set(value, ttl);
    }

    public void evict(String key) {
        redissonClient.getBucket(key).delete();
    }

    public boolean exists(String key) {
        return redissonClient.getBucket(key).isExists();
    }
}
