package com.superred.supervisor.manager.model.vo.devices.manager;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 设备状态编辑请求
 *
 * <AUTHOR>
 * @since 2025/03/14
 */
@Data

public class DeviceStatusEditReq {

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    @NotNull(message = "设备ID不能为空")
    private String deviceId;
    /**
     * 原因
     */
    @Schema(description = "原因")
    @NotNull(message = "原因不能为空")
    private String cause;
}
