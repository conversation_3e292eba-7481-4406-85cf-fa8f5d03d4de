package com.superred.supervisor.gateway.config.interceptor;

import cn.hutool.core.util.StrUtil;
import com.superred.common.core.utils.WebUtils;
import com.superred.supervisor.gateway.aop.IgnoreLogin;
import com.superred.supervisor.gateway.constant.auth.AuthStatusEnum;
import com.superred.supervisor.gateway.exception.ApiUnAuthException;
import com.superred.supervisor.gateway.model.auth.LoginAgent;
import com.superred.supervisor.gateway.service.cache.AgentCacheService;
import com.superred.supervisor.gateway.utils.AgentAuthUtils;
import com.superred.supervisor.gateway.utils.WebExtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;


/**
 * 终端 授权拦截器
 *
 * <AUTHOR>
 * @since 2025/06/30
 */
@Slf4j
@Component
public class TerminalAgentAuthInterceptor implements HandlerInterceptor {


    @Resource
    private AgentCacheService agentCacheService;


    /*
     * auth-status：
     * 1：请求头里设备ID为空
     * 2：未注册
     * 3：未认证
     * 4：已认证
     * activate-status:
     * 0：未激活
     * 1：已激活
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (WebUtils.isAuthIgnoredUrl()) {
            return true;
        }
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        if (ignoreAuth(handlerMethod)) {
            return true;
        }

        String deviceId = AgentAuthUtils.getDeviceIdFromHeader();

        if (StrUtil.isEmpty(deviceId)) {
            throw new ApiUnAuthException(AuthStatusEnum.DEVICE_ID_EMPTY);
        }
        AgentAuthUtils.addDeviceIdToRequest(deviceId);

//        response.addHeader(AuthStatusEnum.KEY, AuthStatusEnum.AUTHENTICATED.getCode());
//        response.addHeader(AuthActiveEnum.KEY, AuthActiveEnum.ACTIVATED.getCode());

        LoginAgent loginAgent = agentCacheService.getLoginAgent(deviceId);
        if (loginAgent == null) {
            throw new ApiUnAuthException(AuthStatusEnum.UNAUTHENTICATED);
        }

        if (!StrUtil.equalsIgnoreCase(WebExtUtils.getCookie("SESSION"),
                loginAgent.getSessionId())) {
            log.info("AgentV2Interceptor deviceId: {}, sessionId不一致，返回401", deviceId);
            throw new ApiUnAuthException(AuthStatusEnum.UNAUTHENTICATED);
        }

        return true;
    }


    private boolean ignoreAuth(HandlerMethod handlerMethod) {
        Method method = handlerMethod.getMethod();
        if (method.isAnnotationPresent(IgnoreLogin.class)) {
            return true;
        }
        IgnoreLogin passToken = method.getAnnotation(IgnoreLogin.class);
        if (passToken != null) {
            return true;
        }

        Class<?> beanType = handlerMethod.getBeanType();

        IgnoreLogin ignoreAuth = AnnotationUtils.findAnnotation(beanType, IgnoreLogin.class);
        return ignoreAuth != null;
    }

}
