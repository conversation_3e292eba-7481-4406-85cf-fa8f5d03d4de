package com.superred.supervisor.manager.model.vo.devices.manager;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 监测器业务状态
 */
@Data

@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessStatusDetectorResp {

    /**
     * 监测器业务状态数据采集时间
     */
    @Schema(description = "监测器业务状态数据采集时间")
    private LocalDateTime time;
    /**
     * 上次加电启动开始运行时长 秒
     */
    @Schema(description = "上次加电启动开始运行时长 秒")
    private Long uptime;
}
