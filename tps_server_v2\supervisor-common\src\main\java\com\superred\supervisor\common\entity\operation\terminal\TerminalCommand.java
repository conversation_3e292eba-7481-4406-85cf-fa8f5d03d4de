package com.superred.supervisor.common.entity.operation.terminal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.superred.supervisor.common.entity.operation.enums.OperationExecStatus;
import com.superred.supervisor.common.entity.operation.enums.OperationType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端指令表 实体
 *
 * <AUTHOR>
 * @since 2025-07-24 14:22:55
 */
@Data
@TableName("op_terminal_command")
public class TerminalCommand {


    /**
     * 指令ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 指令编码 标准中编码格式
     */
    @TableField("cmd_code")
    private String cmdCode;

    /**
     * 指令名称
     */
    @TableField("command_name")
    private String commandName;

    /**
     * 指令类型：inner_policy_update 内置策略更新 startm 模块启动指令 stopm 模块停止指令 startm_inner 开启模块内置策略 stopm_inner 停止模块内置策略 update 系统软件更新
     */
    @TableField("command_type")
    private String commandType;

    /**
     * 指令参数配置，如模块名称、时间参数等
     */
    @TableField("command_params")
    private String commandParams;

    /**
     * 目标模块：alarm file_detect device_info
     */
    @TableField("module")
    private String module;

    /**
     * 子模块名称 [] 模块启动指令的子模块名称
     */
    @TableField("submodule")
    private String submodule;

    /**
     * 指令描述
     */
    @TableField("description")
    private String description;

    /**
     * 指令状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 更新人
     */
    @TableField("updater_id")
    private String updaterId;

    public TerminalOperation toOperation() {
        TerminalOperation operation = new TerminalOperation();
        operation.setId(IdWorker.getId());
        operation.setOperationType(OperationType.COMMAND);
        operation.setCmd(this.commandType);
        operation.setRefId(this.cmdCode);
        operation.setParam(this.commandParams);
        operation.setModule(this.module);
        operation.setSubmodule(this.submodule);
        operation.setCreateTime(this.createTime);
        operation.setCreatorId(this.creatorId);
        return operation;
    }
}

