package com.superred.supervisor.manager.model.vo.system.user;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 *  分页查询入参
 * @since 2025年03月12日
 */
@Data
public class UserPageReq extends PageReqDTO implements Serializable {


    @Schema(description = "姓名")
    private String realName;


    /**
     * 账号
     */
    @Schema(description = "账号")
    private String username;


    @Schema(description = "手机号")
    private String card;

    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    private Integer orgId;
}
