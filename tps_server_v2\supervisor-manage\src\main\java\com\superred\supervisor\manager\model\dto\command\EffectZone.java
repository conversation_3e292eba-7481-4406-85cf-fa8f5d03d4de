package com.superred.supervisor.manager.model.dto.command;

import lombok.Data;

import java.util.List;

/**
 * 选择生效范围
 *
 * <AUTHOR>
 * @since 2023/10/10
 **/
@Data
public class EffectZone {

    /**
     * 选择的行业监管或者行政监管的id编码
     */
    private Long supervisionId;

    /**
     * 行业监管还是行政监管  1 行政监管 2 行业监管
     */
    private Integer supervisionType;

    /**
     * #选择类型 1全部设备 2全部监测器 3全部管理系统 9 选择范围
     */
    private Integer selectType;

    /**
     * 部分设备自监管
     */
    private List<PartDevice> selfRegulations;

    /**
     * 部分设备监测器
     */
    private List<PartDevice> detectors;

    /**
     * 部分设备管理系统
     */
    private List<PartDevice> managers;

    /**
     * 是否是全部省 或者十或者区
     */
    private Boolean isSelect;

    /**
     * 行业/行政编码层级
     */
    private Integer level;
}
