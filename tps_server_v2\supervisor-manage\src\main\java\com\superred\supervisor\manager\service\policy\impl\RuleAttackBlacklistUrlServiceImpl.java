package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleThreeEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleAttackBlacklistUrl;
import com.superred.supervisor.common.repository.policy.RuleAttackBlacklistUrlRepository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.constant.RuleSourceTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.config.BlackUrlPolicyConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackBlacklistUrlPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackBlacklistUrlReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackBlacklistUrlResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleAttackBlacklistUrlService;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-12 17:18
 */
@Slf4j
@Service("ruleAttackBlacklistUrlService")
@AllArgsConstructor
public class RuleAttackBlacklistUrlServiceImpl implements RuleAttackBlacklistUrlService, RuleService {

    @Resource
    private RuleAttackBlacklistUrlRepository ruleAttackBlacklistUrlRepository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;

    @Override
    public IPage<RuleAttackBlacklistUrlResp> page(RuleAttackBlacklistUrlPageReq ruleAttackBlacklistUrl) {
        List<String> ruleIdList = this.getRuleIdList(ruleAttackBlacklistUrl);
        LambdaQueryWrapper<RuleAttackBlacklistUrl> queryWrapper = new LambdaQueryWrapper<RuleAttackBlacklistUrl>()
                .le(StrUtil.isNotEmpty(ruleAttackBlacklistUrl.getEndDate()), RuleAttackBlacklistUrl::getUpdateTime, ruleAttackBlacklistUrl.getEndDate())
                .ge(StrUtil.isNotEmpty(ruleAttackBlacklistUrl.getStartDate()), RuleAttackBlacklistUrl::getUpdateTime, ruleAttackBlacklistUrl.getStartDate())
                .like(StrUtil.isNotEmpty(ruleAttackBlacklistUrl.getRuleId()), RuleAttackBlacklistUrl::getRuleId, ruleAttackBlacklistUrl.getRuleId())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistUrl.getRisk()), RuleAttackBlacklistUrl::getRisk, ruleAttackBlacklistUrl.getRisk())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistUrl.getAttackClass()), RuleAttackBlacklistUrl::getAttackClass, ruleAttackBlacklistUrl.getAttackClass())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistUrl.getMatchType()), RuleAttackBlacklistUrl::getMatchType, ruleAttackBlacklistUrl.getMatchType())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistUrl.getIsShare()), RuleAttackBlacklistUrl::getIsShare, ruleAttackBlacklistUrl.getIsShare())
                .eq(StrUtil.isNotEmpty(ruleAttackBlacklistUrl.getRuleSource()), RuleAttackBlacklistUrl::getRuleSource, ruleAttackBlacklistUrl.getRuleSource())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleAttackBlacklistUrl::getRuleId, ruleIdList)
                .ne(RuleAttackBlacklistUrl::getRuleSource, RuleSourceTypeEnum.SUPER_POLICY_SHARE.getKey())
                .orderByDesc(RuleAttackBlacklistUrl::getUpdateTime);
        Page<RuleAttackBlacklistUrl> page = new Page<>(ruleAttackBlacklistUrl.getStart(), ruleAttackBlacklistUrl.getLimit());
        IPage<RuleAttackBlacklistUrl> page1 = this.ruleAttackBlacklistUrlRepository.page(page, queryWrapper);
        return page1.convert(RuleAttackBlacklistUrlResp::fromRuleAttackBlacklistUrl);
    }

    @Override
    public RuleAttackBlacklistUrlResp getById(Long ruleId) {
        RuleAttackBlacklistUrl ruleAttackBlacklistUrl = this.ruleAttackBlacklistUrlRepository.getById(ruleId);
        return RuleAttackBlacklistUrlResp.fromRuleAttackBlacklistUrl(ruleAttackBlacklistUrl);
    }

    @Override
    public void save(RuleAttackBlacklistUrlReq ruleAttackBlacklistUrlReq) {
        RuleAttackBlacklistUrl ruleAttackBlacklistUrl = fromRuleAttackBlacklistUrlReq(ruleAttackBlacklistUrlReq);
        // 赋值ruleId
        ruleAttackBlacklistUrl.setRuleId(this.ruleIdBuilder.buildRuleId().toString());
        this.ruleAttackBlacklistUrlRepository.save(ruleAttackBlacklistUrl);
    }

    @Override
    public void edit(RuleAttackBlacklistUrlReq ruleAttackBlacklistUrlReq) {
        RuleAttackBlacklistUrl ruleAttackBlacklistUrl = fromRuleAttackBlacklistUrlReq(ruleAttackBlacklistUrlReq);
        this.ruleAttackBlacklistUrlRepository.update(ruleAttackBlacklistUrl, Wrappers.<RuleAttackBlacklistUrl>lambdaUpdate().eq(RuleAttackBlacklistUrl::getRuleId, ruleAttackBlacklistUrlReq.getRuleId()));
    }


    public static RuleAttackBlacklistUrl fromRuleAttackBlacklistUrlReq(RuleAttackBlacklistUrlReq ruleAttackBlacklistUrlReq) {
        return RuleAttackBlacklistUrl.builder()
                .ruleId(ruleAttackBlacklistUrlReq.getRuleId())
                .url(ruleAttackBlacklistUrlReq.getUrl())
                .ruleType(ruleAttackBlacklistUrlReq.getRuleType())
                .matchType(ruleAttackBlacklistUrlReq.getMatchType())
                .ruleName(ruleAttackBlacklistUrlReq.getRuleName())
                .attackClass(ruleAttackBlacklistUrlReq.getAttackClass())
                .attackGroup(ruleAttackBlacklistUrlReq.getAttackGroup())
                .attackStage(ruleAttackBlacklistUrlReq.getAttackStage())
                .facilityType(ruleAttackBlacklistUrlReq.getFacilityType())
                .desc(ruleAttackBlacklistUrlReq.getDesc())
                .risk(ruleAttackBlacklistUrlReq.getRisk())
                .isShare(ruleAttackBlacklistUrlReq.getIsShare())
                .status(ruleAttackBlacklistUrlReq.getStatus())
                .ruleSource(ruleAttackBlacklistUrlReq.getRuleSource())
                .level(ruleAttackBlacklistUrlReq.getLevel())
                .updateTime(ruleAttackBlacklistUrlReq.getUpdateTime())
                .createTime(ruleAttackBlacklistUrlReq.getCreateTime())
                .ext1(ruleAttackBlacklistUrlReq.getExt1())
                .ext2(ruleAttackBlacklistUrlReq.getExt2())
                .ext3(ruleAttackBlacklistUrlReq.getExt3())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleAttackBlacklistUrlRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 验证是否在使用
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleAttackBlacklistUrl> list = this.ruleAttackBlacklistUrlRepository.list(Wrappers.<RuleAttackBlacklistUrl>lambdaQuery()
                .in(RuleAttackBlacklistUrl::getRuleId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (StrUtil.equals(PolicyApplyStatusEnum.APPLY.getKey().toString(), item.getStatus())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
                Integer ruleSource = Integer.valueOf(item.getRuleSource());
                if (ruleSource != RuleSourceTypeEnum.LOCAL_POLICY.getKey()) {
                    throw new BaseBusinessException("存在上级策略，上级策略不允许被删除");
                }
            });
        }
    }

    /**
     * 获取规则ID列表
     * @param ruleAttackBlacklistUrl
     * @return
     */
    private List<String> getRuleIdList(RuleAttackBlacklistUrlPageReq ruleAttackBlacklistUrl) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleAttackBlacklistUrl.getIssueDeviceType())
                || StrUtil.isBlank(ruleAttackBlacklistUrl.getPolicyId())
                || StrUtil.isBlank(ruleAttackBlacklistUrl.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleAttackBlacklistUrl.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleAttackBlacklistUrl.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleAttackBlacklistUrl.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleAttackBlacklistUrl.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 攻击窃密检测 - 黑名单检测策略 - URL黑名单检测策略
        return StrUtil.equals(module, PolicyModuleThreeEnum.URL_BLACKLIST.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 攻击窃密检测 - 黑名单检测策略 - URL黑名单检测策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleThreeEnum.URL_BLACKLIST.getKey())
                .moduleStr(PolicyModuleThreeEnum.URL_BLACKLIST.getValue())
                .moduleParentStr(PolicyModuleOneEnum.ALARM.getValue() + " - " + PolicyModuleTwoEnum.BLACKLIST.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 攻击窃密检测 - 黑名单检测策略 - URL黑名单检测策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleAttackBlacklistUrl> list = this.ruleAttackBlacklistUrlRepository.list(Wrappers.<RuleAttackBlacklistUrl>lambdaQuery()
                .in(RuleAttackBlacklistUrl::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<BlackUrlPolicyConfigDTO> configDTOS = list.stream().map(item -> {
            return BlackUrlPolicyConfigDTO.getPolicyConfig(item);
        }).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 攻击窃密检测 - 黑名单检测策略 - URL黑名单检测策略
        this.ruleAttackBlacklistUrlRepository.update(Wrappers.<RuleAttackBlacklistUrl>lambdaUpdate()
                .in(RuleAttackBlacklistUrl::getRuleId, ruleIds)
                .set(RuleAttackBlacklistUrl::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 攻击窃密检测 - 黑名单检测策略 - URL黑名单检测策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleAttackBlacklistUrl> list = this.ruleAttackBlacklistUrlRepository.list(Wrappers.<RuleAttackBlacklistUrl>lambdaQuery()
                .in(RuleAttackBlacklistUrl::getRuleId, ruleIdList));
        List<RuleAttackBlacklistUrlResp> respList = new ArrayList<>();
        list.forEach(item -> {
            RuleAttackBlacklistUrlResp resp = RuleAttackBlacklistUrlResp.fromRuleAttackBlacklistUrl(item);
            respList.add(resp);
        });
        policyDetail.setBlacklistUrlList(respList);
        return policyDetail;
    }
}
