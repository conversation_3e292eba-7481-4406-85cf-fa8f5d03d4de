package com.superred.supervisor.manager.model.vo.command.terminal;


import com.superred.supervisor.manager.model.vo.command.OperationRangeReq;
import com.superred.supervisor.manager.model.vo.command.IssueModuleCmd;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> Leixm
 * @since: 2025/03/19 16:48
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class TerminalModuleSwitchCmdReq extends OperationRangeReq {

    @Schema(description = "模块启停数据")
    private List<IssueModuleCmd> modules;

}
