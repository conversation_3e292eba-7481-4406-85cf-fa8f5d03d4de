package com.superred.supervisor.gateway.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * redission配置
 *
 * <AUTHOR>
 * @since 2025/3/10 21:45
 */
@Configuration
public class RedissionConfig {


    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient(RedisProperties redisProperties, ObjectMapper objectMapper) {


        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://" + redisProperties.getHost() + ":" + redisProperties.getPort()) // Redis 地址
                .setIdleConnectionTimeout(10000) // 空闲连接超时时间
                .setConnectTimeout(10000) // 连接超时时间
                .setTimeout(3000) // 命令超时时间
                .setRetryAttempts(3) // 命令失败重试次数
                .setPassword(redisProperties.getPassword()) // Redis 认证密码（若无密码，可设为 null）
                .setSubscriptionsPerConnection(5) // 每个连接的订阅数
                .setClientName("SUPERVISION-GATEWAY") // 客户端名称
                .setSubscriptionConnectionMinimumIdleSize(1) // 订阅连接最小空闲数
                .setSubscriptionConnectionPoolSize(2) // 订阅连接池大小
                .setConnectionMinimumIdleSize(5) // 连接最小空闲数
                .setConnectionPoolSize(64) // 连接池大小
                .setDatabase(redisProperties.getDatabase()) // Redis 数据库索引
                .setDnsMonitoringInterval(5000); // DNS 监控间隔

        config.setThreads(16); // 线程池大小
        config.setNettyThreads(32); // Netty 线程池大小
        config.setCodec(new org.redisson.codec.JsonJacksonCodec(objectMapper)); // JSON 编码方式
        config.setTransportMode(org.redisson.config.TransportMode.NIO); // 传输模式：NIO

        return Redisson.create(config);
    }

}
