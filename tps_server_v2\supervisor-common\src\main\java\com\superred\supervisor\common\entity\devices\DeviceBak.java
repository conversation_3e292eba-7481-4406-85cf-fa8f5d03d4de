package com.superred.supervisor.common.entity.devices;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 设备报备
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_device_bak")
public class DeviceBak extends Model<DeviceBak> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */
    private String deviceId;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 证书编号
     */
    private String deviceCa;


    /**
     * 产品软件版本号，最长16个字符。如2012.1.5.12
     */
    private String softVersion;

    /**
     * 厂商名称
     */
    private String contractor;

    /**
     * 内存总数，单位MB
     */
    private Integer memTotal;

    /**
     * 监测器部署的客户单位名，如“XX信息中心”
     */
    private String organs;

    /**
     * 监测器部署的地理位置，如“北京市海淀区复兴路128号区政府接入机房F-12”
     */
    private String address;

    /**
     * 行政/行业区划编码，如“100085”
     */
    private String addressCode;

    /**
     * 网卡信息
     */
    private String interfaceInfo;

    /**
     * cpu信息
     */
    private String cpuInfo;

    /**
     * 磁盘信息
     */
    private String diskInfo;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 公网IP
     */
    private String publicIp;

    /**
     * 报备时间
     */
    private LocalDateTime addTime;

    /**
     * 预留字段
     */
    private String ext1;

    /**
     * 预留字段
     */
    private String ext2;

    /**
     * 预留字段
     */
    private String ext3;

    /**
     * 预留字段
     */
    private String ext4;

    /**
     * 预留字段
     */
    private String ext5;

}
