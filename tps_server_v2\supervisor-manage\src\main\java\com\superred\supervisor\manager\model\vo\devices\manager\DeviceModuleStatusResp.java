package com.superred.supervisor.manager.model.vo.devices.manager;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p> 模块状态VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/13 13:43
 **/
@Data

public class DeviceModuleStatusResp {

    @Schema(description = "模块ID")
    private String id;

    @Schema(description = "模块中文名称")
    private String module;

    @Schema(description = "模块英文名称")
    private String name;

    /**
     * 24小时产生告警数
     */
    @Schema(description = "24小时产生告警数")
    private Integer record24hNum;

    /**
     * 未上报数据条数
     */
    @Schema(description = "未上报数据条数")
    private Integer recordDelayednum;


    @Schema(description = "状态")
    private String status;

    @Schema(description = "内部策略状态")
    private String innerPolicyStatus;

    @Schema(description = "策略版本")
    private List<String> version;

    @Schema(description = "子模块")
    private List<DeviceModuleStatusResp> children;
}
