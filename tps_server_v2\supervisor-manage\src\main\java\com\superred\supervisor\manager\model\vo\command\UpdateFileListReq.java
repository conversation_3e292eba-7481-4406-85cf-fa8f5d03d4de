package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class UpdateFileListReq {

    @Schema(description = "inner_policy_update:内置策略，update:系统固件")
    @NotBlank(message = "cmd不能为空")
    private String cmd;

    @Schema(description = "更新固件文件名")
    private String filename;

    @Schema(description = "更新固件文件校验和")
    private String md5;

    @Schema(description = "升级文件路径")
    private String filePath;

    @Schema(description = "更新后产品软件版本号")
    private String softVersion;

    @Schema(description = "上传时间")
    private LocalDateTime createTime;

}
