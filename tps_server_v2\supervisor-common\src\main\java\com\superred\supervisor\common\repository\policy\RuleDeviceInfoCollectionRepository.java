package com.superred.supervisor.common.repository.policy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.RuleDeviceInfoCollection;
import com.superred.supervisor.common.mapper.policy.RuleDeviceInfoCollectionMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2025-04-18 14:49
 */
@Slf4j
@Repository
@AllArgsConstructor
public class RuleDeviceInfoCollectionRepository extends ServiceImpl<RuleDeviceInfoCollectionMapper, RuleDeviceInfoCollection> {
}
