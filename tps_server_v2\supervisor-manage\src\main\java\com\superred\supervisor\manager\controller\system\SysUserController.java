package com.superred.supervisor.manager.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.system.user.DeleteUserReq;
import com.superred.supervisor.manager.model.vo.system.user.ModifyUserPasswordReq;
import com.superred.supervisor.manager.model.vo.system.user.UserEnableReq;
import com.superred.supervisor.manager.model.vo.system.user.UserPageReq;
import com.superred.supervisor.manager.model.vo.system.user.UserReq;
import com.superred.supervisor.manager.model.vo.system.user.UserResetPwdReq;
import com.superred.supervisor.manager.model.vo.system.user.UserResp;
import com.superred.supervisor.manager.model.vo.system.user.UserRoleReq;
import com.superred.supervisor.manager.service.system.SysUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * sys用户控制器
 *
 * <AUTHOR>
 * @since 2025/03/14
 */
@Tag(name = "1.6. 用户管理")
@RestController
@AllArgsConstructor
@Validated
@RequestMapping("/sys_user")
public class SysUserController {

    // 用户管理模块
    private static final String MODEL = "用户管理";

    @Resource
    SysUserService sysUserService;


    @Operation(summary = "1.分页查询")
    @ApiOperationSupport(order = 1)
    @PostMapping("/page")
    public RPage<UserResp> getSysUserPage(@Valid @RequestBody UserPageReq userPageReq) {
        IPage<UserResp> sysUserPage = sysUserService.getSysUserPage(userPageReq);
        return new RPage<>(sysUserPage);
    }


    @ApiOperationSupport(order = 2)
    @Operation(summary = "2.通过id查询用户")

    @GetMapping("/{id}")
    public R<UserResp> getUserById(@PathVariable("id") Integer id) {
        UserResp userRespById = sysUserService.getUserRespById(id);
        return R.success(userRespById);
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "3.新增用户")
    @PostMapping("/add")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.ADD, desc = "新增用户")
    public R<Boolean> saveUser(@Validated @RequestBody UserReq userReq) {
        Boolean b = sysUserService.saveUser(userReq);
        return R.success(b);
    }


    @ApiOperationSupport(order = 4)
    @Operation(summary = "4.更新用户信息")
    @PostMapping("/update")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "更新用户信息")
    public R<Boolean> updateById(@Validated @RequestBody UserReq userReq) {
        Boolean b = sysUserService.updateUser(userReq);
        return R.success(b);
    }

    /**
     * 解锁用户账号
     *
     * @param id
     * @return
     */
    @ApiOperationSupport(order = 5)
    @Operation(summary = "5.解锁用户")
    @GetMapping("/deblocking/{id}")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "解锁用户")
    public R<Boolean> deblocking(@PathVariable("id") Integer id) {
        Boolean deblocking = sysUserService.deblocking(id);
        return R.success(deblocking);
    }


    @ApiOperationSupport(order = 6)
    @Operation(summary = "6.通过id删除用户")
    @GetMapping("/del/{id}")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除用户")
    public R<Boolean> removeById(@PathVariable Integer id) {

        Boolean b = sysUserService.removeUserById(id);
        return R.success(b);
    }

    /**
     * 修改个人信息
     *
     * @param userDto userDto
     * @return success/false
     */
    //    @DecryptRequest
    //    @EncryptResponse
    @ApiOperationSupport(order = 7)
    @Operation(summary = "7.修改密码")
    @PostMapping("/update_password")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "修改密码")
    public R<Boolean> updateUserPassword(@RequestBody @Valid ModifyUserPasswordReq userDto) {
        Boolean b = sysUserService.updateUserPassword(userDto);
        return R.success(b);
    }


    @ApiOperationSupport(order = 9)
    @Operation(summary = "9.启用、停用用户")
    @PostMapping("/enable")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "启用、停用用户")
    public R<Boolean> enable(@RequestBody UserEnableReq req) {
        Boolean enable = sysUserService.enable(req);
        return R.success(enable);
    }


    @ApiOperationSupport(order = 10)
    @Operation(summary = "10.重置密码")
    @PostMapping("/reset_pwd")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "重置密码")
    public R<Boolean> resetPwd(@RequestBody UserResetPwdReq userDTO) {
        Boolean b = sysUserService.resetPwd(userDTO);
        return R.success(b);
    }

    @ApiOperationSupport(order = 11)
    @Operation(summary = "11.角色授权")
    @PostMapping("/update/role")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "角色授权")
    public R<Boolean> updateRole(@Validated @RequestBody UserRoleReq request) {
        Boolean b = sysUserService.updateRole(request);
        return R.success(b);
    }

    @ApiOperationSupport(order = 12)
    @Operation(summary = "12.批量删除用户")
    @PostMapping("/del")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除用户")
    public R<Boolean> del(@RequestBody @Valid DeleteUserReq req) {
        Boolean del = sysUserService.del(req);
        return R.success(del);
    }


    @ApiOperationSupport(order = 13)
    @Operation(summary = "13.下载模板")
    @PostMapping("/download")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.DOWNLOAD, desc = "用户管理下载模板")
    public void download(HttpServletResponse response) {
        //        String filePath = "template/用户模板.xlsx";
        //        String fileName = "用户模板.xlsx";
        //        ClassPathResource classpathResource = new ClassPathResource(filePath);
        //        InputStream inputStream = null;
        //        try {
        //            inputStream = classpathResource.getInputStream();
        //            FileUtils.downloadFileExcel(response,inputStream,fileName);
        //        } catch (IOException e) {
        //            log.error("download error: {}", e);
        //            throw new BaseBusinessException("下载用户模板出现错误");
        //        }
    }

    @ApiOperationSupport(order = 14)
    @Operation(summary = "14.用户导出")
    @PostMapping("/export")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.EXPORT, desc = "导出用户")
    public void export(HttpServletResponse response, @RequestBody UserReq userReq) throws IOException {
        //        PageModel page = new PageModel();
        //        page.setCurrentPage(1);
        //        page.setCurrentPage(1000000);
        //        R result = this.sysUserService.getUserWithRolePage(page, userDTO, false);
        //        JSONObject jsonObject = JSONUtil.parseObj(result.getData());
        //        JSONArray records = jsonObject.getJSONArray("records");
        //        List<UserVO> userVOList = JSONUtil.toList(records, UserVO.class);
        //        List<ExportUserDTO> exportUserDTOList = this.handleExportUser(userVOList);
        //        try {
        //            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        //            response.setCharacterEncoding("utf-8");
        //            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        //            String fileName = URLEncoder.encode("用户列表", "UTF-8").replaceAll("\\+", "%20");
        //            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        //            // 这里需要设置不关闭流
        //            EasyExcel.write(response.getOutputStream(), ExportUserDTO.class).autoCloseStream(Boolean.FALSE).sheet("数据")
        //                    .doWrite(exportUserDTOList);
        //        } catch (Exception e) {
        //            // 重置response
        //            response.reset();
        //            response.setContentType("application/json");
        //            response.setCharacterEncoding("utf-8");
        //            Map<String, String> map = new HashMap<>(16);
        //            map.put("status", "failure");
        //            map.put("message", "下载文件失败" + e.getMessage());
        //            response.getWriter().println(JSON.toJSONString(map));
        //        }

    }

    @ApiOperationSupport(order = 15)
    @Operation(summary = "21.用户导入")
    @PostMapping("/import")
    @SysLogAnn(module = LogTypeConstants.USER_MANAGEMENT, operateType = OperateTypeConstants.IMPORT, desc = "导入用户")
    public R importUser(@RequestParam("file") MultipartFile file) throws IOException {
        //        // 验证文件格式为xlsx
        //        if (!file.getOriginalFilename().endsWith(".xlsx")) {
        //            return R.failed("上传文件错误");
        //        }
        //        // 查询部门列表
        //        List<SysOrg> orgList = this.sysOrgService.list(Wrappers.emptyWrapper());
        //        Map<String, SysOrg> orgMap = orgList.stream().collect(Collectors.toMap(SysOrg::getName, item -> item, (o, n) -> n));
        //        UserListener userListener = new UserListener(sysUserService, orgMap);
        //        EasyExcel.read(file.getInputStream(), UserImportDTO.class, userListener).sheet().doRead();
        //        // todo 如果需要导出错误数据文件的话，导出这个集合
        //        List<ErrUserImportDTO> errList = userListener.getErrList();
        //        int successCount = userListener.getSuccessCount();
        //        int failCount = userListener.getFailCount();
        //        if (failCount == 0) {
        //            return R.ok(null, "导入成功,添加"+successCount+"条数据");
        //        } else {
        //            log.info("errList: {}", JSON.toJSONString(errList));
        //            // 导出错误文件
        //            String filePath = ExcelErrListWriteUtils.writeErrList(errList, ErrStaffImportDTO.class, errorExportPath);
        //            if (successCount == 0) {
        //                return R.failed(filePath, "导入失败,导入失败"+failCount+"条数据，详见错误文件");
        //            } else {
        //                return R.ok(filePath, "导入成功,其中添加"+successCount+"条数据，导入失败"+failCount+"条数据，详见错误文件");
        //            }
        //        }
        return R.success();

    }


}
