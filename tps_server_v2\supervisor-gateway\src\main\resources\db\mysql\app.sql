CREATE TABLE `app_agent_info` (
                                  `device_id` varchar(25) NOT NULL COMMENT '设备编号',
                                  `device_type` varchar(255) NOT NULL COMMENT '全数字组成的最长为2个字节的字符串，检测器为“01” 终端为"02"',
                                  `soft_version` varchar(32) DEFAULT NULL COMMENT '前八位为年月日，下划线后自定义',
                                  `vendor_name` varchar(32) DEFAULT '' COMMENT '厂商英文名',
                                  `interface` text DEFAULT NULL COMMENT '设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值',
                                  `mem_total` bigint(20) DEFAULT NULL COMMENT '内存总数，表示整个设备的内存大小，单位MB。',
                                  `cpu_info` text DEFAULT NULL COMMENT 'CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。\r\nphysical_id：CPU ID，数值类型\r\ncore：CPU核心数，数值类型；\r\nclock：CPU主频，数值类型精确到小数点后1位\r\n',
                                  `disk_info` text DEFAULT NULL COMMENT '磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。\r\nsize为数值类型，表示磁盘大小，单位GB；\r\nserial为字符串类型，最长64个字节，表示磁盘序列号',
                                  `organs` varchar(255) DEFAULT NULL COMMENT '检测器部署的单位名',
                                  `address` varchar(255) DEFAULT NULL COMMENT '检测器部署的地理位置',
                                  `address_code` varchar(255) DEFAULT NULL COMMENT '行政区域编码类型，表示检测器部署所在地的区域编码。',
                                  `region_path` varchar(255) DEFAULT NULL COMMENT '区域code对应路径',
                                  `contact` text DEFAULT NULL COMMENT '单位联系人信息。\r\nname表示联系人，最长为64个字节的字符串；\r\nemail表示联系人邮件地址，最长为64个字节的字符串；\r\nphone表示联系人电话，最长为32字节，；\r\nposition表示联系人职务，最长为64个字节的字符串；\r\n',
                                  `memo` varchar(500) DEFAULT NULL COMMENT '备注信息',
                                  `register_time` timestamp NOT NULL DEFAULT current_timestamp() COMMENT '注册时间',
                                  `register_status` tinyint(4) NOT NULL DEFAULT 2 COMMENT '注册状态，0 审核通过 1  审核不通过 2 待审核 3 禁用',
                                  `connection_status` tinyint(4) DEFAULT 2 COMMENT '在线状态， 1 在线 2 离线 3 已卸载',
                                  `register_message` varchar(255) DEFAULT NULL COMMENT '注册状态描述',
                                  `verify_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
                                  `uptime` int(10) unsigned DEFAULT 0 COMMENT '开机时间,运行时长，单位秒',
                                  `heartbeat_time` timestamp NULL DEFAULT NULL COMMENT '心跳时间',
                                  `business_time` timestamp NULL DEFAULT NULL COMMENT '业务状态时间',
                                  `device_ca` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '设备ca证书序列号',
                                  `ca_validate_start` datetime DEFAULT NULL COMMENT '设备ca证书有效期起始时间',
                                  `ca_validate_end` datetime DEFAULT NULL COMMENT '设备ca证书序有效期结束时间',
                                  `audit_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '审核方式0人工审核。1自动审核',
                                  `invalid_msg` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '设备备注有效或者无效的原因',
                                  `local_remarks` text CHARACTER SET utf8 DEFAULT NULL COMMENT '本机备注信息，页面输入',
                                  PRIMARY KEY (`device_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='应用系统自监管组件设备信息';

CREATE TABLE `app_business_status` (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `device_id` varchar(25) NOT NULL COMMENT '设备编号',
                                       `uptime` int(11) DEFAULT NULL COMMENT '上次加电运行时长 秒',
                                       `input_file_num` int(11) DEFAULT NULL COMMENT '统计间隔内接入文件数',
                                       `output_file_num` int(11) DEFAULT NULL COMMENT '统计间隔内产生的告警数',
                                       `backlog_file_num` int(11) DEFAULT NULL COMMENT '积压的文件数',
                                       `time` timestamp NULL DEFAULT NULL COMMENT '系统运行状态采集',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='应用系统组件运行状态';

CREATE TABLE `app_suspected_log` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                     `device_id` varchar(25) NOT NULL,
                                     `event_type` tinyint(4) NOT NULL COMMENT '异常类型：\r\n1（系统异常）\r\n2（软件异常）\r\n3（流量异常）\r\n4（策略异常）',
                                     `time` timestamp NULL DEFAULT NULL COMMENT '异常产生时间',
                                     `risk` tinyint(4) DEFAULT NULL COMMENT '告警级别:\r\n0（无风险）\r\n1（一般级）\r\n2（关注级）\r\n3（严重级）\r\n4（紧急级）',
                                     `msg` varchar(255) DEFAULT NULL COMMENT '异常事件描述',
                                     `report` tinyint(4) DEFAULT 0 COMMENT '是否上报',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='APP系统异常日志';

CREATE TABLE `app_system_audit` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
                                    `msg_id` varchar(64) NOT NULL COMMENT '日志ID，不得使用除字母、数字、下划线"_"以外的其他字符，并且保证ID的唯一性不得重复。最长20个字节。',
                                    `device_id` varchar(25) DEFAULT NULL COMMENT '设备编号',
                                    `user` varchar(255) DEFAULT NULL COMMENT '操作用户',
                                    `time` timestamp NULL DEFAULT NULL COMMENT '事件时间',
                                    `event_type` varchar(64) DEFAULT NULL COMMENT '日志类型。\r\n远程系统命令，远程策略更新，本地用户操作，本地系统配置，本地策略配置，其他日志类型',
                                    `opt_type` varchar(64) DEFAULT NULL COMMENT '操作类型。\r\n关机、重启，启动模块、停止模块，更新内置策略，升级固件；添加策略、删除策略、重置策略；用户登陆、用户注销、关机、重启、恢复出厂设置，添加用户、删除用户，导出数据，离线升级；网络配置、路由配置、管理机IP配置、管理系统IP配置、监测器部署信息配置；添加关键词、删除关键词，其他操作类型',
                                    `message` varchar(512) DEFAULT NULL COMMENT '日志详情',
                                    `is_report` tinyint(4) DEFAULT 0 COMMENT '是否上报到管理中心，0：否，1：是 ',
                                    `report_time` timestamp NULL DEFAULT NULL COMMENT '上报到管理中心时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='app系统审计日志';

CREATE TABLE `app_system_status` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `device_id` varchar(25) NOT NULL COMMENT '设备编号',
                                     `cpu` text DEFAULT NULL COMMENT '表示CPU使用率，取0-100的数值，多个CPU以列表方式上传。\r\nphysical_id： CPU ID，数值类型;\r\ncpu_usage：CPU使用率百分比，数值类型，取0-100的数值\r\n',
                                     `mem` int(11) DEFAULT NULL COMMENT '表示内存利用率，取0-100数值',
                                     `disk` int(11) DEFAULT NULL COMMENT '表示数据磁盘整体可用空间，单位GB',
                                     `time` timestamp NULL DEFAULT NULL COMMENT '系统运行状态采集',
                                     `did` int(11) DEFAULT NULL COMMENT '当检测器由多台服务器组成时，表示服务器的编号',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='应用系统组件运行状态';


CREATE TABLE `sys_ip_whitelist` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `name` varchar(100) NOT NULL COMMENT '名称',
                                    `ip` varchar(1024) NOT NULL COMMENT 'ip地址范围',
                                    `type` tinyint(4) NOT NULL COMMENT '访问控制类型，1：系统访问控制；2：通信访问控制 3 下载服务控制',
                                    `enabled` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否启用',
                                    `create_time` timestamp NULL DEFAULT current_timestamp() COMMENT '创建时间',
                                    `update_time` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '修改时间',
                                    `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
                                    `start_ip_long` bigint(20) NOT NULL COMMENT 'ip2long 开始',
                                    `end_ip_long` bigint(20) NOT NULL COMMENT 'ip2long 终止',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    KEY `ip_range` (`start_ip_long`,`end_ip_long`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='访问控制白名单';


CREATE TABLE `d_terminal_agent_info` (
                                         `device_id` varchar(25)  NOT NULL COMMENT '设备编号',
                                         `soft_version` varchar(32)  DEFAULT NULL COMMENT '前八位为年月日，下划线后自定义',
                                         `interface` text  DEFAULT NULL COMMENT '设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值',
                                         `mem_total` bigint(20) NOT NULL DEFAULT 0 COMMENT '内存总数，表示整个设备的内存大小，单位MB。',
                                         `cpu_info` text  DEFAULT NULL COMMENT 'CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。\r\nphysical_id：CPU ID，数值类型\r\ncore：CPU核心数，数值类型；\r\nclock：CPU主频，数值类型精确到小数点后1位\r\n',
                                         `disk_info` text  DEFAULT NULL COMMENT '磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。\r\nsize为数值类型，表示磁盘大小，单位GB；\r\nserial为字符串类型，最长64个字节，表示磁盘序列号',
                                         `org_id` int(11) NOT NULL COMMENT '部门id',
                                         `ip` varchar(255)  DEFAULT NULL COMMENT 'ip地址',
                                         `ip_long` bigint(20) DEFAULT NULL COMMENT 'ip转为long',
                                         `mac` varchar(255)  DEFAULT NULL COMMENT 'mac地址',
                                         `audit_type` tinyint(4) DEFAULT 2 COMMENT '1 自动审核 2人工审核',
                                         `region_path` varchar(255)  DEFAULT NULL COMMENT '区域code对应路径',
                                         `verify_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
                                         `heartbeat_time` timestamp NULL DEFAULT NULL COMMENT '心跳时间',
                                         `last_upgrade_time` timestamp NULL DEFAULT NULL COMMENT '最后升级时间',
                                         `user_id` varchar(36)  NOT NULL DEFAULT '' COMMENT '使用人编号',
                                         `user_name` varchar(500)  DEFAULT '' COMMENT '使用人姓名',
                                         `host_name` varchar(255)  DEFAULT '' COMMENT '主机名称',
                                         `os` varchar(255)  DEFAULT '' COMMENT '主机操作系统',
                                         `arch` varchar(255)  DEFAULT '' COMMENT '主机CPU架构',
                                         `register_time` timestamp NOT NULL DEFAULT current_timestamp() COMMENT '注册时间',
                                         `register_status` tinyint(4) DEFAULT 2 COMMENT '终端状态，0 审核通过 1  审核不通过 2 待审核 3 禁用',
                                         `register_message` varchar(255)  DEFAULT NULL COMMENT '注册状态描述',
                                         `memo` varchar(500)  DEFAULT NULL COMMENT '备注信息',
                                         `connection_status` tinyint(4) NOT NULL DEFAULT 2 COMMENT '在线状态， 1 在线 2 离线 3 已卸载',
                                         `activate_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '激活状态， 1 未激活 2 已激活',
                                         PRIMARY KEY (`device_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='终端设备信息';