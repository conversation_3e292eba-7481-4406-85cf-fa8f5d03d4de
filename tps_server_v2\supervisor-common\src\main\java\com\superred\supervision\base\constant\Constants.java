package com.superred.supervision.base.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/6/15 16:08
 **/
public final class Constants {

    private Constants() {
    }

    public static final String TERMINAL_TYPE_TASK = "task";

    public static final String SRC_DEVICE = "src_device";

    public static final String ID = "id";

    public static final String TERMINAL_TYPE_TASK_CHECK = "check";

    public static final String SOURCE_INNER = "inner";

    public static final String INNER_POLICY_UPDATE = "inner_policy_update";
    public static final String AGENT_DEVICE_UNINSTALL = "agent_device_uninstall";
    public static final String AGENT_FILE_TRACE = "agent_file_trace";
    public static final String AGENT_FILE_TRACE_TERMINATE = "agent_file_trace_terminate";
    public static final String AGENT_DEVICE_REGISTER_INFO_UPDATE = "agent_device_register_info_update";

    public static final String SOURCE_CENTER = "center";

    public static final String CMD_DEL = "del";
    public static final String CMD_ADD = "add";
    public static final String CMD_RESET = "reset";

    public static final String RULE_MODULE_KEYWORD = "keyword_detect";
    public static final String RULE_MODULE_MD5 = "file_md5";
    public static final String RULE_MODULE_BLACK_LIST_IP = "ip_blacklist";
    public static final String RULE_MODULE_BLACK_LIST_DOMAIN = "domain_blacklist";


    public static final String CENTER_FILE_HEAD = "---------------------------7d81741d1803de--------------\r\n";

    public static final String ZIP_TYPE_GZ = "GZ";

    /**
     * 应用系统自监管组件
     */
    public static final String DEVICE_TYPE_DETECTOR = "01";

    public static final String DEVICE_TYPE_TERMINAL = "05";

    public static final String DEVICE_TYPE_MANAGER = "03";
    /**
     * 注册成功
     */
    public static final Integer REG_STATUS_YES = 0;

    /**
     * 在线
     */
    public static final Integer REG_STATUS_ONLINE = 6;

    public static final String REG_STATUS_YES_DESC = "注册成功";
    public static final String AGENT_REG_STATUS_YES_DESC = "成功";

    /**
     * 审核中
     */
    public static final Integer REG_STATUS_AUDIT = 2;


    public static final String REG_STATUS_AUDIT_DESC = "审核中";
    /**
     * 注册失败
     */
    public static final Integer REG_STATUS_NO = 1;

    public static final String REG_STATUS_NO_DESC = "注册失败";
    public static final String AGENT_REG_STATUS_NO_DESC = "失败";

    /**
     * 已下发
     */
    public static final Integer ISSUED_STATUS_YES = 1;
    /**
     * 未下发
     */
    public static final Integer ISSUED_STATUS_NO = 0;

    /**
     * 管理系统下发 1是  0否
     */

    public static final Integer MGR_YES = 1;

    public static final Integer MGR_NO = 0;
    /**
     * 上报到管理系统
     */
    public static final Integer REPORT_MGR_YES = 1;

    /**
     * 未上报的管理系统
     */
    public static final Integer REPORT_MGR_NO = 0;

    /**
     * 上报管理系统失败
     */
    public static final Integer REPORT_MGR_ERROR = 2;

    /**
     * 命令类型——策略命令
     */
    public static final String COMMAND_TYPE_POLICY = "policy";
    /**
     * 命令类型——指令
     */
    public static final String COMMAND_TYPE_CMD = "command";
    /**
     * 命令类型——插件
     */
    public static final String COMMAND_TYPE_PLUG = "plug";
    //关机
    public static final String CMD_SHUTDOWN = "shutdown";
    //重启
    public static final String CMD_REBOOT = "reboot";
    //启用模块
    public static final String CMD_STARTM = "startm";
    //停止模块
    public static final String CMD_STOPM = "stopm";

    public static final String CMD_STARTM_INNER = "startm_inner";
    //停止模块
    public static final String CMD_STOPM_INNER = "stopm_inner";
    public static final String CMD_AUTOM = "autom";
    /**
     * 策略上报指令下发
     */
    public static final String CMD_REPORT_POLICY = "report_policy";

    /**
     * 检查结果 一致 1
     */
    public static final Integer VERSION_CHECK_YES = 1;
    /**
     * 检查结果 不一致 0
     */
    public static final Integer VERSION_CHECK_NO = 0;


    /**
     * 积压数据删除命令
     */
    public static final String CMD_DROPDATA = "dropdata";
    /**
     * 时间同步
     */
    public static final String CMD_SYNCTIME = "sync_time";
    /**
     * 固件升级
     */
    public static final String CMD_UPDATE = "update";
    /**
     * 版本一致性检查
     */
    public static final String CMD_VERSION_CHECK = "version_check";
    /**
     * 内置策略更新
     */
    public static final String CMD_INNER_POLICY_UPDATE = "inner_policy_update";


    /**
     * 内置规则启停
     */
    public static final String CMD_CTRL_INNER_POLICY = "ctrl_inner_policy";


    /**
     * 威胁预警处置命令
     */
    public static final String CMD_WARNING_DISPOSE = "warning_dispose";

    /**
     * 4.3.13设备部署信息修改命令
     */
    public static final String CMD_DEVICE_SYNC = "device_sync";


    /**
     * 4.3.14接入设备信息上报命令
     */
    public static final String CMD_DEVICE_REPORT = "device_report";


    public static final String CMD_CERT_UPDATE = "cert_update";

    /**
     * web管理用户密码重置
     */
    public static final String CMD_PASSWD = "passwd";


    public static final String EFFECT_ALL = "1";
    public static final String EFFECT_DEVICE = "2";
    public static final String EFFECT_MANAGE = "3";

    public static final Integer REPORT_YES = 0;

    public static final Integer REPORT_NO = 1;


    public static final Integer RESULT_YES = 0;
    public static final Integer RESULT_NO = 1;


    public static final String SHELL_CMD_SYNC_TIME = "update_time.sh";
    public static final String SHELL_CMD_SHUTDOWN = "dac_shutdown.sh";
    public static final String SHELL_CMD_REBOOT = "dac_reboot.sh";
    public static final String SHELL_CMD_UPDATE = "dac_update.sh";
    public static final String SHELL_CMD_POLICY_APPLY = "dac_stop.sh";
    public static final String SHELL_PLUG_ADD = "dac_plug_add.sh";
    public static final String SHELL_PLUG_POLICY_ADD = "dac_plug_policy_add.sh";
    public static final String SHELL_PLUG_UPDATE = "dac_plug_update.sh";
    public static final String SHELL_PLUG_POLICY_UPDATE = "dac_plug_policy_update.sh";
    public static final String SHELL_PLUG_DEL = "dac_plug_del.sh";
    public static final String SHELL_PLUG_START = "dac_plug_start.sh";
    public static final String SHELL_PLUG_STOP = "dac_plug_stop.sh";

    public static final String CHECK_CONFIG_TYPE_WHITE = "white";

    public static final String CHECK_CONFIG_TYPE_BLACK = "black";

    public static final String CHECK_CONFIG_TYPE_SERVICE = "service";
    public static final String CHECK_CONFIG_TYPE_PROCESS = "process";
    public static final String CHECK_CONFIG_TYPE_PORT = "port";


    public static final Map<String, String> MODULE_TYPE_MAP = new HashMap<>(16);

    static {
        MODULE_TYPE_MAP.put("trojan", "alarm");
        MODULE_TYPE_MAP.put("attack", "alarm");
        MODULE_TYPE_MAP.put("malware", "alarm");
        MODULE_TYPE_MAP.put("ip_blacklist", "alarm");
        MODULE_TYPE_MAP.put("domain_blacklist", "alarm");
        MODULE_TYPE_MAP.put("url_blacklist", "alarm");
        MODULE_TYPE_MAP.put("account_blacklist", "alarm");
        MODULE_TYPE_MAP.put("abnormal", "alarm");
        MODULE_TYPE_MAP.put("alarm_whitelist", "alarm");
        MODULE_TYPE_MAP.put("inner_policy", "alarm");
        MODULE_TYPE_MAP.put("file_keyword", "sensitive");
        MODULE_TYPE_MAP.put("file_layout", "sensitive");
        MODULE_TYPE_MAP.put("file_seal", "sensitive");
        MODULE_TYPE_MAP.put("file_hash", "sensitive");
        MODULE_TYPE_MAP.put("sensitive_whitelist", "sensitive");
        MODULE_TYPE_MAP.put("file_encryption", "file_filter");
        MODULE_TYPE_MAP.put("file_attributes", "file_filter");
        MODULE_TYPE_MAP.put("net_audit", "net_audit");
        MODULE_TYPE_MAP.put("net_log_upload", "net_audit");
        MODULE_TYPE_MAP.put("app_behavior_upload", "net_audit");
        MODULE_TYPE_MAP.put("unknown_protocol", "net_audit");
        MODULE_TYPE_MAP.put("encrypt_protocol", "net_audit");
        MODULE_TYPE_MAP.put("ip_listen", "object_listen");
        MODULE_TYPE_MAP.put("domain_listen", "object_listen");
        MODULE_TYPE_MAP.put("target_ident", "target_ident");


    }

    public static final String MODULE_ALARM = "alarm";
    public static final String MODULE_SENSITIVE = "sensitive";

    public static final String MODULE_FILE_SELECTION = "file_filter";

    public static final String MODULE_FILE_SELECTION_KEYWORD = "keyword_filter";
    public static final String MODULE_FILE_SELECTION_ACCOUNT = "account_filter";
    public static final String MODULE_FILE_SELECTION_ENCRYPTION = "encryption_filter";
    public static final String MODULE_FILE_MD5_FILTER = "md5_filter";
    public static final String MODULE_FILE_SECURITY_CLASSIFICATION_LEVEL = "security_classification_level";
    public static final String MODULE_FILE_SECRET_LEVEL_FILTER = "secret_level_filter";

    public static final String MODULE_OBJECT_LISTEN = "object_listen";

    public static final String MODULE_ACTIVE_OBJECT = "active_object_audit";

    public static final String MODULE_ACTIVE_OBJECT_DEVICE = "active_object_device";
    public static final String MODULE_ACTIVE_OBJECT_APP = "active_object_app";
    public static final String MODULE_ACTIVE_OBJECT_ACCOUNT = "active_object_account";

    public static final String LISTEN_IP_LISTEN = "ip_listen";

    public static final String LISTEN_DOMAIN_LISTEN = "domain_listen";

    public static final String MODULE_NET_AUDIT = "net_audit";

    public static final String AUDIT_NET_LOG = "net_log";

    public static final String AUDIT_APP_BEHAVIOR = "app_behavior";

    public static final String AUDIT_APP_BEHAVIOR_EXTEND = "app_behavior_extend";

    public static final String AUDIT_UNKNOWN_PROTOCOL = "unknown_protocol";

    public static final String AUDIT_ENCRYPT_PROTOCOL = "encrypt_protocol";


    public static final String MODULE_TARGET_IDENT = "active_object_audit";

    public static final String TARGET_IDENT_APPS = "apps";
    public static final String TARGET_IDENT_ACCOUNTS = "accounts";
    public static final String TARGET_IDENT_DEVICES = "devices";
    /**
     * 木马攻击
     */
    public static final String ALARM_TROJAN = "trojan";
    /**
     *
     */
    public static final String ALARM_ATTACK = "attack";
    public static final String ALARM_MALWARE = "malware";
    public static final String ALARM_IP_BLACKLIST = "ip_blacklist";
    public static final String ALARM_DOMAIN_BLACKLIST = "domain_blacklist";
    public static final String ALARM_URL_BLACKLIST = "url_blacklist";
    public static final String ALARM_ACCOUNT_BLACKLIST = "account_blacklist";
    public static final String ALARM_ABNORMAL = "abnormal";
    public static final String ALARM_EXTENDED = "extended";

    public static final String SENSITIVE = "sensitive";

    public static final String DISPOSE = "dispose";

    public static final String SENSITIVE_SENSITIVE_FILE = "sensitive_file";
    public static final String SENSITIVE_FILE_KEYWORD = "file_keyword";
    public static final String SENSITIVE_FILE_STYLE = "file_style";
    public static final String SENSITIVE_FILE_COVER = "file_cover";
    public static final String SENSITIVE_FILE_SEAL = "file_seal";
    public static final String SENSITIVE_FILE_HASH = "file_hash";
    public static final String SENSITIVE_EXTENDED = "extended";

    public static final String SENSITIVE_KEYWORD_DETECT = "keyword_detect";
    public static final String SENSITIVE_FILE_MD5 = "file_md5";

    public static final String AGENT_KEYWORD_DETECT_ALERT = "agent_keyword_detect_alert";
    public static final String AGENT_ALARM_DOMAIN_BLACKLIST = "agent_alarm_domain_blacklist_alert";
    public static final String AGENT_ALARM_IP_BLACKLIST = "agent_alarm_ip_blacklist_alert";


    public static final Integer TYPE_INNER = 0;

    public static final Integer TYPE_LOCAL = 1;

    public static final Integer TYPE_MGR = 2;

    /**
     * 内置规则和本级规则产生的文件存放位置
     */
    public static final String MINIO_FILE = "file";

    public static final String MINIO_TASK = "task";

    public static final String MINIO_AUDIT = "net-audit";
    /**
     * 上级策略下发产生的文件存放位置
     */
    public static final String MINIO_UP_FILE = "up-file";
    /**
     * 内置规则和本级规则产生的pcap文件存放位置
     */
    public static final String MINIO_PCAP = "pcap";
    /**
     * 上级策略下发产生的pcap文件存放位置
     */
    public static final String MINIO_UP_PCAP = "up-pcap";

    /**
     * 终端异常通信告警存放位置
     */
    public static final String MINIO_AGENT_FILE = "agent-file";

    /**
     * 终端异常通信告警存放位置
     */
    public static final String MINIO_AGENT_UP_FILE = "agent-up-file";

    /**
     * 终端内置策略
     */
    public static final String AGENT_BUILD_IN_POLICY_BUCKET = "agent-built-in-policy";
    /**
     * 终端软件包
     */
    public static final String AGENT_SOFTWARE_BUCKET = "agent-software";
    /**
     * 监测器内置策略
     */
    public static final String DETECTOR_BUILT_IN_POLICY_BUCKET = "detector-built-in-policy";
    /**
     * 监测器升级包
     */
    public static final String DETECTOR_SOFTWARE_BUCKET = "detector-software";
    /**
     * 监测器通信证书
     */
    public static final String DETECTOR_COMMUNICATION_CERT_BUCKET = "detector-communication-cert";
    /**
     * 管理系统下发威胁预警指令携带的附件
     */
    public static final String ATTACHMENT_FILE_BUCKET = "attachment-file";


    /**
     * 注册
     */
    public static final String TYPE_ADD = "add";
    /**
     * 重新注册
     */
    public static final String TYPE_UPDATE = "update";
    /**
     * 开关 yes
     */
    public static final String YES = "1";
    /**
     * 开关 no
     */
    public static final String NO = "0";

    /**
     * 待审核
     */
    public static final Integer DEVICE_REPORT_STATUS_0 = 0;
    /**
     * 待认证
     */
    public static final Integer DEVICE_REPORT_STATUS_1 = 1;

    /**
     * 在线
     */
    public static final Integer DEVICE_REPORT_STATUS_2 = 2;
    /**
     * 离线
     */
    public static final Integer DEVICE_REPORT_STATUS_3 = 3;
    /**
     * 无效
     */
    public static final Integer DEVICE_REPORT_STATUS_4 = 4;
    /**
     * 已删除
     */
    public static final Integer DEVICE_REPORT_STATUS_5 = 5;


    /**
     * 统计延时的时间
     */
    public static final Integer REPORT_DELAY_TIME = 10 * 1000;

    /**
     * 终端检查任务文件的密码
     */
    public static final String TASK_FILE_PASSWORD = "123456";
}
