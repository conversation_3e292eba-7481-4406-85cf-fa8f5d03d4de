package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月13日
 */
@Data
public class SuperSysConfigResp {
    @Schema(description = "主键")
    private Integer id;

    @Schema(description = "上级系统标识  1监管数据上报  2自监管数据上报")
    private String sysFlag;

    @Schema(description = "上级中心名称")
    private String name;

    @Schema(description = "ip地址")
    @NotNull(message = "ip 不能为空")
    @Pattern(regexp = "^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)$",
            message = "ip 格式错误")
    private String ip;

    @Schema(description = "端口")
    private Integer port;


    @Schema(description = "状态  0停用  1启用")
    private Integer state;
}
