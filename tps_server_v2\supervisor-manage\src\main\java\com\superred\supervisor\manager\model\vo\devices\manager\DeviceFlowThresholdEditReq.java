package com.superred.supervisor.manager.model.vo.devices.manager;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 修改流量阈值
 *
 * <AUTHOR>
 * @since 2025/3/12 17:03
 */

@Data
public class DeviceFlowThresholdEditReq {

    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */

    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    @NotBlank(message = "设备编号不能为空")
    private String deviceId;


    /**
     * 流量阈值 数据库默认是10 单位 Mbps
     */
    @Schema(description = "流量阈值 数据库默认是10 单位 Mbps")
    @NotNull(message = "流量阈值不能为空")
    private Integer flowThreshold;
}
