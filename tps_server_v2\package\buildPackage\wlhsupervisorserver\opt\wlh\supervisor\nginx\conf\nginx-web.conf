
    server {
        listen      12300 ssl;
        enable_ntls  on;
        server_name  localhost;
        access_log      /opt/wlh/platform/lib/nginx/logs/nginx-12300.log gmsslog;

        ssl_trusted_certificate		    /opt/wlh/supervisor/nginx/cert/chain-ca.crt;

        ssl_sign_certificate			/opt/wlh/supervisor/nginx/cert/web/server_sign.crt;
        ssl_sign_certificate_key		/opt/wlh/supervisor/nginx/cert/web/server_sign.key;

        ssl_enc_certificate		    /opt/wlh/supervisor/nginx/cert/web/server_enc.crt;
        ssl_enc_certificate_key		/opt/wlh/supervisor/nginx/cert/web/server_enc.key;

        ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;
        ssl_verify_depth		1;
        ssl_ciphers "ECC-SM2-SM4-CBC-SM3:ECC-SM2-SM4-GCM-SM3:ECDHE-SM2-SM4-CBC-SM3:ECDHE-SM2-SM4-GCM-SM3:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-SHA:AES128-GCM-SHA256:AES128-SHA256:AES128-SHA:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES256-SHA:AES256-GCM-SHA384:AES256-SHA256:AES256-SHA:ECDHE-RSA-AES128-SHA256:!aNULL:!eNULL:!RC4:!EXPORT:!DES:!3DES:!MD5:!DSS:!PKS";
        ssl_prefer_server_ciphers  on;

        ssl_session_cache		shared:SSL:1m;
        ssl_session_timeout  10m;

        location / {
             try_files $uri $uri/ /index.html;
             root   /opt/wlh/supervisor/nginx/html/zjg;
             index  index.html index.htm;
            }

        location ^~ /api {
                proxy_pass http://127.0.0.1:8083;
                proxy_set_header Host $http_host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }


    server {
            listen      12400;
            server_name  localhost;
            access_log  /opt/wlh/platform/lib/nginx/logs/nginx-12400.log;

            location / {
             root   /opt/wlh/supervisor/nginx/html/zjg;
             index  index.html index.htm;
             try_files $uri $uri/ /index.html;
            }

            location ^~ /api {
                    proxy_pass http://127.0.0.1:8083;
                    proxy_set_header Host $http_host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
            }

            error_page   500 502 503 504  /50x.html;
            location = /50x.html {
                    root   html;
            }
    }

