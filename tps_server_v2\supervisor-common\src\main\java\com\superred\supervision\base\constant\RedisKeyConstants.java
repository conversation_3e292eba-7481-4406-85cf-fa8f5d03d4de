package com.superred.supervision.base.constant;

/**
 * <AUTHOR>
 * @create 2023-11-28 14:03
 */
public final class RedisKeyConstants {

    private RedisKeyConstants() {
    }

    /**
     * api，redis记录缓存前缀
     */
    public static final String API_PREFIX = "cache:api:";

    /**
     * report redis记录缓存前缀
     */
    public static final String REPORT_PREFIX = "cache:report:";

    /**
     * 上级管理系统sessionid key
     */
    public static final String MGR_SESSION_ID = REPORT_PREFIX + "mgr:sessionId";

    /**终端信息redis key**/
    public static final String AGENT_DEVICE_INFO_CACHE_KEY = "agent_device_info:";
    /**监测器信息redis key**/
    public static final String DEVICE_INFO_CACHE_KEY = "device_info:";

    /**终端消息redus key**/
    public static final String AGENT_MSG_CACHE_KEY = "agent_msg:";
}
