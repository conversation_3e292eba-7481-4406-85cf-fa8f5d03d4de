package com.superred.supervisor.common.repository.system;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.system.SysUserUkey;
import com.superred.supervisor.common.mapper.system.SysUserUkeyMapper;
import org.springframework.stereotype.Repository;

/**
 * 用户绑定ukey Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:23
 */
@Repository
public class SysUserUkeyRepository extends ServiceImpl<SysUserUkeyMapper, SysUserUkey> {

}

