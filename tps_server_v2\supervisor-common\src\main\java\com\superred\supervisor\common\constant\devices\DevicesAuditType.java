package com.superred.supervisor.common.constant.devices;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * 审核方式 1 自动审核 2人工审核
 * <AUTHOR>
 * @since 2025/3/18 13:57
 */
@Getter
@AllArgsConstructor
public enum DevicesAuditType implements IEnum<Integer> {

    /**
     * 自动审核
     */
    AUTO(1, "自动审核"),
    /**
     * 人工审核
     */
    MANUAL(2, "人工审核");

    private final Integer value;
    private final String desc;
}
