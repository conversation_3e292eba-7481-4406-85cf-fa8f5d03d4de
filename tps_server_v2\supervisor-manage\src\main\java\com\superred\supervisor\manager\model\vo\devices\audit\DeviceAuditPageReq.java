package com.superred.supervisor.manager.model.vo.devices.audit;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2025-03-11 16:54
 */
@EqualsAndHashCode(callSuper = true)
@Data

public class DeviceAuditPageReq extends PageReqDTO {


    @Schema(description = "注册时间start")
    private LocalDateTime regTimeStart;


    @Schema(description = "注册时间end")
    private LocalDateTime regTimeEnd;

    /**
     * 注册状态，0（成功），1（失败），2（审核中） 3离线；4无效；5已删除 6 在线
     */
    @Schema(description = "注册状态，0（成功），1（失败），2（审核中） 3离线；4无效；5已删除 6 在线")
    private Integer regStatus;


}
