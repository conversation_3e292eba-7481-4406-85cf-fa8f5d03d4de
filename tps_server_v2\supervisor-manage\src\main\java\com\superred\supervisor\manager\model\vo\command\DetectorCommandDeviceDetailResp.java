package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 指令设备详情
 */
@Data
public class DetectorCommandDeviceDetailResp {

    @Schema(description = "指令id")
    private String cmdId;

    @Schema(description = "设备编号")
    private String deviceId;

    @Schema(description = "部署区域")
    private String address;

    @Schema(description = "生产厂商")
    private String vendorName;

    @Schema(description = "执行结果，取值为：0（成功）、1（失败）")
    private Integer result;

    @Schema(description = "执行结果描述，result为1，说明失败原因")
    private String message;

    @Schema(description = "指令详情")
    private String detail;

    @Schema(description = "是否在线：1在线，0离线")
    private Integer isOnline;

    @Schema(description = "下发状态：0未下发，1已下发")
    private Integer status;

    @Schema(description = "版本一致性检查结果：0不一致，1一致")
    private Integer checkResult;

    @Schema(description = "指令类型")
    private String cmd;
}