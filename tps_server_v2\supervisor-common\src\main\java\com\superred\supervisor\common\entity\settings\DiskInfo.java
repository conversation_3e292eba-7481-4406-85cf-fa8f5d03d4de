package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 磁盘信息(LocalDiskInfo) 实体
 *
 * <AUTHOR>
 * @since 2025-03-27 14:55:28
 */
@Data
@TableName("local_disk_info")
public class DiskInfo {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 磁盘大小，单位GB
     */
    @TableField("size")
    private Integer size;

    /**
     * 磁盘序列号，“ST1000NM0012”
     */
    @TableField("serial")
    private String serial;

    /**
     * 当由多台服务器组成时，表示服务器的编号
     */
    @TableField("did")
    private Integer did;

    /**
     * 表示数据磁盘整体可用空间，单位GB
     */
    @TableField("disk")
    private Integer disk;

    /**
     * 数据磁盘整体空间，单位GB
     */
    @TableField("disk_total")
    private Integer diskTotal;

}

