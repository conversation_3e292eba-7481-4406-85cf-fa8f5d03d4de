package com.superred.supervisor.manager.model.vo.policy;

import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-03-28 15:17
 */
@Data

public class RuleRiskSoftwareReq {

    private static final long serialVersionUID = 1L;

    @Schema(description = "策略ID")
    private String ruleId;

    @Schema(description = "策略名称")
    private String ruleName;

    @Schema(description = "策略内容")
    @ByteSize(max = 128, message = "策略内容长度不能超过128个字节")
    private String ruleContent;

    @Schema(description = "策略描述")
    private String ruleDesc;

    @Schema(description = "是否共享")
    private String isShare;

    @Schema(description = "规则应用状态，0未应用，1已应用")
    private String status;

    @Schema(description = "策略来源 1 本级 2上级")
    private String ruleSource;

    @Schema(description = "规则更新时间")
    private String updateTime;

    @Schema(description = "扩展字段1")
    private Long ext1;

    @Schema(description = "扩展字段2")
    private String ext2;

    @Schema(description = "扩展字段3")
    private String ext3;

    @Schema(description = "策略创建时间")
    private String createTime;

    @Schema(description = "开始时间")
    private String startDate;

    @Schema(description = "结束时间")
    private String endDate;

}
