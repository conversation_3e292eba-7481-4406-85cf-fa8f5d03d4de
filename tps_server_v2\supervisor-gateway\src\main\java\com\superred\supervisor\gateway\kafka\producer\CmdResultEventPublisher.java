package com.superred.supervisor.gateway.kafka.producer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *  终端指令响应结果、策略响应结果
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CmdResultEventPublisher {


    @Resource
    private KafkaProducerService kafkaProducerService;


    @Value("${spring.kafka.cmd-result.topic:cmd-result-request-msg}")
    private String topic;


    public void sendMessage(String jsonMsg) {
        kafkaProducerService.sendMessage(jsonMsg, topic);
    }


}
