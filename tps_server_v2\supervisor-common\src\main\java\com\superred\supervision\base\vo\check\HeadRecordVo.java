package com.superred.supervision.base.vo.check;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/8/30 20:14
 */
@Data
@ToString
@Slf4j
public class HeadRecordVo {

    private String checkGUID;

    private String taskTargetName;

    private String taskTargetDepartment;

    private String taskTargetPerson;
    private String position;
    private String taskCheckPerson;
    private String date;
    private String region;
    private String city;
    private String area;
    private String supName;
    private String secretLevel;
    private String equipmentType;
    private String remark;
    private String reserved;

    public void build(FieldVo fieldVo) {

        try {
            ReflectUtil.setFieldValue(this, StrUtil.lowerFirst(fieldVo.getName()), fieldVo.getValue());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
