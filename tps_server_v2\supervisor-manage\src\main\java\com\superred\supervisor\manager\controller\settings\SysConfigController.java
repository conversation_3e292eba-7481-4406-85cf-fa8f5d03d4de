package com.superred.supervisor.manager.controller.settings;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.settings.CertificateReq;
import com.superred.supervisor.manager.model.vo.settings.CertificateResp;
import com.superred.supervisor.manager.model.vo.settings.DeviceNetworkReq;
import com.superred.supervisor.manager.model.vo.settings.DeviceNetworkResp;
import com.superred.supervisor.manager.model.vo.settings.IpConfigAddReq;
import com.superred.supervisor.manager.model.vo.settings.IpConfigResp;
import com.superred.supervisor.manager.model.vo.settings.IpConfigUpdateReq;
import com.superred.supervisor.manager.model.vo.settings.IpWhiteListEnableReq;
import com.superred.supervisor.manager.model.vo.settings.IpWhiteListPageReq;
import com.superred.supervisor.manager.model.vo.settings.IpWhiteListReq;
import com.superred.supervisor.manager.model.vo.settings.IpWhiteListResp;
import com.superred.supervisor.manager.model.vo.settings.LocalDeviceInfoReq;
import com.superred.supervisor.manager.model.vo.settings.LocalDeviceInfoResp;
import com.superred.supervisor.manager.model.vo.settings.MgrCenterInfoReq;
import com.superred.supervisor.manager.model.vo.settings.MgrCenterInfoResp;
import com.superred.supervisor.manager.model.vo.settings.OpsPersonPageReq;
import com.superred.supervisor.manager.model.vo.settings.OpsPersonReq;
import com.superred.supervisor.manager.model.vo.settings.OpsPersonResp;
import com.superred.supervisor.manager.model.vo.settings.RouteInfoPageReq;
import com.superred.supervisor.manager.model.vo.settings.RouteInfoReq;
import com.superred.supervisor.manager.model.vo.settings.RouteInfoResp;
import com.superred.supervisor.manager.model.vo.settings.TerminalPermissionReq;
import com.superred.supervisor.manager.model.vo.settings.TerminalPermissionResp;
import com.superred.supervisor.manager.service.settings.CertificateManagerService;
import com.superred.supervisor.manager.service.settings.DeviceNetworkService;
import com.superred.supervisor.manager.service.settings.IpConfigService;
import com.superred.supervisor.manager.service.settings.IpWhiteListService;
import com.superred.supervisor.manager.service.settings.LocalDeviceInfoService;
import com.superred.supervisor.manager.service.settings.MgrCenterInfoService;
import com.superred.supervisor.manager.service.settings.RouteInfoService;
import com.superred.supervisor.manager.service.settings.SysOpsPersonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * 系统配置
 * @since 2025年03月12日
 */

@Tag(name = "5.1 系统配置-配置管理")
@RestController
@RequestMapping("/sys/config")
@Slf4j
@SuppressWarnings("MethodCount")
public class SysConfigController {


    @Resource
    DeviceNetworkService deviceNetworkService;

    @Resource
    RouteInfoService routeInfoService;

    @Resource
    IpWhiteListService ipWhiteListService;

    @Resource
    SysOpsPersonService sysOpsPersonService;

    @Resource
    CertificateManagerService certificateManagerService;

    @Resource
    LocalDeviceInfoService localDeviceInfoService;

    @Resource
    private MgrCenterInfoService mgrCenterInfoService;

    @Resource
    private IpConfigService ipConfigService;


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1.获取网卡信息")
    @GetMapping("/network")
    public R<List<DeviceNetworkResp>> getNetworkInfo() {
        List<DeviceNetworkResp> networkInfo = deviceNetworkService.getNetworkInfo();

        return R.success(networkInfo);
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2.更新网卡信息")
    @PostMapping("/network/update")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "更新网卡信息")
    public R<Boolean> updateNetworkInfo(@Validated @RequestBody DeviceNetworkReq deviceNetwork) {
        Boolean b = deviceNetworkService.updateNetworkInfo(deviceNetwork);
        return R.success(b);
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "3.获取路由信息(分页)")
    @PostMapping("/route/page")
    public RPage<RouteInfoResp> getRouteInfoByPage(@RequestBody @Valid RouteInfoPageReq req) {
        IPage<RouteInfoResp> routeInfoByPage = routeInfoService.getRouteInfoByPage(req);
        return new RPage<>(routeInfoByPage);
    }

    @ApiOperationSupport(order = 4)
    @Operation(summary = "4.获取路由信息(根据id)")
    @GetMapping("/route/{id}")
    public R<RouteInfoResp> getRouteInfoById(@PathVariable("id") Integer id) {
        RouteInfoResp routeInfoById = routeInfoService.getRouteInfoById(id);
        return R.success(routeInfoById);
    }


    @ApiOperationSupport(order = 4)
    @Operation(summary = "4.添加路由")
    @PostMapping("/route/add")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.ADD, desc = "新增路由")
    public R<Boolean> addRouteInfo(@RequestBody @Valid RouteInfoReq routeInfoReq) {
        Boolean b = routeInfoService.addRouteInfo(routeInfoReq);
        return R.success(b);
    }

    @ApiOperationSupport(order = 5)
    @Operation(summary = "5.更新路由信息")
    @PostMapping("/route/update")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "编辑路由信息")
    public R<Boolean> updateRouteInfo(@RequestBody RouteInfoReq routeInfoReq) {
        Boolean b = routeInfoService.updateRouteInfo(routeInfoReq);
        return R.success(b);
    }

    @ApiOperationSupport(order = 6)
    @Operation(summary = "6.删除路由信息")
    @PostMapping("/route/delete/{id}")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.DELETE, desc = "删除路由信息")
    public R<Boolean> deleteRouteInfo(@PathVariable("id") Integer id) {
        Boolean b = routeInfoService.deleteRouteInfo(id);
        return R.success(b);
    }


    @ApiOperationSupport(order = 7)
    @Operation(summary = "7.访问控制白名单新增")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.ADD, desc = "新增访问控制白名单")
    @PostMapping("/whitelist/add")
    public R<Boolean> add(@Validated @RequestBody IpWhiteListReq aclWhitelist) {
        Boolean add = ipWhiteListService.add(aclWhitelist);

        return R.success(add);
    }

    @ApiOperationSupport(order = 8)
    @Operation(summary = "8.访问控制白名单删除")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.DELETE, desc = "删除访问控制白名单")
    @PostMapping("/whitelist/delete/{id}")
    public R<Boolean> delete(@PathVariable("id") Integer id) {
        Boolean delete = ipWhiteListService.delete(id);

        return R.success(delete);
    }

    @ApiOperationSupport(order = 9)
    @Operation(summary = "9.访问控制白名单修改")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "修改访问控制白名单")
    @PostMapping("/whitelist/update")
    public R<Boolean> update(@Validated @RequestBody IpWhiteListReq aclWhitelist) {
        Boolean update = ipWhiteListService.update(aclWhitelist);
        return R.success(update);
    }

    @ApiOperationSupport(order = 10)
    @Operation(summary = "10.访问控制白名单分页查询")
    @PostMapping("/whitelist/page")
    public RPage<IpWhiteListResp> page(@Validated @RequestBody IpWhiteListPageReq ipWhiteListPageReq) {
        IPage<IpWhiteListResp> page = ipWhiteListService.page(ipWhiteListPageReq);
        return new RPage<>(page);
    }

    @ApiOperationSupport(order = 11)
    @Operation(summary = "11.访问控制白名单启用/禁用")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "启用/禁用访问控制白名单")
    @PostMapping("/whitelist/enable")
    public R<Boolean> enableSource(@Validated @RequestBody IpWhiteListEnableReq aclWhitelist) {
        Boolean b = ipWhiteListService.enableSource(aclWhitelist);
        return R.success(b);
    }


    @ApiOperationSupport(order = 12)
    @Operation(summary = "12.获取运维人员信息(分页)")
    @PostMapping("/ops/page")
    public RPage<OpsPersonResp> getOpsInfoByPage(@RequestBody @Valid OpsPersonPageReq req) {
        IPage<OpsPersonResp> opsInfoByPage = sysOpsPersonService.getOpsInfoByPage(req);
        return new RPage<>(opsInfoByPage);

    }

    @ApiOperationSupport(order = 13)
    @Operation(summary = "13.获取运维人员信息(根据id)")
    @GetMapping("/ops/{id}")
    public R<OpsPersonResp> getOpsInfoById(@PathVariable("id") Integer id) {
        OpsPersonResp opsInfoById = this.sysOpsPersonService.getOpsInfoById(id);
        return R.success(opsInfoById);
    }


    @ApiOperationSupport(order = 14)
    @Operation(summary = "14.添加运维人员信息")
    @PostMapping("/ops/add")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.ADD, desc = "新增运维人员")
    public R<Boolean> addOpsInfo(@RequestBody @Valid OpsPersonReq opsPersonReq) {
        Boolean b = sysOpsPersonService.addOpsInfo(opsPersonReq);
        return R.success(b);
    }

    @ApiOperationSupport(order = 15)
    @Operation(summary = "15.更新运维人员信息")
    @PostMapping("/ops/update")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "编辑运维人员信息")
    public R<Boolean> updateOpsInfo(@RequestBody @Valid OpsPersonReq opsPersonReq) {
        Boolean b = sysOpsPersonService.updateOpsInfo(opsPersonReq);

        return R.success(b);
    }

    @ApiOperationSupport(order = 16)
    @Operation(summary = "16.删除运维人员")
    @GetMapping("/ops/delete/{id}")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.DELETE, desc = "删除运维人员")
    public R<Boolean> deleteOpsInfo(@PathVariable("id") Integer id) {
        Boolean b = sysOpsPersonService.deleteOpsInfo(id);
        return R.success(b);
    }


    @ApiOperationSupport(order = 17)
    @Operation(summary = "17.证书查询")
    @GetMapping("/access/cert/search/{type}")
    public R<List<CertificateResp>> certSearch(@PathVariable("type") Integer type) {
        List<CertificateResp> certificateResps = certificateManagerService.certSearch(type);
        return R.success(certificateResps);
    }

    @ApiOperationSupport(order = 18)
    @Operation(summary = "18.证书删除")
    @GetMapping("/access/certificate/delete/{id}")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.DELETE, desc = "证书删除")
    public R<Boolean> certDelete(@PathVariable("id") Integer id) {
        Boolean b = this.certificateManagerService.certDelete(id);
        return R.success(b);
    }

    @ApiOperationSupport(order = 19)
    @Operation(summary = "19.证书添加")
    @PostMapping("/access/certificate/upload")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.UPLOAD, desc = "证书添加")
    public R<Boolean> certUpload(@ModelAttribute CertificateReq certificateReq) {

        Boolean b = certificateManagerService.certUpload(certificateReq);
        return R.success(b);
    }


    @Operation(summary = "32.证书启用")
    @PostMapping("/access/certificate/open/{id}")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "开启证书")
    public R<Boolean> openCert(@PathVariable("id") Integer id) {
        Boolean b = certificateManagerService.openCert(id);
        return R.success(b);
    }

//    @Operation(summary = "33.下发nginx重启指令")
//    @GetMapping("/cmd/reboot/nginx")
//    @SysLogAnn(module =MODEL, action = "下发",value = "下发nginx重启指令")
//    public void issueCmdRebootNginx() {
//        ShellUtils.exec(rebootNginxPath);
//    }


    @ApiOperationSupport(order = 20)
    @Operation(summary = "20.终端检测权限设置")
    @PostMapping("/terminal/permission/update")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "编辑终端检测权限")
    public R<Boolean> permission(@Valid @RequestBody TerminalPermissionReq req) {
        Boolean permission = this.ipConfigService.permission(req);
        return R.success(permission);
    }


    @ApiOperationSupport(order = 21)
    @Operation(summary = "21.终端检测权限查询")
    @GetMapping("/terminal/permissio/search")
    public R<TerminalPermissionResp> permissionSearch() {
        TerminalPermissionResp terminalPermissionResp = this.ipConfigService.permissionSearch();
        return R.success(terminalPermissionResp);
    }


    @ApiOperationSupport(order = 22)
    @Operation(summary = "22.获取公网出口ip配置")
    @GetMapping("/ip/config")
    public R<List<IpConfigResp>> getIpConfig() {
        List<IpConfigResp> ipConfig = ipConfigService.getIpConfig();
        return R.success(ipConfig);
    }

    @ApiOperationSupport(order = 23)
    @Operation(summary = "23.添加公网出口ip配置")
    @PostMapping("/ip/config/save")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.ADD, desc = "添加公网出口ip配置")
    public R<Boolean> addIpConfig(@Validated @RequestBody IpConfigAddReq ipConfigAddReq) {
        Boolean b = ipConfigService.addIpConfig(ipConfigAddReq);
        return R.success(b);
    }

    @ApiOperationSupport(order = 24)
    @Operation(summary = "24.删除ip范围配置")
    @PostMapping("/ip/config/del/{id}")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.DELETE, desc = "删除公网出口ip配置")
    public R<Boolean> ipConfigDel(@PathVariable("id") Integer id) {
        Boolean b = ipConfigService.ipConfigDel(id);
        return R.success(b);

    }

    @ApiOperationSupport(order = 23)
    @Operation(summary = "23.修改公网出口ip配置")
    @PostMapping("/ip/config/update")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "修改公网出口ip配置")
    public R<Boolean> updateIpConfig(@Validated @RequestBody IpConfigUpdateReq ipConfigAddReq) {
        Boolean b = ipConfigService.updateIpConfig(ipConfigAddReq);
        return R.success(b);
    }

//
//    @ApiOperationSupport(order = 25)
//    @Operation(summary = "25.获取上级目标系统配置(全部)")
//    @GetMapping("/super/systemctl/search")
//    public R<List<SuperSysConfigResp>> getSuperSysConfig() {
//        return null;
//    }
//
//    @ApiOperationSupport(order = 26)
//    @Operation(summary = "26.获取上级目标系统配置(根据id)")
//    @GetMapping("/super/systemctl/{id}")
//    public R<OpsPersonResp> getSuperSysConfigById(@PathVariable("id") Integer id) {
//        return null;
//    }
//
//
//    @ApiOperationSupport(order = 27)
//    @Operation(summary = "27.添加上级目标系统配置")
//    @PostMapping("/super/systemctl/add")
//    @SysLogAnn(module = MODEL, action = "加上级目标系统配置", value = "加上级目标系统配置")
//    public R<Boolean> addSuperSysConfig(@RequestBody @Valid SuperSysConfigReq req) {
//
//        return null;
//    }
//
//    @ApiOperationSupport(order = 28)
//    @Operation(summary = "28.更新上级目标系统配置")
//    @PostMapping("/super/systemctl/update")
//    @SysLogAnn(module = MODEL, action = "编辑上级目标系统配置", value = "编辑上级目标系统配置")
//    public R<Boolean> updateSuperSysConfig(@RequestBody @Valid SuperSysConfigReq req) {
//
//        //        boolean flag = routeInfoService.updateEntity(routeInfo);
//        return R.success(true);
//    }
//
//    @ApiOperationSupport(order = 29)
//    @Operation(summary = "29.删除上级目标系统配置")
//    @PostMapping("/super/systemctl/delete")
//    @SysLogAnn(module = MODEL, action = "上级目标系统配置", value = "上级目标系统配置")
//    public R<Boolean> deleteSuperSysConfig(Integer id) {
//
//        return R.success(true);
//    }


    @ApiOperationSupport(order = 30)
    @Operation(summary = "30.获取系统基本信息")
    @GetMapping("/deploy")
    public R<LocalDeviceInfoResp> getDeployInfo() {
        LocalDeviceInfoResp deployInfo = localDeviceInfoService.getDeployInfo();

        return R.success(deployInfo);
    }

    @ApiOperationSupport(order = 31)
    @Operation(summary = "31.更新系统基本信息")
    @PostMapping("/deploy/save")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "更新部署信息")
    public R<Boolean> saveDeployInfo(@Validated @RequestBody LocalDeviceInfoReq req) {
        Boolean b = localDeviceInfoService.saveDeployInfo(req);
        return R.success(b);
    }


    @ApiOperationSupport(order = 32)
    @Operation(summary = "32.获取上级管理中心信息")
    @GetMapping("/mgr/center")
    public R<MgrCenterInfoResp> getMgrCenter() {
        MgrCenterInfoResp mgrCenter = this.mgrCenterInfoService.getMgrCenter();
        return R.success(mgrCenter);
    }


    @ApiOperationSupport(order = 33)
    @Operation(summary = "33.向上级管理中心注册")
    @PostMapping("/mgr/register")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.REGISTER, desc = "向上级管理中心注册")
    public R<String> register(@Validated @RequestBody MgrCenterInfoReq req) {
        String msg = this.mgrCenterInfoService.register(req);
        return R.success(msg);
    }


}
