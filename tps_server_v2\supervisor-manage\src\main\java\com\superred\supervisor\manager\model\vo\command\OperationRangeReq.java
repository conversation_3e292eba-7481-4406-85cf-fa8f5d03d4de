package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OperationRangeReq {

    @Schema(description = "下发类型：1. 按部门 2.按设备 3.按ip地址段")
    @Pattern(regexp = "^[1-3]$", message = "下发类型只能为1、2或3")
    private Integer publishType;

    @Schema(description = "设备id列表")
    private List<String> deviceIds;

    @Schema(description = "部门id列表")
    private List<Integer> orgIds;

    @Schema(description = "IP地址段列表, 支持单IP、IP范围、子网掩码格式(需要切分为数组)")
    private List<String> ipRanges;
}
