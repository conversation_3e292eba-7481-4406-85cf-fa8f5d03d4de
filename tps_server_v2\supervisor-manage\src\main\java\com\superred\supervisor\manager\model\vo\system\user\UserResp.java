package com.superred.supervisor.manager.model.vo.system.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.common.entity.system.SysRole;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * @Classname UserVO

 */
@Data
public class UserResp implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;
    /**
     * 账号
     */
    @Schema(description = "账号")
    private String username;

    /**
     * 账号
     */
    @Schema(description = "账号")
    private String realName;
    /**
     * 账号
     */
    @Schema(description = "账号")
    private String password;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-dd-MM HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-dd-MM HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-dd-MM HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-dd-MM HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 0-正常，1-删除
     */
    @Schema(description = "0-正常，1-删除")
    private String delFlag;

    /**
     * 锁定标记
     */
    @Schema(description = "锁定标记")
    private String lockFlag;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime lockTime;
    /**
     * 用户是否可编辑，三员角色不可以修改，删除
     */
    @Schema(description = "用户是否可编辑，三员角色不可以修改，删除")
    private Boolean isEdit;

    /**
     * 用户数据是否显示（默认显示用户数据） 1:不显示 0：显示
     */
    @Schema(description = "用户数据是否显示（默认显示用户数据） 1:不显示 0：显示")
    private Integer isShow;

    /**
     * 组织机构ID
     */
    @Schema(description = "组织机构ID")
    private Integer orgId;

    /**
     * 组织机构名称
     */
    @Schema(description = "组织机构名称")
    private String orgName;

    /**
     * 角色列表
     */
    @Schema(description = "角色列表")
    private List<SysRole> roleList;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号")
    private String card;

    /**
     * 启用状态(1-启用，2-停用)
     */
    @Schema(description = "启用状态(1-启用，2-停用)")
    private Integer enable;

    /**
     * 密级
     * 1：公开
     * 2：内部
     * 3：秘密
     * 4：机密
     * 5：绝密
     */
    @Schema(description = "密级 1公开  2内部  3秘密 4机密 5绝密")
    private Integer secret;

    /**
     * 职称
     */
    @Schema(description = "职称")
    private String professional;

    /**
     * 是否首次登录
     */
    @Schema(description = "是否首次登录")
    private Integer firstLogin;

    /**
     * 备注信息
     **/
    @Schema(description = "备注信息")
    private String remarks;

    /**
     * 是否是主账号
     */
    @Schema(description = "是否是主账号")
    private Integer isMaster;


    /**
     * 状态：0：未授权；1：正常；2：锁定；3：禁用
     */
    @Schema(description = "状态：0：未授权；1：正常；2：锁定；3：禁用")
    private Integer status;

}
