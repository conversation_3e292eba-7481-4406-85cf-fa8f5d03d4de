package com.superred.supervisor.manager.model.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/3/19 16:41
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LicenseInvalidDTO {

    private static final int SYSTEM_ADMIN_CODE = 454;
    private static final int NORMAL_USER_CODE = 554;

    public String machineCode;

    public boolean isSystemAdmin;

    public String userName;

    public Integer userId;

    public Integer roleId;

    public String roleName;

    public String licenseMsg;


    public Integer getCode() {
        if (isSystemAdmin) {
            return SYSTEM_ADMIN_CODE;
        } else {
            return NORMAL_USER_CODE;
        }

    }
}
