package com.superred.supervisor.common.entity.policy;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 黑名单检测策略-IP黑名单
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_rule_attack_blacklist_ip", autoResultMap = true)
public class RuleAttackBlacklistIp extends Model<RuleAttackBlacklistIp> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "rule_id")
    private String ruleId;

    /**
     * 源IP地址  IP子网类型。未标注子网掩码时表示单个IP地址。"0.0.0.0/0"或空时表示所有IP地址。
     */
    //@TableField(typeHandler = EncryptAndDecryptHandler.class)
    private String sip;

    /**
     * 源端口范围  端口范围类型，0或空时表示所有端口 例，"443-65534"
     */
    //@TableField(typeHandler = EncryptAndDecryptHandler.class)
    private String sport;

    /**
     * 目的IP地址 IP子网类型。 未标注子网掩码时表示单个IP地址。"0.0.0.0/0"或空时表示所有IP地址。
     */
    //@TableField(typeHandler = EncryptAndDecryptHandler.class)
    private String dip;

    /**
     * 目的端口范围  端口范围类型，0或空时表示所有端口  例，"1024-2033"
     */
    //@TableField(typeHandler = EncryptAndDecryptHandler.class)
    private String dport;

    /**
     * 通信协议  6表示TCP、17表示UDP、0表示无限制。
     */
    private String protocol;

    /**
     * 策略名称
     */
    private String ruleName;

    /**
     * 攻击分类 1. 窃密木马 2. 远控木马 3. 电脑病毒 4. 僵尸网络 5. 网络蠕虫 6. 间谍软件 7. 挖矿木马 8. 黑客工具 9. 勒索软件 10. 恶意文档 11. 后门程序 99. 其它
     */
    private String attackClass;

    /**
     * 攻击组织
     */
    private String attackGroup;

    /**
     * 攻击阶段枚举分类 1 侦查扫描 2 攻击渗透 3 样本投递 4 持续控制 5 横向移动 6 数据窃取 99 其它
     */
    private String attackStage;

    /**
     * 攻击设施类型ID 1 密码爆破 2 漏洞扫描 3 样本分发 4 恶意发邮 5 钓鱼网站 6 信息搜集 7 数据窃取 8 命令控制 99 其它
     */
    private String facilityType;

    /**
     * 描述信息
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 告警级别，0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）。
     */
    private String risk;

    /**
     * 策略应用状态，0未应用，1已应用
     */
    private String status;

    /**
     * 是否共享状态，0是，1否
     */
    private String isShare;

    /**
     * 策略来源 1 本级 2上级
     */
    private String ruleSource;

    /**
     * 平台级别
     */
    private String level;
    /**
     * 策略更新时间
     */
    private String updateTime;

    /**
     * 策略创建时间
     */
    private String createTime;

    /**
     * 扩展字段1
     */
    private Long ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 上级共享策略ID
     */
    private Long upRuleId;


}
