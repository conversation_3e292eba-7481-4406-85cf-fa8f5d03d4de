<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.superred.supervisor.manager.mapper.command.IssueAgentExtMapper">


    <select id="statisticsPage"
            resultType="com.superred.supervisor.manager.model.vo.command.TerminalCmdRecordPageResp">
        select
        type,
        cmd_id,
        cmd,
        a.create_time,
        (select count(1) from sys_issue_agent as b WHERE b.cmd_id = a.cmd_id and b.`result` = 0 ) as success_count,
        (select count(1) from sys_issue_agent as b WHERE b.cmd_id = a.cmd_id and b.`result` = 1 ) as fail_count,
        (select count(1) from sys_issue_agent as b WHERE b.cmd_id = a.cmd_id and b.status= 1 ) as issue_count,
        (select count(1) from sys_issue_agent as b WHERE b.cmd_id = a.cmd_id and b.status= 0 ) as un_issue_count,
        COUNT(a.device_id) as device_count
        from sys_issue_agent a
        where a.type = 'command'
        group by cmd_id
        having
        1=1
        <if test="query.cmdId != null and query.cmdId != ''">
            and cmd_id like CONCAT('%',#{query.cmdId},'%')
        </if>
        <if test="query.cmd != null and query.cmd == 'update'">
            and type = 'update'
        </if>
        <if test="query.cmd != null and query.cmd == 'inner_policy_update'">
            and type = 'command' and cmd = 'inner_policy_update'
        </if>
        <if test="query.result != null and query.result == 0">
            and success_count > 0
        </if>
        <if test="query.result != null and query.result == 1">
            and fail_count > 0
        </if>
        order by create_time desc

    </select>

</mapper>
