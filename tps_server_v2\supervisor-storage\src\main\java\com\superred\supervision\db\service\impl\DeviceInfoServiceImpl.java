package com.superred.supervision.db.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervision.base.vo.device.BusinessStatusVo;
import com.superred.supervision.base.vo.device.InterfaceConnectVo;
import com.superred.supervision.base.vo.device.ModuleStatusVo;
import com.superred.supervision.base.vo.device.SubModuleVo;
import com.superred.supervision.base.vo.device.Suspected;
import com.superred.supervision.db.entity.DeviceInfo;
import com.superred.supervision.db.entity.DeviceInterface;
import com.superred.supervision.db.entity.DeviceModuleStatus;
import com.superred.supervision.db.entity.DeviceModuleStatusHistory;
import com.superred.supervision.db.entity.DeviceSuspectedLog;
import com.superred.supervision.db.mapper.DeviceInfoMapper;
import com.superred.supervision.db.service.DeviceInfoService;
import com.superred.supervision.db.service.DeviceInterfaceService;
import com.superred.supervision.db.service.DeviceModuleStatusHistoryService;
import com.superred.supervision.db.service.DeviceModuleStatusService;
import com.superred.supervision.db.service.DeviceSuspectedLogService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 检测器设备信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Service
@AllArgsConstructor
public class DeviceInfoServiceImpl extends ServiceImpl<DeviceInfoMapper, DeviceInfo> implements DeviceInfoService {

    private final DeviceInterfaceService deviceInterfaceService;

    private final DeviceModuleStatusService deviceModuleStatusService;

    private final DeviceModuleStatusHistoryService deviceModuleStatusHistoryService;

    private final DeviceSuspectedLogService deviceSuspectedLogService;

    @Override
    public void saveBusiness(String deviceId, BusinessStatusVo businessStatusVo) {

        DeviceInfo deviceInfo = new DeviceInfo();

        deviceInfo.setDeviceId(deviceId);
        deviceInfo.setUptime(businessStatusVo.getUptime());
        deviceInfo.setSoftVersion(businessStatusVo.getSoftVersion());
        deviceInfo.setBusinessTime(businessStatusVo.getTime());
        LambdaUpdateWrapper<DeviceInfo> update = new LambdaUpdateWrapper<>();
        update.eq(DeviceInfo::getDeviceId, deviceId);
        baseMapper.update(deviceInfo, update);

        final List<InterfaceConnectVo> interfaces = businessStatusVo.getInterfaces();
        if (CollUtil.isNotEmpty(interfaces)) {
            final List<DeviceInterface> deviceInterfaceList = interfaces.stream()
                    .map(i -> BeanUtil.toBean(i, DeviceInterface.class))
                    .peek(i -> i.setDeviceId(deviceId))
                    .collect(Collectors.toList());
            deviceInterfaceService.remove(Wrappers.<DeviceInterface>lambdaQuery().eq(DeviceInterface::getDeviceId, deviceId));
            deviceInterfaceService.saveBatch(deviceInterfaceList);
        }
        final List<ModuleStatusVo> moduleStatusList = businessStatusVo.getModuleStatus();
        if (CollUtil.isNotEmpty(moduleStatusList)) {
            List<DeviceModuleStatus> deviceModuleStatusList = new ArrayList<>();
            for (ModuleStatusVo module : moduleStatusList) {
                for (SubModuleVo subModuleVo : module.getSubmodule()) {
                    DeviceModuleStatus moduleStatus = new DeviceModuleStatus();
                    moduleStatus.setModule(module.getName());
                    moduleStatus.setDeviceId(deviceId);
                    moduleStatus.setStatus(subModuleVo.getStatus());
                    moduleStatus.setSubmodule(subModuleVo.getName());
                    moduleStatus.setInnerPolicy(subModuleVo.getInnerPolicy());
                    moduleStatus.setRecordDelayednum(subModuleVo.getRecordDelayednum());
                    moduleStatus.setRecord24hNum(subModuleVo.getRecord24hNum());
                    moduleStatus.setFileDelayednum(subModuleVo.getFileDelayednum());
                    moduleStatus.setVersion(JSONUtil.toJsonStr(subModuleVo.getVersion()));
                    deviceModuleStatusList.add(moduleStatus);
                }
            }
            deviceModuleStatusService.remove(Wrappers.<DeviceModuleStatus>lambdaQuery().eq(DeviceModuleStatus::getDeviceId, deviceId));
            deviceModuleStatusService.saveBatch(deviceModuleStatusList);
            // 记录历史表
            List<DeviceModuleStatusHistory> deviceModuleStatusHistoryList = new ArrayList<>();
            for (DeviceModuleStatus deviceModuleStatus : deviceModuleStatusList) {
                DeviceModuleStatusHistory deviceModuleStatusHistory = new DeviceModuleStatusHistory();
                BeanUtils.copyProperties(deviceModuleStatus, deviceModuleStatusHistory);
                deviceModuleStatusHistory.setTime(DateUtil.now());
                deviceModuleStatusHistoryList.add(deviceModuleStatusHistory);
            }
            deviceModuleStatusHistoryService.saveBatch(deviceModuleStatusHistoryList);
        }

        final List<Suspected> suspectedList = businessStatusVo.getSuspected();
        if (CollUtil.isNotEmpty(suspectedList)) {
            final List<DeviceSuspectedLog> deviceSuspectedLogList = suspectedList.stream().map(i -> BeanUtil.toBean(i, DeviceSuspectedLog.class))
                    .peek(i -> i.setDeviceId(deviceId))
                    .collect(Collectors.toList());
            deviceSuspectedLogService.saveBatch(deviceSuspectedLogList);
            // 添加判断监测器上报系统异常告警并禁用该设备，人工审核通过后恢复正常
            for (Suspected suspected : suspectedList) {
                if (suspected.getEventType() != null && suspected.getEventType() == 3) {
                    DeviceInfo deviceInfo1 = new DeviceInfo();
                    deviceInfo1.setDeviceId(deviceId);
                    deviceInfo1.setRegisterStatus((byte) 3);
                    baseMapper.update(deviceInfo1, update);
                    break;
                }
            }
        }

    }
}
