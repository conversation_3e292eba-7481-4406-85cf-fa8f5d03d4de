package com.superred.common.core.filter;

import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPInputStream;

/**
 * 处理gzip压缩的请求
 *
 * <AUTHOR>
 * @since 2025/7/15 14:21
 */
@Slf4j
public class GzipHttpServletRequestWrapper extends HttpServletRequestWrapper {
    private final byte[] decompressedBody;

    public GzipHttpServletRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        try (ServletInputStream inputStream = request.getInputStream()) {
            // 空检查
            if (inputStream == null) {
                decompressedBody = new byte[0];
                return;
            }
            try (GZIPInputStream gzipStream = new GZIPInputStream(inputStream)) {
                decompressedBody = IoUtil.readBytes(gzipStream, false);
            }
        } catch (IOException e) {
            throw new IOException("请求体解压失败", e);
        }
    }

    @Override
    public ServletInputStream getInputStream() {
        ByteArrayInputStream byteStream = new ByteArrayInputStream(decompressedBody);
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return byteStream.available() == 0;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
            }

            @Override
            public int read() {
                return byteStream.read();
            }
        };
    }

    @Override
    public BufferedReader getReader() {
        return new BufferedReader(new InputStreamReader(getInputStream(), StandardCharsets.UTF_8));
    }
}
