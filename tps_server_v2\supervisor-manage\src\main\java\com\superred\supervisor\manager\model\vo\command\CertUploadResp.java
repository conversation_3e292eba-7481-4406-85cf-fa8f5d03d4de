package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/03/24
 */
@Data
public class CertUploadResp {

    @Schema(description = "通信证书文件ID")
    private Integer id;

    @Schema(description = "通信证书文件名")
    private String filename;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}