package com.superred.supervisor.gateway.model.app.detection;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.superred.supervisor.standard.base.ComReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Map;

/**
 * APP检测组件监测数据上报.
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class AppFileFilterAlertReq extends ComReq {

    /**
     * 告警ID.
     **/
    @JsonProperty("id")
    private String id;

    /**
     * 告警时间.
     **/
    @JsonProperty("time")
    private String time;

    /**
     * 命中策略 ID.
     **/
    @JsonProperty("rule_id")
    private String ruleId;

    /**
     * 命中策略 ID.
     **/
    @JsonProperty("rule_desc")
    private String ruleDesc;


    /**
     * 策略类型 B.2.3   1 关键词 2 账号 3 加密文件 4 文件md5 5 电子文件涉密标志 6 密级标志
     **/
    @JsonProperty("filter_type")
    private Integer filterType;


    /**
     * 告警文件摘要
     **/
    @JsonProperty("file_summary")
    private String fileSummary;



    /**
     * 告警文件MD5.
     **/
    @JsonProperty("file_md5")
    private String fileMd5;

    /**
     * 告警文件路径.
     **/
    @JsonProperty("file_path")
    private String filePath;

    /**
     * 告警文件文件名.
     **/
    @JsonProperty("filename")
    private String filename;


    /**
     * 告警文件文件大小.
     **/
    @JsonProperty("filesize")
    private Double filesize;


    /**
     * 告警关键词
     **/
    @JsonProperty("highlight_text")
    private String highlightText;


    /**
     *  告警信息描述 即为命中的关键词.
     **/
    @JsonProperty("file_desc")
    private String fileDesc;

    /**
     * 文件密级标志
     **/
    @JsonProperty("file_secret_level")
    private Integer fileSecretLevel;


    /**
     * 责任人.
     **/
    @JsonProperty("user_name")
    private String userName;




    /**
     * 接入第三方系统名称.
     **/
    @JsonProperty("access_system_name")
    private String accessSystemName;

    /**
     * 接入第三方系统的详细信息. 自定义
     **/
    @JsonProperty("access_system_detail_info")
    private Map<String, Object> accessSystemDetailInfo;


    /**
     * 拓展字段，json 格式.
     **/
    @JsonProperty("extended_fields")
    private Map<String, Object> extendedFields;


}
