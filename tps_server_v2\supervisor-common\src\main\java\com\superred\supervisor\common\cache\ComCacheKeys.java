package com.superred.supervisor.common.cache;

/**
 * 公共缓存的key
 *
 * <AUTHOR>
 * @since 2025/7/18 14:28
 */
public final class ComCacheKeys {
    private ComCacheKeys() {
    }


    /**
     * 终端设备缓存前缀
     */
    public static final String AGENT_TERMINAL_CACHE = "common:ag-terminal-cache:";

    public static final String AGENT_TERMINAL_LOGIN_CACHE = "common:ag-terminal-login-cache:";

    /**
     * 应用自监管组件缓存前缀
     */
    public static final String AGENT_APP_CACHE = "common:ag-app-cache:";

    public static final String AGENT_APP_LOGIN_CACHE = "common:ag-app-login-cache:";

    /**
     * ZJG 设备ID缓存前缀
     */
    public static final String LOCAL_DEVICE_ID = "common:local-device:id:";

    /**
     * ip 白名单缓存前缀
     */
    public static final String IP_WHITE_PREDIX = "common:ip-white-by-type:list:";

}
