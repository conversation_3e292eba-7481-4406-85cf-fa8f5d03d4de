package com.superred.supervisor.manager.service.policy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleThreeEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.constant.policy.PolicyIssueStatusEnum;
import com.superred.supervisor.common.constant.policy.PolicyIssueTypeEnum;
import com.superred.supervisor.common.entity.command.IssueAgent;
import com.superred.supervisor.common.entity.policy.AgentPolicy;
import com.superred.supervisor.common.entity.policy.AgentPolicyDevice;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DeviceRule;
import com.superred.supervisor.common.entity.system.SysOrg;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import com.superred.supervisor.common.repository.policy.AgentPolicyDeviceRepository;
import com.superred.supervisor.common.repository.policy.AgentPolicyRepository;
import com.superred.supervisor.common.repository.policy.AgentPolicyRuleRepository;
import com.superred.supervisor.common.repository.policy.DeviceRuleRepository;
import com.superred.supervisor.common.repository.system.SysOrgRepository;
import com.superred.supervisor.manager.constant.IssueTypeEnum;
import com.superred.supervisor.manager.model.dto.policy.AgentPolicyDeviceDTO;
import com.superred.supervisor.manager.model.dto.policy.AgentPolicyRuleDTO;
import com.superred.supervisor.manager.model.dto.policy.PolicyResultFailDTO;
import com.superred.supervisor.manager.model.vo.policy.AgentPolicyIssueReq;
import com.superred.supervisor.manager.model.vo.policy.AgentPolicyPageReq;
import com.superred.supervisor.manager.model.vo.policy.AgentPolicyReq;
import com.superred.supervisor.manager.model.vo.policy.AgentPolicyResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyHistoryVersionResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyIssueDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyIssueDeviceResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyTreeResp;
import com.superred.supervisor.manager.repository.command.IssueAgentExtRepository;
import com.superred.supervisor.manager.repository.terminal.TerminalAgentInfoExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.AgentPolicyService;
import com.superred.supervisor.manager.service.policy.RuleService;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-03-13 14:34
 */
@Slf4j
@Service("agentPolicyService")
@AllArgsConstructor
public class AgentPolicyServiceImpl implements AgentPolicyService {

    @Resource
    private AgentPolicyRepository agentPolicyRepository;
    @Resource
    private AgentPolicyDeviceRepository agentPolicyDeviceRepository;
    @Resource
    private AgentPolicyRuleRepository agentPolicyRuleRepository;
    @Resource
    private DeviceRuleRepository deviceRuleRepository;
    @Resource
    private IssueAgentExtRepository issueAgentExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;
    @Resource
    private TerminalAgentInfoExtRepository terminalAgentInfoExtRepository;
    @Resource
    private SysOrgRepository sysOrgRepository;
    @Resource
    private List<RuleService> ruleServiceList;

    @Override
    public IPage<AgentPolicyResp> page(AgentPolicyPageReq agentPolicyPageReq) {
        // 查询
        LambdaQueryWrapper<AgentPolicy> queryWrapper = new LambdaQueryWrapper<AgentPolicy>()
                .eq(StrUtil.isNotBlank(agentPolicyPageReq.getName()), AgentPolicy::getName, agentPolicyPageReq.getName())
                .eq(StrUtil.isNotBlank(agentPolicyPageReq.getModule()), AgentPolicy::getModule, agentPolicyPageReq.getModule())
                .eq(StrUtil.isNotBlank(agentPolicyPageReq.getCmd()), AgentPolicy::getCmd, agentPolicyPageReq.getCmd())
                .orderByDesc(AgentPolicy::getCreateTime);
        Page<AgentPolicy> page = new Page<>(agentPolicyPageReq.getStart(), agentPolicyPageReq.getLimit());
        IPage<AgentPolicy> page1 = this.agentPolicyRepository.page(page, queryWrapper);
        return page1.convert(item -> {
            AgentPolicyResp agentPolicyResp = AgentPolicyResp.fromAgentPolicy(item);
            RuleService ruleService = this.ruleServiceList.stream().filter(item1 -> item1.isSupported(agentPolicyResp.getModule())).findFirst().orElse(null);
            PolicyModuleResp module = ruleService.getModule();
            agentPolicyResp.setModuleParentStr(module.getModuleParentStr());
            agentPolicyResp.setModuleStr(module.getModuleStr());
            return agentPolicyResp;
        });
    }

    @Override
    public AgentPolicyResp getById(Long policyId) {
        AgentPolicy agentPolicy = this.agentPolicyRepository.getById(policyId);
        AgentPolicyResp agentPolicyResp = AgentPolicyResp.fromAgentPolicy(agentPolicy);
        if (agentPolicyResp != null) {
            // 绑定终端
            List<AgentPolicyDevice> agentPolicyDeviceList = this.agentPolicyDeviceRepository.list(Wrappers.<AgentPolicyDevice>lambdaQuery()
                    .eq(AgentPolicyDevice::getPolicyId, policyId));
            agentPolicyResp.setDeviceList(AgentPolicyDeviceDTO.fromAgentPolicyDevice(agentPolicyDeviceList));
            // 绑定规则
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, policyId));
            agentPolicyResp.setRuleList(AgentPolicyRuleDTO.fromAgentPolicyRule(agentPolicyRuleList));
            // 查询具体规则信息
            List<Long> ruleIdList = agentPolicyRuleList.stream().map(AgentPolicyRule::getRuleId).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ruleIdList)) {
                this.getRuleDetail(ruleIdList, agentPolicy.getModule(), agentPolicyResp);
            }
        }
        return agentPolicyResp;
    }

    @Override
    public IPage<PolicyIssueDetailResp> detail(AgentPolicyPageReq agentPolicyPageReq) {
        Page<AgentPolicyRule> page = new Page<>(agentPolicyPageReq.getStart(), agentPolicyPageReq.getLimit());
        Page<AgentPolicyRule> policyRulePage = this.agentPolicyRuleRepository.page(page, Wrappers.<AgentPolicyRule>lambdaQuery()
                .eq(AgentPolicyRule::getPolicyId, agentPolicyPageReq.getPolicyId())
                .eq(StrUtil.isNotBlank(agentPolicyPageReq.getRuleId()), AgentPolicyRule::getRuleId, agentPolicyPageReq.getRuleId()));
        // 查询设备列表
        List<TerminalAgentInfo> deviceInfos = this.terminalAgentInfoExtRepository.list(Wrappers.emptyWrapper());
        deviceInfos = CollectionUtil.isNotEmpty(deviceInfos) ? deviceInfos : new ArrayList<>();
        Map<String, TerminalAgentInfo> deviceMap = deviceInfos.stream().collect(Collectors.toMap(TerminalAgentInfo::getDeviceId, item -> item));
        // 查询部门列表
        List<SysOrg> sysOrgList = this.sysOrgRepository.list(Wrappers.emptyWrapper());
        sysOrgList = CollectionUtil.isNotEmpty(sysOrgList) ? sysOrgList : new ArrayList<>();
        Map<Integer, SysOrg> sysOrgMap = sysOrgList.stream().collect(Collectors.toMap(SysOrg::getId, item -> item));
        // 查询策略下发记录表
        List<IssueAgent> policyIssue = this.issueAgentExtRepository.list(Wrappers.<IssueAgent>lambdaQuery()
                .eq(IssueAgent::getPolicyId, agentPolicyPageReq.getPolicyId()));

        return policyRulePage.convert(policyRule -> {
            String ruleId = policyRule.getRuleId().toString();
            PolicyIssueDetailResp policyIssueDetailResp = new PolicyIssueDetailResp();
            List<PolicyIssueDeviceResp> successDeviceList = new ArrayList<>();
            List<PolicyIssueDeviceResp> failDeviceList = new ArrayList<>();
            List<PolicyIssueDeviceResp> issueDeviceList = new ArrayList<>();
            for (IssueAgent issue : policyIssue) {
                String deviceId = issue.getDeviceId();
                String organs = "";
                TerminalAgentInfo agentDeviceInfo = deviceMap.get(deviceId);
                if (agentDeviceInfo != null && agentDeviceInfo.getOrgId() != null) {
                    SysOrg org = sysOrgMap.get(agentDeviceInfo.getOrgId());
                    organs = org.getName();
                }
                PolicyIssueDeviceResp policyIssueDeviceResp = new PolicyIssueDeviceResp();
                policyIssueDeviceResp.setDeviceId(deviceId);
                policyIssueDeviceResp.setDeviceType(PolicyDeviceTypeEnum.AGENT.getValue());
                policyIssueDeviceResp.setOrgans(organs);
                issueDeviceList.add(policyIssueDeviceResp);
                if (StrUtil.isNotBlank(issue.getSuccess())) {
                    List<String> successIdList = JsonUtil.fromJson(issue.getSuccess(), new TypeReference<List<String>>() {
                    });
                    String ruleIdFilter = successIdList.stream().filter(item -> item.equals(ruleId)).findFirst().orElse(null);
                    if (StrUtil.isNotBlank(ruleIdFilter)) {
                        successDeviceList.add(policyIssueDeviceResp);
                    }
                }
                if (StrUtil.isNotBlank(issue.getFail())) {
                    List<PolicyResultFailDTO> policyResultFailDTOS = JsonUtil.fromJson(issue.getFail(), new TypeReference<List<PolicyResultFailDTO>>() {
                    });
                    PolicyResultFailDTO policyResultFailDTO = policyResultFailDTOS.stream().filter(item -> item.getRuleId().equals(ruleId)).findFirst().orElse(null);
                    if (policyResultFailDTO != null) {
                        policyIssueDeviceResp.setMessage(policyResultFailDTO.getMsg());
                        failDeviceList.add(policyIssueDeviceResp);
                    }
                }
            }
            policyIssueDetailResp.setRuleId(ruleId);
            policyIssueDetailResp.setIssueDeviceAmount(policyIssue.size());
            policyIssueDetailResp.setIssueDeviceList(issueDeviceList);
            policyIssueDetailResp.setSuccessDeviceAmount(successDeviceList.size());
            policyIssueDetailResp.setFailDeviceAmount(failDeviceList.size());
            policyIssueDetailResp.setSuccessDeviceList(successDeviceList);
            policyIssueDetailResp.setFailDeviceList(failDeviceList);
            return policyIssueDetailResp;
        });
    }

    @Override
    public void save(AgentPolicyReq agentPolicyReq) {
        // 验证重复
        this.validateSaveRepeat(agentPolicyReq);
        // 新增策略表
        AgentPolicy agentPolicy = fromAgentPolicyReq(agentPolicyReq);
        agentPolicy.setVersion(this.ruleIdBuilder.buildPolicyVersion());
        agentPolicy.setNum(agentPolicyReq.getRuleIds().size());
        this.agentPolicyRepository.save(agentPolicy);
        // 修改策略应用状态 + 添加关系表
        if (CollectionUtil.isNotEmpty(agentPolicyReq.getRuleIds())) {
            this.updatePolicyStatus(agentPolicyReq.getRuleIds(), agentPolicy.getId(), agentPolicyReq.getModule());
        }
        // 进行下发
        if (CollectionUtil.isNotEmpty(agentPolicyReq.getDeviceIds())) {
            AgentPolicyIssueReq agentPolicyIssueReq = new AgentPolicyIssueReq();
            agentPolicyIssueReq.setPolicyId(agentPolicy.getId());
            agentPolicyIssueReq.setModule(agentPolicy.getModule());
            agentPolicyIssueReq.setDeviceIds(agentPolicyReq.getDeviceIds());
            this.issue(agentPolicyIssueReq);
        }

    }

    public static AgentPolicy fromAgentPolicyReq(AgentPolicyReq agentPolicyReq) {
        return AgentPolicy.builder()
                .module(agentPolicyReq.getModule())
                .name(agentPolicyReq.getName())
                .version(agentPolicyReq.getVersion())
                .num(agentPolicyReq.getNum())
                .config(agentPolicyReq.getConfig())
                .createTime(agentPolicyReq.getCreateTime())
                .description(agentPolicyReq.getDescription())
                .issuedStatus(agentPolicyReq.getIssuedStatus())
                .issuedTime(agentPolicyReq.getIssuedTime())
                .isDefault(agentPolicyReq.getIsDefault())
                .cmd(agentPolicyReq.getCmd())
                .build();
    }

    @Override
    public void edit(AgentPolicyReq agentPolicyReq) {
        // 验证重复
        this.validateEditRepeat(agentPolicyReq);
        // 更新策略表
        AgentPolicy agentPolicy = AgentPolicy.builder()
                .id(agentPolicyReq.getId())
                .num(agentPolicyReq.getRuleIds().size())
                .build();
        this.agentPolicyRepository.updateById(agentPolicy);
        // 删除之前的关系表
        this.agentPolicyRuleRepository.remove(Wrappers.<AgentPolicyRule>lambdaQuery()
                .eq(AgentPolicyRule::getPolicyId, agentPolicyReq.getId()));
        // 修改策略应用状态 + 添加关系表
        if (CollectionUtil.isNotEmpty(agentPolicyReq.getRuleIds())) {
            this.updatePolicyStatus(agentPolicyReq.getRuleIds(), agentPolicyReq.getId(), agentPolicyReq.getModule());
        }
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 策略删除，将原生效范围的设备策略改为默认策略，删除策略记录级策略和规则的关系表
        // 判断该策略是否存在生效范围，如果存在则不能删除
        List<AgentPolicyDevice> list = this.agentPolicyDeviceRepository.list(Wrappers.<AgentPolicyDevice>lambdaQuery()
                .eq(AgentPolicyDevice::getPolicyId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("存在正在生效的终端，请检查策略是否生效");
        }
        this.agentPolicyRepository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public void issue(AgentPolicyIssueReq agentPolicyIssueReq) {
        AgentPolicy agentPolicy = this.agentPolicyRepository.getById(agentPolicyIssueReq.getPolicyId());
        if (agentPolicy.getIssuedStatus() == PolicyIssueStatusEnum.ISSUE.getKey()) {
            throw new BaseBusinessException("该策略已经下发，不可重复下发");
        }
        // 修改下发状态下发时间
        AgentPolicy updateBean = AgentPolicy.builder()
                .id(agentPolicyIssueReq.getPolicyId())
                .issuedStatus(PolicyIssueStatusEnum.ISSUE.getKey())
                .issuedTime(DateUtil.date())
                .build();
        this.agentPolicyRepository.updateById(updateBean);
        // 下发给终端的策略详情,需要参照标准变动
        List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                .eq(AgentPolicyRule::getPolicyId, agentPolicy.getId())
                .eq(AgentPolicyRule::getModule, agentPolicy.getModule()));
        List<Long> ruleIds = agentPolicyRuleList.stream().map(AgentPolicyRule::getRuleId).collect(Collectors.toList());
        String config = this.getRuleConfig(ruleIds, agentPolicy.getModule());
        List<IssueAgent> issueAgentList = new ArrayList<>();
        for (String deviceId : agentPolicyIssueReq.getDeviceIds()) {
            // 更新终端策略
            this.updateDeviceRule(deviceId, agentPolicy.getCmd(), agentPolicy.getModule(), ruleIds);
            IssueAgent issueAgent = IssueAgent.builder()
                    .cmd(agentPolicy.getCmd())
                    .module(agentPolicy.getModule())
                    .version(agentPolicy.getVersion())
                    .num(agentPolicy.getNum())
                    .config(config)
                    .deviceId(deviceId)
                    .type(IssueTypeEnum.POLICY.getKey())
                    .policyId(agentPolicyIssueReq.getPolicyId())
                    .build();
            issueAgentList.add(issueAgent);
        }
        this.issueAgentExtRepository.saveBatch(issueAgentList);
    }

    @Override
    public List<PolicyTreeResp> getPolicyRulesAgent() {
        List<PolicyTreeResp> oneList = new ArrayList<>();
        List<PolicyModuleOneEnum> oneEnumList = PolicyModuleOneEnum.get(PolicyDeviceTypeEnum.AGENT.getKey());
        oneEnumList.forEach(item -> {
            List<PolicyTreeResp> twoList = null;
            List<PolicyModuleTwoEnum> twoEnumList = PolicyModuleTwoEnum.getTwoByParent(item, PolicyDeviceTypeEnum.AGENT.getKey());
            if (CollectionUtil.isNotEmpty(twoEnumList)) {
                twoList = new ArrayList<>();
                for (PolicyModuleTwoEnum twoItem : twoEnumList) {
                    List<PolicyTreeResp> threeList = null;
                    List<PolicyModuleThreeEnum> threeEnumList = PolicyModuleThreeEnum.getThreeByParent(twoItem, PolicyDeviceTypeEnum.AGENT.getKey());
                    if (CollectionUtil.isNotEmpty(threeEnumList)) {
                        threeList = new ArrayList<>();
                        for (PolicyModuleThreeEnum threeItem : threeEnumList) {
                            PolicyTreeResp policyTreeResp = PolicyTreeResp.builder()
                                    .module(threeItem.getKey())
                                    .label(threeItem.getValue())
                                    .children(null)
                                    .build();
                            threeList.add(policyTreeResp);
                        }
                    }
                    PolicyTreeResp policyTreeResp = PolicyTreeResp.builder()
                            .module(twoItem.getKey())
                            .label(twoItem.getValue())
                            .children(threeList)
                            .build();
                    twoList.add(policyTreeResp);
                }
            }
            PolicyTreeResp policyTreeResp = PolicyTreeResp.builder()
                    .module(item.getKey())
                    .label(item.getValue())
                    .children(twoList)
                    .build();
            oneList.add(policyTreeResp);
        });
        return oneList;
    }

    @Override
    public List<PolicyHistoryVersionResp> getPolicyByModule(String module) {
        List<IssueAgent> issueAgentList = this.issueAgentExtRepository.list(Wrappers.<IssueAgent>lambdaQuery()
                .eq(IssueAgent::getType, IssueTypeEnum.POLICY.getKey())
                .eq(IssueAgent::getModule, module));
        if (CollectionUtil.isEmpty(issueAgentList)) {
            return new ArrayList<>();
        }
        List<IssueAgent> distinctList = issueAgentList.stream()
                .collect(Collectors.toMap(IssueAgent::getVersion, p -> p, (p1, p2) -> p1))
                .values()
                .stream()
                .collect(Collectors.toList());
        List<PolicyHistoryVersionResp> list = new ArrayList<>(distinctList.size());
        distinctList.forEach(item -> {
            list.add(PolicyHistoryVersionResp.builder()
                    .module(item.getModule())
                    .version(item.getVersion())
                    .policyId(item.getPolicyId())
                    .build());
        });
        return list;
    }

    /**
     * 更新终端策略
     */
    private void updateDeviceRule(String deviceId, String cmd, String module, List<Long> ruleIds) {
        PolicyIssueTypeEnum policyIssueTypeEnum = PolicyIssueTypeEnum.getByKey(cmd);
        if (policyIssueTypeEnum == null) {
            throw new BaseBusinessException("下发命令错误");
        }
        switch (policyIssueTypeEnum) {
            case CMD_ADD:
                //判断每一个规则是否已经存在关系中，如果没有存在则添加到数据库中
                this.cmdAdd(deviceId, module, ruleIds);
                break;
            case CMD_RESET:
                //先删除原来的规则，然后全量的更新新的规则
                this.cmdReset(deviceId, module, ruleIds);
                break;
            case CMD_DEL:
                //根据规则id设备id和模块删除对应的规则
                this.cmdDel(deviceId, module, ruleIds);
                break;
            default:
                throw new BaseBusinessException("下发命令错误");
        }
    }

    private void cmdDel(String deviceId, String module, List<Long> ruleIds) {
        this.deviceRuleRepository.remove(Wrappers.<DeviceRule>lambdaQuery()
                .eq(DeviceRule::getDeviceId, deviceId)
                .in(DeviceRule::getRuleId, ruleIds)
                .eq(DeviceRule::getModule, module));
    }

    private void cmdReset(String deviceId, String module, List<Long> ruleIds) {
        this.deviceRuleRepository.remove(Wrappers.<DeviceRule>lambdaQuery()
                .eq(DeviceRule::getDeviceId, deviceId)
                .eq(DeviceRule::getModule, module));
        List<DeviceRule> deviceRuleList = new ArrayList<>();
        for (Long ruleId : ruleIds) {
            DeviceRule deviceRule = DeviceRule.builder()
                    .deviceId(deviceId)
                    .ruleId(ruleId)
                    .module(module)
                    .build();
            deviceRuleList.add(deviceRule);
        }
        this.deviceRuleRepository.saveBatch(deviceRuleList);
    }

    private void cmdAdd(String deviceId, String module, List<Long> ruleIds) {
        List<DeviceRule> deviceRuleList = new ArrayList<>();
        List<DeviceRule> queryList = this.deviceRuleRepository.list(Wrappers.<DeviceRule>lambdaQuery()
                .eq(DeviceRule::getDeviceId, deviceId)
                .in(DeviceRule::getRuleId, ruleIds)
                .eq(DeviceRule::getModule, module));
        List<Long> queryRuleIdList = queryList.stream().map(DeviceRule::getRuleId).collect(Collectors.toList());
        for (Long ruleId : ruleIds) {
            if (queryRuleIdList.contains(ruleId)) {
                continue;
            }
            DeviceRule deviceRule = DeviceRule.builder()
                    .deviceId(deviceId)
                    .ruleId(ruleId)
                    .module(module)
                    .build();
            deviceRuleList.add(deviceRule);
        }
        this.deviceRuleRepository.saveBatch(deviceRuleList);
    }

    /**
     * 获取加密config字段值
     *
     * @param ruleIds
     * @param module
     * @return
     */
    private String getRuleConfig(List<Long> ruleIds, String module) {
        RuleService ruleService = this.getRuleServiceByModule(module);
        String config = ruleService.getRuleConfig(ruleIds);
        // Base64 加密
        return PolicyUtils.base64Encode(config);
    }

    /**
     * 修改策略应用状态 + 添加关系表
     *
     * @param ruleIds
     * @param module
     */
    private void updatePolicyStatus(List<Long> ruleIds, Long policyId, String module) {
        List<AgentPolicyRule> agentPolicyRuleList = new ArrayList<>();
        ruleIds.forEach(item -> {
            AgentPolicyRule agentPolicyRule = new AgentPolicyRule();
            agentPolicyRule.setPolicyId(policyId);
            agentPolicyRule.setRuleId(item);
            agentPolicyRule.setModule(module);
            agentPolicyRuleList.add(agentPolicyRule);
        });
        // 添加关系表
        this.agentPolicyRuleRepository.saveBatch(agentPolicyRuleList);
        // 更新规则状态
        RuleService ruleService = this.getRuleServiceByModule(module);
        ruleService.updateStatus(ruleIds);
    }

    /**
     * 查询具体规则信息
     *
     * @param ruleIdList
     * @param module
     * @param agentPolicyResp
     */
    private void getRuleDetail(List<Long> ruleIdList, String module, AgentPolicyResp agentPolicyResp) {
        RuleService ruleService = this.getRuleServiceByModule(module);
        PolicyDetailResp policyDetail = ruleService.getDetailByRuleId(ruleIdList);
        agentPolicyResp.setPolicyDetail(policyDetail);
    }

    /**
     * 根据模块获取对应的service
     *
     * @param module
     * @return
     */
    private RuleService getRuleServiceByModule(String module) {
        RuleService ruleService = this.ruleServiceList.stream().filter(item -> item.isSupported(module)).findFirst().orElse(null);
        if (ruleService == null) {
            throw new BaseBusinessException("模块不存在");
        }
        return ruleService;
    }

    /**
     * 验证重复
     *
     * @param agentPolicyReq
     */
    private void validateEditRepeat(AgentPolicyReq agentPolicyReq) {
        List<AgentPolicy> list = this.agentPolicyRepository.list(Wrappers.<AgentPolicy>lambdaQuery()
                .eq(AgentPolicy::getName, agentPolicyReq.getName())
                .eq(AgentPolicy::getModule, agentPolicyReq.getModule())
                .ne(AgentPolicy::getId, agentPolicyReq.getId()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("数据已存在，请检查策略名称是否重复");
        }
    }

    /**
     * 验证重复
     *
     * @param agentPolicyReq
     */
    private void validateSaveRepeat(AgentPolicyReq agentPolicyReq) {
        List<AgentPolicy> list = this.agentPolicyRepository.list(Wrappers.<AgentPolicy>lambdaQuery()
                .eq(AgentPolicy::getName, agentPolicyReq.getName())
                .eq(AgentPolicy::getModule, agentPolicyReq.getModule()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("数据已存在，请检查策略名称是否重复");
        }
    }
}
