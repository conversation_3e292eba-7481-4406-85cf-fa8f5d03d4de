package com.superred.supervision.base.vo.device;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 网卡连通性信息
 * <AUTHOR>
 * @since 2022/6/17 11:48
 **/
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InterfaceConnectVo {
    /**
     * 采集数据网卡序号
     */
    private Integer interfaceSeq;
    /**
     *
     */
    private String interfaceFlag;
    /**
     * 网卡状态，数值类型，取值为：1（网卡启用）、2（网卡停用）、3（网线掉落）、4（网卡故障）、99（未知）
     */
    private Integer interfaceStat;

    /**
     * 表示采集到数据流量
     */
    private Integer interfaceFlow;

    /**
     *
     */
    private Integer interfaceError;

    /**
     * 丢包个数
     */
    private Integer interfaceDrop;

    /**
     * 数据采集时长的秒数
     */
    private Long durationTime;


}
