package com.superred.supervisor.manager.model.vo.settings;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月13日
 */
@Data
public class OpsPersonPageReq extends PageReqDTO {

    @Schema(description = "运维联系人姓名")
    private String name;

    @Schema(description = "运维联系人电话")
    private String phone;

    @Schema(description = "运维联系人邮箱")
    private String email;

    @Schema(description = "运维联系人职位")
    private String position;

}
