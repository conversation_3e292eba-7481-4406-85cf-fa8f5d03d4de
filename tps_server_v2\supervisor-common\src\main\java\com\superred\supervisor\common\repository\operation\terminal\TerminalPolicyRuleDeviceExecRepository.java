package com.superred.supervisor.common.repository.operation.terminal;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.operation.terminal.TerminalPolicyRuleDeviceExec;
import com.superred.supervisor.common.mapper.operation.terminal.TerminalPolicyRuleDeviceExecMapper;
import org.springframework.stereotype.Repository;

/**
 * 终端策略规则设备执行详情Repository
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Repository
public class TerminalPolicyRuleDeviceExecRepository extends ServiceImpl<TerminalPolicyRuleDeviceExecMapper, TerminalPolicyRuleDeviceExec> {

}
