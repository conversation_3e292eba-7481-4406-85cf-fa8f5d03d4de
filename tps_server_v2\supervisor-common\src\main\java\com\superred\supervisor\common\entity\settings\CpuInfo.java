package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * cpu信息(LocalCpuInfo) 实体
 *
 * <AUTHOR>
 * @since 2025-03-27 14:55:03
 */
@Data
@TableName("local_cpu_info")
public class CpuInfo {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * cpu核数，8
     */
    @TableField("core")
    private Integer core;

    /**
     * 主频，“1.8GHz”
     */
    @TableField("clock")
    private Double clock;

    @TableField("physical_id")
    private Integer physicalId;

    /**
     * cpu某个核使用率，单位%
     */
    @TableField("cpu_usage")
    private Integer cpuUsage;

    /**
     * 当由多台服务器组成时，表示服务器的编号
     */
    @TableField("did")
    private Integer did;

}

