package com.superred.common.core.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 分页请求公共参数
 *
 * <AUTHOR>
 * @since 2021-07-14 16:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageReqDTO implements Serializable {

    private static final long serialVersionUID = -4086358648737665089L;
    /**
     * 页码 大于等于1
     */
    private Integer start;
    /**
     * 每页大小
     */
    private Integer limit;

}
