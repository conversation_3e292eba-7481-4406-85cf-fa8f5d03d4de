package com.superred.supervisor.manager.model.vo.system.role;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 角色
 */
@Data


public class SysRoleAddReq {


    /**
     * 角色名
     */
    @Schema(description = "角色名")
    @NotBlank(message = "角色名称 不能为空!")
    @Size(min = 1, max = 20, message = "角色名称 长度限制1-20位")
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5]+$", message = "角色描述 只能包含中文、英文、数字")
    private String name;


    /**
     * 角色描述
     */
    @Schema(description = "角色描述")
    @NotBlank(message = "角色描述 不能为空！")
    @Size(min = 1, max = 20, message = "角色描述 长度限制1-20位")
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5]+$", message = "角色描述 只能包含中文、英文、数字")
    private String description;


}
