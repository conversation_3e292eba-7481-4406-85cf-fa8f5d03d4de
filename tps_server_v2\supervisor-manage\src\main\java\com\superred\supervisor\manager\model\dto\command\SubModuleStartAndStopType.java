package com.superred.supervisor.manager.model.dto.command;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 模块启停指令类型
 */
@Getter
public enum SubModuleStartAndStopType {
    // 木马活动检测
    TROJAN("trojan", "木马活动检测", ModuleStartAndStopType.ALARM, 1),
    // 渗透行为检测
    ATTACK("attack", "渗透行为检测", ModuleStartAndStopType.ALARM, 2),
    // 恶意文件检测
    MALWARE("malware", "恶意文件检测", ModuleStartAndStopType.ALARM, 3),
    // 黑名单检测
    BLACKLIST("blacklist", "黑名单检测", ModuleStartAndStopType.ALARM, 4),
    // 异常行为检测
    ABNORMAL("abnormal", "异常行为检测", ModuleStartAndStopType.ALARM, 5),
    // 白名单过滤
    ALARM_WHITELIST("alarm_whitelist", "白名单过滤", ModuleStartAndStopType.ALARM, 6),
    // 关键词筛选
    KEYWORD_FILTER("keyword_filter", "关键词筛选", ModuleStartAndStopType.FILE_FILTER, 1),
    // 账号文件筛选
    ACCOUNT_FILTER("account_filter", "账号文件筛选", ModuleStartAndStopType.FILE_FILTER, 2),
    // 加密文件筛选
    ENCRYPTION_FILTER("encryption_filter", "加密文件筛选", ModuleStartAndStopType.FILE_FILTER, 3),
    // 关键词检测
    KEYWORD_DETECT("keyword_detect", "关键词检测", ModuleStartAndStopType.SENSITIVE, 1),
    // 文件MD5检测
    FILE_MD5("file_md5", "文件MD5检测", ModuleStartAndStopType.SENSITIVE, 2),
    // IP审计
    IP_LISTEN("ip_listen", "IP审计", ModuleStartAndStopType.OBJECT_LISTEN, 1),
    // 域名审计
    DOMAIN_LISTEN("domain_listen", "域名审计", ModuleStartAndStopType.OBJECT_LISTEN, 2),
    // 通联关系审计
    NET_LOG("net_log", "通联关系审计", ModuleStartAndStopType.NET_AUDIT, 1),
    // 应用行为审计
    APP_BEHAVIOR("app_behavior", "应用行为审计", ModuleStartAndStopType.NET_AUDIT, 2),
    // 审计白名单
    AUDIT_WHITELIST("audit_whitelist", "审计白名单", ModuleStartAndStopType.NET_AUDIT, 3),
    // 活动对象审计
    ACTIVE_OBJECT_AUDIT("active_object_audit", "活动对象审计", ModuleStartAndStopType.ACTIVE_OBJECT_AUDIT, 3);

    private final String key;
    private final String desc;
    private final ModuleStartAndStopType parent;
    private final Integer sort;

    SubModuleStartAndStopType(String key, String desc, ModuleStartAndStopType parent, Integer sort) {
        this.key = key;
        this.parent = parent;
        this.desc = desc;
        this.sort = sort;
    }

    public static SubModuleStartAndStopType getSubModuleStartAndStopTypeByKey(String key) {
        for (SubModuleStartAndStopType type : SubModuleStartAndStopType.values()) {
            if (type.getKey().equals(key)) {
                return type;
            }
        }
        return null;
    }

    public static List<SubModuleStartAndStopType> getSubModuleStartAndStopTypeByParent(ModuleStartAndStopType parent) {
        List<SubModuleStartAndStopType> submodule = new ArrayList<>();
        for (SubModuleStartAndStopType type : SubModuleStartAndStopType.values()) {
            if (Objects.equals(parent, type.getParent())) {
                submodule.add(type);
            }
        }
        return submodule;
    }
}