package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.db.entity.ModelDisposal;

/**
 * <p>
 * 自动研判模型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
public interface ModelDisposalService extends IService<ModelDisposal> {

    /**
     * 自动研判结果
     * @param checksum checksum
     * @return 自动研判结果
     */
    public Integer autoJudgeResult(String checksum);
}
