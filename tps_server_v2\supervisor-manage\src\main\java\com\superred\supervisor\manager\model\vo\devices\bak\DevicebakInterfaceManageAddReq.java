package com.superred.supervisor.manager.model.vo.devices.bak;

import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;


/**
 * 设备包界面管理添加请求
 *
 * <AUTHOR>
 * @since 2025/03/14
 */
@Data

public class DevicebakInterfaceManageAddReq {

    private static final long serialVersionUID = 1L;


    /**
     * ip地址,************
     */
    @Schema(description = "ip地址,************")
    @NotBlank(message = "ip 不能为空")
    @Pattern(regexp = "^$|^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", message = "ip 格式错误")
    private String ip;

    /**
     * 子网掩码，***********/24
     */
    @Schema(description = "子网掩码，***********/24")
    @NotBlank(message = "子网掩码 不能为空")
    @ByteSize(max = 32, message = "子网掩码 不能超过32个字节")
    private String netmask;

    /**
     * 网关地址,*************
     */
    @Schema(description = "网关地址,*************")
    @NotBlank(message = "网关地址 不能为空")
    @Pattern(regexp = "^$|^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$", message = "网关地址 格式错误")
    private String gateway;

    /**
     * 是否管理口，true，false
     */
    @Schema(description = "是否管理口，true，false")
    @NotBlank(message = "是否管理口 不能为空")
    private String manage;


    /**
     * mac地址，00:0d:48:09:4e:88
     */
    @Schema(description = "mac地址，00:0d:48:09:4e:88")
    @NotBlank(message = "mac 不能为空")
    private String mac;

}
