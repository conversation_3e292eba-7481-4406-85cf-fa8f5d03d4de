package com.superred.supervisor.manager.controller.command;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.annotation.role.IgnoreAuth;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageResp;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalCmdRecordPageReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdRecordPageResp;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalUninstallCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalInnerPolicyUpdateCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalModuleSwitchCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalSoftUpgradeCmdReq;
import com.superred.supervisor.manager.service.command.terminal.TerminalCmdIssueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "8.1 终端指令下发模块")
@RestController
@AllArgsConstructor
@RequestMapping("/v2/agent/issue_command/")
@IgnoreAuth
public class TerminalCmdIssueController {
    @Resource
    private TerminalCmdIssueService terminalCmdIssueService;

    @ApiOperationSupport(order = 1)
    @Operation(summary = "1. 下发软件升级指令")
    @PostMapping("/component_upgrade")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_COMMAND_ISSUANCE,
            operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发组件升级指令")
    public R<String> componentUpgrade(@RequestBody TerminalSoftUpgradeCmdReq req) {

        String cmdId = terminalCmdIssueService.softUpdate(req);
        return R.success(cmdId);
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2. 下发内置策略更新")
    @PostMapping("/inner_policy_update")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_COMMAND_ISSUANCE,
            operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发内置策略更新指令")
    public R<String> innerPolicyUpdate(@RequestBody TerminalInnerPolicyUpdateCmdReq req) {

        String cmdId = terminalCmdIssueService.innerPolicyUpdate(req);
        return R.success(cmdId);
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "3. 下发终端卸载")
    @PostMapping("/uninstall")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_COMMAND_ISSUANCE,
            operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发终端卸载指令")
    public R<String> uninstall(@RequestBody TerminalUninstallCmdReq req) {

        String cmdId = terminalCmdIssueService.uninstall(req);
        return R.success(cmdId);
    }

    @ApiOperationSupport(order = 4)
    @Operation(summary = "4. 下发模块启停指令")
    @PostMapping("/module_switch")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_COMMAND_ISSUANCE,
            operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发模块启停指令")
    public R<String> moduleSwitch(@RequestBody TerminalModuleSwitchCmdReq req) {

        String cmdId = terminalCmdIssueService.moduleSwitch(req);
        return R.success(cmdId);
    }

    @ApiOperationSupport(order = 5)
    @Operation(summary = "5. 终端指令下发记录-分页")
    @GetMapping("/statistics/page")
    public RPage<TerminalCmdRecordPageResp> recordPage(TerminalCmdRecordPageReq req) {

        IPage<TerminalCmdRecordPageResp> page = terminalCmdIssueService.recordPage(req);
        return new RPage<>(page);
    }

    @ApiOperationSupport(order = 6)
    @Operation(summary = "6 终端指令下发记录-指令设备详情")
    @GetMapping("/device/detail/page")
    public RPage<TerminalCmdExecPageResp> terminalExecPage(TerminalCmdExecPageReq req) {

        IPage<TerminalCmdExecPageResp> page = terminalCmdIssueService.terminalExecPage(req);
        return new RPage<>(page);
    }
}