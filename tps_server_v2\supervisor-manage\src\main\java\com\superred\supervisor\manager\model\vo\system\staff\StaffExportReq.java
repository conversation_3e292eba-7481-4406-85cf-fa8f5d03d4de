package com.superred.supervisor.manager.model.vo.system.staff;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-08-24 16:41
 */

@Data

public class StaffExportReq {

    @Schema(description = "部门ID")
    private Integer orgId;

    @Schema(description = "人员姓名")
    private String staffName;

    @Schema(description = "导出密码")
    private String exportPass;

    @Schema(description = "导出确认密码")
    private String exportVerifyPass;

    @Schema(description = "人员id集合")
    private List<Integer> idList;

}
