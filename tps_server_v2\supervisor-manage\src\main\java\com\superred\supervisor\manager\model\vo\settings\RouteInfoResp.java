package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <AUTHOR>
 *  路由信息
 * @since 2025年03月12日
 */
@Data

public class RouteInfoResp {

    @Schema(description = "主键")
    private Integer id;

    @Schema(description = "目标网络地址")
    private String ip;

    @Schema(description = "网关")
    private String gateway;


    @Schema(description = "网卡名称")
    private String network;


}
