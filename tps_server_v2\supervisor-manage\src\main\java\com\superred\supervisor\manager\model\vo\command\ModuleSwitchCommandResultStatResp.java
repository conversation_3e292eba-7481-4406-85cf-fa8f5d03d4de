package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模块启停指令响应统计响应参数
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data
public class ModuleSwitchCommandResultStatResp {
    /**
     * 设备id
     */
    @Schema(description = "设备id", example = "device123")
    private String deviceId;

    /**
     * 指令编号
     */
    @Schema(description = "指令编号", example = "cmd001")
    private String cmdId;

    /**
     * 指令
     */
    @Schema(description = "指令", example = "start")
    private String cmd;

    /**
     * 指令来源： 1 本级  2 上级
     */
    @Schema(description = "指令来源： 1 本级  2 上级", example = "1")
    private String cmdSource;

    /**
     * 统计结果
     */
    @Schema(description = "统计结果", example = "10")
    private Integer resultCount;
}