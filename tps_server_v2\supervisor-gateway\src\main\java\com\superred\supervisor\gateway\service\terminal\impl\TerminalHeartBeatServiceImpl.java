package com.superred.supervisor.gateway.service.terminal.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.common.minio.core.utils.SimpleMinioUploadClient;
import com.superred.supervisor.common.constant.CommonConstant;
import com.superred.supervisor.common.constant.devices.DevicesConnectStatus;
import com.superred.supervisor.common.entity.command.UpdateFile;
import com.superred.supervisor.common.entity.operation.enums.OperationExecStatus;
import com.superred.supervisor.common.entity.operation.terminal.TerminalOperationExec;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import com.superred.supervisor.common.entity.terminal.enums.ActivateStatus;
import com.superred.supervisor.common.repository.command.UpdateFileRepository;
import com.superred.supervisor.common.repository.operation.terminal.TerminalOperationExecRepository;
import com.superred.supervisor.common.repository.terminal.TerminalAgentInfoRepository;
import com.superred.supervisor.gateway.constant.auth.AuthActiveEnum;
import com.superred.supervisor.gateway.constant.auth.AuthStatusEnum;
import com.superred.supervisor.gateway.exception.ApiUnAuthException;
import com.superred.supervisor.gateway.model.dto.terminal.TerminalCmdDTO;
import com.superred.supervisor.gateway.repository.TerminalCommandExtRepository;
import com.superred.supervisor.gateway.service.cache.AgentCacheService;
import com.superred.supervisor.gateway.service.terminal.TerminalHeartBeatService;
import com.superred.supervisor.gateway.utils.AgentAuthUtils;
import com.superred.supervisor.gateway.utils.WebExtUtils;
import com.superred.supervisor.standard.v202505.terminal.cmd.TerminalCmdResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/5/28 10:10
 */
@Service
@Slf4j
public class TerminalHeartBeatServiceImpl implements TerminalHeartBeatService {

    @Resource
    private TerminalAgentInfoRepository terminalAgentInfoRepository;

    @Resource
    private TerminalCommandExtRepository terminalCommandExtRepository;

    @Resource
    private TerminalOperationExecRepository terminalOperationExecRepository;

    @Resource
    private UpdateFileRepository updateFileRepository;

    @Resource
    private AgentCacheService agentCacheService;

    @Resource
    private SimpleMinioUploadClient minioUploadClient;

    /**
     * 处理终端心跳
     *
     * @return 返回处理结果列表
     */
    @Override
    public List<TerminalCmdResp> handleHeatbeat() {
        String deviceId = AgentAuthUtils.getDeviceIdFromRequest();
        log.warn("Received heartbeat from Terminal: {}", deviceId);

        //1. 更新设备心跳时间和状态
        TerminalAgentInfo deviceInfo = agentCacheService.cacheTerminalAgentInfo(deviceId);
        if (deviceInfo == null) {
            throw new ApiUnAuthException(AuthStatusEnum.UNREGISTERED);
        }
        if (deviceInfo.getActivateStatus() != ActivateStatus.ACTIVE) {
            log.error("Terminal {} is not activated, current status: {}", deviceId, deviceInfo.getActivateStatus());
            throw new ApiUnAuthException(AuthActiveEnum.UN_ACTIVATED);
        }

        TerminalAgentInfo updatedDeviceInfo = new TerminalAgentInfo();
        updatedDeviceInfo.setDeviceId(deviceId);
        updatedDeviceInfo.setHeartbeatTime(LocalDateTime.now());
        updatedDeviceInfo.setConnectionStatus(DevicesConnectStatus.ONLINE);
        terminalAgentInfoRepository.updateById(updatedDeviceInfo);
        // 缓存设备心跳时间, 订阅key过期事件更新设备状态
        agentCacheService.cacheTerminalHeartbeat(deviceId);

        //2、系统指令接收
        List<TerminalCmdDTO> operationDTOS = terminalCommandExtRepository.getBaseMapper().selectOperationsByDeviceId(deviceId);

        if (CollUtil.isEmpty(operationDTOS)) {
            log.debug("No commands found for Terminal: {}", deviceId);
            return Collections.emptyList();
        }

        List<TerminalCmdResp> cmdResps = operationDTOS.stream()
                .map(TerminalHeartBeatServiceImpl::buildAgentCmdResp)
                .collect(Collectors.toList());

        //3、更新指令状态为已接收

        List<TerminalOperationExec> updateList = operationDTOS.stream()
                .map(one -> {
                    TerminalOperationExec operationExec = new TerminalOperationExec();
                    operationExec.setId(one.getOperationExecId());
                    operationExec.setExecStatus(OperationExecStatus.ISSUED); // 设置状态为已接收
                    operationExec.setFetchTime(LocalDateTime.now());
                    operationExec.setUpdateTime(LocalDateTime.now());
                    return operationExec;
                }).collect(Collectors.toList());

        terminalOperationExecRepository.updateBatchById(updateList);

        log.warn("终端组件下发指令或策略 :{} ", JsonUtil.toJson(cmdResps));

        return cmdResps;
    }


    private static TerminalCmdResp buildAgentCmdResp(TerminalCmdDTO one) {
        TerminalCmdResp terminalCmdResp = new TerminalCmdResp();
        terminalCmdResp.setType(one.getType());
        terminalCmdResp.setCmd(one.getCmd());
        terminalCmdResp.setCmdId(one.getCmdId());
        terminalCmdResp.setParam(JsonUtil.fromJson(one.getParamJson(), Object.class));
        terminalCmdResp.setModule(one.getModule());
        terminalCmdResp.setSubmodule(JsonUtil.fromJson(one.getSubmoduleJson(), Object.class));
        terminalCmdResp.setVersion(one.getVersion());
        terminalCmdResp.setNum(one.getNum());
        terminalCmdResp.setConfig(one.getConfig());
        return terminalCmdResp;
    }


    /**
     * 内置策略更新指令响应
     *
     * @param filename 需要更新的策略文件名
     * @param response HttpServletResponse对象，用于发送响应
     */
    @Override
    public void innerPolicyUpdateFile(String filename, HttpServletResponse response) {

        UpdateFile updateFile = updateFileRepository.getOne(Wrappers.<UpdateFile>lambdaQuery()
                .eq(UpdateFile::getFilename, filename)
                .last("limit 1"));

        if (updateFile == null) {
            throw new BaseBusinessException("找不到文件");
        }
        String fileMinioPath = CommonConstant.AGENT_BUILD_IN_POLICY_PATH + StrUtil.SLASH + updateFile.getMd5();
        try (InputStream download = minioUploadClient.download(fileMinioPath);
             ServletOutputStream outputStream = response.getOutputStream()) {
            WebExtUtils.setDownloadHeader(response, filename);
            IoUtil.copy(download, outputStream);
            outputStream.flush(); // 确保缓冲区内容发送
        } catch (Exception e) {
            log.error("Error while sending file {} to agent: {}", filename, e.getMessage(), e);
        }
    }

    /**
     * 系统软件升级指令响应
     *
     * @param filename 需要更新的文件名
     * @param version  文件版本
     * @param response HttpServletResponse对象，用于发送响应
     */
    @Override
    public void agentUpgradeFile(String filename, String version, HttpServletResponse response) {
        //do nothing, 终端升级文件不需要响应

    }

    /**
     * 处理离线终端
     * 定时任务处理，设定时间间隔为40秒
     * 那么在TIMEOUT_SECONDS+(0~40)秒内没有收到心跳的终端将被视为离线
     */
    @Override
    public void processOfflineTerminal() {
        // 批量获取过期客户端
        Collection<String> offlineClients = agentCacheService.getOfflineTerminalHeartbeat();
        if (offlineClients.isEmpty()) {
            return;
        }
        log.warn(">>>>>>>>>> 扫描到离线客户端: {}", offlineClients);
        List<TerminalAgentInfo> updateList = offlineClients.stream().map(one -> {
            TerminalAgentInfo agentDeviceInfo = new TerminalAgentInfo();
            agentDeviceInfo.setDeviceId(one);
            agentDeviceInfo.setConnectionStatus(DevicesConnectStatus.OFFLINE);
            return agentDeviceInfo;
        }).collect(Collectors.toList());

        terminalAgentInfoRepository.updateBatchById(updateList);
        // 批量删除过期客户端
        agentCacheService.removeOfflineTerminalAgentHeartbeat(offlineClients);
    }
}
