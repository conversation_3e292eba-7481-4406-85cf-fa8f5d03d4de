package com.superred.supervisor.manager.controller.policy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterMd5PageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterMd5Req;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterMd5Resp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.service.policy.RuleFilterMd5Service;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 规则过滤器MD5控制器
 *
 * <AUTHOR>
 * @since 2025/03/14
 */
@Tag(name = "4.10. 文件筛选策略 - 文件MD5策略")
@RestController
@RequestMapping("/rule/file_filter/md5")
@Slf4j
@Validated
public class RuleFilterMd5Controller {

    @Resource
    private RuleFilterMd5Service ruleFilterMd5Service;

    @Operation(summary = "1 分页")
    @GetMapping("/page")
    public RPage<RuleFilterMd5Resp> page(RuleFilterMd5PageReq ruleFilterMd5PageReq) {
        IPage<RuleFilterMd5Resp> page = this.ruleFilterMd5Service.page(ruleFilterMd5PageReq);
        return new RPage<>(page);
    }

    @Operation(summary = "2 查详情")
    @GetMapping("/{id}")
    public R<RuleFilterMd5Resp> getById(@PathVariable("id") Integer id) {
        RuleFilterMd5Resp resp = this.ruleFilterMd5Service.getById(id);
        return R.success(resp);
    }

    @Operation(summary = "3 新增")
    @PostMapping("/save")
    @SysLogAnn(module = LogTypeConstants.FILE_MD5_STRATEGY, operateType = OperateTypeConstants.ADD, desc = OperateTypeConstants.ADD + LogTypeConstants.FILE_MD5_STRATEGY)
    public R save(@Valid @RequestBody RuleFilterMd5Req ruleFilterMd5Req) {
        this.ruleFilterMd5Service.save(ruleFilterMd5Req);
        return R.success();
    }

    @Operation(summary = "4 编辑")
    @SysLogAnn(module = LogTypeConstants.FILE_MD5_STRATEGY, operateType = OperateTypeConstants.MODIFY, desc = OperateTypeConstants.MODIFY + LogTypeConstants.FILE_MD5_STRATEGY)
    @PostMapping("/edit")
    public R edit(@Valid @RequestBody RuleFilterMd5Req ruleFilterMd5Req) {
        this.ruleFilterMd5Service.edit(ruleFilterMd5Req);
        return R.success();
    }

    @Operation(summary = "5 删除")
    @PostMapping("/del")
    @SysLogAnn(module = LogTypeConstants.FILE_MD5_STRATEGY, operateType = OperateTypeConstants.DELETE, desc = OperateTypeConstants.DELETE + LogTypeConstants.FILE_MD5_STRATEGY)
    public R del(@Valid @RequestBody PolicyBatchIdsReq batchIdsReq) {
        this.ruleFilterMd5Service.del(batchIdsReq);
        return R.success();
    }

    @Operation(summary = "6 查看策略应用策略情况")
    @PostMapping("/policy/{ruleId}")
    public R<List<RulePolicyApplyResp>> policyApply(@PathVariable("ruleId") Long ruleId) {
        List<RulePolicyApplyResp> result = this.ruleFilterMd5Service.policyApply(ruleId);
        return R.success(result);
    }

    @Operation(summary = "7 导出")
    @PostMapping("/export")
    @SysLogAnn(module = LogTypeConstants.FILE_MD5_STRATEGY, operateType = OperateTypeConstants.EXPORT, desc = OperateTypeConstants.EXPORT + LogTypeConstants.FILE_MD5_STRATEGY)
    public void export(HttpServletResponse response, @RequestBody RuleFilterMd5PageReq ruleFilterMd5PageReq) throws IOException {

        // do something
    }

    @Operation(summary = "8 导入")
    @PostMapping("/import")
    @SysLogAnn(module = LogTypeConstants.FILE_MD5_STRATEGY, operateType = OperateTypeConstants.IMPORT, desc = OperateTypeConstants.IMPORT + LogTypeConstants.FILE_MD5_STRATEGY)
    public R<String> importFile(@RequestParam("file") MultipartFile file) {
        return null;
    }

    @Operation(summary = "9 下载模板")
    @PostMapping("/download")
    @SysLogAnn(module = LogTypeConstants.FILE_MD5_STRATEGY, operateType = OperateTypeConstants.DOWNLOAD, desc = LogTypeConstants.FILE_MD5_STRATEGY + "下载模板")
    public void download(HttpServletResponse response) throws IOException {
        //        String filePath = "template/文件MD5策略模板.xlsx";
        //        String fileName = "文件MD5策略模板.xlsx";
        //        ClassPathResource classpathResource = new ClassPathResource(filePath);
        //        InputStream inputStream = classpathResource.getInputStream();
        //        FileUtils.downloadFileExcel(response, inputStream, fileName);

    }
}
