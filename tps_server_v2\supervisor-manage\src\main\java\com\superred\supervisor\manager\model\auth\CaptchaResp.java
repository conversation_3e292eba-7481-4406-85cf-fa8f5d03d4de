package com.superred.supervisor.manager.model.auth;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 验证码响应
 *
 * <AUTHOR>
 * @since 2025/3/11 13:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

public class CaptchaResp {

    /**
     * 验证码图片base64
     */
    @Schema(description = "验证码图片base64")
    private String imageCodeBase64;

    /**
     * 验证码key
     */
    @Schema(description = "验证码key")
    private String imageCodeKey;
}
