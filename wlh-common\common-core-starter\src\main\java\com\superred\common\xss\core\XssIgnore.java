package com.superred.common.xss.core;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 忽略 xss 过滤注解
 * 需要添加在controller 类或方法上
 *
 * <AUTHOR>
 */
@Target({ ElementType.TYPE, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface XssIgnore {

	/**
	 * @return requestBody 需要忽略的字段列表
	 */
	String[] value() default {};

}
