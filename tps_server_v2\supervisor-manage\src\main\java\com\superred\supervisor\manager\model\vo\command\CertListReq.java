package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/03/24
 */
@Data
public class CertListReq {

    @Schema(description = "内置策略文件ID数组")
    @NotEmpty(message = "内置策略文件ID数组不能为空")
    private List<Integer> idList;

    @Schema(description = "内置策略文件名")
    private String filename;


}