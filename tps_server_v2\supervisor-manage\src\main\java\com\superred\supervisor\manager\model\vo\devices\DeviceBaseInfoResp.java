package com.superred.supervisor.manager.model.vo.devices;

import com.superred.supervisor.common.entity.devices.DeviceInfo;
import com.superred.supervisor.common.entity.devices.enums.DeviceAuditType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 相应设备基础信息
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data

public class DeviceBaseInfoResp {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */
    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    private String deviceId;

    /**
     * 设备类型，最长2个数字
     */
    @Schema(description = "设备类型，最长2个数字")
    private String deviceType;


    /**
     * 厂商英文名
     */
    @Schema(description = "厂商英文名")
    private String vendorName;

    /**
     * 产品软件版本号，最长16个字符。如2012.1.5.12
     */
    @Schema(description = "产品软件版本号，最长16个字符。如2012.1.5.12")
    private String softVersion;

    /**
     * 内存总数，单位MB
     */
    @Schema(description = "内存总数，单位MB")
    private String memTotal;

    /**
     * 监测器部署的客户单位名，如“XX信息中心”
     */
    @Schema(description = "监测器部署的客户单位名，如“XX信息中心”")
    private String organs;

    /**
     * 监测器部署的地理位置，如“北京市海淀区复兴路128号区政府接入机房F-12”
     */
    @Schema(description = "监测器部署的地理位置，如“北京市海淀区复兴路128号区政府接入机房F-12”")
    private String address;

    /**
     * 行政区域编码，如“100085”
     */
    @Schema(description = "行政区域编码，如“100085”")
    private String addressCode;


    /**
     * 监测器的公网IP
     */
    @Schema(description = "监测器的公网IP")
    private String publicIpAddress;


    /**
     * 注册状态  0（成功），1（失败），2（审核中） 3离线；4无效；5已删除 6在线
     */
    @Schema(description = "注册状态， 0（成功），1（失败），2（审核中） 3离线；4无效；5已删除 6在线")
    private Integer regStatus;

    /**
     * 注册状态描述，0（成功），1（需从页面录入），2（审核中）
     */
    @Schema(description = "注册状态描述，0（成功），1（需从页面录入），2（审核中）")
    private String regMessage;

    /**
     * 注册时间
     */
    @Schema(description = "注册时间")
    private LocalDateTime regTime;


    /**
     * 设备ca证书序列号
     */
    @Schema(description = "设备ca证书序列号")
    private String deviceCa;


    /**
     * 备注信息，注册原因
     */
    @Schema(description = "备注信息，注册原因")
    private String memo;


    /**
     * 生效时间
     */
    @Schema(description = "证书生效时间")
    private LocalDateTime validateStart;
    /**
     * 失效时间
     */
    @Schema(description = "证书失效时间")
    private LocalDateTime validateEnd;

    /**
     * 审核方式
     */
    @Schema(description = "审核方式")
    private DeviceAuditType auditType;

    public static DeviceBaseInfoResp from(DeviceInfo deviceInfo) {
        DeviceBaseInfoResp deviceBaseInfoResp = new DeviceBaseInfoResp();
        deviceBaseInfoResp.setDeviceId(deviceInfo.getDeviceId());
        deviceBaseInfoResp.setDeviceType(deviceInfo.getDeviceType());
        deviceBaseInfoResp.setVendorName(deviceInfo.getVendorName());
        deviceBaseInfoResp.setSoftVersion(deviceInfo.getSoftVersion());
        deviceBaseInfoResp.setMemTotal(String.valueOf(deviceInfo.getMemTotal()));
        deviceBaseInfoResp.setOrgans(deviceInfo.getOrgans());
        deviceBaseInfoResp.setAddress(deviceInfo.getAddress());
        deviceBaseInfoResp.setAddressCode(deviceInfo.getAddressCode());

        deviceBaseInfoResp.setPublicIpAddress(deviceInfo.getPublicIpAddress());
        deviceBaseInfoResp.setRegStatus(deviceInfo.getRegisterStatus().getValue());
        deviceBaseInfoResp.setRegMessage(deviceInfo.getRegisterMessage());
        deviceBaseInfoResp.setRegTime(deviceInfo.getRegisterTime());
        deviceBaseInfoResp.setDeviceCa(deviceInfo.getDeviceCa());
        deviceBaseInfoResp.setValidateStart(deviceInfo.getCaValidateStart());
        deviceBaseInfoResp.setValidateEnd(deviceInfo.getCaValidateEnd());
        deviceBaseInfoResp.setMemo(deviceInfo.getMemo());
        deviceBaseInfoResp.setAuditType(deviceInfo.getAuditType());

        return deviceBaseInfoResp;
    }
}
