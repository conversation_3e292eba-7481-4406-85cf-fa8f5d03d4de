package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 *  流量异常
 * @since 2025年03月14日
 */
@Data
public class SysFlowExceptionThresholdReq {


    @Schema(description = "流量异常阈值-上行流量数")
    @NotNull
    private Integer upFlowrate;

    @Schema(description = "流量异常阈值-下行流量数")
    @NotNull
    private Integer downFlowrate;


}
