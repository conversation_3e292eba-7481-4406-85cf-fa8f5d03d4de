package com.superred.supervisor.manager.controller.policy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackPermeatePageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackPermeateReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAttackPermeateResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.service.policy.RuleAttackPermeateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-12 16:00
 */
@Tag(name = "4.6. 攻击窃密策略 - 渗透攻击检测策略")
@RestController
@RequestMapping("/rule/attack/permeate")
@Slf4j
@Validated
public class RuleAttackPermeateController {

    @Resource
    private RuleAttackPermeateService ruleAttackPermeateService;

    @Operation(summary = "1 分页")
    @GetMapping("/page")
    public RPage<RuleAttackPermeateResp> page(RuleAttackPermeatePageReq ruleAttackPermeatePageReq) {
        IPage<RuleAttackPermeateResp> page = this.ruleAttackPermeateService.page(ruleAttackPermeatePageReq);
        return new RPage<>(page);
    }

    @Operation(summary = "2 查询详情")
    @GetMapping("/{ruleId}")
    public R<RuleAttackPermeateResp> getById(@PathVariable("ruleId") Long ruleId) {
        RuleAttackPermeateResp resp = this.ruleAttackPermeateService.getById(ruleId);
        return R.success(resp);
    }

    @Operation(summary = "3 新增")
    @SysLogAnn(module = LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY, operateType = OperateTypeConstants.ADD, desc = OperateTypeConstants.ADD + LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY)
    @PostMapping("/save")
    public R save(@Valid @RequestBody RuleAttackPermeateReq ruleAttackPermeateReq) {
        this.ruleAttackPermeateService.save(ruleAttackPermeateReq);
        return R.success();
    }

    @Operation(summary = "4 编辑")
    @SysLogAnn(module = LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY, operateType = OperateTypeConstants.MODIFY, desc = OperateTypeConstants.MODIFY + LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY)
    @PostMapping("/edit")
    public R edit(@Valid @RequestBody RuleAttackPermeateReq ruleAttackPermeateReq) {
        this.ruleAttackPermeateService.edit(ruleAttackPermeateReq);
        return R.success();
    }

    @Operation(summary = "5 删除")
    @PostMapping("/del")
    @SysLogAnn(module = LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY, operateType = OperateTypeConstants.DELETE, desc = OperateTypeConstants.DELETE + LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY)
    public R del(@Valid @RequestBody PolicyBatchIdsReq batchIdsReq) {
        this.ruleAttackPermeateService.del(batchIdsReq);
        return R.success();
    }

    @Operation(summary = "6 查看策略应用策略情况")
    @PostMapping("/policy/{ruleId}")
    public R<List<RulePolicyApplyResp>> policyApply(@PathVariable("ruleId") Long ruleId) {
        List<RulePolicyApplyResp> result = this.ruleAttackPermeateService.policyApply(ruleId);
        return R.success(result);
    }

    @Operation(summary = "7 导出")
    @PostMapping("/export")
    @SysLogAnn(module = LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY, operateType = OperateTypeConstants.EXPORT, desc = OperateTypeConstants.EXPORT + LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY)
    public void export(HttpServletResponse response, @RequestBody RuleAttackPermeatePageReq ruleAttackPermeatePageReq) {

        // do something
    }

    @Operation(summary = "8 导入")
    @PostMapping("/import")
    @SysLogAnn(module = LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY, operateType = OperateTypeConstants.IMPORT, desc = OperateTypeConstants.IMPORT + LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY)
    public R importFile(@RequestParam("file") MultipartFile file) throws IOException {
        return null;
    }

    @Operation(summary = "9 下载模版")
    @PostMapping("/download")
    @SysLogAnn(module = LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY, operateType = OperateTypeConstants.DOWNLOAD, desc = LogTypeConstants.PENETRATION_ATTACK_DETECTION_STRATEGY + "下载模板")
    public void download(HttpServletResponse response) throws IOException {
        //        String filePath = "template/渗透攻击检测策略模版.xlsx";
        //        String fileName = "渗透攻击检测策略模版.xlsx";
        //        ClassPathResource classpathResource = new ClassPathResource(filePath);
        //        InputStream inputStream = classpathResource.getInputStream();
        //        FileUtils.downloadFileExcel(response, inputStream, fileName);

    }
}
