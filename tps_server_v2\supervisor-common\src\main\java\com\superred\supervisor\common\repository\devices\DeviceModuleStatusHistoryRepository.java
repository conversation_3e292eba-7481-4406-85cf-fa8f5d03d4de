package com.superred.supervisor.common.repository.devices;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.devices.DeviceModuleStatusHistory;
import com.superred.supervisor.common.mapper.devices.DeviceModuleStatusHistoryMapper;
import org.springframework.stereotype.Repository;

/**
 * 检测器模块状态历史 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-18 20:06:46
 */
@Repository
public class DeviceModuleStatusHistoryRepository extends ServiceImpl<DeviceModuleStatusHistoryMapper, DeviceModuleStatusHistory> {

}

