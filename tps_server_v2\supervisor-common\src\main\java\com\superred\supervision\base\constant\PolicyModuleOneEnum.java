package com.superred.supervision.base.constant;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-25 14:02
 */
@Getter
public enum PolicyModuleOneEnum {
    ALARM("alarm", "攻击窃密检测", Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey(), PolicyDeviceTypeEnum.AGENT.getKey()), 1),
    FILE_FILTER("file_filter", "文件筛选", Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey(), PolicyDeviceTypeEnum.AGENT.getKey()), 2),
    NET_AUDIT("net_audit", "网络行为审计", Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 3),
    //OBJECT_LISTEN("object_listen", "目标流量审计", Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 4),
    DEVICE_INFO("device_info", "设备信息采集策略", Arrays.asList(PolicyDeviceTypeEnum.AGENT.getKey()), 5),
    NETWORK_BEHAVIOR("network_behavior", "风险软件使用行为采集", Arrays.asList(PolicyDeviceTypeEnum.AGENT.getKey()), 6);

    private final String key;
    private final String value;
    private final List<String> deviceType;
    private final Integer sort;

    PolicyModuleOneEnum(String key, String value, List<String> deviceType, Integer sort) {
        this.key = key;
        this.value = value;
        this.deviceType = deviceType;
        this.sort = sort;
    }

    public static List<PolicyModuleOneEnum> get(String deviceType) {
        List<PolicyModuleOneEnum> list = new ArrayList<>();
        for (PolicyModuleOneEnum value : PolicyModuleOneEnum.values()) {
            if (value.deviceType.contains(deviceType)) {
                list.add(value);
            }
        }
        return list;
    }
}
