package com.superred.supervisor.common.entity.app.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 注册状态，0 审核通过 1  审核不通过 2 待审核 3 禁用
 *
 * <AUTHOR>
 * @since 2025/6/30 10:34
 */
@Getter
@AllArgsConstructor
public enum AppAgentRegStatus {

    PASS(0, "审核通过"),
    REJECT(1, "审核不通过"),
    PENDING(2, "待审核"),
    DISABLED(3, "禁用");

    private final Integer code;
    private final String desc;

    public static AppAgentRegStatus of(Integer code) {
        for (AppAgentRegStatus status : AppAgentRegStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
