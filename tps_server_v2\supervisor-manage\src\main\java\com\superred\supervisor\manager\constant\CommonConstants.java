package com.superred.supervisor.manager.constant;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.ImmutableSet;

import java.util.Set;

/**
 * 通用常量
 */
public final class CommonConstants {

    public static final String TOKEN_TYPE = "bearer";
    public static final String LOCALHOST = "127.0.0.1";
    // 用户登录密码错误锁定时长（分钟）
    public static final String SYS_LOGIN_LOCKTIME = "sysLoginMaxLockTime";
    // 用户登录密码错误最大尝试次数
    public static final String SYS_LOGIN_MAXTRYCOUNT = "sysLoginMaxTryCount";
    // 登录超时
    public static final String SYS_OVER_TIME = "sysOvertime";
    // 密码修改周期
    public static final String SYS_PASS_CHANE = "sysPassChange";
    /**
     * 检查token是否过期url, 请求不续期token
     */
    public static final String CHECK_TOKEN_URL = "/**/auth/check_token/expire";
    /**
     * 全网系统管理员角色ID
     */
    public static final Integer SYS_ADMIN_ID = 1034;
    /**
     * 全网安全管理员角色ID
     */
    public static final Integer SEC_ADMIN_ID = 1035;
    /**
     * 全网审计管理员角色ID
     */
    public static final Integer AUD_ADMIN_ID = 1036;


    public static final Set<Integer> ADMIN_ROLE_IDS = CollUtil.newHashSet(SYS_ADMIN_ID, SEC_ADMIN_ID, AUD_ADMIN_ID);


    /**
     * 默认密码 Aa12345678! hash
     */
    public static final String PASSWORD_HASH = "$2a$10$3bBVCxJ0K3kre0aM9ksP3u4ls22cxWNJQQJ9svaPG6dVmp1gzUbw6";

    /**
     * 指令策略生效路径版本标识
     */
    public static final String EFFECT_ZONE_VERSION = "0";


    public static final String AGENT_AUTO_CHECK = "agentAutoCheck";

    public static final String DEVICE_AUTO_CHECK = "deviceAutoCheck";

    public static final String COMMAND = "command";

    /**
     * jcq 策略moudle 参数集合
     */
    public static final Set<String> JCQ_POLICY_MODULES = ImmutableSet.of("trojan", "attack", "malware", "ip_blacklist", "domain_blacklist",
            "url_blacklist", "account_blacklist", "abnormal", "alarm_ip_whitelist", "alarm_hash_whitelist", "keyword_filter",
            "account_filter", "encryption_filter", "keyword_detect", "file_md5", "ip_listen", "domain_listen", "net_log",
            "app_behavior", "audit_abnormal_whitelist", "audit_ip_whitelist", "audit_domain_whitelist");

    public static final String LOG_USERNAME = "username";

    private CommonConstants() {

    }


}
