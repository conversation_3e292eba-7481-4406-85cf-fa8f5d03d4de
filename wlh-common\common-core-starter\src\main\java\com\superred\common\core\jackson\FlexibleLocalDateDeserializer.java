package com.superred.common.core.jackson;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * LocalDate反序列化支持 多种格式
 *
 * <AUTHOR>
 * @since 2025/5/20 17:20
 */
public class FlexibleLocalDateDeserializer extends JsonDeserializer<LocalDate> {

    @Override
    public LocalDate deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonToken token = p.getCurrentToken();
        if (token == JsonToken.VALUE_NUMBER_INT) {
            long timestamp = p.getLongValue();
            return Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDate();
        } else if (token == JsonToken.VALUE_STRING) {
            String text = p.getText().trim();
            return LocalDate.parse(text, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } else if (token == JsonToken.START_ARRAY) {
            // 形如 [2024,5,20]
            int year = p.nextIntValue(-1);
            int month = p.nextIntValue(-1);
            int day = p.nextIntValue(-1);
            // 处理 END_ARRAY
            if (p.nextToken() != JsonToken.END_ARRAY) {
                throw new JsonParseException(p, "Expected end of array after [year, month, day]");
            }
            return LocalDate.of(year, month, day);
        } else {
            throw new JsonParseException(p, "Unsupported type for LocalDate");
        }
    }
}
