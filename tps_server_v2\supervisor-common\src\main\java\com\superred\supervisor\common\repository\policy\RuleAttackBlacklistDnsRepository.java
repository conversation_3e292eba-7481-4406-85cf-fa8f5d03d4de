package com.superred.supervisor.common.repository.policy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.RuleAttackBlacklistDns;
import com.superred.supervisor.common.mapper.policy.RuleAttackBlacklistDnsMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2025-03-12 17:04
 */
@Slf4j
@Repository
@AllArgsConstructor
public class RuleAttackBlacklistDnsRepository extends ServiceImpl<RuleAttackBlacklistDnsMapper, RuleAttackBlacklistDns> {
}
