package com.superred.supervisor.common.entity.policy;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 黑名单检测策略-域名黑名单
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_rule_attack_blacklist_dns", autoResultMap = true)
public class RuleAttackBlacklistDns extends Model<RuleAttackBlacklistDns> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "rule_id")
    private String ruleId;

    /**
     * 域名信息
     */
    //@TableField(typeHandler = EncryptAndDecryptHandler.class)
    private String dns;

    /**
     * 策略类型  0表示文本表达式; 1表示正则表达式。
     */
    private String ruleType;

    /**
     * 匹配类型 当策略类型rule_type为0时有效。  0表示子串匹配， 1表示右匹配， 2表示左匹配， 3表示完全匹配。 匹配不区分大小写。
     */
    private String matchType;

    /**
     * 策略名称
     */
    private String ruleName;

    /**
     * 攻击分类 1. 窃密木马 2. 远控木马 3. 电脑病毒 4. 僵尸网络 5. 网络蠕虫 6. 间谍软件 7. 挖矿木马 8. 黑客工具 9. 勒索软件 10. 恶意文档 11. 后门程序 99. 其它
     */
    private String attackClass;

    /**
     * 攻击组织
     */
    private String attackGroup;

    /**
     * 攻击阶段枚举分类 1 侦查扫描 2 攻击渗透 3 样本投递 4 持续控制 5 横向移动 6 数据窃取 99 其它
     */
    private String attackStage;

    /**
     * 攻击设施类型ID 1 密码爆破 2 漏洞扫描 3 样本分发 4 恶意发邮 5 钓鱼网站 6 信息搜集 7 数据窃取 8 命令控制 99 其它
     */
    private String facilityType;

    /**
     * 描述信息
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 告警级别，0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）。
     */
    private String risk;

    /**
     * 策略应用状态，0未应用，1已应用
     */
    private String status;

    /**
     * 是否共享状态，0是，1否
     */
    private String isShare;

    /**
     * 策略来源 1 本级 2上级
     */
    private String ruleSource;

    /**
     * 平台级别
     */
    private String level;
    /**
     * 策略更新时间
     */
    private String updateTime;

    /**
     * 策略创建时间
     */
    private String createTime;

    /**
     * 扩展字段1
     */
    private Long ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 上级共享策略ID
     */
    private Long upRuleId;

}
