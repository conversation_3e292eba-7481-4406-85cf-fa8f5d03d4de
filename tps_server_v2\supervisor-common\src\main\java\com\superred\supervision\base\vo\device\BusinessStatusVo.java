package com.superred.supervision.base.vo.device;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 业务状态
 * <AUTHOR>
 * @since 2022/6/17 10:58
 **/
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
@ToString
public class BusinessStatusVo implements Serializable {

    private Integer uptime;

    private String softVersion;

    private String time;

    @JsonProperty("interface")
    private List<InterfaceConnectVo> interfaces;

    private List<ModuleStatusVo> moduleStatus;

    private List<Suspected> suspected;
}
