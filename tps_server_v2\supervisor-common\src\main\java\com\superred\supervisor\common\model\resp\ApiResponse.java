package com.superred.supervisor.common.model.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

/**
 * <AUTHOR>
 * @since 2022/6/14 15:37
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(NON_NULL)
@ToString
public class ApiResponse<T> {

    public static final int SUCCESS_CODE = 0;

    public static final int FAIL_CODE = 1;

    /**
     * 返回码 响应状态：0为成功，1为失败
     */
    @Schema(description = "", example = "0")
    private Integer type;


    @Schema(description = "响应报文")
    private T data;


    /**
     * 返回信息
     */
    @Schema(description = "返回信息", example = "成功")
    private String message;


    private ApiResponse(String message) {
        this.message = message;
    }

    private ApiResponse(Integer code, String msg) {
        this.type = code;
        this.message = msg;
    }


    private ApiResponse(Integer code, String msg, T data) {
        this.type = code;
        this.message = msg;
        this.data = data;
    }


    public static ApiResponse<String> success() {
        return new ApiResponse<>(SUCCESS_CODE, "请求成功");
    }

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(SUCCESS_CODE, "请求成功", data);
    }

    public static <T> ApiResponse<T> success(T data, String msg) {
        return new ApiResponse<>(SUCCESS_CODE, msg, data);
    }

    public static <T> ApiResponse<T> build(T data, Integer code, String msg) {
        return new ApiResponse<>(SUCCESS_CODE, msg, data);
    }


    public static <T> ApiResponse<T> failure(Integer code, String msg) {
        return new ApiResponse<>(code, msg);
    }

    public static <T> ApiResponse<T> failure(Integer code) {

        return new ApiResponse<>(code, "请求失败");
    }

    public static <T> ApiResponse<T> failure(String message) {
        return new ApiResponse<>(FAIL_CODE, message);
    }


    @JsonIgnore
    public boolean isSuccess() {
        return SUCCESS_CODE == this.type;
    }

    public ApiResponse<T> msg(String message) {
        this.message = message;
        return this;
    }

}
