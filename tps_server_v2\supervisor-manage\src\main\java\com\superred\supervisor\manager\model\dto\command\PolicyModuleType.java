package com.superred.supervisor.manager.model.dto.command;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum PolicyModuleType {

    /**
     * 木马活动检测
     */
    TROJAN("trojan", "木马活动检测", "ruleAttackTrojan", PolicyDetectType.ALARM, 1),
    /**
     * 渗透行为检测
     */
    ATTACK("attack", "渗透行为检测", "ruleAttackPermeate", PolicyDetectType.ALARM, 2),
    /**
     * 异常行为检测
     */
    ABNORMAL("abnormal", "异常行为检测", "ruleAttackAbnormal", PolicyDetectType.ALARM, 3),
    /**
     * 恶意文件检测
     */
    MALWARE("malware", "恶意文件检测", "ruleAttackMalware", PolicyDetectType.ALARM, 4),
    /**
     * URL黑名单
     */
    URL_BLACKLIST("url_blacklist", "URL黑名单", "ruleAttackBlacklistUrl", PolicyDetectType.ALARM, 5),
    /**
     * 帐号黑名单检测
     */
    ACCOUNT_BLACKLIST("account_blacklist", "帐号黑名单检测", "ruleAttackBlacklistAccount", PolicyDetectType.ALARM, 6),
    /**
     * IP黑名单
     */
    IP_BLACKLIST("ip_blacklist", "IP黑名单", "ruleAttackBlacklistIp", PolicyDetectType.ALARM, 7),
    /**
     * 域名黑名单
     */
    DOMAIN_BLACKLIST("domain_blacklist", "域名黑名单", "ruleAttackBlacklistDns", PolicyDetectType.ALARM, 8),
    /**
     * IP白名单
     */
    ALARM_IP_WHITELIST("alarm_ip_whitelist", "IP白名单", "ruleAttackWhitelistIp", PolicyDetectType.ALARM, 9),
    /**
     * 文件哈希白名单
     */
    ALARM_HASH_WHITELIST("alarm_hash_whitelist", "文件哈希白名单", "ruleAttackWhitelistHash", PolicyDetectType.ALARM, 10),

    /**
     * 关键词检测
     */
    KEYWORD_DETECT("keyword_detect", "关键词检测", "ruleDetectKeyword", PolicyDetectType.SENSITIVE, 1),
    /**
     * 文件MD5检测
     */
    FILE_MD5("file_md5", "文件MD5检测", "ruleDetectFileMd5", PolicyDetectType.SENSITIVE, 2),

    /**
     * 关键词筛选
     */
    KEYWORD_FILTER("keyword_filter", "关键词筛选", "ruleFilterKeyword", PolicyDetectType.FILE_FILTER, 1),
    /**
     * 账号文件筛选
     */
    ACCOUNT_FILTER("account_filter", "账号文件筛选", "ruleFilterAccount", PolicyDetectType.FILE_FILTER, 2),
    /**
     * 加密文件筛选
     */
    ENCRYPTION_FILTER("encryption_filter", "加密文件筛选", "ruleFilterEncryption", PolicyDetectType.FILE_FILTER, 3),

    /**
     * 通联关系审计
     */
    NET_LOG("net_log", "通联关系审计", "ruleAuditNetlog", PolicyDetectType.NET_AUDIT, 1),
    /**
     * 应用行为审计
     */
    APP_BEHAVIOR("app_behavior", "应用行为审计", "ruleAuditAppbehavior", PolicyDetectType.NET_AUDIT, 2),
    /**
     * IP审计白名单
     */
    AUDIT_IP_WHITELIST("audit_ip_whitelist", "IP审计白名单", "ruleAuditWhitelistIp", PolicyDetectType.NET_AUDIT, 3),
    /**
     * 域名白名单
     */
    AUDIT_DOMAIN_WHITELIST("audit_domain_whitelist", "域名白名单", "ruleAuditWhitelistDns", PolicyDetectType.NET_AUDIT, 4),

    /**
     * IP审计
     */
    IP_LISTEN("ip_listen", "IP审计", "ruleListenIp", PolicyDetectType.OBJECT_LISTEN, 1),
    /**
     * 域名审计
     */
    DOMAIN_LISTEN("domain_listen", "域名审计", "ruleListenDns", PolicyDetectType.OBJECT_LISTEN, 2),

    /**
     * 活动对象审计
     */
    ACTIVE_OBJECT_AUDIT("active_object_audit", "活动对象审计", "", PolicyDetectType.ACTIVE_OBJECT_AUDIT, 1);

    private final String key;
    private final String desc;
    private final String serviceName;
    private final PolicyDetectType parent;
    private final Integer sort;

    PolicyModuleType(String key, String desc, String serviceName, PolicyDetectType parent, Integer sort) {
        this.key = key;
        this.serviceName = serviceName;
        this.parent = parent;
        this.desc = desc;
        this.sort = sort;
    }

    public static PolicyModuleType getPolicyModuleTypeByKey(String key) {
        for (PolicyModuleType type : PolicyModuleType.values()) {
            if (type.getKey().equals(key)) {
                return type;
            }
        }
        return null;
    }

    public static String getDescTypeBySort(Integer sort, PolicyDetectType parent) {
        for (PolicyModuleType type : PolicyModuleType.values()) {
            if (type.getParent().equals(parent) && type.getSort().equals(sort)) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static String getDescNameBySort(String typeName, PolicyDetectType parent) {
        for (PolicyModuleType type : PolicyModuleType.values()) {
            if (type.getParent().equals(parent) && type.getKey().equals(typeName)) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static String getDescObjectListenTypeBySort(String key, PolicyDetectType parent) {
        for (PolicyModuleType type : PolicyModuleType.values()) {
            if (type.getParent().equals(parent) && type.getKey().startsWith(key)) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static String getDescObjectListenTypeByKey(String key, PolicyDetectType parent) {
        for (PolicyModuleType type : PolicyModuleType.values()) {
            if (type.getParent().equals(parent) && type.getKey().equals(key)) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static PolicyModuleType getPolicyModuleTypeByServiceName(String serviceName) {
        for (PolicyModuleType type : PolicyModuleType.values()) {
            if (type.getServiceName().equals(serviceName)) {
                return type;
            }
        }
        return null;
    }

    public static List<PolicyModuleType> getPolicyModuleTypeByParent(PolicyDetectType parent) {
        List<PolicyModuleType> policyModuleTypes = new ArrayList<>();
        for (PolicyModuleType type : PolicyModuleType.values()) {
            if (Objects.equals(parent, type.getParent())) {
                policyModuleTypes.add(type);
            }
        }
        return policyModuleTypes;
    }
}
