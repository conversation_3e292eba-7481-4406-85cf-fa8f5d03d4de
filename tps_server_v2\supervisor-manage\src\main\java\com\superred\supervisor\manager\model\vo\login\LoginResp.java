package com.superred.supervisor.manager.model.vo.login;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录响应
 *
 * <AUTHOR>
 * @since 2025/3/6 16:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

public class LoginResp {


    @Schema(description = "访问令牌")
    private String accessToken;

    @Schema(description = "令牌类型")
    private String tokenType;

    @Schema(description = "token过期时间s")
    private Integer expiresIn;

    @Schema(description = "用户角色信息")
    private String roleName;

    @Schema(description = "用户角色编码")
    private String roleCode;

    @Schema(description = "用户角色ID")
    private Integer roleId;

    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "用户名")
    private String username;

    /**
     * 是否提醒pass修改
     */
    @Schema(description = "是否提醒pass修改，true需要修改")
    private Boolean isTipPassUpdate;

}
