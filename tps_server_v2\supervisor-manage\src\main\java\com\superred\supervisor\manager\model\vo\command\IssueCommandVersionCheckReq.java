package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class IssueCommandVersionCheckReq {
    @Schema(description = "设备编号")
    private List<String> deviceIdList;

    @Schema(description = "具体的文件检查类型, ls:读取目录文件列表, get_file:读取文件内容, md5sum:判断文件MD5")
    private String method;

    @Schema(description = "检查文件名")
    private String filename;

    @Schema(description = "读取的开始偏移")
    private Integer offset;

    @Schema(description = "读取长度")
    private Integer length;

    @Schema(description = "检查文件完整路径")
    private String path;

    @Schema(description = "本地上传文件计算值")
    private String upValue;

}
