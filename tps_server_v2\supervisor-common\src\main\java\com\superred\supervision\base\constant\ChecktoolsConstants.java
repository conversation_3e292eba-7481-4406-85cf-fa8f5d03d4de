package com.superred.supervision.base.constant;

/**
 * <AUTHOR>
 * @create 2024-04-08 15:19
 */
public final class ChecktoolsConstants {

    private ChecktoolsConstants() {
    }

    /**
     * 备份的数据库名称
     */
    public static final String DATABASESNAME_BACKUP = "authority_manage cascade_manager checktoolsdb config_manager host_manager";

    public static final String DATABASE_NAME = "allDatabases";

    public static final String DATABASESNAME_BACKUP_FILE_SUFFIX = "backup.sql";

    /**
     * 备份时忽略的表明
     */
    public static final String DATABASE_BACKUP_IGNORE_TABLE = " --ignore-table=checktoolsdb.t_db_backup --ignore-table=checktoolsdb.t_es_backup --ignore-table=authority_manage.am_authorization_info ";

    public static final String REDIS_COMPUTER_INFO_BEGINE = "checktools-mgr-service-computer-";

    public static final String ONE_KEY_RESTORE_BACKUP = "一键还原备份";

    /**
     * 文件内容检查index
     */
    public static final String FILE_CONTENT_CHECK_INDEX = "checktools_file_file_content";

    public static final String FILE_CHECK_INDEX_NAME_BEGIN = "checktools_file_";

    public static final String LINUX_CHECKTOOLS_MB_FILE = "checktools_mb_file";

    public static final String WIN_INDEX_TAG = "window_";

    public static final String WIN = "window";

    public static final String CHECKTYPE_STATISTICALREPORT = "统计报告";

    public static final String CHECKTYPE_ILLEGALREPORT = "违规报告";

    public static final String CHECKTYPE_OVERALLREPORT = "整体报告";

    public static final String NO_VIOLATION = "不违规";

    public static final Integer MAP_SIZE_128 = 128;

    public static final Integer MAP_SIZE_16 = 16;

    public static final String CHECKPARAM = "CHECK_PARAM";

    public static final String AOP_LOG_KEY = "aop-log";

    public static final String FAIL_AOP_LOG_KEY = "fail-aop-log";

    public static final String RESOURCE_LOCATIONS_PREFIX = "file:";

    public static final String HOST_REPORT_DIR = "/host_report";
    public static final String HOST_REPORT_HTML_NAME = "result.html";
    public static final String DEPT_REPORT_DIR = "deptreport";

    public static final String REPORT_BASE_PATH = "/report";

    public static final String DEPT_REP_FILE_NAME_PREFIX = "deptreport_";

    public static final String CHECK_TOOLS_HOST_REPORT_ROUTE = "/api/checktools-manage-service/report/";

    public static final String GATEWAY_NAME = "gateway-service";

    public static final String CHECK_TOOLS_DEPT_REPORT_ROUTE = "/api/checktools-manage-service/report/deptreport";

    public static final String INDEX_PATH = "/index.html";


    public static final String RESOLVE_TASKINFO = "checktools-mgr-service-resolve-taskinfo-";

    public static final String FLAG_TRUE = "true";

    public static final String FLAG_FALSE = "false";

    public static final String REDIS_CHECKPARAM_DECODE = "checktools-mgr-service-checkparam-decoded-";

    public static final String REDIS_CHECKPARAM = "checktools-mgr-service-checkparam-";

    public static final String CHECK_ITEM_WARELESS = "wareless";

    public static final String CHECK_ITEM_BLUETOOTH_INFRARED = "bluetooth_infrared";

    public static final String CHECK_ITEM_WIFI_RECORD = "wifi_record";

    public static final String CHECK_ITEM_NET_STATE = "net_state";
    public static final String XML = "xml";

    public static final String CHECK_ITEM_INSTALLED_SOFTWARE = "installed_software";

    public static final String CHECK_ITEM_OTHER_SOFTWARE = "other_software";

    /**
     * 涉密checkparam
     */
    public static final String CHECK_PARAM_SECRET = "CHECK_PARAM_SECRET";
    /**
     * 非密checkparam
     */
    public static final String CHECK_PARAM_NON_SECRET = "CHECK_PARAM_NON_SECRET";

    public static final String ES_USB_PRINTER = "usb_printer";

    public static final String ES_USB_STORAGE = "usb_storage";

    public static final String OFFLINE_FILE_DIR = "offline_xml_report";

    public static final String HOST_REPOST_ZIP_NAME = "主机检查报告集合.zip";

    public static final String DEPT_REPOST_ZIP_NAME = "部门检查报告集合.zip";
    public static final String UN_CHECK_STR = "未检查";

    public static final String ILLEGAL = "违规";

    public static final String NOT_SECRET_UN_ILLEAGE = "可能违规存储涉密信息";
    public static final String NOT_SECRET_ILLEAGE = "违规存储涉密信息";

    public static final String SECRET_UN_ILLEAGE = "可能越级存储涉密信息";
    public static final String SECRET_ILLEAGE = "越级存储涉密信息";

    public static final String COMMON_UN_ILLEAGE = "关键字命中";

    public static final String ONE_VOTE_VETO = "一票否决";
    public static final int DEFAULT_SIZE = 500;
    public static final int FOUR = 4;


    public static final String DEFAULT_RESULTDESC = "在该次检查中，所检查的终端没有违规情况！";
    public static final String DEFAULT_USBPROBLEM = "在该次检查中，无USB设备交叉使用的违规情况！";
    public static final String DEFAULT_SUGGESTION = "基于本次检查的结果，所检查的终端没有违规情况，无需进行整改！";

    public static final String CHECK_ITEM_FILECONTENT = "file_content";

    public static final String CHECK_ITEM_FILE_DDLOG = "file_ddlog";

    public static final String CHECK_ITEM_DEEP_RECOVER = "deep_recover";

    public static final String CHECK_ITEM_VM_SOFTWARE = "vm_software";

    public static final String ROOT_DEPT_CODE = "01000000000000000000";

    public static final String REDIS_MD5_INFOS = "checktools-mgr-service-md5info";

    public static final String REDIS_CLIENT_INSTALL_PACKAGE_COMBOBOX = "checktools-mgr-service:client:installpackage:combobox";

    //7z解压命令，7za -o目标文件夹 x 解压文件
    public static final String SEVEN_Z_DECOMPRESSION_COMMAND = "7za -o%s x %s";

}
