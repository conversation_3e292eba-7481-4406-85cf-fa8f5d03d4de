package com.superred.supervisor.manager.controller.devices;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditDetailResp;
import com.superred.supervisor.manager.model.vo.devices.manager.BusinessStatusDetectorResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceEventlogReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceFlowThresholdEditReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceInterfaceMirrorStatusResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceModuleStatusResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DevicePolicyContentResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceRemarkEditReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceStateInfoPageResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceStatusEditReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceStatusPageReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceSuspectedStatusPageResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceSuspectedTypeStatisticsResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceSystemStatusResp;
import com.superred.supervisor.manager.model.vo.devices.manager.SystemStatusCpuResp;
import com.superred.supervisor.manager.service.devices.DeviceInfoService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * 设备基础信息控制器
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Tag(name = "3.2. 设备状态管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/device_base_info/status")
public class DeviceStatusMangerController {


    @Resource
    private DeviceInfoService deviceInfoService;

    @ApiOperationSupport(order = 1)
    @Operation(summary = "1. 分页查询")
    @PostMapping("/page")
    public RPage<DeviceStateInfoPageResp> stateInfoPage(@Valid @RequestBody DeviceStatusPageReq req) {
        IPage<DeviceStateInfoPageResp> page = deviceInfoService.stateInfoPage(req);
        return new RPage<>(page);
    }

    @Operation(summary = "2. 通过设备id查询监测器业务状态信息")
    @GetMapping("/detector/{deviceId}")
    public R<BusinessStatusDetectorResp> getDetectorByDeviceId(@PathVariable("deviceId") String deviceId) {
        BusinessStatusDetectorResp resp = deviceInfoService.getDetectorByDeviceId(deviceId);
        return R.success(resp);
    }


    @Operation(summary = "3. 设备注册信息")
    @GetMapping("/detail/{deviceId}")
    public R<DeviceAuditDetailResp> getAuditDetail(@PathVariable("deviceId") String deviceId) {
        DeviceAuditDetailResp detail = deviceInfoService.getAuditDetail(deviceId);
        return R.success(detail);
    }

//
//    @Operation(summary = "3. 通过设备id查询自监管业务状态信息")
//    @GetMapping("/supervising/{deviceId}")
//    public R<BusinessStatusSupervisingResp> getSupervisingById(@PathVariable("deviceId") String deviceId) {
//
//        return R.success(null);
//    }


    @Operation(summary = "4. 查询系统模块状态")
    @GetMapping("/device_module/{deviceId}")
    public R<List<DeviceModuleStatusResp>> getByDeviceId(@PathVariable("deviceId") String deviceId) {

        return R.success(null);
    }


    @Operation(summary = "5. 系统工作异常状态分页查询")
    @PostMapping("/device_suspected_log/page")
    public RPage<DeviceSuspectedStatusPageResp> getDeviceSuspectedLogPage(@RequestBody DeviceEventlogReq req) {
        IPage<DeviceSuspectedStatusPageResp> page = deviceInfoService.getDeviceSuspectedLogPage(req);

        return new RPage<>(page);
    }


    @Operation(summary = "6. 通过设备id查询网卡连通性")
    @GetMapping("/device_interface_mirror/{deviceId}")
    public R<List<DeviceInterfaceMirrorStatusResp>> listDeviceInterfaceMirror(
            @PathVariable("deviceId") String deviceId) {
        List<DeviceInterfaceMirrorStatusResp> list = deviceInfoService.listDeviceInterfaceMirror(deviceId);
        return R.success(list);
    }

    @Operation(summary = "7. 系统工作异常状态-柱状图")
    @GetMapping("/device_suspected_count_by_type/{deviceId}")
    public R<List<DeviceSuspectedTypeStatisticsResp>> getDeviceSuspectedLogCountByType(
            @PathVariable("deviceId") String deviceId) {


        return R.success(null);
    }

    @Operation(summary = "8. 通过设备id查询系统运行状态")
    @GetMapping("/system/{deviceId}")
    public R<DeviceSystemStatusResp> getSystemStatus(@PathVariable("deviceId") String deviceId) {
        DeviceSystemStatusResp resp = deviceInfoService.getSystemStatus(deviceId);
        return R.success(resp);
    }

    @Operation(summary = "9. 通过id查询CPU状态信息")
    @GetMapping("/cpu/{deviceId}")
    public R<List<SystemStatusCpuResp>> getCpuStatus(@PathVariable("deviceId") String deviceId) {
        List<SystemStatusCpuResp> resps = deviceInfoService.getCpuStatus(deviceId);
        return R.success(resps);
    }


    @Operation(summary = "10. 根据设备id查询生效策略")
    @GetMapping("/policy/content/{deviceId}")
    public R<List<DevicePolicyContentResp>> devicePolicyList(@PathVariable("deviceId") String deviceId) {


        return R.success(null);
    }


    @Operation(summary = "11. 设置设备流量阈值")
    @PostMapping("/update/flow_threshold")
    public R<Boolean> setFlowThreshold(@Valid @RequestBody DeviceFlowThresholdEditReq req) {

        return R.success(null);
    }

    @Operation(summary = "12. 查询设备备注信息")
    @GetMapping("/remarks/{deviceId}")
    public R<String> getRemarksDevice(@PathVariable("deviceId") String deviceId) {
        String res = deviceInfoService.getRemarksDevice(deviceId);
        return R.success(res);
    }

    @Operation(summary = "12. 修改设备备注信息")
    @SysLogAnn(module = LogTypeConstants.ACCESS_DEVICE_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "修改设备备注信息")
    @PostMapping("/remarks")
    public R<Boolean> remarksDevice(@RequestBody DeviceRemarkEditReq deviceBaseInfo) {
        deviceInfoService.updateRemarksDevice(deviceBaseInfo);
        return R.success(null);
    }

    @Operation(summary = "13. 将设备置为有效")
    @PostMapping("/valid")
    public R<Boolean> valid(@Valid @RequestBody DeviceStatusEditReq dto) {

        return R.success(null);
    }

    @Operation(summary = "14. 将设备置为有效")
    @PostMapping("/invalid")
    public R<Boolean> invalid(@Valid @RequestBody DeviceStatusEditReq dto) {

        return R.success(null);
    }

    @Operation(summary = "15. 通过id删除接入设备")
    @PostMapping("/delete/{deviceId}")
    public R<Boolean> removeById(@PathVariable String deviceId) {

        return R.success(null);
    }


}
