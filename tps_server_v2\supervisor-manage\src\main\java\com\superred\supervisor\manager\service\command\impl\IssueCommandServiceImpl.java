package com.superred.supervisor.manager.service.command.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.entity.command.IssueCommand;
import com.superred.supervisor.common.entity.command.UpdateFile;
import com.superred.supervisor.common.entity.command.VersionCheckInfo;
import com.superred.supervisor.common.entity.devices.DeviceInfo;
import com.superred.supervisor.common.entity.devices.enums.RegisterStatus;
import com.superred.supervisor.common.repository.command.UpdateFileRepository;
import com.superred.supervisor.common.repository.command.VersionCheckInfoRepository;
import com.superred.supervisor.common.repository.devices.DeviceInfoRepository;
import com.superred.supervisor.manager.common.enums.command.CommandType;
import com.superred.supervisor.manager.constant.CommonConstants;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandCertUpdateReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDeviceDetailReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDeviceDetailResp;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDeviceReportReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDropDataReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandInnerPolicySwitchReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandInnerPolicyUpdateReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandModuleSwitchReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandPasswordResetReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandPolicyReportReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandRebootReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandShutdownReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandSoftwareUpdateReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsResp;
import com.superred.supervisor.manager.model.vo.command.IssueCommandSyncTimeReq;
import com.superred.supervisor.manager.model.vo.command.IssueCommandVersionCheckReq;
import com.superred.supervisor.manager.model.vo.command.IssueModuleCmd;
import com.superred.supervisor.manager.repository.command.IssueCommandExtRepository;
import com.superred.supervisor.manager.service.CmdIdBuilder;
import com.superred.supervisor.manager.service.command.IssueCommandService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class IssueCommandServiceImpl implements IssueCommandService {
    @Resource
    private DeviceInfoRepository deviceInfoRepository;
    @Resource
    private IssueCommandExtRepository issueCommandExtRepository;
    @Resource
    private VersionCheckInfoRepository versionCheckInfoRepository;
    @Resource
    private UpdateFileRepository updateFileRepository;
    @Resource
    private CmdIdBuilder cmdIdBuilder;


    @Override
    public void shutdown(DetectorCommandShutdownReq req) {
        // 验证jcq是否是禁用状态，如果是就不允许下发
        List<String> deviceIdList = req.getDeviceIdList();
        this.validateDisable(deviceIdList);
        LocalDateTime now = LocalDateTime.now();
        String cmdId = cmdIdBuilder.buildCmdId();
        List<IssueCommand> issueCommandsToSave = deviceIdList.stream()
                .map(deviceId -> buildCommand(cmdId, deviceId, now, CommandType.SHUTDOWN, ""))
                .collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
    }


    @Override
    public void reboot(DetectorCommandRebootReq req) {
        // 验证jcq是否是禁用状态，如果是就不允许下发
        List<String> deviceIdList = req.getDeviceIdList();
        this.validateDisable(deviceIdList);
        LocalDateTime now = LocalDateTime.now();
        String cmdId = cmdIdBuilder.buildCmdId();
        List<IssueCommand> issueCommandsToSave = deviceIdList.stream()
                .map(deviceId -> buildCommand(cmdId, deviceId, now, CommandType.REBOOT, ""))
                .collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
    }

    @Override
    public void syncTime(IssueCommandSyncTimeReq req) {
        // 验证jcq是否是禁用状态，如果是就不允许下发
        List<String> deviceIdList = req.getDeviceIdList();
        this.validateDisable(deviceIdList);
        LocalDateTime now = LocalDateTime.now();
        String cmdId = cmdIdBuilder.buildCmdId();
        List<IssueCommand> issueCommandsToSave = deviceIdList.stream()
                .map(deviceId ->
                        buildCommand(
                                cmdId,
                                deviceId,
                                now,
                                CommandType.SYNC_TIME,
                                DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss")
                        )
                )
                .collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
    }

    @Override
    public void versionCheck(IssueCommandVersionCheckReq req) {
        // 验证jcq是否是禁用状态，如果是就不允许下发
        List<String> deviceIdList = req.getDeviceIdList();
        this.validateDisable(deviceIdList);
        String cmdId = cmdIdBuilder.buildCmdId();
        LocalDateTime now = LocalDateTime.now();
        String method = req.getMethod();

        Map<String, Object> jsonObject = new HashMap<>(16);
        jsonObject.put("method", method);
        if ("ls".equals(method)) {
            jsonObject.put("path", req.getPath());
        } else {
            jsonObject.put("filename", req.getFilename());
            jsonObject.put("offset", req.getOffset());
            jsonObject.put("length", req.getLength());
        }
        String param = JsonUtil.toJson(jsonObject);
        List<IssueCommand> issueCommandsToSave = deviceIdList.stream()
                .map(deviceId -> buildCommand(cmdId, deviceId, now, CommandType.VERSION_CHECK, param))
                .collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
        List<VersionCheckInfo> versionCheckInfoListToSave = deviceIdList.stream().map(deviceId -> {
            VersionCheckInfo versionCheckInfo = new VersionCheckInfo();
            versionCheckInfo.setDeviceId(deviceId);
            versionCheckInfo.setCmdId(cmdId);
            versionCheckInfo.setFilename(req.getFilename());
            versionCheckInfo.setMethod(req.getMethod());
            versionCheckInfo.setPath(req.getPath());
            versionCheckInfo.setOffset(String.valueOf(req.getOffset()));
            versionCheckInfo.setLength(req.getLength());
            versionCheckInfo.setUpValue(req.getUpValue());
            return versionCheckInfo;
        }).collect(Collectors.toList());
        versionCheckInfoRepository.saveBatch(versionCheckInfoListToSave);

    }

    @Override
    public void softwareUpdate(DetectorCommandSoftwareUpdateReq req) {
        // 验证jcq是否是禁用状态，如果是就不允许下发
        List<String> deviceIdList = req.getDeviceIdList();
        this.validateDisable(deviceIdList);
        String cmdId = cmdIdBuilder.buildCmdId();
        UpdateFile updateFile = updateFileRepository.getById(req.getUpdateFileId());
        Map<String, Object> jsonObject = new HashMap<>(16);
        jsonObject.put("soft_version", updateFile.getSoftVersion());
        jsonObject.put("filename", updateFile.getFilename());
        jsonObject.put("md5", updateFile.getMd5());
        String param = JsonUtil.toJson(jsonObject);
        List<IssueCommand> issueCommandsToSave = deviceIdList.stream()
                .map(deviceId -> buildCommand(cmdId, deviceId, LocalDateTime.now(), CommandType.UPDATE, param))
                .collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
    }

    @Override
    public void passwordReset(DetectorCommandPasswordResetReq req) {
        // 验证jcq是否是禁用状态，如果是就不允许下发
        List<String> deviceIdList = req.getDeviceIdList();
        this.validateDisable(deviceIdList);
        LocalDateTime now = LocalDateTime.now();
        String cmdId = cmdIdBuilder.buildCmdId();
        Map<String, Object> jsonObject = new HashMap<>(16);
        jsonObject.put("user", req.getUsername());
        jsonObject.put("passwd", req.getPassword());
        String param = JsonUtil.toJson(jsonObject);
        List<IssueCommand> issueCommandsToSave = deviceIdList.stream()
                .map(deviceId -> buildCommand(cmdId, deviceId, now, CommandType.PASSWD, param))
                .collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
    }

    @Override
    public void dropData(DetectorCommandDropDataReq req) {

        List<String> deviceIds = req.getDeviceIdList();

        this.validateDisable(deviceIds);

        List<IssueCommand> commands = new ArrayList<>();

        if (req.getIsAll() == 1) {
            // 全量下发
            commands.addAll(buildCommandsForAll(deviceIds, CommandType.DROP_DATA));
        } else if (CollUtil.isNotEmpty(req.getSubmodules())) {
            // 按子模块下发
            commands.addAll(buildCommandsForSubmodules(deviceIds, req, CommandType.DROP_DATA));
        } else {
            log.warn("未指定子模块或全量下发标志，无dropData命令生成");
        }

        issueCommandExtRepository.saveBatch(commands);
    }

    @Override
    public void reportPolicy(DetectorCommandPolicyReportReq req) {
        List<String> deviceIds = req.getDeviceIdList();

        this.validateDisable(deviceIds);

        List<IssueCommand> commands = new ArrayList<>();

        if (req.getIsAll() == 1) {
            // 全量下发
            commands.addAll(buildCommandsForAll(deviceIds, CommandType.REPORT_POLICY));
        } else if (CollUtil.isNotEmpty(req.getSubmodules())) {
            // 按子模块下发
            commands.addAll(buildCommandsForSubmodules(deviceIds, req, CommandType.REPORT_POLICY));
        } else {
            log.warn("未指定子模块或全量下发标志，无reportPolicy命令生成");
        }

        issueCommandExtRepository.saveBatch(commands);
    }

    @Override
    public void innerPolicyUpdate(DetectorCommandInnerPolicyUpdateReq req) {
        // 验证jcq是否是禁用状态，如果是就不允许下发
        List<String> deviceIdList = req.getDeviceIdList();
        this.validateDisable(deviceIdList);
        String cmdId = cmdIdBuilder.buildCmdId();
        UpdateFile updateFile = updateFileRepository.getById(req.getUpdateFileId());
        Map<String, Object> jsonObject = new HashMap<>(16);
        jsonObject.put("filename", updateFile.getFilename());
        jsonObject.put("md5", updateFile.getMd5());
        String param = JsonUtil.toJson(jsonObject);
        List<IssueCommand> issueCommandsToSave = deviceIdList.stream()
                .map(deviceId -> buildCommand(cmdId, deviceId, LocalDateTime.now(), CommandType.INNER_POLICY_UPDATE, param))
                .collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
    }

    @Override
    public void moduleSwitch(DetectorCommandModuleSwitchReq req) {
        // 验证jcq是否是禁用状态，如果是就不允许下发
        List<String> deviceIds = req.getDeviceIds();
        this.validateDisable(deviceIds);
        List<IssueModuleCmd> modules = req.getModules();
        LocalDateTime now = LocalDateTime.now();
        List<IssueCommand> issueCommandsToSave = modules.stream()
                .flatMap(module -> deviceIds.stream()
                        .map(deviceId -> buildCommand(module, deviceId, now))
                )
                .collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
    }


    @Override
    public void innerPolicySwitch(DetectorCommandInnerPolicySwitchReq req) {
        String policyModule = req.getPolicyModule();
        if (!CommonConstants.JCQ_POLICY_MODULES.contains(policyModule)) {
            throw new BaseBusinessException("策略Module选项错误");
        }
        List<String> deviceIdList = req.getDeviceIdList();
        validateDisable(deviceIdList);
        LocalDateTime now = LocalDateTime.now();
        List<IssueCommand> issueCommandsToSave = deviceIdList.stream().map(deviceId -> {
            IssueCommand issueCommand = new IssueCommand();
            String cmdId = cmdIdBuilder.buildCmdId();
            issueCommand.setDeviceId(deviceId);
            issueCommand.setType(CommonConstants.COMMAND);
            issueCommand.setCmdId(cmdId);
            issueCommand.setCmd(CommandType.CTRL_INNER_POLICY.getValue());
            Map<String, Object> param = new HashMap<>(16);
            param.put("rule_id", req.getPolicyId());
            param.put("submodule", req.getPolicyModule());
            param.put("vender_id", req.getVenderId());
            param.put("enable", req.getEnable());
            issueCommand.setParam(JsonUtil.toJson(param));
            issueCommand.setCreateTime(now);
            return issueCommand;
        }).collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
    }

    @Override
    public void certUpdate(DetectorCommandCertUpdateReq req) {
        // 验证jcq是否是禁用状态，如果是就不允许下发
        List<String> deviceIdList = req.getDeviceIdList();
        this.validateDisable(deviceIdList);
        // 查询通信证书信息
        UpdateFile updateFile = updateFileRepository.getById(req.getId());
        if (updateFile == null) {
            throw new BaseBusinessException("查询通信证书信息为空错误");
        }
        LocalDateTime now = LocalDateTime.now();
        // 拼接isssueCommod
        List<IssueCommand> issueCommandsToSave = deviceIdList.stream().map(deviceId -> {
            IssueCommand issueCommand = new IssueCommand();
            String cmdId = cmdIdBuilder.buildCmdId();
            issueCommand.setDeviceId(deviceId);
            issueCommand.setType(CommonConstants.COMMAND);
            issueCommand.setCmdId(cmdId);
            issueCommand.setCmd(CommandType.CERT_UPDATE.getValue());
            Map<String, Object> param = new HashMap<>(16);
            param.put("filename", updateFile.getFilename());
            param.put("md5", updateFile.getMd5());
            issueCommand.setParam(JsonUtil.toJson(param));
            issueCommand.setCreateTime(now);
            return issueCommand;
        }).collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
    }

    @Override
    public void deviceReport(DetectorCommandDeviceReportReq req) {
        // 验证jcq是否是禁用状态，如果是就不允许下发
        List<String> deviceIdList = req.getDeviceIdList();
        this.validateDisable(deviceIdList);
        LocalDateTime now = LocalDateTime.now();
        String cmdId = cmdIdBuilder.buildCmdId();
        List<IssueCommand> issueCommandsToSave = deviceIdList.stream()
                .map(deviceId -> buildCommand(cmdId, deviceId, now, CommandType.DEVICE_REPORT, ""))
                .collect(Collectors.toList());
        issueCommandExtRepository.saveBatch(issueCommandsToSave);
    }

    @Override
    public IPage<DetectorCommandStatisticsResp> statisticsPage(DetectorCommandStatisticsReq req) {
        return issueCommandExtRepository.statisticsPage(req);
    }

    @Override
    public IPage<DetectorCommandDeviceDetailResp> deviceDetailPage(DetectorCommandDeviceDetailReq req) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.minusMinutes(3L);
        LambdaQueryWrapper<IssueCommand> query = new LambdaQueryWrapper<>();
        query.eq(IssueCommand::getCmdId, req.getCmdId());
        query.eq(CharSequenceUtil.isNotBlank(req.getDeviceId()), IssueCommand::getDeviceId, req.getDeviceId());
        query.eq(Objects.nonNull(req.getResult()), IssueCommand::getResult, req.getResult());
        query.eq(Objects.nonNull(req.getStatus()), IssueCommand::getStatus, req.getStatus());
        Page<IssueCommand> cmdPage = issueCommandExtRepository.page(new Page<>(req.getStart(), req.getLimit()), query);
        List<IssueCommand> records = cmdPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new Page<>();
        }
        Set<String> deviceIds = records.stream().map(IssueCommand::getDeviceId).collect(Collectors.toSet());
        LambdaQueryWrapper<DeviceInfo> deviceQueryWrapper = new LambdaQueryWrapper<>();
        deviceQueryWrapper.in(DeviceInfo::getDeviceId, deviceIds);
        List<DeviceInfo> deviceInfoList = deviceInfoRepository.list(deviceQueryWrapper);
        Map<String, DeviceInfo> deviceInfoMap = deviceInfoList.stream()
                .collect(Collectors.toMap(DeviceInfo::getDeviceId, Function.identity(), (d1, d2) -> d2));

        LambdaQueryWrapper<VersionCheckInfo> versionCheckQueryWrapper = new LambdaQueryWrapper<>();
        versionCheckQueryWrapper.eq(VersionCheckInfo::getCmdId, req.getCmdId());
        versionCheckQueryWrapper.in(VersionCheckInfo::getDeviceId, deviceIds);
        List<VersionCheckInfo> versionCheckInfos = versionCheckInfoRepository.list(versionCheckQueryWrapper);
        Map<String, List<VersionCheckInfo>> versionCheckInfoMap = versionCheckInfos.stream()
                .collect(Collectors.groupingBy(VersionCheckInfo::getDeviceId));

        List<DetectorCommandDeviceDetailResp> respList = records.stream().map(issueCommand -> {
            DetectorCommandDeviceDetailResp resp = BeanUtil.copyProperties(issueCommand, DetectorCommandDeviceDetailResp.class);
            DeviceInfo deviceInfo = deviceInfoMap.get(issueCommand.getDeviceId());
            if (deviceInfo != null) {
                resp.setAddress(deviceInfo.getAddress());
                resp.setVendorName(deviceInfo.getVendorName());
                // 判断是否在线, 心跳时间小于跟当前时间小于三分钟表示在线, 超过三分钟表示离线
                if (deviceInfo.getHeartbeatTime() != null && deviceInfo.getHeartbeatTime().isAfter(startTime)) {
                    resp.setIsOnline(1);
                } else {
                    resp.setIsOnline(0);
                }
            }

            if (CommandType.VERSION_CHECK.getValue().equals(issueCommand.getCmd())) {
                // 判断版本一致性检查结果
                resp.setMessage(null);
                if (MapUtil.isNotEmpty(versionCheckInfoMap)) {
                    List<VersionCheckInfo> versionCheckInfoList = versionCheckInfoMap.get(issueCommand.getDeviceId());
                    if (CollUtil.isNotEmpty(versionCheckInfoList)) {
                        Integer checkResult = versionCheckInfos.get(0).getCheckResult();
                        resp.setCheckResult(checkResult);
                        if (checkResult != null && checkResult == 0) {
                            resp.setMessage("不一致");
                        } else if (checkResult != null && checkResult == 1) {
                            resp.setMessage("一致");
                        }
                    }
                }
            }
            return resp;
        }).collect(Collectors.toList());
        Page<DetectorCommandDeviceDetailResp> resultPage = new Page<>(req.getStart(), req.getLimit(), (int) cmdPage.getTotal());
        resultPage.setRecords(respList);
        return resultPage;
    }

    /**
     * 构建全量下发的命令
     */
    private List<IssueCommand> buildCommandsForAll(List<String> deviceIds, CommandType commandType) {
        List<IssueCommand> commands = new ArrayList<>();
        for (String deviceId : deviceIds) {
            commands.add(buildCommand(deviceId, null, null, commandType.getValue()));
        }
        return commands;
    }

    /**
     * 构建按子模块下发的命令
     */
    private List<IssueCommand> buildCommandsForSubmodules(List<String> deviceIds, DetectorCommandDropDataReq req, CommandType commandType) {
        List<IssueCommand> commands = new ArrayList<>();
        for (String deviceId : deviceIds) {
            for (String submodule : req.getSubmodules()) {
                if ("blacklist".equals(submodule)) {
                    handleSubmoduleRules(deviceId, submodule, req.getBlacklistRuleIds(), commandType.getValue(), commands);
                } else if ("keyword_detect".equals(submodule)) {
                    handleSubmoduleRules(deviceId, submodule, req.getKeywordsRuleIds(), commandType.getValue(), commands);
                } else if ("file_md5".equals(submodule)) {
                    handleSubmoduleRules(deviceId, submodule, req.getMd5RuleIds(), commandType.getValue(), commands);
                } else {
                    commands.add(buildCommand(deviceId, submodule, null, commandType.getValue()));
                }
            }
        }
        return commands;
    }

    /**
     * 处理子模块规则
     */
    private void handleSubmoduleRules(String deviceId, String submodule, List<String> ruleIds, String cmd, List<IssueCommand> commands) {
        if (CollUtil.isEmpty(ruleIds)) {
            return;
        }
        for (String ruleId : ruleIds) {
            commands.add(buildCommand(deviceId, submodule, ruleId, cmd));
        }
    }


    private IssueCommand buildCommand(String deviceId, String submodule, String ruleId, String command) {
        IssueCommand issueCommand = new IssueCommand();
        issueCommand.setDeviceId(deviceId);
        issueCommand.setCmd(command);
        issueCommand.setCmdId(cmdIdBuilder.buildCmdId());
        Map<String, Object> jsonObject = new HashMap<>(16);
        if (submodule == null) {
            jsonObject.put("submodule", "");
        } else {
            jsonObject.put("submodule", submodule);
        }
        if (ruleId != null) {
            jsonObject.put("rule_id", ruleId);
        }
        issueCommand.setParam(JsonUtil.toJson(jsonObject));
        return issueCommand;
    }

    private void validateDisable(List<String> deviceIdList) {
        LambdaQueryWrapper<DeviceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DeviceInfo::getDeviceId, deviceIdList);
        List<DeviceInfo> list = this.deviceInfoRepository.list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            throw new BaseBusinessException("查询监测器为空");
        }
        list.forEach(item -> {
            if (item.getRegisterStatus() == RegisterStatus.INVALID) {
                // 禁用状态
                throw new BaseBusinessException("监测器设备编号：" + item.getDeviceId() + " 为禁用状态，不允许下发");
            }
        });
    }

    private IssueCommand buildCommand(String cmdId, String deviceId, LocalDateTime now, CommandType commandType, String param) {
        IssueCommand issueCommand = new IssueCommand();
        issueCommand.setCmdId(cmdId);
        issueCommand.setDeviceId(deviceId);
        issueCommand.setType(CommonConstants.COMMAND);

        issueCommand.setCreateTime(now);
        issueCommand.setIssueTime(now);
        issueCommand.setCmd(commandType.getValue());
        issueCommand.setParam(param);
        return issueCommand;
    }

    @NotNull
    private IssueCommand buildCommand(IssueModuleCmd module, String deviceId, LocalDateTime now) {
        IssueCommand issueCommand = new IssueCommand();
        String cmdId = cmdIdBuilder.buildCmdId();
        issueCommand.setDeviceId(deviceId);
        issueCommand.setCmdId(cmdId);
        issueCommand.setType(CommonConstants.COMMAND);
        issueCommand.setIssueTime(now);
        issueCommand.setIssueTime(now);
        issueCommand.setCmd(module.getCmd());
        issueCommand.setModule(module.getModule());
        if (module.getIsAll() == 1) {
            issueCommand.setSubmodule("[]");
        } else {
            if (CollUtil.isNotEmpty(module.getSubmodules())) {
                String param = JsonUtil.toJson(module.getSubmodules());
                issueCommand.setSubmodule(param);
            }
        }
        return issueCommand;
    }
}
