package com.superred.supervisor.common.entity.operation.terminal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 终端策略规则设备执行详情表
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Data
@TableName("op_terminal_policy_rule_device_exec")
public class TerminalPolicyRuleDeviceExec implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 详情ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 策略编码
     */
    private String policyCode;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 策略版本号
     */
    private String version;

    /**
     * 执行结果：0-成功，1-失败
     */
    private Integer execResult;

    /**
     * 执行结果描述，失败时说明失败原因
     */
    private String execMessage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
