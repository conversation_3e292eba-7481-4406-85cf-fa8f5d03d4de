package com.superred.supervisor.manager.common.annotation;

import com.superred.common.core.exception.BaseBusinessException;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.constraints.Pattern.Flag;
import java.util.regex.Matcher;
import java.util.regex.PatternSyntaxException;

/**
 * 验证器  当字段不为null且长度>0  才执行正则校验
 *
 * <AUTHOR>
 * @create 2024-07-09 17:53
 */
public class BlankOrPatternValidator implements ConstraintValidator<BlankOrPattern, String> {
    private java.util.regex.Pattern pattern;

    public BlankOrPatternValidator() {

    }

    @Override
    public void initialize(BlankOrPattern parameters) {
        // ConstraintValidator.super.initialize(constraintAnnotation);
        Flag[] flags = parameters.flags();
        int intFlag = 0;
        Flag[] arr = flags;
        int len = flags.length;

        for (int i = 0; i < len; ++i) {
            Flag flag = arr[i];
            intFlag |= flag.getValue();
        }
        try {
            this.pattern = java.util.regex.Pattern.compile(parameters.regexp(), intFlag);
        } catch (PatternSyntaxException e) {
            throw new BaseBusinessException("初始化验证器错误", e);
        }
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.length() == 0) {
            return true;
        } else {
            Matcher m = this.pattern.matcher(value);
            return m.matches();
        }
    }
}
