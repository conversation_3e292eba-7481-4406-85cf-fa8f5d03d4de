package com.superred.supervisor.manager.model.dto.policy.config;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleAttackBlacklistDns;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-27 20:56
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BlackDnsPolicyConfigDTO {

    /**
     * 策略ID
     */
    private Long ruleId;

    /**
     * 域名信息
     */
    private String dns;

    /**
     * 规则类型  0表示文本表达式; 1表示正则表达式。
     */
    private Integer ruleType;

    /**
     * 匹配类型 当规则类型rule_type为0时有效。  0表示子串匹配， 1表示右匹配， 2表示左匹配， 3表示完全匹配。 匹配不区分大小写。
     */
    private Integer matchType;

    /**
     * 告警级别，0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）。
     */
    private Integer risk;

    /**
     * 攻击组织
     */
    private String attackGroup;

    /**
     * 攻击阶段枚举分类 1 侦查扫描 2 攻击渗透 3 样本投递 4 持续控制 5 横向移动 6 数据窃取 99 其它
     */
    private Integer attackStage;

    /**
     * 策略名称
     */
    private String ruleName;


    /**
     * 攻击设施类型ID 1 密码爆破 2 漏洞扫描 3 样本分发 4 恶意发邮 5 钓鱼网站 6 信息搜集 7 数据窃取 8 命令控制 99 其它
     */
    private List<Integer> facilityType;

    /**
     * 攻击窃密告警描述
     */
    private String desc;

    /**
     * 攻击分类 1. 窃密木马 2. 远控木马 3. 电脑病毒 4. 僵尸网络 5. 网络蠕虫 6. 间谍软件 7. 挖矿木马 8. 黑客工具 9. 勒索软件 10. 恶意文档 11. 后门程序 99. 其它
     */
    private Integer attackClass;

    public static BlackDnsPolicyConfigDTO getPolicyConfig(RuleAttackBlacklistDns blacklistDns) {
        if (blacklistDns == null) {
            return null;
        }
        return BlackDnsPolicyConfigDTO.builder()
                .ruleId(Long.parseLong(blacklistDns.getRuleId()))
                .dns(PolicyUtils.handleStrNull(blacklistDns.getDns()))
                .ruleType(PolicyUtils.strToInt(blacklistDns.getRuleType()))
                .matchType(PolicyUtils.strToInt(blacklistDns.getMatchType()))
                .risk(PolicyUtils.strToInt(blacklistDns.getRisk()))
                .attackGroup(PolicyUtils.handleStrNull(blacklistDns.getAttackGroup()))
                .attackStage(PolicyUtils.strToInt(blacklistDns.getAttackStage()))
                .ruleName(PolicyUtils.handleStrNull(blacklistDns.getRuleName()))
                .facilityType(PolicyUtils.strToIntList(blacklistDns.getFacilityType()))
                .desc(PolicyUtils.handleStrNull(blacklistDns.getDesc()))
                .attackClass(PolicyUtils.strToInt(blacklistDns.getAttackClass()))
                .build();
    }

}
