package com.superred.supervisor.manager.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.supervisor.common.entity.system.SysUser;
import com.superred.supervisor.manager.model.vo.system.user.UserPageReq;
import com.superred.supervisor.manager.model.vo.system.user.UserResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户表表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:22
 */
@Mapper
public interface SysUserExtMapper extends BaseMapper<SysUser> {

    Page<UserResp> getUserRespPage(Page<UserResp> page, @Param(value = "query") UserPageReq query);

    UserResp getUserRespById(Integer id);
}

