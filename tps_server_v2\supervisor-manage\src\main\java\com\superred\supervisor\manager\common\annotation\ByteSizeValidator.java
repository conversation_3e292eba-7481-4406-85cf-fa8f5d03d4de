package com.superred.supervisor.manager.common.annotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @create 2024-07-26 13:57
 */
public class ByteSizeValidator implements ConstraintValidator<ByteSize, String> {

    protected int min;
    protected int max;

    public void initialize(ByteSize parameters) {
        min = parameters.min();
        max = parameters.max();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.length() == 0) {
            return true;
        }
        return value.getBytes().length >= this.min && value.getBytes().length <= this.max;
    }
}
