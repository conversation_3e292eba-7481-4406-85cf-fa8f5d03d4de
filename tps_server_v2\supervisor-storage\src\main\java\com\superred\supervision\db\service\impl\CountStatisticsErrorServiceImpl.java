package com.superred.supervision.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervision.db.entity.CountStatisticsError;
import com.superred.supervision.db.mapper.CountStatisticsErrorMapper;
import com.superred.supervision.db.service.CountStatisticsErrorService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Service
public class CountStatisticsErrorServiceImpl extends ServiceImpl<CountStatisticsErrorMapper, CountStatisticsError> implements CountStatisticsErrorService {

    @Override
    public List<CountStatisticsError> findByTime(String startTime, String endTime) {
        return baseMapper.findByTime(startTime, endTime);
    }

    @Override
    public void deleteByTime(String startTime, String endTime) {
        baseMapper.deleteByTime(startTime, endTime);
    }
}
