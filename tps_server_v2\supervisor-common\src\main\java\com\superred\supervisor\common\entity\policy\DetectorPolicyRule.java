package com.superred.supervisor.common.entity.policy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 监测器策略规则关系表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "policy_detector_policy_rule", autoResultMap = true)
public class DetectorPolicyRule extends Model<DetectorPolicyRule> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则ID
     */

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ruleId;

    /**
     * 策略ID
     */

    private Long policyId;

    /**
     * 模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单
     */
    private String module;
}
