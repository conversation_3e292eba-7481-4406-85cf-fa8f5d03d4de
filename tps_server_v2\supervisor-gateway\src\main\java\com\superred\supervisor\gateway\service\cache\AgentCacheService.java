package com.superred.supervisor.gateway.service.cache;

import com.superred.supervisor.common.cache.ComCacheKeys;
import com.superred.supervisor.common.cache.CommonCacheService;
import com.superred.supervisor.common.entity.app.AppAgentInfo;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import com.superred.supervisor.common.repository.app.AppAgentInfoRepository;
import com.superred.supervisor.common.repository.terminal.TerminalAgentInfoRepository;
import com.superred.supervisor.gateway.model.auth.LoginAgent;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;

/**
 * 终端缓存
 *
 * <AUTHOR>
 * @since 2025/5/19 14:20
 */
@Service
@Slf4j
public class AgentCacheService {

    private static final String AGENT_TERMINAL_HEARTBEAT_CACHE = "common:ag-terminal-heartbeat-cache:";

    private static final String AGENT_APP_HEARTBEAT_CACHE = "common:ag-app-heartbeat-cache:";

    /**
     * 终端心跳超时时间，单位：秒 ,心跳一分钟一次，一般设置为2-3倍的心跳间隔
     */
    private static final long TIMEOUT_SECONDS = 150;

    @Resource
    private CommonCacheService commonCacheService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private TerminalAgentInfoRepository terminalAgentInfoRepository;
    @Resource
    private AppAgentInfoRepository appAgentInfoRepository;


    public TerminalAgentInfo cacheTerminalAgentInfo(String deviceId) {
        return commonCacheService.getOrLoad(ComCacheKeys.AGENT_TERMINAL_CACHE + deviceId,
                () -> terminalAgentInfoRepository.getById(deviceId),
                Duration.ofHours(1));
    }

    public void evictTerminalAgentInfo(String deviceId) {
        commonCacheService.evict(ComCacheKeys.AGENT_TERMINAL_CACHE + deviceId);
    }

    public void terminalLogin(String deviceId, LoginAgent agent) {
        commonCacheService.set(ComCacheKeys.AGENT_TERMINAL_LOGIN_CACHE + deviceId, agent, Duration.ofDays(1));
    }


    public void terminalLogout(String deviceId) {
        commonCacheService.evict(ComCacheKeys.AGENT_TERMINAL_LOGIN_CACHE + deviceId);
        this.evictTerminalAgentInfo(deviceId);
    }

    public LoginAgent getLoginAgent(String deviceId) {
        return commonCacheService.get(ComCacheKeys.AGENT_TERMINAL_LOGIN_CACHE + deviceId);
    }

    public void cacheTerminalHeartbeat(String deviceId) {
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(AGENT_TERMINAL_HEARTBEAT_CACHE);
        long expireTime = Instant.now().getEpochSecond() + TIMEOUT_SECONDS;
        scoredSortedSet.add(expireTime, deviceId);
    }

    public Collection<String> getOfflineTerminalHeartbeat() {
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(AGENT_TERMINAL_HEARTBEAT_CACHE);
        return scoredSortedSet.valueRange(0, true,
                Instant.now().getEpochSecond(), true,
                0, 200);

    }

    public void removeOfflineTerminalAgentHeartbeat(Collection<String> deviceIds) {
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(AGENT_TERMINAL_HEARTBEAT_CACHE);
        scoredSortedSet.removeAll(deviceIds);
    }

    //
    //
    /*=============================================================================================================*/
    //  APP组件相关缓存操作
    //

    public AppAgentInfo cacheAppAgentInfo(String deviceId) {
        return commonCacheService.getOrLoad(ComCacheKeys.AGENT_APP_CACHE + deviceId,
                () -> appAgentInfoRepository.getById(deviceId), Duration.ofHours(1));
    }

    public void evictAppAgentInfo(String deviceId) {
        commonCacheService.evict(ComCacheKeys.AGENT_APP_CACHE + deviceId);
    }


    public void appAgentLogin(String deviceId, LoginAgent agent) {
        commonCacheService.set(ComCacheKeys.AGENT_APP_LOGIN_CACHE + deviceId, agent, Duration.ofDays(1));
    }


    public void appAgentLogout(String deviceId) {
        commonCacheService.evict(ComCacheKeys.AGENT_APP_LOGIN_CACHE + deviceId);
        this.evictAppAgentInfo(deviceId);
    }

    public LoginAgent getLoginAppAgent(String deviceId) {
        return commonCacheService.get(ComCacheKeys.AGENT_APP_LOGIN_CACHE + deviceId);
    }

    public void cacheAppAgentHeartbeat(String deviceId) {
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(AGENT_APP_HEARTBEAT_CACHE);
        long expireTime = Instant.now().getEpochSecond() + TIMEOUT_SECONDS;
        scoredSortedSet.add(expireTime, deviceId);
    }

    public Collection<String> getOfflineAppAgentHeartbeat() {
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(AGENT_APP_HEARTBEAT_CACHE);
        return scoredSortedSet.valueRange(0, true,
                Instant.now().getEpochSecond(), true,
                0, 200);
    }

    public void removeOfflineAppAgentHeartbeat(Collection<String> deviceIds) {
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(AGENT_APP_HEARTBEAT_CACHE);
        scoredSortedSet.removeAll(deviceIds);
    }


}
