package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2025-04-07 14:39
 */
@Getter
public enum RiskServiceAndSoftwareEnum {
    TYPE1(1, "境外邮件服务机客户端"),
    TYPE2(2, "境外存储服务"),
    TYPE3(3, "境外文件扫描或转换服务"),
    TYPE4(4, "境外代码托管服务"),
    TYPE5(5, "境外即时通信服务及软件"),
    TYPE6(6, "“翻墙”工具");

    private final Integer key;
    private final String value;

    RiskServiceAndSoftwareEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

}
