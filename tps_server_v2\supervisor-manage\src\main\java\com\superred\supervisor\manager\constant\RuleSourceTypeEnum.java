package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * <p> ruleId 中第26-27位类型
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/3/7 11:17
 **/
@Getter
public enum RuleSourceTypeEnum {
    /**
     * 是共享策略
     */
    LOCAL_POLICY(1, "本级策略"),
    /**
     * 不是共享策略
     */
    SUPER_POLICY_NO_SHARE(2, "上级共享生成策略"),

    SUPER_POLICY_SHARE(3, "上级非共享策略");


    private final int key;
    private final String value;

    RuleSourceTypeEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }
}
