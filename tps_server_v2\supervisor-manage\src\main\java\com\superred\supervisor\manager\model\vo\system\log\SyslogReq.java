package com.superred.supervisor.manager.model.vo.system.log;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 *  操作日志请求实体
 * @since 2025年06月26日
 */
@Data
@Schema(description = "操作日志请求实体")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyslogReq extends PageReqDTO {

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private String beginTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private String endTime;

    /**
     * 操作人员
     */
    @Schema(description = "操作人员")
    private String username;

    /**
     * 操作模块
     */
    @Schema(description = "操作模块")
    private String operateModule;

    /**
     * 操作类型
     */
    @Schema(description = "操作类型")
    private String operateType;

    /**
     * 行为类型(级别)
     */
    @Schema(description = "行为类型(级别)")
    private String behaviourType;

    /**
     * 日志风险级别
     */
    @Schema(description = "日志风险级别")
    private String level;

    /**
     * 角色id
     */
    @Schema(description = "角色ids")
    private List<String> roleIds;

    /**
     * 角色
     */
    @Schema(description = "角色")
    private String role;

    /**
     * 是否包含 1，不包含，0，包含
     */
    @Schema(description = "是否包含 1，不包含，0，包含")
    private  String notIn;

    /**
     * ip地址
     */
    @Schema(description = "ip地址")
    private String hostIp;

    /**
     * 操作行为描述
     */
    @Schema(description = "操作行为描述")
    private String description;

    /**
     * 结果备注
     */
    @Schema(description = "结果备注")
    private String remarks;

    /**
     * 操作状态
     */
    @Schema(description = "操作状态")
    private Integer result;
}
