package com.superred.supervisor.common.repository.agent;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.agent.AgentSuspectedLog;
import com.superred.supervisor.common.mapper.agent.AgentSuspectedLogMapper;
import org.springframework.stereotype.Repository;

/**
 * 系统异常日志 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-24 09:23:59
 */
@Repository
public class AgentSuspectedLogRepository extends ServiceImpl<AgentSuspectedLogMapper, AgentSuspectedLog> {

}

