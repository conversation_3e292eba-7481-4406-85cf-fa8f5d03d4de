package com.superred.supervisor.manager.model.vo.devices.manager;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DevicePolicyContent
 * <p> 设备当前生效策略
 * @since 2023-06-05 13:47:52
 **/
@Data

public class DevicePolicyContentResp {


    /**
     *
     */
    @Schema(description = "")
    private Integer id;

    /**
     * 设备编号
     */
    @Schema(description = "设备编号")
    private String deviceId;

    /**
     * 模块
     */
    @Schema(description = "模块")
    private String module;

    /**
     * 策略版本
     */
    @Schema(description = "策略版本")
    private String version;

    /**
     * 策略内容
     */
    @Schema(description = "策略内容")
    private String policy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
