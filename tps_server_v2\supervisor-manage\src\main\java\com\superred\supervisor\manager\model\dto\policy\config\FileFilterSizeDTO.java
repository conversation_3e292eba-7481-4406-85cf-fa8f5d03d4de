package com.superred.supervisor.manager.model.dto.policy.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-03-11 17:13
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FileFilterSizeDTO {

    @JsonProperty(value = "min_size")
    private Integer minSize;

    @JsonProperty(value = "max_size")
    private Integer maxSize;
}
