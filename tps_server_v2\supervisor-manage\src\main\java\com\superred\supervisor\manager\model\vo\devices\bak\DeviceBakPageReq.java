package com.superred.supervisor.manager.model.vo.devices.bak;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 设备报备页面对应
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceBakPageReq extends PageReqDTO {


    /**
     * 设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次
     */
    @Schema(description = "设备编号，最长10位，前四位表示出厂年月，五六位表示生产厂商，后生产批次")
    private String deviceId;


}
