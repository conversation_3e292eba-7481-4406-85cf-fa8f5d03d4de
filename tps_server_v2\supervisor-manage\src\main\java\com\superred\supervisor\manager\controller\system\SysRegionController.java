package com.superred.supervisor.manager.controller.system;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.supervisor.manager.model.vo.system.RegionResp;
import com.superred.supervisor.manager.service.RegionCacheService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * 系统区域控制器
 *
 * <AUTHOR>
 * @since 2025/03/18
 */
@Tag(name = "1.9 行政区划查询统一接口")
@RestController
@RequestMapping("/region/")
@Slf4j
public class SysRegionController {


    @Resource
    private RegionCacheService regionCacheService;


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1. id查询区域")
    @GetMapping("/{regionId}")
    public R<RegionResp> getRegion(@PathVariable String regionId) {
        RegionResp resp = regionCacheService.cacheRegionById(regionId);

        return R.success(resp);
    }


    @ApiOperationSupport(order = 2)
    @Operation(summary = "2. 行政区划树(所有)")
    @GetMapping("/tree")
    public R<List<RegionResp>> treeRegion() {

        List<RegionResp> resp = regionCacheService.buildRegionTreeByLevel();

        return R.success(resp);
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "3. 行政区子级")
    @GetMapping("/children/{regionId}")
    public R<List<RegionResp>> getChildrenById(@PathVariable String regionId) {

        List<RegionResp> resp = regionCacheService.getChildrenById(regionId);
        return R.success(resp);
    }


    @ApiOperationSupport(order = 4)
    @Operation(summary = "4. 获取区域路径")
    @GetMapping("/path/{regionId}")
    public R<String> path(@PathVariable String regionId) {

        String s = regionCacheService.regionIdToChinesPath(Integer.valueOf(regionId));
        return R.success(s);
    }

}
