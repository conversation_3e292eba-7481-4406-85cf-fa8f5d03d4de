package com.superred.supervisor.manager.controller.policy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.constant.AuditBehaviorTypeEnum;
import com.superred.supervisor.manager.constant.DatabaseTypeEnum;
import com.superred.supervisor.manager.constant.DnsTypeEnum;
import com.superred.supervisor.manager.constant.FileActionEnum;
import com.superred.supervisor.manager.constant.FileTransDirectionEnum;
import com.superred.supervisor.manager.constant.FileTypeEnum;
import com.superred.supervisor.manager.constant.LoginSuccEnum;
import com.superred.supervisor.manager.constant.RequestMethodEnum;
import com.superred.supervisor.manager.constant.RiskServiceAndSoftwareEnum;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyEnumResp;
import com.superred.supervisor.manager.model.vo.policy.RuleAuditAppBehaviorPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAuditAppBehaviorReq;
import com.superred.supervisor.manager.model.vo.policy.RuleAuditAppBehaviorResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.service.policy.RuleAuditAppBehaviorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-04-07 13:39
 */
@Tag(name = "4.12. 网络行为审计策略 - 应用行为审计策略")
@RestController
@RequestMapping("/rule/audit/app_behavior")
@Slf4j
@Validated
public class RuleAuditAppBehaviorController {

    @Resource
    private RuleAuditAppBehaviorService ruleAuditAppBehaviorService;

    @Operation(summary = "1 分页")
    @GetMapping("/page")
    public RPage<RuleAuditAppBehaviorResp> page(RuleAuditAppBehaviorPageReq ruleAuditAppBehaviorPageReq) {
        IPage<RuleAuditAppBehaviorResp> page = this.ruleAuditAppBehaviorService.page(ruleAuditAppBehaviorPageReq);
        return new RPage<>(page);
    }

    @Operation(summary = "2 查询详情")
    @GetMapping("/{ruleId}")
    public R<RuleAuditAppBehaviorResp> getById(@PathVariable("ruleId") Long ruleId) {
        RuleAuditAppBehaviorResp resp = this.ruleAuditAppBehaviorService.getById(ruleId);
        return R.success(resp);
    }

    @Operation(summary = "3 新增")
    @SysLogAnn(module = LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY, operateType = OperateTypeConstants.ADD, desc = OperateTypeConstants.ADD + LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY)
    @PostMapping("/save")
    public R save(@Valid @RequestBody RuleAuditAppBehaviorReq ruleAuditAppBehaviorReq) {
        this.ruleAuditAppBehaviorService.save(ruleAuditAppBehaviorReq);
        return R.success();
    }

    @Operation(summary = "4 编辑")
    @SysLogAnn(module = LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY, operateType = OperateTypeConstants.MODIFY, desc = OperateTypeConstants.MODIFY + LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY)
    @PostMapping("/edit")
    public R edit(@Valid @RequestBody RuleAuditAppBehaviorReq ruleAuditAppBehaviorReq) {
        this.ruleAuditAppBehaviorService.edit(ruleAuditAppBehaviorReq);
        return R.success();
    }

    @Operation(summary = "5 删除")
    @PostMapping("/del")
    @SysLogAnn(module = LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY, operateType = OperateTypeConstants.DELETE, desc = OperateTypeConstants.DELETE + LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY)
    public R del(@Valid @RequestBody PolicyBatchIdsReq batchIdsReq) {
        this.ruleAuditAppBehaviorService.del(batchIdsReq);
        return R.success();
    }

    @Operation(summary = "6 查看策略应用策略情况")
    @PostMapping("/policy/{ruleId}")
    public R<List<RulePolicyApplyResp>> policyApply(@PathVariable("ruleId") Long ruleId) {
        List<RulePolicyApplyResp> result = this.ruleAuditAppBehaviorService.policyApply(ruleId);
        return R.success(result);
    }

    @Operation(summary = "7 导出")
    @PostMapping("/export")
    @SysLogAnn(module = LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY, operateType = OperateTypeConstants.EXPORT, desc = OperateTypeConstants.EXPORT + LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY)
    public void export(HttpServletResponse response, @RequestBody RuleAuditAppBehaviorPageReq ruleAuditAppBehaviorPageReq) throws IOException {

        // do something
    }

    @Operation(summary = "8 导入")
    @PostMapping("/import")
    @SysLogAnn(module = LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY, operateType = OperateTypeConstants.IMPORT, desc = OperateTypeConstants.IMPORT + LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY)
    public R importFile(@RequestParam("file") MultipartFile file) throws IOException {
        return null;
    }

    @Operation(summary = "9 下载模版")
    @PostMapping("/download")
    @SysLogAnn(module = LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY, operateType = OperateTypeConstants.DOWNLOAD, desc = LogTypeConstants.APPLICATION_BEHAVIOR_AUDIT_STRATEGY + "下载模板")
    public void download(HttpServletResponse response) throws IOException {
        //        String filePath = "template/木马活动检测策略模版.xlsx";
        //        String fileName = "木马活动检测策略模版.xlsx";
        //        ClassPathResource classpathResource = new ClassPathResource(filePath);
        //        InputStream inputStream = classpathResource.getInputStream();
        //        FileUtils.downloadFileExcel(response, inputStream, fileName);

    }

    @Operation(summary = "10 获取审计类型下拉")
    @GetMapping("/audit_behavior_type/list")
    public R<List<PolicyEnumResp>> list() {
        List<AuditBehaviorTypeEnum> enumList = Arrays.asList(AuditBehaviorTypeEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }

    @Operation(summary = "11 获取WEB行为审计请求方法下拉")
    @GetMapping("/request_method/list")
    public R<List<PolicyEnumResp>> requestMethodList() {
        List<RequestMethodEnum> enumList = Arrays.asList(RequestMethodEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }

    @Operation(summary = "12 获取DNS行为DNS访问类型下拉")
    @GetMapping("/dns_type/list")
    public R<List<PolicyEnumResp>> dnsTypeList() {
        List<DnsTypeEnum> enumList = Arrays.asList(DnsTypeEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }

    @Operation(summary = "13 获取数据库操作行为数据库类型下拉")
    @GetMapping("/database_type/list")
    public R<List<PolicyEnumResp>> databaseTypeList() {
        List<DatabaseTypeEnum> enumList = Arrays.asList(DatabaseTypeEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }

    @Operation(summary = "14 获取文件传输行为文件传输方向下拉")
    @GetMapping("/file_trans_direction/list")
    public R<List<PolicyEnumResp>> fileTransDirectionList() {
        List<FileTransDirectionEnum> enumList = Arrays.asList(FileTransDirectionEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }

    @Operation(summary = "15 获取文件传输行为文件类型下拉")
    @GetMapping("/file_type/list")
    public R<List<PolicyEnumResp>> fileTypeList() {
        List<FileTypeEnum> enumList = Arrays.asList(FileTypeEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }

    @Operation(summary = "16 获取登录行为登录是否成功下拉")
    @GetMapping("/login_succ/list")
    public R<List<PolicyEnumResp>> loginSuccList() {
        List<LoginSuccEnum> enumList = Arrays.asList(LoginSuccEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }

    @Operation(summary = "17 获取风险服务和软件行为服务或软件类型下拉")
    @GetMapping("/risk_service_soft/list")
    public R<List<PolicyEnumResp>> riskSoftList() {
        List<RiskServiceAndSoftwareEnum> enumList = Arrays.asList(RiskServiceAndSoftwareEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }

    @Operation(summary = "18 获取风险服务和软件行为文件动作下拉")
    @GetMapping("/file_action/list")
    public R<List<PolicyEnumResp>> fileActionList() {
        List<FileActionEnum> enumList = Arrays.asList(FileActionEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }
}
