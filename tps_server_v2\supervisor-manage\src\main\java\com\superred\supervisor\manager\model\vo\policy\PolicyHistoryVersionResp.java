package com.superred.supervisor.manager.model.vo.policy;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 * @create 2025-03-27 18:00
 */
@Data
@Builder

public class PolicyHistoryVersionResp {

    @Schema(description = "模块")
    private String module;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "策略ID")
    private Long policyId;

//    @Schema(description = "规则ID数组")
//    private List<Long> ruleIdList;
}
