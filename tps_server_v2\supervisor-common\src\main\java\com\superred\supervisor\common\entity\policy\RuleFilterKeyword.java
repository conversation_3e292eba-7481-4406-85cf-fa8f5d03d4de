package com.superred.supervisor.common.entity.policy;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 关键词策略
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName(value = "policy_rule_keyword", autoResultMap = true)
public class RuleFilterKeyword extends Model<RuleFilterKeyword> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 策略ID
     */
    private Long ruleId;

    /**
     * 策略类型，0 关键词，1正则表达式
     */
    private Integer ruleType;

    /**
     * 策略描述
     */
    private String ruleDesc;

    /**
     * 最少命中次数，默认为1
     */
    private Integer minMatchCount;

    /**
     * 包含相对位置
     */
    private String includePosition;

    /**
     * 排除
     */
    private String exclude;

    /**
     * 包含
     */
    private String include;

    /**
     * 策略内容
     */
    //@TableField(typeHandler = EncryptAndDecryptHandler.class)
    private String ruleContent;

    /**
     * 告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级
     */
    private Integer risk;

    /**
     * 策略应用状态，0未应用，1已应用
     */
    private Integer status;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 包含策略
     */
    private String ruleContain;

    /**
     * 排除策略
     */
    private String ruleExclude;

    /**
     * 位置策略
     */
    private String rulePosition;

    /**
     * 文件过滤类型，1文档 2图片 3文本/网页 4压缩包 5邮件 1,2
     */
    private String fileFilterType;

    /**
     * 文档过滤最小值
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer fileFilterMinSize;

    /**
     * 文档过滤最大值
     */
    @TableField(fill = FieldFill.UPDATE)
    private Integer fileFilterMaxSize;


}
