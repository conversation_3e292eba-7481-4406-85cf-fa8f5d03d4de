package com.superred.supervisor.manager.model.vo.settings;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 上传文件状态改变req
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ClientInstallPackageStatusReq {

    @NotNull(message = "安装包id不能为空")
    @Schema(description = "主键id")
    private Integer id;

    @NotNull(message = "状态值不能为空")
    @Schema(description = "安装包启用/停用状态  0停用  1启用")
    private Integer isPublish;

}
