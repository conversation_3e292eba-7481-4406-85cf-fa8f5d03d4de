package com.superred.supervisor.manager.model.vo.terminal.manage;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 终端设备授权DTO
 *
 * <AUTHOR>
 * @since 2024/1/19 14:56
 */

@Data
public class TerminalDeviceAuthReq {

    @Schema(description = "授权的终端设备ID列表")
    @NotEmpty(message = "设备ID不能为空")
    private List<String> deviceIds;

}
