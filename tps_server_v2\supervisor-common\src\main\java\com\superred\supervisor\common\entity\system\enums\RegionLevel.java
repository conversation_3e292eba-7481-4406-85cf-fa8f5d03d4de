package com.superred.supervisor.common.entity.system.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

/**
 * 0中国 1省 2市 3区县
 *
 * <AUTHOR>
 * @since 2025/3/17 15:29
 */
@Getter
public enum RegionLevel implements IEnum<Integer> {

    /**
     * 中国
     */
    CHINA(0, "中国"),
    /**
     * 省
     */
    PROVINCE(1, "省"),
    /**
     * 市
     */
    CITY(2, "市"),
    /**
     * 区县
     */
    COUNTY(3, "区县");

    private final Integer value;
    private final String desc;

    RegionLevel(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }


}
