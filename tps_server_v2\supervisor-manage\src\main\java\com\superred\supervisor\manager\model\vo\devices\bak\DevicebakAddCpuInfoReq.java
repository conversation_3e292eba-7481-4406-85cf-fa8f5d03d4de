package com.superred.supervisor.manager.model.vo.devices.bak;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;


/**
 * 设备贝克添加中央处理器信息请求
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Data

public class DevicebakAddCpuInfoReq {

    private static final long serialVersionUID = 1L;


    /**
     * cpu核数，8
     */
    @Schema(description = "CPU核数，8")
    @Min(value = 0, message = "CPU核数 不能小于0")
    @Max(value = 10000, message = "CPU核数 不能大于10000")
    private Integer core;

    /**
     * 主频，“1.8GHz”
     */
    @Schema(description = "主频，“1.8GHz”")
    @NotNull(message = "CPU主频 不能为空")
    private Double clock;

    /**
     *
     */
    @Schema(description = "CPU ID")
    @NotNull(message = "CPU ID 不能为空")
    @Size(max = 16, message = "CPU ID 长度不能超过16位")
    private String physicalId;


}
