package com.superred.supervision.db.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervision.base.vo.command.CommandResultVo;
import com.superred.supervision.db.entity.Command;
import com.superred.supervision.db.mapper.CommandMapper;
import com.superred.supervision.db.service.CommandService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 指令表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-22
 */
@Service
public class CommandServiceImpl extends ServiceImpl<CommandMapper, Command> implements CommandService {

    @Override
    public void saveResult(String deviceId, CommandResultVo commandResultVo) {
        Command command = new Command();
        command.setDeviceId(deviceId);
        command.setUpTime(commandResultVo.getTime());
        command.setCmd(commandResultVo.getCmd());
        command.setType(commandResultVo.getType());
        command.setResult(commandResultVo.getResult());
        command.setMessage(commandResultVo.getMessage());
        command.setDetail(JSONUtil.toJsonStr(commandResultVo.getDetail()));

        LambdaUpdateWrapper<Command> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Command::getCmdId, commandResultVo.getCmdId());
        baseMapper.update(command, updateWrapper);
    }
}
