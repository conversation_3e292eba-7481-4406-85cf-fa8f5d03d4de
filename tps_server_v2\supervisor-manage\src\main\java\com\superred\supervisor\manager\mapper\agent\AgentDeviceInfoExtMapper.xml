<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.superred.supervisor.manager.mapper.agent.AgentDeviceInfoExtMapper">


    <select id="pageAuditAgentInfo"
            resultType="com.superred.supervisor.manager.model.vo.terminal.TerminalAuditPageResp">
        SELECT
        t1.device_id deviceId,
        t1.soft_version softVersion,
        t1.register_time registerTime,
        t1.memo,
        t1.heartbeat_time heartbeatTime,
        t1.user_id userId ,
        t1.user_name userName,
        t3.`name` orgName,
        t1.ip,
        t1.mac,
        t1.host_name hostName,
        t1.os ,
        t1.register_status status,
        t1.audit_type auditWay
        FROM
        `agent_device_info` t1
        LEFT JOIN p_sys_staff t2 ON t1.user_id = t2.id
        LEFT JOIN p_sys_org t3 ON t2.org_id = t3.id
        where
        t1.audit_type in (1,2)
        <if test="req.status != 0 and req.status != 4 and req.status != null">
            and t1.register_status = #{req.status}
        </if>
        <if test="req.status == null">
            and t1.register_status in (1, 2)
        </if>

        <if test="req.regTimeStart != null">
            and t1.register_time &gt;= #{req.regTimeStart}
        </if>
        <if test="req.regTimeEnd != null">
            and t1.register_time &lt;= #{req.regTimeEnd}
        </if>
        <if test="req.orgId != null">
            and t3.id = #{req.orgId}
        </if>


    </select>
    <select id="pageAgentStatus"
            resultType="com.superred.supervisor.manager.model.vo.terminal.manage.TerminalStatusPageResp">
        SELECT
        t1.device_id deviceId,
        t1.soft_version softVersion,
        t1.register_time registerTime,
        t1.memo,
        t1.heartbeat_time heartbeatTime,
        t1.user_id userId ,
        t1.user_name userName,
        t3.`name` orgName,
        t1.ip,
        t1.mac,
        t1.host_name hostName,
        t1.os ,
        t1.register_status status,
        t1.audit_type auditWay,
        t3.region_code regionId,
        t3.name orgNamePath,
        t1.last_upgrade_time lastUpgradeTime

        FROM
        `agent_device_info` t1
        LEFT JOIN p_sys_staff t2 ON t1.user_id = t2.id
        LEFT JOIN p_sys_org t3 ON t2.org_id = t3.id
        <where>

            <if test="req.status != null and req.status != 0 and req.status != 4">
                and t1.register_status = #{req.status}
            </if>
            <if test="req.status == null">
                and t1.register_status in (0, 3, 4, 5, 6)
            </if>
            <if test="req.ip != null">
                and t1.ip = #{req.ip}
            </if>
            <if test="req.mac != null">
                and t1.mac = #{req.mac}
            </if>
            <if test="req.userName != null">
                and t1.user_name = #{req.userName}
            </if>
            <if test="req.lastHeartbeatTimeStart != null">
                and t1.heartbeat_time &gt;= #{req.lastHeartbeatTimeStart}
            </if>
            <if test="req.lastHeartbeatTimeEnd != null">
                and t1.heartbeat_time &lt;= #{req.lastHeartbeatTimeEnd}
            </if>


        </where>


    </select>
    <select id="countByStatus"
            resultType="com.superred.supervisor.manager.model.vo.terminal.manage.TerminalCountByStatusResp">

        SELECT SUM(CASE
                       WHEN register_status = 0 AND heartbeat_time >= NOW() - INTERVAL #{intervalMinutes} MINUTE THEN 1
                       ELSE 0
            END)        AS countOnline,
               SUM(CASE
                       WHEN (register_status = 0 AND
                             (heartbeat_time &lt; NOW() - INTERVAL #{intervalMinutes} MINUTE OR heartbeat_time IS NULL))
                           OR register_status = 4 THEN 1
                       ELSE 0
                   END) AS countOffline,
               SUM(CASE
                       WHEN register_status = 3 THEN 1
                       ELSE 0
                   END) AS countDisable,
               SUM(CASE
                       WHEN register_status = 6 THEN 1
                       ELSE 0
                   END) AS countUninstall
        FROM agent_device_info;

    </select>
</mapper>