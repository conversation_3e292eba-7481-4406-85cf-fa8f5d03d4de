package com.superred.supervisor.manager.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.common.BatchIdsReq;
import com.superred.supervisor.manager.model.vo.system.role.SysRoleAddReq;
import com.superred.supervisor.manager.model.vo.system.role.SysRolePageReq;
import com.superred.supervisor.manager.model.vo.system.role.SysRolePageResp;
import com.superred.supervisor.manager.service.system.SysRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;


/**
 * 系统角色控制器
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Tag(name = "1.3. 角色管理")
@RestController
@AllArgsConstructor
@RequestMapping("/sys_role")
@Slf4j
public class SysRoleController {

    private SysRoleService sysRoleService;


    // 角色管理
    private static final String MODEL = "角色管理";


    @ApiOperationSupport(order = 1)
    @Operation(summary = "1. 分页查询")
    @PostMapping("/page")
    public RPage<SysRolePageResp> getSysRolePage(@Valid @RequestBody SysRolePageReq req) {


        IPage<SysRolePageResp> respIPage = sysRoleService.getSysRolePage(req);
        return new RPage<>(respIPage);
    }


    @ApiOperationSupport(order = 2)
    @Operation(summary = "2. 通过id查询角色")
    @GetMapping("/{id}")
    public R<SysRolePageResp> getById(@PathVariable("id") Integer id) {
        SysRolePageResp sysRolePageResp = sysRoleService.getById(id);
        return R.success(sysRolePageResp);
    }


    @ApiOperationSupport(order = 3)
    @Operation(summary = "3. 新增角色")
    @SysLogAnn(module = LogTypeConstants.ROLES, operateType = OperateTypeConstants.ADD, desc = "新增角色")
    @PostMapping("/add")
    public R<Integer> save(@Validated @RequestBody SysRoleAddReq req) {
        Integer id = sysRoleService.save(req);

        return R.success(id);
    }


    @ApiOperationSupport(order = 4)
    @Operation(summary = "4. 修改角色")
    @SysLogAnn(module = LogTypeConstants.ROLES, operateType = OperateTypeConstants.MODIFY, desc = "修改角色")
    @PostMapping("/edit/{id}")
    public R<Integer> updateById(@Validated @RequestBody SysRoleAddReq sysRole, @PathVariable Integer id) {

        sysRoleService.updateById(sysRole, id);

        return R.success(id);
    }

    @ApiOperationSupport(order = 5)
    @Operation(summary = "5. 批量删除角色")
    @SysLogAnn(module = LogTypeConstants.ROLES, operateType = OperateTypeConstants.DELETE, desc = "删除角色")
    @PostMapping("/delete_batch")
    public R<Integer> batchDelete(@Validated @RequestBody BatchIdsReq ids) {

        sysRoleService.batchDelete(ids.getIds());

        return R.success(ids.getIds().size());
    }
}
