package com.superred.supervisor.manager.model.vo.terminal.manage;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 代理运行时状态对应
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TerminalRuntimeStatusResp {

    @Schema(description = "内存总数，表示整个设备的内存大小，单位GB。")
    private Long memTotal;

    @Schema(description = "内存使用率 0-100")
    private Integer memRate;

    @Schema(description = "内存使用情况 GB")
    private Long memUsed;

    @Schema(description = "内存剩余情况 GB")
    private Long memFree;


    @Schema(description = "系统cpu使用率")
    private Double cpuRate;


    @Schema(description = "磁盘总大小G")
    private Long diskTotal;

    @Schema(description = "磁盘使用率")
    private Double diskRate;

    @Schema(description = "磁盘使用情况")
    private Long diskUsed;

    @Schema(description = "磁盘剩余情况")
    private Long diskFree;


    @Schema(description = "系统运行时间")
    private Long upTime;

    @Schema(description = "最后上报时间")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "前八位为年月日，下划线后自定义")
    private String softVersion;
}
