package com.superred.supervisor.manager.model.vo.settings;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 客户端安装包信息Dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientInstallPackagePageReq extends PageReqDTO {

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Integer id;

    /**
     * 安装包名称
     */
    @Schema(description = "安装包名称")
    private String installPackageName;

    /**
     * 安装包描述
     */
    @Schema(description = "安装包描述")
    private String installPackageDesc;

    /**
     * 安装包版本
     */
    @Schema(description = "安装包版本")
    private String installPackageVersion;

    /**
     * 操作系统
     */
    @Schema(description = "操作系统")
    private String operatingSystem;

    /**
     * 操作系统展示名
     */
    @Schema(description = "操作系统展示名")
    private String operatingSystemName;

    /**
     * 平台架构
     */
    @Schema(description = "平台架构")
    private String platformArchitecture;

    /**
     * 平台架构展示名
     */
    @Schema(description = "平台架构展示名")
    private String platformArchitectureName;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private String fileType;

    /**
     * 上传时间
     */
    @Schema(description = "上传时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime uploadTime;

    /**
     * 发布状态
     */
    @Schema(description = "发布状态  0未发布  1已发布")
    private Integer publishStatus;


    @Schema(description = "校验码  0停用  1启用")
    private Integer checkCode;

}
