package com.superred.supervisor.manager.model.vo.system;

import com.superred.supervisor.common.entity.system.SysAuthorizationInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * 系统授权信息
 *
 * <AUTHOR>
 * @since 2025/03/13
 */
@Data
@Builder
public class SysAuthorizationInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 角色名
     */
    @Schema(description = "授权类型：0试用授权 1正式授权")
    private Integer authorityType;

    /**
     * 角色编码
     */
    @Schema(description = "系统授权有效期：-1，永久有效")
    private Long authValidDays;

    /**
     * 角色描述
     */
    @Schema(description = "生效时间")
    private LocalDate validStartDate;

    /**
     * 失效时间
     */
    @Schema(description = "失效时间")
    private LocalDate validEndDate;
    /**
     * 终端组件授权数
     */
    @Schema(description = "终端组件授权数")
    private Integer agentAuthCount;
    /**
     * 已用授权点数
     */
    @Schema(description = "已用授权点数")
    private Integer agentUsedCount;

    /**
     * 剩余授权点数
     */
    @Schema(description = "剩余授权点数")
    private Integer agentRemainingCount;

    /**
     * 失效时间
     */
    @Schema(description = "设备标号")
    private String sysNumber;

    public static SysAuthorizationInfoResp from(SysAuthorizationInfo authorizationInfo) {

        return SysAuthorizationInfoResp.builder()
                .id(authorizationInfo.getId())
                .authorityType(authorizationInfo.getAuthorityType())
                .authValidDays(authorizationInfo.getAuthValidDays())
                .validStartDate(authorizationInfo.getValidStartDate())
                .validEndDate(authorizationInfo.getValidEndDate())
                .agentAuthCount(authorizationInfo.getAgentAuthCount())
                .sysNumber(authorizationInfo.getSysNumber())
                .build();
    }
}
