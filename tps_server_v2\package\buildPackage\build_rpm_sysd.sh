#!/bin/sh
verno=`cat version.ini`
sed -i 's/Version:.*/Version: '$verno'/' ./sysd_files/wlhsupervisorserver.spec


# delete system config
rm -rf wlhsupervisorserver/etc
rm -rf wlhsupervisorserver/lib
rm -rf wlhsupervisorserver/DEBIAN

# 拷贝业务代码
cd ../../
mvn clean package -Dmaven.test.skip=true

rm -rf package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/*

mv -f supervisor-gateway/target/supervisor-gateway/dependencies package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/
cp -r supervisor-gateway/target/supervisor-gateway package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/

mv -f supervisor-report/target/supervisor-report/dependencies/* package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/dependencies
rm -rf supervisor-report/target/supervisor-report/dependencies
cp -r supervisor-report/target/supervisor-report package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/

mv -f supervisor-manage/target/supervisor-web/dependencies/* package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/dependencies
rm -rf supervisor-manage/target/supervisor-web/dependencies
cp -r supervisor-manage/target/supervisor-web package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/

mv -f supervisor-storage/target/supervisor-storage/dependencies/* package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/dependencies
rm -rf supervisor-storage/target/supervisor-storage/dependencies
cp -r supervisor-storage/target/supervisor-storage package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/

mv -f supervisor-monitor/target/supervisor-monitor/dependencies/* package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/dependencies
rm -rf supervisor-monitor/target/supervisor-monitor/dependencies
cp -r supervisor-monitor/target/supervisor-monitor package/buildPackage/wlhsupervisorserver/opt/wlh/supervisor/lib/

# copy new system config
cd package/buildPackage/
cp sysd_files/lib wlhsupervisorserver -r
cp -r ../docs/*.pdf wlhsupervisorserver/opt/wlh/supervisor/
# sign file
cp ../sign/private_key.pem ./
chmod +x sign_and_generate_manifest.sh
sh sign_and_generate_manifest.sh wlhsupervisorserver/opt/wlh/supervisor/sign wlhsupervisorserver/
rm -rf ./private_key.pem
# change permission
chmod 755 -R ./wlhsupervisorserver/

# tar
tar czvf wlhsupervisorserver.tar.gz ./wlhsupervisorserver

# copy tar file to rpmbuild source dir
mv wlhsupervisorserver.tar.gz ~/rpmbuild/SOURCES/
rpmbuild -ba sysd_files/wlhsupervisorserver.spec

echo "build rpm success"
ls -l ~/rpmbuild/RPMS/noarch/
# mv rpm file to sysd_files/bin
mv /root/rpmbuild/RPMS/noarch/wlhsupervisorserver-"$verno"'-1.noarch'.rpm sysd_files/bin/
#rm -rf buildtmp