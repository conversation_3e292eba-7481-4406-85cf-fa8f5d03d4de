package com.superred.common.core.filter;

import org.springframework.http.HttpHeaders;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/3/3 14:17
 */

public class SecurityHeaderFilter extends OncePerRequestFilter {


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        // 禁止缓存
        response.addHeader(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, max-age=0, must-revalidate");
        response.addHeader(HttpHeaders.PRAGMA, "no-cache");
        response.addHeader(HttpHeaders.EXPIRES, "0");
        // 防止 MIME 嗅探
        response.addHeader("X-Content-Type-Options", "nosniff");
        // 强制 HTTPS
        response.addHeader("Strict-Transport-Security", "max-age=31536000 ; includeSubDomains");
        // 防止 Clickjacking 攻击
        response.addHeader("X-Frame-Options", "DENY");
        //防止跨站脚本攻击（XSS）
        response.addHeader("X-XSS-Protection", "1; mode=block");
        // 设置内容安全策略（CSP）
        response.setHeader("Content-Security-Policy",
                "default-src 'self'; script-src 'self'; object-src 'none'; style-src 'self'; frame-ancestors 'none';");



        filterChain.doFilter(request, response);

    }
}
