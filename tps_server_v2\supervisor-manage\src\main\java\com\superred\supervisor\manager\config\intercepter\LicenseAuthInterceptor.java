package com.superred.supervisor.manager.config.intercepter;

import cn.hutool.core.util.StrUtil;
import com.superred.common.core.utils.WebUtils;
import com.superred.supervisor.common.entity.system.SysAuthorizationInfo;
import com.superred.supervisor.manager.annotation.role.IgnoreAuth;
import com.superred.supervisor.manager.exception.LicInvalidException;
import com.superred.supervisor.manager.model.auth.LicenseInvalidDTO;
import com.superred.supervisor.manager.model.auth.LoginUser;
import com.superred.supervisor.manager.service.CacheService;
import com.superred.supervisor.manager.utils.LicenceUtil;
import com.superred.supervisor.manager.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.time.LocalDate;

/**
 *  授权文件校验拦截器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class LicenseAuthInterceptor implements HandlerInterceptor {

    @Value("${superred.license.private-key}")
    private String publicKeyPath;




/*    @Value("${superred.license.enable:true}")
    private boolean enable;*/

    @Resource
    private CacheService cacheService;

    /**
     * 目标方法执行前
     * 该方法在控制器处理请求方法前执行，其返回值表示是否中断后续操作
     * 返回 true 表示继续向下执行，返回 false 表示中断后续操作
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (WebUtils.isAuthIgnoredUrl()) {
            return true;
        }
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        if (ignoreAuth(handlerMethod)) {
            return true;
        }
        LoginUser loginUser = SecurityUtils.getUser();

        this.doValidAuthInfo(loginUser);
        return true;
    }

    private boolean ignoreAuth(HandlerMethod handlerMethod) {
        Class<?> beanType = handlerMethod.getBeanType();

        IgnoreAuth ignoreAuth = AnnotationUtils.findAnnotation(beanType, IgnoreAuth.class);
        if (ignoreAuth != null) {
            return true;
        }
        Method method = handlerMethod.getMethod();
        return AnnotationUtils.findAnnotation(method, IgnoreAuth.class) != null;
    }

    private void doValidAuthInfo(LoginUser loginUser) {

        String machineCode = cacheService.cacheMachineCode();

        SysAuthorizationInfo authorizationInfo = cacheService.cacheAuthorizationInfo();
        if (authorizationInfo == null) {
            LicenseInvalidDTO licenseInvalidDTO = this.buildLicenseInvalidDTO(loginUser, machineCode);
            throw new LicInvalidException(licenseInvalidDTO, "授权文件不存在");
        }
        // 授权文件签名校验
        //3.3 验证签名
        String signContent = authorizationInfo.getSignContent();

        boolean verify = LicenceUtil.verify(signContent, authorizationInfo.getSignature(), publicKeyPath);
        if (!verify) {
            LicenseInvalidDTO licenseInvalidDTO = this.buildLicenseInvalidDTO(loginUser, machineCode);
            throw new LicInvalidException(licenseInvalidDTO, "授权文件签名无效");
        }

        // 机器码校验
        if (!StrUtil.equals(machineCode, authorizationInfo.getSysNumber())) {
            log.warn("授权文件机器码与授权机器机器码不匹配：{}", machineCode);
            throw new LicInvalidException(this.buildLicenseInvalidDTO(loginUser, machineCode), "授权文件机器码与授权机器机器码不匹配");
        }
        // 授权是否生效校验
        if (LocalDate.now().isBefore(authorizationInfo.getValidStartDate())) {
            log.warn("授权文件未生效");
            throw new LicInvalidException(this.buildLicenseInvalidDTO(loginUser, machineCode), "授权文件未生效");
        }
        // 授权是否过期校验
        if (authorizationInfo.getAuthValidDays() > 0 && LocalDate.now().isAfter(authorizationInfo.getValidEndDate())) {
            log.warn("授权文件已过期");
            throw new LicInvalidException(this.buildLicenseInvalidDTO(loginUser, machineCode), "授权文件已过期");
        }
    }

    private LicenseInvalidDTO buildLicenseInvalidDTO(LoginUser loginUser, String machineCode) {


        return LicenseInvalidDTO.builder()
                .userId(loginUser.getUserId())
                .isSystemAdmin(SecurityUtils.isSysAdmin())
                .userName(loginUser.getUsername())
                .roleId(loginUser.getRoleId())
                .machineCode(machineCode)
                .build();
    }

}
