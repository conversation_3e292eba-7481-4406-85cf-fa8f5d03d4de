package com.superred.supervisor.manager.model.dto.policy.config;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleRiskSoftware;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-28 17:00
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RiskSoftwarePolicyConfigDTO {

    private Long ruleId;

    private List<Integer> ruleContent;

    private String ruleDesc;

    public static RiskSoftwarePolicyConfigDTO getPolicyConfig(RuleRiskSoftware ruleRiskSoftware) {
        if (ruleRiskSoftware == null) {
            return null;
        }
        return RiskSoftwarePolicyConfigDTO.builder()
                .ruleId(Long.parseLong(ruleRiskSoftware.getRuleId()))
                .ruleContent(StrUtil.isBlank(ruleRiskSoftware.getRuleContent()) ? null : PolicyUtils.strToIntList(ruleRiskSoftware.getRuleContent()))
                .ruleDesc(PolicyUtils.handleStrNull(ruleRiskSoftware.getRuleDesc()))
                .build();
    }
}
