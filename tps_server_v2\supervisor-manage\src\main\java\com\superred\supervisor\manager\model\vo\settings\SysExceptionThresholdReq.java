package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 *  系统异常阈值
 * @since 2025年03月24日
 */
@Data
public class SysExceptionThresholdReq {

    @Schema(description = "系统阈值设置-磁盘使用率")
    @Min(value = 1, message = "磁盘使用率 取值范围1-99")
    @Max(value = 99, message = "磁盘使用率 取值范围1-99")
    @NotNull
    private Integer diskUsageRate;

    @Schema(description = "系统阈值设置-CPU使用率")
    @Min(value = 1, message = "CPU使用率 取值范围1-99")
    @Max(value = 99, message = "CPU使用率 取值范围1-99")
    @NotNull
    private Integer cpuUsageRate;

    @Schema(description = "系统阈值设置-内存使用率")
    @Min(value = 1, message = "内存使用率 取值范围1-99")
    @Max(value = 99, message = "内存使用率 取值范围1-99")
    @NotNull
    private Integer memoryUsageRate;

    @Schema(description = "系统阈值设置-是否开启报警；0：关闭；1：开启")
    @Min(value = 0, message = "是否开启报警 取值错误")
    @Max(value = 1, message = "是否开启报警 取值错误")
    @NotNull
    private Integer openAlarm;
}
