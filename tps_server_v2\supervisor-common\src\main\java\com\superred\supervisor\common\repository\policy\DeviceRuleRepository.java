package com.superred.supervisor.common.repository.policy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.DeviceRule;
import com.superred.supervisor.common.mapper.policy.DeviceRuleMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @create 2025-03-26 10:07
 */
@Slf4j
@Repository
@AllArgsConstructor
public class DeviceRuleRepository extends ServiceImpl<DeviceRuleMapper, DeviceRule> {
}
