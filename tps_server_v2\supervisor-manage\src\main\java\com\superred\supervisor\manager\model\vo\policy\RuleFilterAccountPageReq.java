package com.superred.supervisor.manager.model.vo.policy;

import com.superred.common.core.model.PageReqDTO;
import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-03-12 11:19
 */
@Data

public class RuleFilterAccountPageReq extends PageReqDTO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "策略下发版本")
    private String version;

    @Schema(description = "策略下发ID")
    private String policyId;

    @Schema(description = "策略下发设备类型 05 终端；01 检测器")
    private String issueDeviceType;

    @Schema(description = "策略ID")
    private String ruleId;

    @Schema(description = "策略内容")
    @ByteSize(max = 128, message = "策略内容长度不能超过128个字节")
    private String ruleContent;

    @Schema(description = "策略描述")
    private String ruleDesc;

    @Schema(description = "过滤文件类型 文档 1 图片 2 文本/网页 3 压缩包 4 邮件 5")
    private String filterFileType;

    @Schema(description = "文件过滤最小值(KB)")
    private Long filterFileSizeMin;

    @Schema(description = "文件过滤最大值(KB)")
    private Long filterFileSizeMax;

    @Schema(description = "是否共享")
    private String isShare;

    @Schema(description = "规则应用状态，0未应用，1已应用")
    private String status;

    @Schema(description = "策略来源 1 本级 2上级")
    private String ruleSource;

    @Schema(description = "平台级别")
    private String level;

    @Schema(description = "规则更新时间")
    private String updateTime;

    @Schema(description = "扩展字段1")
    private Long ext1;

    @Schema(description = "扩展字段2")
    private String ext2;

    @Schema(description = "扩展字段3")
    private String ext3;

    @Schema(description = "策略创建时间")
    private String createTime;

    @Schema(description = "开始时间")
    private String startDate;

    @Schema(description = "结束时间")
    private String endDate;
}
