package com.superred.supervisor.manager.model.dto.agent;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: zxy
 * @Date: 2023/4/18 18:01
 * @FileName: AgentSoftDTO
 * @Description: 终端软件查询DTO
 */
@Data
public class AgentSoftDTO {
    @Schema(description = "软件包名")
    private String packetName;

    @Schema(description = "软件包版本")
    private String version;

    @Schema(description = "操作系统")
    private String os;

    @Schema(description = "适用的cpu架构")
    private String cpuArchitecture;

    @Schema(description = "软件类型：1：安装包；2：升级包")
    private Short softType;

    @Schema(description = "是否发布：0未发布，1发布")
    private Integer isPublish;

    @Schema(description = "分页起始")
    private Integer start;
    @Schema(description = "分页大小")
    private Integer limit;
}