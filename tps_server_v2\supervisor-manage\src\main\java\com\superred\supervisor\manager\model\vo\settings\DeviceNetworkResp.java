package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 *  网卡信息返回实体
 * @since 2025年03月13日
 */
@Data

public class DeviceNetworkResp {

    @Schema(description = "主键id")
    private Integer id;

    @Schema(description = "网络编号")
    private String flag;

    @Schema(description = "ip地址,************")
    @NotBlank(message = "IP地址不能为空")
    private String ip;

    @Schema(description = "子网掩码，***********/24")
    @NotBlank(message = "IP地址不能为空")
    private String netmask;

    @Schema(description = "网关地址,*************")
    private String gateway;

    @Schema(description = "mac地址，00:0d:48:09:4e:88")
    private String mac;

    @Schema(description = "是否管理口，true，false")
    private Boolean manage;

    @Schema(description = "接口类型 1本地管理网络接口 2通信服务网络接口 ")
    private Integer type;

    @Schema(description = "是否在线，0 离线，1 在线")
    private Integer isOnline;
}
