package com.superred.supervisor.manager.model.vo.login;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 用户登录实体
 * username: a
 * password: irFXc7MbpAnjxahgcqZroA==
 * imageCode: 24
 * grant_type: password
 * scope: select
 * client_id: th_client
 * client_secret: th
 *
 * <AUTHOR>
 * @since 2025/3/6 16:21
 */
@Data

public class LoginReq {


    @NotBlank(message = "用户名不能为空")
    @Schema(description = "用户名")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Schema(description = "密码, AES加密")
    private String password;

    @NotBlank(message = "验证码不能为空")
    @Schema(description = "验证码")
    private String imageCode;

    /**
     * 验证码key
     */
    @Schema(description = "验证码key")
    @NotBlank(message = "验证码key不能为空")
    private String imageCodeKey;

    /**
     * 验证码key
     */
    @Schema(description = "SM2密钥keyId")
    @NotBlank(message = "keyId不能为空")
    private String keyId;
}
