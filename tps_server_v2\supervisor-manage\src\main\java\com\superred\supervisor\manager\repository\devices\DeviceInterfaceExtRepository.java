package com.superred.supervisor.manager.repository.devices;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.devices.DeviceInterface;
import com.superred.supervisor.common.mapper.devices.DeviceInterfaceMapper;
import com.superred.supervisor.manager.model.dto.devices.CountDTO;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 检测器网卡流量 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-18 20:06:46
 */
@Repository
public class DeviceInterfaceExtRepository extends ServiceImpl<DeviceInterfaceMapper, DeviceInterface> {

    public Map<String, CountDTO> selectCountMapByDeviceIds(List<String> deviceIds) {
        if (CollUtil.isEmpty(deviceIds)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<DeviceInterface> query = new LambdaQueryWrapper<DeviceInterface>().in(DeviceInterface::getDeviceId, deviceIds);
        List<DeviceInterface> deviceInterfaces = this.list(query);

        Map<String, List<DeviceInterface>> groupMap = deviceInterfaces.stream().collect(Collectors.groupingBy(DeviceInterface::getDeviceId));
        return groupMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            CountDTO countDTO = new CountDTO();
            countDTO.setCountTotal(entry.getValue().size());
            countDTO.setCountOnline((int) entry.getValue().stream().filter(one -> one.getInterfaceStat() == 1).count());
            int sum = entry.getValue().stream().map(DeviceInterface::getInterfaceFlow).filter(Objects::nonNull).mapToInt(Integer::intValue).sum();
            countDTO.setCountFlow((double) (sum / (1024 * 1024)));
            return countDTO;
        }));
    }
}

