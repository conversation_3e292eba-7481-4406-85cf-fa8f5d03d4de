package com.superred.supervisor.manager.model.vo.terminal;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


/**
 * 请求
 *
 * <AUTHOR>
 * @since 2025/03/17
 */
@Data

public class TerminalCheckReq {

    @Schema(description = "设备id")
    private String deviceId;

    @Schema(description = "审核状态0 审核通过 1审核不通过")
    private String registerStatus;

    @Schema(description = "审核状态描述信息")
    private String registerMessage;

    @Schema(description = "审核device_id列表")
    private List<String> idList;
}
