package com.superred.supervisor.manager.service.command;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandCertUpdateReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDeviceDetailReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDeviceDetailResp;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDeviceReportReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDropDataReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandInnerPolicySwitchReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandInnerPolicyUpdateReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandModuleSwitchReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandPasswordResetReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandPolicyReportReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandRebootReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandShutdownReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandSoftwareUpdateReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsResp;
import com.superred.supervisor.manager.model.vo.command.IssueCommandSyncTimeReq;
import com.superred.supervisor.manager.model.vo.command.IssueCommandVersionCheckReq;

/**
 * <AUTHOR>
 */
public interface IssueCommandService {

    void shutdown(DetectorCommandShutdownReq detectorCommandShutdownReq);

    void reboot(DetectorCommandRebootReq req);

    void syncTime(IssueCommandSyncTimeReq req);

    void versionCheck(IssueCommandVersionCheckReq req);

    void softwareUpdate(DetectorCommandSoftwareUpdateReq req);

    void passwordReset(DetectorCommandPasswordResetReq req);

    void dropData(DetectorCommandDropDataReq req);

    void reportPolicy(DetectorCommandPolicyReportReq req);

    void innerPolicyUpdate(DetectorCommandInnerPolicyUpdateReq req);

    void moduleSwitch(DetectorCommandModuleSwitchReq req);

    void innerPolicySwitch(DetectorCommandInnerPolicySwitchReq req);

    void certUpdate(DetectorCommandCertUpdateReq req);

    void deviceReport(DetectorCommandDeviceReportReq req);

    IPage<DetectorCommandStatisticsResp> statisticsPage(DetectorCommandStatisticsReq req);

    IPage<DetectorCommandDeviceDetailResp> deviceDetailPage(DetectorCommandDeviceDetailReq req);


}
