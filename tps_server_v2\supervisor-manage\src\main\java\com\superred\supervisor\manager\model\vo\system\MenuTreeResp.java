package com.superred.supervisor.manager.model.vo.system;

import com.superred.supervisor.common.entity.system.SysMenu;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * 在菜单中
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor

public class MenuTreeResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    @Schema(description = "菜单ID")
    private Integer id;
    /**
     * 菜单名称
     */
    @Schema(description = "菜单名称")
    private String name;
    /**
     * 菜单编码
     */
    @Schema(description = "菜单编码")
    private String code;
    /**
     * 菜单权限标识
     */
    @Schema(description = "菜单权限标识")
    private String permission;
    /**
     * 父菜单ID
     */
    @Schema(description = "父菜单ID")
    private Integer parentId;
    /**
     * 图标
     */
    @Schema(description = "图标")
    private String icon;
    /**
     * 一个路径
     */
    @Schema(description = "一个路径")
    private String pathUrl;
    /**
     * VUE页面
     */
    @Schema(description = "VUE页面")
    private String component;
    /**
     * 排序值
     */
    @Schema(description = "排序值")
    private Integer sort;

    /**
     * 菜单类型 （1菜单 2按钮）
     */
    @Schema(description = "菜单类型 （1菜单 2按钮）")
    private Integer type;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remarks;
    /**
     * 开启关闭
     */
    @Schema(description = "开启关闭")
    private Integer keepAlive;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime modifiedTime;

    /**
     * 叶子节点
     */
    private Boolean leaf;


    @Schema(description = "子集")
    private List<MenuTreeResp> children;


    public static List<MenuTreeResp> buildTree(List<SysMenu> menus, Integer parentId) {

        List<MenuTreeResp> result = new ArrayList<>();

        for (SysMenu menu : menus) {
            if (menu.getParentId().equals(parentId)) {
                MenuTreeResp menuTreeResp = MenuTreeResp.builder()
                        .id(menu.getId())
                        .name(menu.getName())
                        .code(menu.getCode())
                        .permission(menu.getPermission())
                        .parentId(menu.getParentId())
                        .icon(menu.getIcon())
                        .pathUrl(menu.getPathUrl())
                        .component(menu.getComponent())
                        .sort(menu.getSort())
                        .type(menu.getType())
                        .remarks(menu.getRemarks())
                        .keepAlive(menu.getKeepAlive())
                        .createTime(menu.getCreateTime())
                        .modifiedTime(menu.getModifiedTime())
                        .leaf(menu.getLeaf())
                        .children(buildTree(menus, menu.getId()))
                        .build();
                result.add(menuTreeResp);
            }
        }
        return result;

    }
}
