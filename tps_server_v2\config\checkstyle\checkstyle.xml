<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
        "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<module name="Checker">

    <module name="TreeWalker">
        <module name="SuppressWarningsHolder"/>
        <module name="EmptyCatchBlock"/>
        <module name="AvoidNestedBlocks"/>
        <module name="EmptyBlock"/>
        <module name="NeedBraces"/>
        <module name="FinalClass"/>
        <module name="HideUtilityClassConstructor"/>
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode"/>
        <module name="IllegalInstantiation">
            <property name="classes" value="java.lang.Boolean,java.lang.Integer"/>
        </module>
        <module name="InnerAssignment"/>
        <!--        <module name="MagicNumber">-->
        <!--            <property name="ignoreAnnotation" value="true"/>-->
        <!--            <property name="ignoreHashCodeMethod" value="true"/>-->
        <!--        </module>-->
        <module name="MissingSwitchDefault"/>
        <module name="NestedForDepth">
            <property name="max" value="3"/>
        </module>
        <module name="NestedIfDepth">
            <property name="max" value="3"/>
        </module>
        <module name="SimplifyBooleanExpression"/>
        <module name="UnusedLocalVariable"/>

        <module name="AvoidStarImport"/>
        <module name="IllegalImport"/>
        <module name="UnusedImports"/>

        <module name="UpperEll"/>
        <module name="ArrayTypeStyle"/>
        <module name="ModifierOrder"/>

        <module name="ConstantName">
            <property name="format" value="^log(ger)?$|^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$"/>
        </module>
        <module name="LocalVariableName"/>
        <module name="MemberName"/>
        <module name="MethodName"/>
        <module name="PackageName"/>
        <module name="PackageName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>

        <module name="MethodCount">
            <property name="maxPublic" value="30"/>
            <property name="maxTotal" value="50"/>
        </module>
        <module name="FallThrough"/>
        <module name="InterfaceTypeParameterName"/>
        <module name="AbbreviationAsWordInName">
            <property name="ignoreFinal" value="false"/>
        </module>
        <module name="MethodLength">
            <property name="max" value="200"/>
        </module>
        <module name="ParameterNumber"/>

        <module name="GenericWhitespace"/>
        <module name="MethodParamPad"/>
        <module name="NoWhitespaceAfter"/>
        <module name="NoWhitespaceBefore"/>
        <module name="OperatorWrap"/>
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter"/>

    </module>
    <module name="SuppressWarningsFilter"/>

</module>