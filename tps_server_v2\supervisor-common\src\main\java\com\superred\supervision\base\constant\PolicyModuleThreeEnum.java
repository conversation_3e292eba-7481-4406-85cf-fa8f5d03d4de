package com.superred.supervision.base.constant;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2025-03-25 14:47
 */
@Getter
public enum PolicyModuleThreeEnum {
    // 攻击窃密检测 alarm
    // 黑名单检测策略 blacklist
    IP_BLACKLIST("ip_blacklist", "IP黑名单检测策略", PolicyModuleTwoEnum.BLACKLIST, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey(), PolicyDeviceTypeEnum.AGENT.getKey()), 1),
    DOMAIN_BLACKLIST("domain_blacklist", "域名黑名单检测策略", PolicyModuleTwoEnum.BLACKLIST, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey(), PolicyDeviceTypeEnum.AGENT.getKey()), 2),
    URL_BLACKLIST("url_blacklist", "URL黑名单检测策略", PolicyModuleTwoEnum.BLACKLIST, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 3),
    ACCOUNT_BLACKLIST("account_blacklist", "帐号黑名单检测检测策略", PolicyModuleTwoEnum.BLACKLIST, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 4);

    // 攻击窃密检测 alarm
    // 白名单过滤策略 alarm_whitelist
    //ALARM_IP_WHITELIST("alarm_ip_whitelist", "IP白名单策略", PolicyModuleTwoEnum.ALARM_WHITELIST, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 1),
    //ALARM_HASH_WHITELIST("alarm_hash_whitelist", "哈希白名单策略", PolicyModuleTwoEnum.ALARM_WHITELIST, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 2),

    // 网络行为审计 net_audit
    // 审计白名单策略 audit_whitelist
    //AUDIT_IP_WHITELIST("audit_ip_whitelist", "IP白名单策略", PolicyModuleTwoEnum.AUDIT_WHITELIST, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 1),
    //AUIDT_DOMAIN_WHITELIST("audit_domain_whitelist", "域名白名单策略", PolicyModuleTwoEnum.AUDIT_WHITELIST, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 2);

    private final String key;
    private final String value;
    private final PolicyModuleTwoEnum parent;
    private final List<String> deviceType;
    private final Integer sort;

    PolicyModuleThreeEnum(String key, String value, PolicyModuleTwoEnum parent, List<String> deviceType, Integer sort) {
        this.key = key;
        this.value = value;
        this.parent = parent;
        this.deviceType = deviceType;
        this.sort = sort;
    }

    public static List<PolicyModuleThreeEnum> getThreeByParent(PolicyModuleTwoEnum parent, String deviceType) {
        List<PolicyModuleThreeEnum> list = new ArrayList<>();
        for (PolicyModuleThreeEnum value : PolicyModuleThreeEnum.values()) {
            if (Objects.equals(parent, value.getParent()) && value.deviceType.contains(deviceType)) {
                list.add(value);
            }
        }
        return list;
    }

}
