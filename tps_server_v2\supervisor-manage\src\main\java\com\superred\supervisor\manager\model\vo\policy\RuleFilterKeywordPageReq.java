package com.superred.supervisor.manager.model.vo.policy;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-11 16:54
 */
@Data

public class RuleFilterKeywordPageReq extends PageReqDTO {

    @Schema(description = "策略下发版本")
    private String version;

    @Schema(description = "策略下发ID")
    private String policyId;

    @Schema(description = "策略下发设备类型 05 终端；01 检测器")
    private String issueDeviceType;

    @Schema(description = "策略ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ruleId;

    @Schema(description = "策略ID字符串")
    private String ruleIdStr;

    @Schema(description = "策略类型，0 关键词，1正则表达式")
    private Integer ruleType;

    @Schema(description = "告警级别，0无风险，1一般级，2关注级，3严重级，4紧急级")
    private Integer risk;

    @Schema(description = "策略应用状态，0未应用，1已应用")
    private Integer status;

    @Schema(description = "策略内容")
    private String ruleContent;

    @Schema(description = "导出策略id列表")
    private List<Integer> idList;
}
