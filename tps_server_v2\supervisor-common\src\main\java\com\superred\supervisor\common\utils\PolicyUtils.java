package com.superred.supervisor.common.utils;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025-03-27 20:08
 */
public final class PolicyUtils {


    /**
     * 处理规则的过期时间
     *
     * @param hour 则默认当前时间加hour
     * @return 2021-01-01 00:00:00
     */
    public static String handleExpireTime(int hour) {
        DateTime offset = DateUtil.offset(new Date(), DateField.HOUR, hour);
        return DateUtil.format(offset, DatePattern.NORM_DATETIME_PATTERN);
    }

}
