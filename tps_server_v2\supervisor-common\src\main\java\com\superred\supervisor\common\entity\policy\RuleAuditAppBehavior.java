package com.superred.supervisor.common.entity.policy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.superred.supervisor.common.utils.PolicyUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 应用行为审计策略
 * <AUTHOR>
 * @create 2025-04-07 10:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_rule_audit_appbehavior", autoResultMap = true)
public class RuleAuditAppBehavior extends Model<RuleAuditAppBehavior> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 策略ID
     */
    @TableId(value = "rule_id", type = IdType.AUTO)
    private String ruleId;

    /**
     * 策略名称
     */
    private String ruleName;

    /**
     * 策略描述
     */
    private String ruleDesc;

    /**
     * IP地址 每个数组元素类型为:
     * 单个 IP,IP 子网，IP 范围类型。
     * 0.0.0.0/0 表示 ANY
     */
    private String ip;

    /**
     * 审计行为类型 1 WEB行为
     * 2 DNS 行为
     * 3 SSL/TLS 行为，
     * 4 数据库操作行为
     * 5 文件传输行为
     * 6 控制行为
     * 7 登录行为
     * 8 邮件行为
     * 99 其它
     */
    private String auditType;

    /**
     * 策略失效时间
     */
    private String expireTime = PolicyUtils.handleExpireTime(24);

    /**
     * JSON字符串 过滤规则参数
     */
    private String param;

    /**
     * 规则应用状态，0未应用，1已应用
     */
    private String status;

    /**
     * 是否共享
     */
    private String isShare;

    /**
     * 策略来源 1 本级 2上级
     */
    private String ruleSource;

    /**
     * 平台级别
     */
    private String level;

    /**
     * 规则更新时间
     */
    private String updateTime;

    /**
     * 策略创建时间
     */
    private String createTime;

    /**
     * 扩展字段1
     */
    private Long ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 上级共享策略ID
     */
    private String upRuleId;

}
