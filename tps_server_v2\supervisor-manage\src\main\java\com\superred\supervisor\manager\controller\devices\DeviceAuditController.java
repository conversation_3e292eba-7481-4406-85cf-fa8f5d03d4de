package com.superred.supervisor.manager.controller.devices;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditDetailResp;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditFailedReq;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditPageReq;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditPageResp;
import com.superred.supervisor.manager.service.devices.DeviceInfoService;
import com.superred.supervisor.manager.service.system.SystemSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * 设备基础信息控制器
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@Tag(name = "3.0. 设备审核管理")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/device_base_info")
public class DeviceAuditController {

    @Resource
    private DeviceInfoService deviceInfoService;

    @Resource
    private SystemSettingService systemSettingService;

    @ApiOperationSupport(order = 1)
    @Operation(summary = "1. 分页查询")
    @PostMapping("/audit/page")
    public RPage<DeviceAuditPageResp> getDeviceBaseInfoPage(@Valid @RequestBody DeviceAuditPageReq req) {
        IPage<DeviceAuditPageResp> pageRespIPage = deviceInfoService.pageDeviceAudit(req);

        return new RPage<>(pageRespIPage);

    }


    @Operation(summary = "2. 设备注册状态")
    @GetMapping("/audit/{deviceId}")
    public R<DeviceAuditDetailResp> getAuditDetail(@PathVariable("deviceId") String deviceId) {
        DeviceAuditDetailResp detail = deviceInfoService.getAuditDetail(deviceId);
        return R.success(detail);
    }


    @Operation(summary = "3. 审核通过")
    @SysLogAnn(module = LogTypeConstants.ACCESS_DEVICE_MANAGEMENT, operateType = OperateTypeConstants.TO_EXAMINE, desc = "审核通过")
    @GetMapping("/audit/check_success/{deviceId}")
    public R<Boolean> checkSuccess(@PathVariable("deviceId") String deviceId) {
        deviceInfoService.checkSuccess(deviceId);

        return R.success(true);
    }


    @Operation(summary = "4. 审核驳回")
    @SysLogAnn(module = LogTypeConstants.ACCESS_DEVICE_MANAGEMENT, operateType = OperateTypeConstants.TO_EXAMINE, desc = "审核驳回")
    @PostMapping("/audit/check_failed")
    public R<Boolean> checkFailed(@Valid @RequestBody DeviceAuditFailedReq req) {
        deviceInfoService.checkFailed(req);

        return R.success(true);
    }


    @Operation(summary = "5. 获取是否自动审核")
    @GetMapping("/audit/get_auto_check_state")
    public R<Boolean> getAutoCheckState() {

        return R.success(systemSettingService.isDeviceAutoAudit());
    }

    /**
     * 更新自动检查状态
     *
     * @param state 状态 0 自动审核 1 手动审核
     * @return {@link R }<{@link Boolean }>
     */
    @Operation(summary = "6. 更新自动审核")
    @SysLogAnn(module = LogTypeConstants.ACCESS_DEVICE_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "更新自动审核")
    @GetMapping("/update/auto_check_state/{state}")
    public R<Boolean> updateAutoCheckState(
            @Parameter(description = "状态 0 自动审核 1 手动审核") @PathVariable String state) {
        systemSettingService.updateDeviceAutoAudit(state);
        return R.success(true);
    }
}
