package com.superred.supervisor.manager.service.command.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.entity.agent.AgentAuthInfo;
import com.superred.supervisor.common.entity.agent.enums.AuthStatus;
import com.superred.supervisor.common.entity.command.AgentSoftware;
import com.superred.supervisor.common.entity.command.IssueAgent;
import com.superred.supervisor.common.entity.command.UpdateFile;
import com.superred.supervisor.common.entity.system.SysOrg;
import com.superred.supervisor.common.entity.system.SysStaff;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import com.superred.supervisor.common.repository.agent.AgentAuthInfoRepository;
import com.superred.supervisor.common.repository.command.AgentSoftwareRepository;
import com.superred.supervisor.common.repository.command.UpdateFileRepository;
import com.superred.supervisor.common.repository.system.SysOrgRepository;
import com.superred.supervisor.common.repository.system.SysStaffRepository;
import com.superred.supervisor.manager.common.enums.command.CommandType;
import com.superred.supervisor.manager.constant.CommonConstants;
import com.superred.supervisor.manager.model.vo.command.IssueModuleCmd;
import com.superred.supervisor.manager.model.vo.command.OperationRangeReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageResp;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdRecordPageResp;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalCmdRecordPageReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalInnerPolicyUpdateCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalModuleSwitchCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalSoftUpgradeCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalUninstallCmdReq;
import com.superred.supervisor.manager.repository.command.IssueAgentExtRepository;
import com.superred.supervisor.manager.repository.terminal.TerminalAgentInfoExtRepository;
import com.superred.supervisor.manager.service.CmdIdBuilder;
import com.superred.supervisor.manager.service.command.IssueAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IssueAgentServiceImpl implements IssueAgentService {
    @Resource
    private IssueAgentExtRepository issueAgentExtRepository;
    @Resource
    private AgentSoftwareRepository agentSoftwareRepository;
    @Resource
    private TerminalAgentInfoExtRepository terminalAgentInfoExtRepository;
    @Resource
    private AgentAuthInfoRepository agentAuthInfoRepository;
    @Resource
    private CmdIdBuilder cmdIdBuilder;
    @Resource
    private UpdateFileRepository updateFileRepository;
    @Resource
    private SysStaffRepository sysStaffRepository;
    @Resource
    private SysOrgRepository sysOrgRepository;

    @Override
    public void componentUpgrade(TerminalSoftUpgradeCmdReq req) {
        this.fillDeviceIds(req);
        List<String> deviceIds = req.getDeviceIds();
        if (CollUtil.isEmpty(deviceIds)) {
            log.warn("下发component_upgrade指令，未勾选任何终端");
            return;
        }
        AgentSoftware agentSoftware = agentSoftwareRepository.getById(req.getId());
        LambdaQueryWrapper<TerminalAgentInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TerminalAgentInfo::getDeviceId, deviceIds);
//        List<TerminalAgentInfo> list = terminalAgentInfoExtRepository.list(wrapper);
        // 验证终端激活状态
        validateAgentAuth(deviceIds);
        // 验证升级包是否可以下发给这些终端
        StringBuilder sb = new StringBuilder();
//        for (TerminalAgentInfo agentDeviceInfo : list) {
//            if (CharSequenceUtil.isBlank(agentDeviceInfo.getUpdateOs()) || CharSequenceUtil.isBlank(agentDeviceInfo.getUpdateArch())) {
//                sb.append(agentDeviceInfo.getUserName()).append('-').append(agentDeviceInfo.getHostName()).append(',');
//                continue;
//            }
//            if (!CharSequenceUtil.equalsIgnoreCase(agentDeviceInfo.getUpdateOs(), agentSoftware.getOs())) {
//                sb.append(agentDeviceInfo.getUserName()).append('-').append(agentDeviceInfo.getHostName()).append(',');
//                continue;
//            }
//            if (!StringUtils.equals(agentDeviceInfo.getUpdateArch(), agentSoftware.getCpuArchitecture())) {
//                sb.append(agentDeviceInfo.getUserName()).append('-').append(agentDeviceInfo.getHostName()).append(',');
//            }
//        }
        String str = sb.toString();
        if (CharSequenceUtil.isNotBlank(str)) {
            str = str.substring(0, str.length() - 1);
            throw new BaseBusinessException("当前存在勾选终端的操作系统、CPU架构、与升级包不一致，已取消终端升级下发：" + str);
        }

        List<IssueAgent> issueAgents = new ArrayList<>();
        for (String deviceId : deviceIds) {
            IssueAgent issueAgent = new IssueAgent();
            issueAgent.setDeviceId(deviceId);
            issueAgent.setType(CommonConstants.COMMAND);
            issueAgent.setCmd(CommandType.UPDATE.getValue());
            String cmdId = cmdIdBuilder.buildCmdId();
            issueAgent.setCmdId(cmdId);
            Map<String, Object> jo = new HashMap<>(16);
            jo.put("filename", agentSoftware.getPacketName());
            jo.put("md5", agentSoftware.getMd5());
            jo.put("soft_version", agentSoftware.getVersion());
            jo.put("update_method", req.getUpdateMethod());

            issueAgent.setParam(JsonUtil.toJson(jo));
            issueAgent.setCreateTime(LocalDateTime.now());
            issueAgents.add(issueAgent);
        }
        issueAgentExtRepository.saveBatch(issueAgents);
    }

    @Override
    public void innerPolicyUpdate(TerminalInnerPolicyUpdateCmdReq req) {
        fillDeviceIds(req);
        List<String> deviceIdList = req.getDeviceIds();
        this.validateAgentAuth(deviceIdList);
        // 查询内置策略信息
        UpdateFile updateFile = updateFileRepository.getById(req.getId());
        if (updateFile == null) {
            throw new BaseBusinessException("查询内置策略信息为空错误");
        }
        LocalDateTime now = LocalDateTime.now();
        // 入库
        List<IssueAgent> list = new ArrayList<>(deviceIdList.size());
        deviceIdList.forEach(deviceId -> {
            IssueAgent issueAgent = new IssueAgent();
            issueAgent.setDeviceId(deviceId);
            issueAgent.setType("command");
            String cmdId = cmdIdBuilder.buildCmdId();
            issueAgent.setCmdId(cmdId);
            issueAgent.setCmd("inner_policy_update");
            Map<String, Object> param = new HashMap<>(16);
            param.put("filename", updateFile.getFilename());
            param.put("md5", updateFile.getMd5());
            issueAgent.setParam(JsonUtil.toJson(param));
            issueAgent.setCreateTime(now);
            issueAgent.setStatus(0);
            list.add(issueAgent);
        });
        issueAgentExtRepository.saveBatch(list);
    }

    @Override
    public void uninstall(TerminalUninstallCmdReq req) {
        fillDeviceIds(req);
        // 过滤掉不存在的终端设备信息
        List<String> agentDeviceIds = req.getDeviceIds();
        List<TerminalAgentInfo> agentDeviceInfos = terminalAgentInfoExtRepository.list(
                Wrappers.<TerminalAgentInfo>lambdaQuery().in(TerminalAgentInfo::getDeviceId, agentDeviceIds)
        );
        if (CollUtil.isEmpty(agentDeviceInfos)) {
            return;
        }
        agentDeviceIds = agentDeviceInfos.stream().map(TerminalAgentInfo::getDeviceId).collect(Collectors.toList());
        // 防止卸载指令重复下发
        List<IssueAgent> uninstallCommandList = issueAgentExtRepository.list(
                Wrappers.<IssueAgent>lambdaQuery()
                        .eq(IssueAgent::getType, "command")
                        .eq(IssueAgent::getCmd, "agent_device_uninstall")
                        .isNull(IssueAgent::getResult)
                        .in(IssueAgent::getDeviceId, agentDeviceIds));
        Map<String, IssueAgent> issueAgentMap = uninstallCommandList.stream().collect(Collectors.toMap(IssueAgent::getDeviceId, item -> item, (o, n) -> n));
        LocalDateTime now = LocalDateTime.now();
        // 入库
        List<IssueAgent> list = new ArrayList<>(agentDeviceIds.size());
        agentDeviceIds.forEach(deviceId -> {
            IssueAgent dbIssueAgent = issueAgentMap.get(deviceId);
            if (dbIssueAgent == null) {
                IssueAgent issueAgent = new IssueAgent();
                issueAgent.setDeviceId(deviceId);
                issueAgent.setType("command");
                String cmdId = cmdIdBuilder.buildCmdId();
                issueAgent.setCmdId(cmdId);
                issueAgent.setCmd("agent_device_uninstall");
                Map<String, Object> param = new HashMap<>(16);
                issueAgent.setParam(JsonUtil.toJson(param));
                issueAgent.setCreateTime(now);
                issueAgent.setStatus(0);
                list.add(issueAgent);
            } else {
                dbIssueAgent.setCreateTime(now);
                list.add(dbIssueAgent);
            }
        });
        issueAgentExtRepository.saveOrUpdateBatch(list);
    }

    @Override
    public void moduleSwitch(TerminalModuleSwitchCmdReq req) {
        fillDeviceIds(req);
        List<String> deviceIds = req.getDeviceIds();
        this.validateAgentAuth(deviceIds);
        List<IssueModuleCmd> modules = req.getModules();
        LocalDateTime now = LocalDateTime.now();
        List<IssueAgent> issueAgentsToSave = modules.stream()
                .flatMap(module -> deviceIds.stream().map(deviceId -> {
                    IssueAgent issueAgent = new IssueAgent();
                    String cmdId = cmdIdBuilder.buildCmdId();
                    issueAgent.setCmdId(cmdId);
                    issueAgent.setType(CommonConstants.COMMAND);
                    issueAgent.setIssueTime(now);
                    issueAgent.setIssueTime(now);
                    issueAgent.setCmd(module.getCmd());
                    issueAgent.setModule(module.getModule());
                    if (module.getIsAll() == 1) {
                        // issueAgent.setSubmodule("[]");
                        issueAgent.setParam("[]");
                    } else {
                        if (CollUtil.isNotEmpty(module.getSubmodules())) {
                            String param = JsonUtil.toJson(module.getSubmodules());
                            // issueAgent.setSubmodule(param);
                            issueAgent.setParam(param);
                        }
                    }
                    return issueAgent;
                })).collect(Collectors.toList());
        issueAgentExtRepository.saveBatch(issueAgentsToSave);
    }

    @Override
    public IPage<TerminalCmdRecordPageResp> statisticsPage(TerminalCmdRecordPageReq req) {
        Page<TerminalCmdRecordPageResp> agentCmdVOPage = issueAgentExtRepository.statisticsPage(req);
        if (agentCmdVOPage != null && CollectionUtil.isNotEmpty(agentCmdVOPage.getRecords())) {
            agentCmdVOPage.getRecords().forEach(item -> {
//                if (StringUtils.equals("update", item.getType())) {
//                    item.setCmdType("终端组件升级");
//                }
//                if (StringUtils.equals("command", item.getType()) && StringUtils.equals("inner_policy_update", item.getCmd())) {
//                    item.setCmdType("内置策略更新");
//                }
            });
        }

        return agentCmdVOPage;
    }

    @Override
    public IPage<TerminalCmdExecPageResp> deviceDetailPage(TerminalCmdExecPageReq req) {
        LambdaQueryWrapper<IssueAgent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IssueAgent::getCmdId, req.getCmdId());
//        queryWrapper.eq(CharSequenceUtil.isNotBlank(req.getDeviceId()), IssueAgent::getDeviceId, req.getDeviceId());
        queryWrapper.eq(req.getResult() != null, IssueAgent::getResult, req.getResult());
        queryWrapper.eq(req.getStatus() != null, IssueAgent::getStatus, req.getStatus());
        Page<IssueAgent> issueAgentPage = this.issueAgentExtRepository.page(new Page<>(req.getStart(), req.getLimit()), queryWrapper);
        List<IssueAgent> records = issueAgentPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new Page<>();
        }
        // 查询人员
        List<SysStaff> staffs = this.sysStaffRepository.list();
        Map<Integer, SysStaff> staffMap = staffs.stream().collect(Collectors.toMap(SysStaff::getId, Function.identity()));
        List<SysOrg> sysOrgs = sysOrgRepository.list();
        Map<Integer, SysOrg> sysOrgMap = sysOrgs.stream().collect(Collectors.toMap(SysOrg::getId, Function.identity()));
        Set<String> devicesIds = records.stream().map(IssueAgent::getDeviceId).collect(Collectors.toSet());
        LambdaQueryWrapper<TerminalAgentInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TerminalAgentInfo::getDeviceId, devicesIds);
        List<TerminalAgentInfo> agentDeviceInfos = this.terminalAgentInfoExtRepository.list(wrapper);
        Map<String, TerminalAgentInfo> agentDeviceInfoMap = agentDeviceInfos.stream().collect(Collectors.toMap(TerminalAgentInfo::getDeviceId, Function.identity()));
        List<TerminalCmdExecPageResp> respList = records.stream().map(issueAgent -> {
            TerminalCmdExecPageResp resp = BeanUtil.copyProperties(issueAgent, TerminalCmdExecPageResp.class);
            TerminalAgentInfo agent = agentDeviceInfoMap.get(issueAgent.getDeviceId());
            if (agent != null) {

                resp.setHostName(agent.getHostName());
//                resp.setOs(agent.getOs());
//                resp.setUserName(agent.getUserName());
                SysStaff staff = staffMap.get(Integer.valueOf(agent.getUserId()));
                if (staff != null) {
                    SysOrg org = sysOrgMap.get(staff.getOrgId());
                    SysOrg unit = sysOrgMap.get(staff.getUnitId());
                    resp.setOrgName(org == null ? "" : org.getName());
                    resp.setCompany(unit == null ? "" : unit.getName());
                }
//                resp.setSoftVersion(agent.getSoftVersion());

            }
            return resp;
        }).collect(Collectors.toList());
        Page<TerminalCmdExecPageResp> respPage = new Page<>(req.getStart(), req.getLimit(), (int) issueAgentPage.getTotal());
        respPage.setRecords(respList);
        return respPage;
    }

    public void validateAgentAuth(List<String> deviceIdList) {
        // 验证终端激活状态
        List<TerminalAgentInfo> agentDeviceList = terminalAgentInfoExtRepository.list(
                Wrappers.<TerminalAgentInfo>lambdaQuery()
                        .in(TerminalAgentInfo::getDeviceId, deviceIdList));
        Map<String, AgentAuthInfo> authorizationInfoMap = MapUtil.newHashMap();
        List<AgentAuthInfo> agentDeviceAuthorizationInfos =
                agentAuthInfoRepository.list(
                        Wrappers.<AgentAuthInfo>lambdaQuery()
                                .in(AgentAuthInfo::getAgentDeviceId, deviceIdList));
        if (CollectionUtil.isNotEmpty(agentDeviceAuthorizationInfos)) {
            authorizationInfoMap = agentDeviceAuthorizationInfos.stream().collect(
                    Collectors.toMap(AgentAuthInfo::getAgentDeviceId, Function.identity(), (k1, k2) -> k1));
        }
        StringBuilder sb1 = new StringBuilder();
        for (TerminalAgentInfo agentDeviceInfo : agentDeviceList) {
            AgentAuthInfo agentDeviceAuthorizationInfo = authorizationInfoMap.get(agentDeviceInfo.getDeviceId());
            if (agentDeviceAuthorizationInfo == null || agentDeviceAuthorizationInfo.getActivationStatus() == AuthStatus.NOT_ACTIVE) {
                sb1.append(agentDeviceInfo.getUserName()).append('-').append(agentDeviceInfo.getHostName()).append(',');
            }
        }
        String str1 = sb1.toString();
        if (CharSequenceUtil.isNotBlank(str1)) {
            str1 = str1.substring(0, str1.length() - 1);
            throw new BaseBusinessException("当前存在勾选终端是未激活状态：" + str1);
        }
    }

    public void fillDeviceIds(OperationRangeReq req) {
        if (CollUtil.isNotEmpty(req.getDeviceIds())) {
            return;
        }
        if (CollUtil.isEmpty(req.getOrgIds())) {
            return;
        }

        List<TerminalAgentInfo> agentDeviceInfos = terminalAgentInfoExtRepository.list(
                Wrappers.<TerminalAgentInfo>lambdaQuery()
                        .in(TerminalAgentInfo::getOrgId, req.getOrgIds()));
        if (CollUtil.isEmpty(agentDeviceInfos)) {
            log.warn("未查询到orgId={}的终端设备信息", req.getOrgIds());
            return;
        }
        req.setDeviceIds(agentDeviceInfos.stream().map(TerminalAgentInfo::getDeviceId).collect(Collectors.toList()));
    }
}
