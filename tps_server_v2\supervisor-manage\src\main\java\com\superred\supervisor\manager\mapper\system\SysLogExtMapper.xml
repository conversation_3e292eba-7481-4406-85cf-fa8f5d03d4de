<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.superred.supervisor.manager.mapper.system.SysLogExtMapper">

    <resultMap type="com.superred.supervisor.common.entity.system.SysLog" id="PSysLogMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
        <result property="role" column="role" jdbcType="VARCHAR"/>
        <result property="operateModule" column="operate_module" jdbcType="VARCHAR"/>
        <result property="operateType" column="operate_type" jdbcType="VARCHAR"/>
        <result property="operateDate" column="operate_date" jdbcType="TIMESTAMP"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="behaviourType" column="behaviour_type" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="VARCHAR"/>
        <result property="hostIp" column="host_ip" jdbcType="VARCHAR"/>
        <result property="hostId" column="host_id" jdbcType="VARCHAR"/>
        <result property="moduleType" column="module_type" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifiedTime" column="modified_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="result" column="result" jdbcType="INTEGER"/>
        <result property="product" column="product" jdbcType="VARCHAR"/>
    </resultMap>


    <resultMap id="sysLogVoResultMap" type="com.superred.supervisor.manager.model.vo.system.log.SyslogResp">
        <id property="id" column="id"/>
        <result property="uuid" column="uuid"/>
        <result property="role" column="role"/>
        <result property="operateModule" column="operate_module"/>
        <result property="operateType" column="operate_type"/>
        <result property="description" column="description"/>
        <result property="behaviourType" column="behaviour_type"/>
        <result property="level" column="level"/>
        <result property="hostIp" column="host_ip"/>
        <result property="username" column="username"/>
        <result property="remarks" column="remarks"/>
        <result property="result" column="result"/>
        <result property="createTime" column="create_time"/>
        <!--<collection property="roleList" ofType="SysRole">
            <id column="roleId" property="id"/>
            <id column="roleName" property="name"/>
        </collection>-->
    </resultMap>

    <select id="querySysLogList"  resultMap="sysLogVoResultMap">
        SELECT
        l.id,
        l.role,
        l.operate_module,
        l.operate_type,
        l.description,
        l.behaviour_type,
        l.level,
        l.remarks,
        l.host_ip,
        l.username,
        l.result,
        l.create_time
        FROM p_sys_log l
        <where>
            l.is_deleted = '0'
            <if test="query.operateModule != null and query.operateModule != ''">
                and `l`.operate_module = #{query.operateModule}
            </if>
            <if test="query.operateType != null and query.operateType != ''">
                and `l`.operate_type like concat('%',#{query.operateType},'%')
            </if>
            <if test="query.behaviourType != null and query.behaviourType != ''">
                and `l`.behaviour_type = #{query.behaviourType}
            </if>
            <if test="query.level != null and query.level != ''">
                and `l`.level = #{query.level}
            </if>
            <if test="query.result != null and query.result != ''">
                and `l`.result = #{query.result}
            </if>
            <if test="query.remarks != null and query.remarks != ''">
                and `l`.remarks LIKE CONCAT('%',#{query.remarks},'%')
            </if>
            <if test="query.role == '1035'">
                and `l`.role not in ('1034','1035')
            </if>
            <if test="query.role == '1036'">
                and `l`.role in ('1034','1035')
            </if>
            <!--<if test='query.logType != "auth"'>
                <if test="query.notIn == 0 and query.roleIds != null and query.roleIds.size()>0">
                    and `l`.role in
                    <foreach collection="query.roleIds" item= "roleId" open="(" close=")" separator=",">
                        #{roleId}
                    </foreach>
                </if>
                <if test="query.notIn == 1 and query.roleIds != null and query.roleIds.size()>0">
                    and  `l`.role not in
                    <foreach collection="query.roleIds" item= "roleId" open="(" close=")" separator=",">
                        #{roleId}
                    </foreach>
                </if>
            </if>-->
            <if test="query.username != null and query.username != ''">
                and `l`.username LIKE CONCAT('%',#{query.username},'%')
            </if>
            <if test="query.hostIp != null and query.hostIp != ''">
                and `l`.host_ip LIKE CONCAT('%',#{query.hostIp},'%')
            </if>
            <if test="query.description != null and query.description != ''">
                and `l`.description LIKE CONCAT('%',#{query.description},'%')
            </if>
            <if test="query.beginTime != '' and query.beginTime != null  and  query.endTime != '' and query.endTime != null">
                and `l`.create_time BETWEEN  #{query.beginTime} AND #{query.endTime}
            </if>
            <if test="query.beginTime != '' and query.beginTime != null  and (query.endTime == '' or query.endTime == null) ">
                and `l`.create_time &gt;=  #{query.beginTime}
            </if>
            <if test="query.endTime != '' and query.endTime != null  and (query.beginTime == '' or query.beginTime == null) ">
                and `l`.create_time &lt;=  #{query.endTime}
            </if>
        </where>
        ORDER BY l.id DESC
    </select>

</mapper>

