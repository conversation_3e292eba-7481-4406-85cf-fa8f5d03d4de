package com.superred.supervisor.manager.model.vo.system.org;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 导如部门Excel信息实体
 *
 * @author: hailong.qu
 * @since: 2025/6/17 10:24
 */
@Data
public class ImportOrgVO {

    @ExcelProperty(value = "部门名称（必填项）", index = 0)
    @NotBlank(message = "部门名称为必填项")
    @Size(max = 20, message = "部门名称长度不能超过20")
    private String name;
    @ExcelProperty(value = "上级部门（必填项，根部门可不填）", index = 1)
    private String parentName;
    @ExcelProperty(value = "行政区域（必填项）", index = 2)
    @NotBlank(message = "行政区域为必填项")
    private String regionName;
    @ExcelProperty(value = "排序（非必填项）", index = 3)
    @Min(value = 1, message = "排序不能小于1")
    @Max(value = 999, message = "排序不能大于999")
    private Integer sort;
    @ExcelProperty(value = "描述（非必填项）", index = 4)
    @Size(max = 128, message = "描述长度不能超过128")
    private String desc;
}
