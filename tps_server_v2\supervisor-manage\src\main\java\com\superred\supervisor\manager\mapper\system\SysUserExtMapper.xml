<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.superred.supervisor.manager.mapper.system.SysUserExtMapper">

    <resultMap type="com.superred.supervisor.common.entity.system.SysUser" id="sysUserMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="INTEGER"/>
        <result property="lockTime" column="lock_time" jdbcType="TIMESTAMP"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="TIMESTAMP"/>
        <result property="tryCount" column="try_count" jdbcType="INTEGER"/>
        <result property="lockFlag" column="lock_flag" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifiedTime" column="modified_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="realName" column="real_name" jdbcType="VARCHAR"/>
        <result property="passUpdateTime" column="pass_update_time" jdbcType="TIMESTAMP"/>
        <result property="card" column="card" jdbcType="VARCHAR"/>
        <result property="isShow" column="is_show" jdbcType="INTEGER"/>
        <result property="secret" column="secret" jdbcType="INTEGER"/>
        <result property="enable" column="enable" jdbcType="INTEGER"/>
        <result property="firstLogin" column="first_login" jdbcType="INTEGER"/>
        <result property="isMaster" column="is_master" jdbcType="INTEGER"/>
    </resultMap>


    <!-- userVo结果集 -->
    <resultMap id="userRespMap" type="com.superred.supervisor.manager.model.vo.system.user.UserResp">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="real_name" property="realName"/>
        <result column="password" property="password"/>
        <result column="create_time" property="createTime"/>
        <result column="modified_time" property="updateTime"/>
        <result column="lock_flag" property="lockFlag"/>
        <result column="lock_time" property="lockTime"/>
        <result column="is_deleted" property="delFlag"/>
        <result column="is_master" property="isMaster"/>
        <result column="is_show" property="isShow"/>
        <result column="orgId" property="orgId"/>
        <result column="orgName" property="orgName"/>
        <result column="card" property="card"/>
        <result property="secret" column="secret"/>
        <result property="firstLogin" column="first_login"/>
        <result property="enable" column="enable"/>
        <result property="remarks" column="remarks"/>
        <collection property="roleList" ofType="com.superred.supervisor.common.entity.system.SysRole">
            <id column="roleId" property="id"/>
            <id column="roleName" property="name"/>
            <id column="code" property="code"/>
            <id column="isEdit" property="isEdit"/>
            <id column="description" property="description"/>
            <result column="rCreateTime" property="createTime"/>
            <result column="rModifiedTime" property="modifiedTime"/>
        </collection>
    </resultMap>

    <select id="getUserRespPage" resultMap="userRespMap">
        SELECT u.id,
        u.username,
        u.card,
        u.real_name,
        '' password,
        u.create_time,
        u.modified_time,
        u.is_deleted,
        u.is_show,
        u.lock_flag,
        u.lock_time,
        u.is_master,
        u.enable,
        u.secret,
        u.remarks,
        r.id roleId,
        r.name roleName,
        r.description description,
        r.code code,
        r.is_edit isEdit,
        r.create_time rCreateTime,
        r.modified_time rModifiedTime,
        d.NAME orgName,
        d.id orgId
        FROM p_sys_user u
        LEFT JOIN p_sys_user_role ur ON u.id = ur.user_id and ur.is_deleted = '0'
        LEFT JOIN p_sys_role r ON ur.role_id = r.id
        LEFT JOIN p_sys_org d ON u.org_id = d.id
        <where>
            u.is_deleted = '0'
            and
            u.is_show = '0'
            <if test="query.username != null and query.username != ''">
                and `u`.username LIKE CONCAT('%',#{query.username},'%')
            </if>

            <if test="query.realName != null and query.realName != ''">
                and `u`.real_name LIKE CONCAT('%',#{query.realName},'%')
            </if>

            <if test="query.card != null and query.card != ''">
                and `u`.card LIKE CONCAT('%',#{query.card},'%')
            </if>
            <if test="query.orgId != null and query.orgId != ''">
                and `u`.org_id = #{query.orgId}
            </if>

            <!--            <if test="query.role != null and query.role != ''">-->
            <!--                and  r.id = #{query.role}-->
            <!--            </if>-->
            <!--            <if test="query.orgIds != null  and query.orgIds.size()>0 ">-->
            <!--                and `u`.org_id in-->
            <!--                <foreach collection="query.orgIds" item= "org" open="(" close=")" separator=",">-->
            <!--                    #{org}-->
            <!--                </foreach>-->
            <!--            </if>-->
        </where>
        ORDER BY u.create_time DESC
    </select>


    <select id="getUserRespById" resultMap="userRespMap">

        SELECT u.id,
               u.username,
               u.password,
               u.create_time,
               u.modified_time,
               u.is_deleted,
               u.lock_flag,
               u.enable,
               u.secret,
               r.id            roleId,
               r.name          roleName,
               r.create_time   rCreateTime,
               r.modified_time rModifiedTime,
               d.NAME          orgName,
               d.id            orgId
        FROM p_sys_user u
                 LEFT JOIN p_sys_user_role ur ON ur.user_id = u.id
                 LEFT JOIN p_sys_role r ON r.id = ur.role_id
                 LEFT JOIN p_sys_org d ON d.id = u.org_id
        WHERE u.id = #{id}
    </select>

</mapper>

