package com.superred.supervisor.manager.model.dto.command;

import lombok.Data;

/**
 * 部分设备
 *
 * <AUTHOR>
 * @since 2023/10/10
 **/
@Data
public class PartDevice {

    /**
     * 自监管设备id
     */
    private String deviceId;

    /**
     * 父级设备id
     */
    private String parentId;

    /**
     * 完整的层级路径 -230312030001-230312010012
     */
    private String path;

    /**
     * 行政/行业区划
     */
    private String addressCode;


    /**
     * 每一台设备的第二层设备id
     */
    private String secondDeviceId;
}