package com.superred.supervisor.manager.model.vo.policy;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-26 15:13
 */
@Data
@Builder

public class PolicyOrgTreeResp {

    @Schema(description = "ID")
    protected int id;

    @Schema(description = "组织名称")
    private String name;

    @Schema(description = "父ID")
    protected int parentId;

    @Schema(description = "是否存在下级")
    private boolean subset;

    @Schema(description = "子节点")
    private List<PolicyOrgTreeResp> children;
}
