package com.superred.supervisor.manager.repository.system;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.supervisor.common.entity.system.SysConfig;
import com.superred.supervisor.common.mapper.system.SysConfigMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * 字典项表 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:20
 */
@Repository
public class SysConfigExtRepository extends ServiceImpl<SysConfigMapper, SysConfig> {

    public SysConfig getOneByName(String name) {
        return this.getOne(Wrappers.<SysConfig>lambdaQuery().eq(SysConfig::getName, name));
    }

    public void updateValueByName(String name, String value) {
        SysConfig sysConfig = this.getOneByName(name);
        if (sysConfig == null) {
            throw new BaseBusinessException("配置不存在:" + name);
        }
        SysConfig update = new SysConfig();
        update.setId(sysConfig.getId());
        update.setValue(value);
        update.setModifiedTime(LocalDateTime.now());
        this.updateById(update);
    }
}

