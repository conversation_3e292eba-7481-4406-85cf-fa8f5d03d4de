package com.superred.supervisor.manager.mapper.terminal;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import com.superred.supervisor.manager.model.vo.terminal.TerminalAuditPageReq;
import com.superred.supervisor.manager.model.vo.terminal.TerminalAuditPageResp;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalCountByStatusResp;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalStatusPageReq;
import com.superred.supervisor.manager.model.vo.terminal.manage.TerminalStatusPageResp;
import org.apache.ibatis.annotations.Param;

/**
 * 终端设备信息表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-17 17:55:16
 */
public interface TerminalAgentInfoExtMapper extends BaseMapper<TerminalAgentInfo> {


    IPage<TerminalAuditPageResp> pageAuditAgentInfo(Page<TerminalAuditPageResp> objectPage, @Param("req") TerminalAuditPageReq req);

    IPage<TerminalStatusPageResp> pageAgentStatus(Page<TerminalStatusPageResp> objectPage, @Param("req") TerminalStatusPageReq req);

    TerminalCountByStatusResp countByStatus(Integer intervalMinutes);

}

