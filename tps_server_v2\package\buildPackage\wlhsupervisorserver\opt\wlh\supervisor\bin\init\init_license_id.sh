#!/bin/bash

# 设置目标目录和文件
LICENSE_DIR="/etc/wlh/supervisor"
LICENSE_FILE="$LICENSE_DIR/license_id"

# 检查目录是否存在
if [ ! -d "$LICENSE_DIR" ]; then
    echo "目录不存在，正在创建: $LICENSE_DIR"
    mkdir -p "$LICENSE_DIR"
    if [ $? -ne 0 ]; then
        echo "无法创建目录，权限不足？"
        exit 1
    fi
fi

# 检查文件是否已存在
if [ -f "$LICENSE_FILE" ]; then
    echo "许可证文件已存在: $LICENSE_FILE"
    echo "内容如下:"
    cat "$LICENSE_FILE"
    exit 0
fi

# 生成 UUID 并写入文件
UUID=$(uuidgen | tr -d '-')
echo "$UUID" > "$LICENSE_FILE"

# 设置权限为只读
chmod 444 "$LICENSE_FILE"

echo "许可证已生成: $LICENSE_FILE"
echo "内容为: $UUID"