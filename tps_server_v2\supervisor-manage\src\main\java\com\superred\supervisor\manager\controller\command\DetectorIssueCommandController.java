package com.superred.supervisor.manager.controller.command;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandCertUpdateReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDeviceDetailReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDeviceDetailResp;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDeviceReportReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandDropDataReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandInnerPolicySwitchReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandInnerPolicyUpdateReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandModuleSwitchReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandPasswordResetReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandPolicyReportReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandRebootReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandShutdownReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandSoftwareUpdateReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsReq;
import com.superred.supervisor.manager.model.vo.command.DetectorCommandStatisticsResp;
import com.superred.supervisor.manager.model.vo.command.IssueCommandSyncTimeReq;
import com.superred.supervisor.manager.model.vo.command.IssueCommandVersionCheckReq;
import com.superred.supervisor.manager.service.command.IssueCommandService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "8.0 检测器指令下发模块")
@RestController
@AllArgsConstructor
@RequestMapping("/detector/issue_command/")
public class DetectorIssueCommandController {
    @Resource
    private IssueCommandService issueCommandService;

    @ApiOperationSupport(order = 1)
    @Operation(summary = "1.关机")
    @PostMapping("/shutdown")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发关机指令")
    public R<String> shutdown(@RequestBody DetectorCommandShutdownReq req) {

        issueCommandService.shutdown(req);
        return R.success();
    }

    @ApiOperationSupport(order = 2)
    @Operation(summary = "2.重启")
    @PostMapping("/reboot")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发重启指令")
    public R<String> reboot(@RequestBody DetectorCommandRebootReq req) {

        issueCommandService.reboot(req);
        return R.success();
    }

    @ApiOperationSupport(order = 3)
    @Operation(summary = "3.模块启停")
    @PostMapping("/module_switch")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发模块启停指令")
    public R<String> moduleSwitch(@RequestBody DetectorCommandModuleSwitchReq req) {
        issueCommandService.moduleSwitch(req);
        return R.success();
    }

    /**
     * 4.时间同步
     * <p>
     * 该接口用于同步设备的时间。
     *
     * @param req 包含时间同步指令的DTO对象
     * @return 返回操作结果
     */
    @ApiOperationSupport(order = 4)
    @Operation(summary = "4.时间同步")
    @PostMapping("/sync_time")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发时间同步指令")
    public R<String> syncTime(@RequestBody IssueCommandSyncTimeReq req) {

        issueCommandService.syncTime(req);
        return R.success();
    }

    @ApiOperationSupport(order = 5)
    @Operation(summary = "5.系统软件更新")
    @PostMapping("/software_update")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发系统软件更新指令")
    public R<String> softwareUpdate(@RequestBody DetectorCommandSoftwareUpdateReq req) {

        issueCommandService.softwareUpdate(req);
        return R.success();
    }

    @ApiOperationSupport(order = 6)
    @Operation(summary = "6.版本一致性检查")
    @PostMapping("/version_check")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发版本一致性检查指令")
    public R<String> versionCheck(@RequestBody IssueCommandVersionCheckReq req) {

        issueCommandService.versionCheck(req);
        return R.success();
    }

    @ApiOperationSupport(order = 7)
    @Operation(summary = "7.内置策略更新")
    @PostMapping("/inner_policy_update")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发内置策略更新指令")
    public R<String> innerPolicyUpdate(@RequestBody DetectorCommandInnerPolicyUpdateReq req) {

        issueCommandService.innerPolicyUpdate(req);
        return R.success();
    }

    @ApiOperationSupport(order = 8)
    @Operation(summary = "8.WEB管理用户密码重置")
    @PostMapping("/password_reset")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发WEB管理用户密码重置指令")
    public R<String> passwordReset(@RequestBody DetectorCommandPasswordResetReq req) {

        issueCommandService.passwordReset(req);
        return R.success();
    }

    @ApiOperationSupport(order = 9)
    @Operation(summary = "9.积压数据删除")
    @PostMapping("/drop_data")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发积压数据删除指令")
    public R<String> dropData(@RequestBody DetectorCommandDropDataReq req) {

        issueCommandService.dropData(req);
        return R.success();
    }

    @ApiOperationSupport(order = 10)
    @Operation(summary = "10.生效策略上报")
    @PostMapping("/report_policy")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发生效策略上报指令")
    public R<String> reportPolicy(@RequestBody DetectorCommandPolicyReportReq req) {

        issueCommandService.reportPolicy(req);
        return R.success();
    }

    @ApiOperationSupport(order = 11)
    @Operation(summary = "11.内置策略启停")
    @PostMapping("/inner_policy_switch")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发内置策略启停指令")
    public R<String> innerPolicySwitch(@RequestBody @Validated DetectorCommandInnerPolicySwitchReq req) {

        issueCommandService.innerPolicySwitch(req);
        return R.success();
    }

    @ApiOperationSupport(order = 12)
    @Operation(summary = "12.通信证书更新")
    @PostMapping("/cert_update")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发通信证书更新指令")
    public R<String> certUpdate(@RequestBody DetectorCommandCertUpdateReq req) {

        issueCommandService.certUpdate(req);
        return R.success();
    }


    @ApiOperationSupport(order = 13)
    @Operation(summary = "13.接入设备信息上报")
    @PostMapping("/device_report")
    @SysLogAnn(module = LogTypeConstants.DETECTOR_INSTRUCTION_ISSUANCE, operateType = OperateTypeConstants.INSTRUCTION_ISSUANCE, desc = "下发接入设备信息上报指令")
    public R<String> deviceReport(@RequestBody DetectorCommandDeviceReportReq req) {

        issueCommandService.deviceReport(req);
        return R.success();
    }


    @ApiOperationSupport(order = 14)
    @Operation(summary = "14.指令统计分页查询")
    @GetMapping("/statistics/page")
    public RPage<DetectorCommandStatisticsResp> statisticsPage(DetectorCommandStatisticsReq req) {

        IPage<DetectorCommandStatisticsResp> page = issueCommandService.statisticsPage(req);
        return new RPage<>(page);
    }

    @ApiOperationSupport(order = 15)
    @Operation(summary = "15 指令设备详情")
    @GetMapping("/device/detail/page")
    public RPage<DetectorCommandDeviceDetailResp> deviceDetailPage(DetectorCommandDeviceDetailReq req) {

        IPage<DetectorCommandDeviceDetailResp> page = issueCommandService.deviceDetailPage(req);
        return new RPage<>(page);
    }
}