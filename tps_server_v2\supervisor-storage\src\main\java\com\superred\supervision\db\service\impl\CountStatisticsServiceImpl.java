package com.superred.supervision.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervision.db.entity.CountStatistics;
import com.superred.supervision.db.mapper.CountStatisticsMapper;
import com.superred.supervision.db.service.CountStatisticsService;
import com.superred.supervision.db.vo.statistics.CountStatisticsVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Service
public class CountStatisticsServiceImpl extends ServiceImpl<CountStatisticsMapper, CountStatistics> implements CountStatisticsService {

    @Override
    public List<CountStatisticsVo> findByTime(String startTime, String endTime, Integer isReport) {

        return baseMapper.findByTime(startTime, endTime, isReport);
    }

    @Override
    public void deleteByTime(String startTime, String endTime, Integer isReport) {
        baseMapper.deleteByTime(startTime, endTime, isReport);
    }
}
