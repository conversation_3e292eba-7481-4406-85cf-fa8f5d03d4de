package com.superred.supervisor.common.entity.agent.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 终端状态 0 在线，1 审核失败，2 待审核，3 禁用 4 离线 5 注销 6已卸载
 *
 * <AUTHOR>
 * @since 2025/3/18 13:52
 */
@Getter
@AllArgsConstructor
public enum AgentStatus implements IEnum<Integer> {

    ONLINE(0, "在线"),
    AUDIT_FAIL(1, "审核失败"),
    AUDIT_WAIT(2, "待审核"),
    DISABLE(3, "禁用"),
    OFFLINE(4, "离线"),
    LOGOUT(5, "注销"),
    UNINSTALL(6, "已卸载");

    private final int value;
    private final String desc;

    /**
     * 审核状态0 审核通过 1审核不通过
     * @param registerStatus
     * @return
     */
    public static AgentStatus formAuditReq(String registerStatus) {
        if ("0".equals(registerStatus)) {
            return ONLINE;
        } else if ("1".equals(registerStatus)) {
            return AUDIT_FAIL;
        }
        throw new IllegalArgumentException("审核状态不合法");
    }

    public static AgentStatus parseOnlineStatus(LocalDateTime heartbeatTime) {
        if (heartbeatTime == null) {
            return OFFLINE;
        }
        // 如果心跳时间超过5分钟，则认为离线: 现在1.35，心跳时间1.28 设置为离线
        if (LocalDateTime.now().minusMinutes(3).isAfter(heartbeatTime)) {
            return OFFLINE;
        }

        return ONLINE;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
