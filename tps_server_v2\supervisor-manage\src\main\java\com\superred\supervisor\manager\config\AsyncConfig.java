package com.superred.supervisor.manager.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 异步调用线程池配置
 *
 * <AUTHOR>
 */
@Configuration
public class AsyncConfig {
    private static final int MAX_POOL_SIZE = 10;
    private static final int CORE_POOL_SIZE = 2;

    private static final int DISPATCHER_MAX_POOL_SIZE = 2;
    private static final int DISPATCHER_CORE_POOL_SIZE = 1;

    /**
     *  检查任务的线程池
     */
    private static final int MAX_POOL_SIZE_COMPUTER_CHECK = 10;
    private static final int CORE_POOL_SIZE_COMPUTER_CHECK = 5;


    @Bean("commonExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setMaxPoolSize(MAX_POOL_SIZE);
        taskExecutor.setCorePoolSize(CORE_POOL_SIZE);
        taskExecutor.setThreadNamePrefix("COMMON-");
        taskExecutor.initialize();
        return taskExecutor;
    }


    @Bean("taskDispatcherExecutor")
    public ThreadPoolTaskExecutor taskDispatcherExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setMaxPoolSize(DISPATCHER_MAX_POOL_SIZE);
        taskExecutor.setCorePoolSize(DISPATCHER_CORE_POOL_SIZE);
        taskExecutor.setThreadNamePrefix("TASK-DISPATCHER-");
        taskExecutor.initialize();
        return taskExecutor;
    }


    @Bean("taskExecutor")
    public ThreadPoolTaskExecutor computerCheckExecutor() {
        ThreadPoolTaskExecutor computerCheckExecutor = new ThreadPoolTaskExecutor();
        computerCheckExecutor.setMaxPoolSize(MAX_POOL_SIZE_COMPUTER_CHECK);
        computerCheckExecutor.setCorePoolSize(CORE_POOL_SIZE_COMPUTER_CHECK);
        computerCheckExecutor.setThreadNamePrefix("TASK-CHECKER-");
        computerCheckExecutor.initialize();
        return computerCheckExecutor;
    }

    @Bean("autoCheckDeviceExecutor")
    public ThreadPoolTaskExecutor autoCheckDeviceExecutor() {
        ThreadPoolTaskExecutor computerCheckExecutor = new ThreadPoolTaskExecutor();
        computerCheckExecutor.setMaxPoolSize(MAX_POOL_SIZE);
        computerCheckExecutor.setCorePoolSize(CORE_POOL_SIZE);
        computerCheckExecutor.setThreadNamePrefix("TASK-AUTO-CHECKE-DEVICE");
        computerCheckExecutor.initialize();
        return computerCheckExecutor;
    }
}
