package com.superred.supervisor.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 系统授权信息表 实体
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:16
 */
@Data
@TableName("p_sys_authorization_info")
public class SysAuthorizationInfo {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 授权类型：0试用授权 1正式授权
     */
    @TableField("authority_type")
    private Integer authorityType;

    /**
     * 系统授权有效期：-1，永久有效
     */
    @TableField("auth_valid_days")
    private Long authValidDays;

    /**
     * 生效时间
     */
    @TableField("valid_start_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate validStartDate;

    /**
     * 失效时间
     */
    @TableField("valid_end_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate validEndDate;

    /**
     * 终端组件授权数
     */
    @TableField("agent_auth_count")
    private Integer agentAuthCount;

    /**
     * 下级已分配点数
     */
    @TableField("agent_sub_issued_count")
    @Deprecated
    private Integer agentSubIssuedCount;

    /**
     * 已用授权点数
     */
    @TableField("agent_used_count")
    @Deprecated
    private Integer agentUsedCount;

    /**
     * 设备标号
     */
    @TableField("sys_number")
    private String sysNumber;

    /**
     * 授权文件路径
     */
    @TableField("lic_path")
    private String licPath;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modified_time")
    private LocalDateTime modifiedTime;

    /**
     * md5
     */
    @TableField("md5")
    private String md5;

    /**
     * 签名
     */
    @TableField("signature")
    private String signature;

    /**
     * 厂商名称
     */
    @TableField("vendor")
    private String vendor;


    public String getSignContent() {
        return this.getVendor()
                + " " + this.getSysNumber()
                + " " + this.getAuthorityType()
                + " " + this.getAuthValidDays()
                + " " + this.getValidStartDate()
                + " " + this.getValidEndDate()
                + " " + this.getAgentAuthCount();
    }


}

