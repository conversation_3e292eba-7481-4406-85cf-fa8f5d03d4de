package com.superred.supervision.base.vo.data;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

/**
 * AgentCmdRepVO.
 * 指令响应.
 * <AUTHOR>
 * @since 2023-08-28 18:04
 * @version 1.0
 **/
@Data
@JsonInclude(NON_NULL)
public class AgentCmdRepVO {


    /**
     * 上报时间.
     **/
    private String time;

    /**
     * 指令类型
     **/
    private String type;

    /**
     * 指令名称
     **/
    private String cmd;

    /**
     * 指令ID.
     **/
    private String cmd_id;

    /**
     * 执行结果.0:成功；1：失败
     **/
    private int result;

    /**
     * 执行结果描述.
     **/
    private String message;

    /**
     * 根据不同的指令类
     * 型，定制不同的详
     * 情内容.
     **/
    private List<String> detail;

    /**
     * 设备版本
     */
    private String softVersion;

}
