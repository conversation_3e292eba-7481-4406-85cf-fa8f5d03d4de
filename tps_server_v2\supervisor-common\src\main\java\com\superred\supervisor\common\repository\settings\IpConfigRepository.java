package com.superred.supervisor.common.repository.settings;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.settings.IpConfig;
import com.superred.supervisor.common.mapper.settings.IpConfigMapper;
import org.springframework.stereotype.Repository;

/**
 * 公网ip出口配置(SysIpConfig) Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-26 17:22:19
 */
@Repository
public class IpConfigRepository extends ServiceImpl<IpConfigMapper, IpConfig> {

}

