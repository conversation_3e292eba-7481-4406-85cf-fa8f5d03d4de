package com.superred.supervisor.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.superred.supervisor.common.entity.system.enums.ConfigCategoryEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字典项表 实体
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:20
 */
@Data
@TableName("p_sys_config")
public class SysConfig {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型
     */
    @TableField("name")
    private String name;

    /**
     * 标签名
     */
    @TableField("label")
    private String label;

    /**
     * 数据值
     */
    @TableField("value")
    private String value;

    /**
     * 描述
     */
    @TableField("description")
    private String description;


    /**
     * 分类 sys_security_policy 系统配置
     */
    @TableField("category")
    private ConfigCategoryEnum category;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modified_time")
    private LocalDateTime modifiedTime;

}

