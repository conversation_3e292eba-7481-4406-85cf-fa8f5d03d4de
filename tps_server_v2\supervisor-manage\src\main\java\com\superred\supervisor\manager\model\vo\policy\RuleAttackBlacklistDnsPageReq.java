package com.superred.supervisor.manager.model.vo.policy;

import com.superred.common.core.model.PageReqDTO;
import com.superred.supervisor.manager.common.annotation.BlankOrPattern;
import com.superred.supervisor.manager.common.annotation.ByteSize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025-03-12 17:08
 */
@Data

public class RuleAttackBlacklistDnsPageReq extends PageReqDTO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "策略下发版本")
    private String version;

    @Schema(description = "策略下发ID")
    private String policyId;

    @Schema(description = "策略下发设备类型 05 终端；01 检测器")
    private String issueDeviceType;

    @Schema(description = "策略ID")
    private String ruleId;

    @Schema(description = "域名信息")
    @ByteSize(max = 64, message = "域名信息长度不能超过64个字节")
    private String dns;

    @Schema(description = "策略类型  0表示文本表达式; 1表示正则表达式。")
    private String ruleType;

    @Schema(description = "匹配类型 当策略类型rule_type为0时有效。  0表示子串匹配， 1表示右匹配， 2表示左匹配， 3表示完全匹配。 匹配不区分大小写。")
    private String matchType;

    @Schema(description = "策略名称")
    @ByteSize(max = 128, message = "策略名称长度不能超过128个字节")
    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]+$", message = "策略名称格式错误,只允许输入中文、英文、数字")
    private String ruleName;

    @Schema(description = "攻击分类 1. 窃密木马 2. 远控木马 3. 电脑病毒 4. 僵尸网络 5. 网络蠕虫 6. 间谍软件 7. 挖矿木马 8. 黑客工具 9. 勒索软件 10. 恶意文档 11. 后门程序 99. 其它")
    private String attackClass;

    @Schema(description = "攻击组织")
    @ByteSize(max = 128, message = "攻击组织长度不能超过128个字节")
    private String attackGroup;

    @Schema(description = "攻击阶段枚举分类 1 侦查扫描 2 攻击渗透 3 样本投递 4 持续控制 5 横向移动 6 数据窃取 99 其它")
    private String attackStage;

    @Schema(description = "攻击设施类型ID 1 密码爆破 2 漏洞扫描 3 样本分发 4 恶意发邮 5 钓鱼网站 6 信息搜集 7 数据窃取 8 命令控制 99 其它")
    private String facilityType;

    @Schema(description = "描述信息")
    private String desc;

    @Schema(description = "告警级别，0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）。")
    private String risk;

    @Schema(description = "是否共享状态，0是，1否")
    private String isShare;

    @Schema(description = "策略应用状态，0未应用，1已应用")
    private String status;

    @Schema(description = "策略来源 1 本级 2上级")
    private String ruleSource;

    @Schema(description = "平台级别")
    private String level;

    @Schema(description = "策略更新时间")
    private String updateTime;

    @Schema(description = "策略创建时间")
    private String createTime;

    @Schema(description = "扩展字段1")
    private Long ext1;

    @Schema(description = "扩展字段2")
    private String ext2;

    @Schema(description = "扩展字段3")
    private String ext3;

    @Schema(description = "开始时间")
    private String startDate;

    @Schema(description = "结束时间")
    private String endDate;

}
