package com.superred.supervisor.manager.model.vo.system.user;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 *  TODO
 * @since 2025年03月18日
 */
@Data
public class UserRoleReq {

    /**
     * 主键
     */
    @NotNull(message = "主键id 不可为空")
    @Schema(description = "主键")
    private Integer id;

    /**
     * 角色id
     */
    @NotNull(message = "角色id 不可为空")
    @Schema(description = "角色id")
    private Integer roleId;

    /**
     * 密级
     * 1：一般涉密
     * 2：重要涉密
     * 3：核心涉密
     */
    @Schema(description = "密级 1：一般涉密 2：重要涉密 3：核心涉密")
    @Min(value = 1, message = "密级 格式错误")
    @Max(value = 3, message = "密级 格式错误")
    private Integer secret;

    @Schema(description = "是否是主账号：0否，1是")
    private Integer isMaster;
}
