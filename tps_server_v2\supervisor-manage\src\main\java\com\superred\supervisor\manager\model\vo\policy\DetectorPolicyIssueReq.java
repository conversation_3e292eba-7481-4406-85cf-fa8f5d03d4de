package com.superred.supervisor.manager.model.vo.policy;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 15:25
 */
@Data

public class DetectorPolicyIssueReq {

    @Schema(description = "策略id")
    private Long policyId;

    @Schema(description = "模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单")
    private String module;

    @Schema(description = "设备列表")
    private List<String> deviceIds;

}
