package com.superred.supervisor.manager.model.vo.policy.terminal;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端策略下发详情响应
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Data
@Schema(description = "终端策略下发详情响应")
public class TerminalPolicyIssueDetailResp {

    @Schema(description = "下发名称")
    private String issueName;

    @Schema(description = "下发类型")
    private String issueType;

    @Schema(description = "策略类型")
    private String module;

    @Schema(description = "策略个数")
    private Integer policyCount;

    @Schema(description = "下发范围")
    private Integer deviceCount;

    @Schema(description = "下发时间")
    private LocalDateTime issueTime;

    @Schema(description = "下发描述")
    private String issueDesc;

    @Schema(description = "规则详情列表")
    private List<PolicyRuleDetail> ruleDetails;

    /**
     * 策略规则详情
     */
    @Data
    @Schema(description = "策略规则详情")
    public static class PolicyRuleDetail {

        @Schema(description = "策略ID（规则ID）")
        private Long ruleId;

        @Schema(description = "策略版本")
        private String version;

        @Schema(description = "策略名称")
        private String ruleName;

        @Schema(description = "下发成功数量")
        private Long successCount;

        @Schema(description = "下发失败数量")
        private Long failedCount;
    }
    private String successItems;

    @Schema(description = "未成功执行的规则对象列表")
    private String failedItems;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
