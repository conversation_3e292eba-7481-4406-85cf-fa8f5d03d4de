package com.superred.supervisor.manager.repository.policy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.manager.mapper.policy.DetectorPolicyRuleExtMapper;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-13 15:16
 */
@Slf4j
@Repository
@AllArgsConstructor
public class DetectorPolicyRuleExtRepository extends ServiceImpl<DetectorPolicyRuleExtMapper, DetectorPolicyRule> {

    public List<RulePolicyApplyResp> selectPolicyApply(Long ruleId) {
        return baseMapper.selectPolicyApply(ruleId);
    }
}
