package com.superred.supervisor.gateway.service.cache;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.superred.supervision.base.constant.Constants;
import com.superred.supervisor.common.cache.ComCacheKeys;
import com.superred.supervisor.common.cache.CommonCacheService;
import com.superred.supervisor.common.constant.CommonConstant;
import com.superred.supervisor.common.entity.setting.LocalDeviceInfo;
import com.superred.supervisor.common.entity.settings.IpWhitelist;
import com.superred.supervisor.common.entity.settings.enums.IpWhiteListType;
import com.superred.supervisor.common.entity.system.SysConfig;
import com.superred.supervisor.common.entity.system.enums.ConfigCategoryEnum;
import com.superred.supervisor.common.repository.setting.LocalDeviceInfoRepository;
import com.superred.supervisor.common.repository.settings.IpWhitelistRepository;
import com.superred.supervisor.common.repository.system.SysConfigRepository;
import org.redisson.api.RMap;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Date;
import java.util.List;

/**
 * 终端缓存
 *
 * <AUTHOR>
 * @since 2025/5/19 14:20
 */
@Service
public class GatewayCacheService {


    private static final String AGENT_ID = "gateway:client_id";

    private static final String CACHE_AGENT_ID_KEY = "gateway:client_id_cache:";

    private static final String APP_AGENT_ID = "gateway:app_agent_id";

    private static final String CACHE_APP_AGENT_ID_KEY = "gateway:app_agent_id_cache:";

    private static final String IP_RATE_LIMITER_KEY = "limiter:ip-white:";

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CommonCacheService commonCacheService;
    @Resource
    private SysConfigRepository sysConfigRepository;
    @Resource
    private LocalDeviceInfoRepository localDeviceInfoRepository;
    @Resource
    private IpWhitelistRepository ipWhitelistRepository;


    public String generateAgentId() {
        RMap<String, Long> map = redissonClient.getMap(AGENT_ID);

        final String date = DateUtil.format(new Date(), "yyMM");
        // Redisson 没有直接的 hash 自增方法，但可以这样做（线程安全）：
        Long count = map.addAndGet(date, 1L);

        return date + "12" + Constants.DEVICE_TYPE_TERMINAL + StrUtil.fillBefore(count.toString(), '0', 4);
    }


    public String cacheComputerClientId(String md5Str) {
        return commonCacheService.getOrLoad(CACHE_AGENT_ID_KEY + md5Str,
                () -> sysConfigRepository.getOne(Wrappers.<SysConfig>lambdaQuery()
                                .eq(SysConfig::getName, md5Str))
                        .getValue(),
                Duration.ofHours(1));
    }

    public void cacheComputerClientId(String md5Str, String deviceId) {

        SysConfig one = sysConfigRepository.getOne(Wrappers.<SysConfig>lambdaQuery()
                .eq(SysConfig::getName, md5Str));
        if (one != null) {
            one.setValue(deviceId);
            sysConfigRepository.updateById(one);
        } else {
            SysConfig sysConfig = new SysConfig();
            sysConfig.setName(md5Str);
            sysConfig.setValue(deviceId);
            sysConfig.setCategory(ConfigCategoryEnum.AGENT_DEVICE_ID);
            sysConfig.setDescription("终端组件设备ID");
            sysConfigRepository.save(sysConfig);
        }
        commonCacheService.set(CACHE_AGENT_ID_KEY + md5Str, deviceId, Duration.ofHours(1));
    }

    /* 应用系统自监管组件   */
    //------------------------

    /**
     * 生成检测器ID
     *
     * @return 生成的检测器ID
     */
    public String generateAppAgentId() {
        RMap<String, Long> map = redissonClient.getMap(APP_AGENT_ID);

        final String date = DateUtil.format(new Date(), "yyMM");
        // Redisson 没有直接的 hash 自增方法，但可以这样做（线程安全）：
        Long count = map.addAndGet(date, 1L);

        return date + "12" + CommonConstant.DEVICE_TYPE_DETECTOR + StrUtil.fillBefore(count.toString(), '0', 4);
    }


    public String cacheAppAgentId(String md5Str) {

        return commonCacheService.getOrLoad(CACHE_APP_AGENT_ID_KEY + md5Str,
                () -> sysConfigRepository.getOne(Wrappers.<SysConfig>lambdaQuery()
                                .eq(SysConfig::getName, md5Str))
                        .getValue(),
                Duration.ofHours(1));
    }

    public void cacheAppAgentId(String md5Str, String deviceId) {

        SysConfig one = sysConfigRepository.getOne(Wrappers.<SysConfig>lambdaQuery()
                .eq(SysConfig::getName, md5Str));
        if (one != null) {
            one.setValue(deviceId);
            sysConfigRepository.updateById(one);
        } else {
            SysConfig sysConfig = new SysConfig();
            sysConfig.setName(md5Str);
            sysConfig.setValue(deviceId);
            sysConfig.setCategory(ConfigCategoryEnum.DETECTOR_DEVICE_ID);
            sysConfig.setDescription("终端组件设备ID");
            sysConfigRepository.save(sysConfig);
        }
        commonCacheService.set(CACHE_APP_AGENT_ID_KEY + md5Str, deviceId, Duration.ofHours(1));

    }

    public String cacheLocalDeviceId() {

        return commonCacheService.getOrLoad(ComCacheKeys.LOCAL_DEVICE_ID,
                        () -> localDeviceInfoRepository.getOne(Wrappers.<LocalDeviceInfo>lambdaQuery().last("limit 1")),
                        Duration.ofHours(1))
                .getDeviceId();
    }

    public List<IpWhitelist> getIpWhiteListByType(IpWhiteListType type) {

        return commonCacheService.getOrLoad(ComCacheKeys.IP_WHITE_PREDIX + type,
                () -> ipWhitelistRepository.getIpWhitelistByType(type),
                Duration.ofHours(4));
    }

    public RRateLimiter getIpRateLimiter(String clientIp) {
        return redissonClient.getRateLimiter(IP_RATE_LIMITER_KEY + clientIp);
    }
}
