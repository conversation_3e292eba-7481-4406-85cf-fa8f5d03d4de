package com.superred.supervisor.gateway.controller.app;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.aop.IgnoreLogin;
import com.superred.supervisor.gateway.model.app.register.AppAgentIdReq;
import com.superred.supervisor.gateway.model.app.register.AppAgentIdResp;
import com.superred.supervisor.gateway.model.app.register.AppRegStatusResp;
import com.superred.supervisor.gateway.model.app.register.AppRegisterReq;
import com.superred.supervisor.gateway.service.app.AppRegisterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 应用系统检测组件注册、认证接口
 *
 * <AUTHOR>
 * @since 2025/5/16 16:03
 */
@Tag(name = "D.3.3 注册、认证接口(2025-05)")
@RestController
@Slf4j
@RequestMapping("/A2/")
public class AppRegisterController {

    @Resource
    private AppRegisterService appRegisterService;

    /**
     * D.3.3.1 组件唯一编码查询接口
     *
     **/
    @PostMapping("/register/get_appsystem_client_id")
    @Operation(summary = "D.3.3.1 组件唯一编码查询接口")
    @ApiOperationSupport(order = 1)
    @IgnoreLogin
    public ApiResponse<AppAgentIdResp> getAppClientIdRequest(@RequestBody AppAgentIdReq req) {
        AppAgentIdResp resp = appRegisterService.getAppClientIdRequest(req);
        return ApiResponse.success(resp);
    }


    /**
     * D.3.3.2 组件注册接口
     **/
    @PostMapping("/register/reg_request")
    @Operation(summary = "D.3.3.2 组件注册接口")
    @ApiOperationSupport(order = 2)
    @IgnoreLogin
    public ApiResponse<String> regRequest(@RequestBody AppRegisterReq req) {

        appRegisterService.regRequest(req);
        return ApiResponse.success();
    }

    /**
     * D.3.3.3 组件重新注册接口
     **/
    @PostMapping("/register/re_reg_request")
    @Operation(summary = "D.3.3.3 组件重新注册接口")
    @ApiOperationSupport(order = 3)
    @IgnoreLogin
    public ApiResponse<String> reRegRequest(@RequestBody AppRegisterReq req) {

        appRegisterService.reRegRequest(req);
        return ApiResponse.success();
    }

    /**
     * D.3.3.4 认证接口.
     *
     **/
    @PostMapping("/auth/login")
    @Operation(summary = "D.3.3.4 认证接口")
    @ApiOperationSupport(order = 4)
    @IgnoreLogin
    public ApiResponse<String> authLoginRequest(@RequestBody AppRegisterReq req) {

        appRegisterService.authLogin(req);
        return ApiResponse.success();
    }

    /**
     * D.3.3.5 注册状态查询接口
     **/
    @PostMapping("/register/regstatus")
    @Operation(summary = "D.3.3.5 注册状态查询接口")
    @ApiOperationSupport(order = 5)
    @IgnoreLogin
    public ApiResponse<String> registerStatus() {

        AppRegStatusResp resp = appRegisterService.registerStatus();

        return ApiResponse.build(resp.getMessage(), resp.getStatus(), resp.getMessage());
    }

}
