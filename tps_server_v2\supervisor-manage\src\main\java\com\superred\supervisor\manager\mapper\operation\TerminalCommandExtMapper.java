package com.superred.supervisor.manager.mapper.operation;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.supervisor.common.entity.operation.terminal.TerminalCommand;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageResp;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdRecordPageResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 终端指令表表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-24 14:22:54
 */
public interface TerminalCommandExtMapper extends BaseMapper<TerminalCommand> {

    List<TerminalCmdRecordPageResp> groupCountByCmdIds(@Param("cmdIds") List<String> cmdIds);

    IPage<TerminalCmdExecPageResp> terminalExecPage(Page<TerminalCmdExecPageResp> objectPage, @Param("req")TerminalCmdExecPageReq req);
}

