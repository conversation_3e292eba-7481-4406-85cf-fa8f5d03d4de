package com.superred.supervisor.gateway.kafka.config;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.DefaultKafkaProducerFactoryCustomizer;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;

import javax.annotation.Resource;
import java.util.Map;

/**
 * kafka 生产者配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableKafka
@Slf4j
public class KafkaProducerConfig {


    @Value("${spring.kafka.producer.properties.sasl.jaas.config:}")
    private String producerSaslJaasConfig;
    @Value("${spring.kafka.producer.properties.security.protocol:}")
    private String producerSecurityProtocol;
    @Value("${spring.kafka.producer.properties.sasl.mechanism:}")
    private String producerSaslMechanism;


    @Resource
    private KafkaProperties properties;

    @Bean("commonKafkaTemplate")
    public KafkaTemplate<String, String> commonKafkaTemplate(DefaultKafkaProducerFactory<String, String> kafkaProducerFactory) {
        return new KafkaTemplate<>(kafkaProducerFactory);
    }

    @Bean
    public DefaultKafkaProducerFactory<String, String> kafkaProducerFactory(ObjectProvider<DefaultKafkaProducerFactoryCustomizer> customizers) {

        log.warn("==============::: kafkaProducerFactory init,  bootstrapAddress: {}", properties.getBootstrapServers());

        Map<String, Object> producerProperties = this.properties.buildProducerProperties();
        if (StrUtil.isNotBlank(producerSecurityProtocol)) {
            producerProperties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, producerSecurityProtocol);
            producerProperties.put(SaslConfigs.SASL_MECHANISM, producerSaslMechanism);
            producerProperties.put(SaslConfigs.SASL_JAAS_CONFIG, producerSaslJaasConfig);
        }
        producerProperties.put(ProducerConfig.CLIENT_ID_CONFIG, "DISPOSE");
        producerProperties.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, 1024 * 1024 * 20);
        DefaultKafkaProducerFactory<String, String> factory = new DefaultKafkaProducerFactory<>(producerProperties);
        String transactionIdPrefix = this.properties.getProducer().getTransactionIdPrefix();
        if (transactionIdPrefix != null) {
            factory.setTransactionIdPrefix(transactionIdPrefix);
        }
        customizers.orderedStream().forEach((customizer) -> customizer.customize(factory));
        return factory;
    }


}
