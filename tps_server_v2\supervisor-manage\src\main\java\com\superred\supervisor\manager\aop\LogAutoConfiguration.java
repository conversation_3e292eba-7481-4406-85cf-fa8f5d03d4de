package com.superred.supervisor.manager.aop;

import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 * @version 1.0
 * @Classname LogAutoConfiguration
 * @Description 日志自动配置
 * @since 2020-02-19 9:31
 */
@EnableAsync
@Configuration
@AllArgsConstructor
@ConditionalOnWebApplication
public class LogAutoConfiguration {


    @Bean
    public SysLogAspect sysLogAspect() {
        return new SysLogAspect();
    }
}
