package com.superred.supervisor.manager.model.dto.policy.config;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleDeviceInfoCollection;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-04-18 16:41
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceInfoCollectionConfigDTO {

    private Long ruleId;

    private List<Integer> ruleContent;

    private String ruleDesc;

    public static DeviceInfoCollectionConfigDTO getPolicyConfig(RuleDeviceInfoCollection ruleDeviceInfoCollection) {
        if (ruleDeviceInfoCollection == null) {
            return null;
        }
        return DeviceInfoCollectionConfigDTO.builder()
                .ruleId(Long.parseLong(ruleDeviceInfoCollection.getRuleId()))
                .ruleContent(StrUtil.isBlank(ruleDeviceInfoCollection.getRuleContent()) ? null : PolicyUtils.strToIntList(ruleDeviceInfoCollection.getRuleContent()))
                .ruleDesc(PolicyUtils.handleStrNull(ruleDeviceInfoCollection.getRuleDesc()))
                .build();
    }
}
