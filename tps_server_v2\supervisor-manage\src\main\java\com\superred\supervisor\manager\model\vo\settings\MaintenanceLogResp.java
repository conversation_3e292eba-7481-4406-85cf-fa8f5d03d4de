package com.superred.supervisor.manager.model.vo.settings;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 *  获取系统维护记录
 * @since 2025年03月14日
 */
@Data
public class MaintenanceLogResp {

    private Integer id;

    @Schema(description = "操作类型")
    private String operateType;

    @Schema(description = "操作结果")
    private String operateResult;

    @Schema(description = "错误信息")
    private String errorInfo;

    @Schema(description = "操作时间")
    private Date operateTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "操作起始时间")
    private Date operateStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "操作截止时间")
    private Date operateEndTime;

}
