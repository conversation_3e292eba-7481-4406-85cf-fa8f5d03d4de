package com.superred.supervisor.manager.controller.policy;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.constant.DeviceInfoCollectionEnum;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyEnumResp;
import com.superred.supervisor.manager.model.vo.policy.RuleDeviceInfoCollectionPageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleDeviceInfoCollectionReq;
import com.superred.supervisor.manager.model.vo.policy.RuleDeviceInfoCollectionResp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.service.policy.RuleDeviceInfoCollectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025-04-18 14:51
 */
@Tag(name = "4.13. 设备信息采集策略")
@RestController
@RequestMapping("/rule/device_info_collection")
@Slf4j
@Validated
public class RuleDeviceInfoCollectionController {

    @Resource
    private RuleDeviceInfoCollectionService ruleDeviceInfoCollectionService;

    @Operation(summary = "1 分页")
    @GetMapping("/page")
    public RPage<RuleDeviceInfoCollectionResp> page(RuleDeviceInfoCollectionPageReq ruleDeviceInfoCollectionPageReq) {
        IPage<RuleDeviceInfoCollectionResp> page = this.ruleDeviceInfoCollectionService.page(ruleDeviceInfoCollectionPageReq);
        return new RPage<>(page);
    }

    @Operation(summary = "2 查询详情")
    @GetMapping("/{ruleId}")
    public R<RuleDeviceInfoCollectionResp> getById(@PathVariable("ruleId") Long ruleId) {
        RuleDeviceInfoCollectionResp resp = this.ruleDeviceInfoCollectionService.getById(ruleId);
        return R.success(resp);
    }

    @Operation(summary = "3 新增")
    @SysLogAnn(module = LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY, operateType = OperateTypeConstants.ADD, desc = OperateTypeConstants.ADD + LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY)
    @PostMapping("/save")
    public R save(@Valid @RequestBody RuleDeviceInfoCollectionReq ruleDeviceInfoCollectionReq) {
        this.ruleDeviceInfoCollectionService.save(ruleDeviceInfoCollectionReq);
        return R.success();
    }

    @Operation(summary = "4 编辑")
    @SysLogAnn(module = LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY, operateType = OperateTypeConstants.MODIFY, desc = OperateTypeConstants.MODIFY + LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY)
    @PostMapping("/edit")
    public R edit(@Valid @RequestBody RuleDeviceInfoCollectionReq ruleDeviceInfoCollectionReq) {
        this.ruleDeviceInfoCollectionService.edit(ruleDeviceInfoCollectionReq);
        return R.success();
    }

    @Operation(summary = "5 删除")
    @PostMapping("/del")
    @SysLogAnn(module = LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY, operateType = OperateTypeConstants.DELETE, desc = OperateTypeConstants.DELETE + LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY)
    public R del(@Valid @RequestBody PolicyBatchIdsReq batchIdsReq) {
        this.ruleDeviceInfoCollectionService.del(batchIdsReq);
        return R.success();
    }

    @Operation(summary = "6 查看策略应用策略情况")
    @PostMapping("/policy/{ruleId}")
    public R<List<RulePolicyApplyResp>> policyApply(@PathVariable("ruleId") Long ruleId) {
        List<RulePolicyApplyResp> result = this.ruleDeviceInfoCollectionService.policyApply(ruleId);
        return R.success(result);
    }

    @Operation(summary = "7 导出")
    @PostMapping("/export")
    @SysLogAnn(module = LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY, operateType = OperateTypeConstants.EXPORT, desc = OperateTypeConstants.EXPORT + LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY)
    public void export(HttpServletResponse response, @RequestBody RuleDeviceInfoCollectionPageReq ruleDeviceInfoCollectionPageReq) throws IOException {

        // do something
    }

    @Operation(summary = "8 导入")
    @PostMapping("/import")
    @SysLogAnn(module = LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY, operateType = OperateTypeConstants.IMPORT, desc = OperateTypeConstants.IMPORT + LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY)
    public R importFile(@RequestParam("file") MultipartFile file) throws IOException {
        return null;
    }

    @Operation(summary = "9 下载模版")
    @PostMapping("/download")
    @SysLogAnn(module = LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY, operateType = OperateTypeConstants.DOWNLOAD, desc = LogTypeConstants.EQUIPMENT_INFORMATION_COLLECTION_STRATEGY + "下载模版")
    public void download(HttpServletResponse response) throws IOException {
        //        String filePath = "template/木马活动检测策略模版.xlsx";
        //        String fileName = "木马活动检测策略模版.xlsx";
        //        ClassPathResource classpathResource = new ClassPathResource(filePath);
        //        InputStream inputStream = classpathResource.getInputStream();
        //        FileUtils.downloadFileExcel(response, inputStream, fileName);

    }

    @Operation(summary = "10 获取策略内容下拉")
    @GetMapping("/rule_content/list")
    public R<List<PolicyEnumResp>> list() {
        List<DeviceInfoCollectionEnum> enumList = Arrays.asList(DeviceInfoCollectionEnum.values());
        List<PolicyEnumResp> list = enumList.stream().map(item -> PolicyEnumResp.builder()
                .key(item.getKey().toString())
                .value(item.getValue())
                .build()).collect(Collectors.toList());
        return R.success(list);
    }
}
