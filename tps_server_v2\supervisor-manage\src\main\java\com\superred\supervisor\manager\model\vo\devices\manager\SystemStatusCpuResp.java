package com.superred.supervisor.manager.model.vo.devices.manager;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 系统状态中央处理器
 *
 * <AUTHOR>
 * @since 2025/03/14
 */
@Data

public class SystemStatusCpuResp {

    /**
     * CPU ID
     */
    @Schema(description = "CPU ID")
    @JsonProperty("physical_id")
    private Integer physicalId;

    /**
     * CPU 使用率百分比   0-100
     */
    @Schema(description = "CPU 使用率百分比   0-100")
    @JsonProperty("cpu_usage")
    private Integer cpuUsage;


}
