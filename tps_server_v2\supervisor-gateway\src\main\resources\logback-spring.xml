<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">

    <!--spring 2.4+ 完美支持-->
    <springProperty scope="context" name="LOG_HOME" source="logging.file.path"/>

    <!-- 错误日志单独记录方便排查问题-->
    <appender name="ERROR_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/error.log</file>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{20}] - %msg%n</pattern>
            <charset>utf8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/error-log-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>200MB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>

        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名-->
        <file>${LOG_HOME}/main.log</file>
        <!--日志文件输出格式-->
        <encoder>
            <!-- 记录行号、文件名、方法名，对性能影响较大，应避免使用 ！！-->
            <pattern>%date [%thread] %-5level [%logger{20}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/log-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>20MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!--控制台输出-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%(%date) %green([%thread]) %highlight(%-5level) %cyan(%logger{10}) - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="sun.net.www.protocol.http.HttpURLConnection" level="ERROR"/>
    <!--打印nacos读取的配置文件-->
    <logger name="com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder" level="DEBUG"/>
    <logger name="org.apache.tomcat.util.net.NioEndpoint" level="ERROR"/>
    <logger name="o.a.coyote.http11.Http11InputBuffer" level="ERROR"/>
    <logger name="o.apache.coyote.http11.Http11Processor" level="ERROR"/>
    <logger name="org.apache.tomcat.util.net.SocketWrapperBase" level="ERROR"/>
    <logger name="sun.rmi.transport.tcp" level="ERROR"/>
    <logger name="sun.rmi.loader" level="ERROR"/>
    <logger name="c.netflix.loadbalancer.BaseLoadBalancer" level="ERROR"/>
    <logger name="c.n.l.DynamicServerListLoadBalancer" level="ERROR"/>
    <logger name="org.apache.catalina.session.ManagerBase" level="ERROR"/>
    <logger name="org.springframework" level="WARN"/>

    <logger name="com.alibaba.druid.filter.stat.StatFilter" level="OFF"/>
    <logger name="com.superred" level="DEBUG"/>


    <!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->

    <!-- 异步输出  https://developer.aliyun.com/article/1055500 -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE"/>
        <!-- 设置异步阻塞队列的大小，为了不丢失日志建议设置的大一些，单机压测时100000是没问题的，应该不用担心OOM -->
        <queueSize>10000</queueSize>
        <!-- 设置丢弃DEBUG、TRACE、INFO日志的阀值，不丢失 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 设置队列入队时非阻塞，当队列满时会直接丢弃日志，但是对性能提升极大 -->
        <neverBlock>true</neverBlock>
    </appender>

    <root level="INFO">
        <appender-ref ref="ASYNC_FILE"/>
        <appender-ref ref="ERROR_LOG_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>