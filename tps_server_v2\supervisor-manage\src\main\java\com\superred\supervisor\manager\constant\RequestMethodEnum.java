package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2025-04-07 13:57
 */
@Getter
public enum RequestMethodEnum {
    POST("POST", "POST"),
    PUT("PUT", "PUT"),
    GET("GET", "GET"),
    HEAD("HEAD", "HEAD"),
    DELETE("DELETE", "DELETE"),
    TRACE("TRACE", "TRACE"),
    OPTIONS("OPTIONS", "OPTIONS");

    private final String key;
    private final String value;

    RequestMethodEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

}
