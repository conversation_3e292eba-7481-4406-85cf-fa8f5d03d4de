package com.superred.supervisor.file.consant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * 单导数据类型
 *
 * <AUTHOR>
 * @since 2025/3/31 15:15
 */
@AllArgsConstructor
@Getter
public enum ExportFile {

    /**
     * 攻击窃密告警
     */
    TROJAN("trojan", "木马活动"),
    ATTACK("attack", "渗透行为"),
    MALWARE("malware", "恶意软件"),
    ABNORMAL("abnormal", "异常行为"),
    IP("ip_blacklist", "ip黑名单"),
    URL("url_blacklist", "url黑名单"),
    ACCOUNT("account_blacklist", "账号黑名单"),
    DOMAIN("domain_blacklist", "域名黑名单"),
    EXTENDED("extended", "扩展告警数据"),

    ALARM_DESC("alarm_filedesc", "攻击告警文件描述"),
    ALARM_SOURCE_FILE("alarm_filedesc", "攻击告警PCAP文件"),


    /**
     * 文件筛选结果数据
     */
    KEYWORD_FILTER("keyword_filter", "关键词筛选"),
    MD5_FILTER("md5_filter", "文件MD5筛选"),
    SECURITY_CLASSIFICATION_FILTER("security_classification_level_filter", "电子文件涉密标识筛选"),
    SECRET_LEVEL_FILTER("secret_level_filter", "密级标志文件筛选"),
    ACCOUNT_FILTER("account_filter", "账号筛选"),
    ENCRYPT_FILTER("encrypt_filter", "加密文件筛选"),

    FILE_FILTER_DESC("file_filter_filedesc", "文件筛选告警描述"),
    FILE_FILTER_SOURCE_FILE("file_filter_filedesc", "文件筛选数据文件"),


    /**
     * 网络行为审计数据
     */
    NET_LOG("net_log", "通联关系审计"),
    APP_BEHAVIOR("app_behavior", "应用行为审计"),
    ACTIVE_OBJECT_AUDIT_DEVICE("devices", "设备活动对象审计"),
    ACTIVE_OBJECT_AUDIT_APP("apps", "应用活动对象审计"),
    ACTIVE_OBJECT_AUDIT_ACCOUNTS("accounts", "账号活动对象审计"),
    DETECTOR("detector_data", "账号活动对象审计"),

    NETWORK_BEHAVIOR("network_behavior", "风险软件使用行为"),

    /**
     * 策略数据
     */
    POLICY("policy", "策略数据"),

    /**
     * 设备信息采集
     */
    TERMINAL_DEVICE("terminal_device", "终端设备信息采集"),
    INFORMATION_DEVICE("information_device", "终端设备信息采集"),
    MEDIUM_DEVICE("medium_device", "介质类设备信息采集"),
    WIRELESS_DEVICE("wireless_device", "无线外接设备信息采集"),
    APP_SOFTWARE("app_software", "应用软件类信息采集"),
    WIRELESS_HOTSPOT("wireless_hotspot", "无线热点采集");

    /**
     *文件存放类型
     */
    private final String storageType;

    private final String desc;

    public static ExportFile fromName(String name) {
        for (ExportFile exportFile : ExportFile.values()) {
            if (exportFile.name().equalsIgnoreCase(name)) {
                return exportFile;
            }
        }
        throw new IllegalArgumentException("未知的文件类型");
    }

    public static ExportFile getExportFileType(String storageType) {
        for (ExportFile exportFile : ExportFile.values()) {
            if (exportFile.getStorageType().equalsIgnoreCase(storageType)) {
                return exportFile;
            }
        }
        throw new IllegalArgumentException("未知的文件类型");
    }

    public String getDataFileName() {
        if (ALARM.contains(this)) {
            return "alarm_data_" + System.currentTimeMillis() + "_" + Constants.MARK;
        }

        if (FILE_FILTER.contains(this)) {
            return "file_filter_data_" + System.currentTimeMillis() + "_" + Constants.MARK;
        }

        if (DEVICE_INFO_SET.contains(this)) {
            return FileDirEnum.DEVICE_INFO.getDirName() + "_" + System.currentTimeMillis() + "_" + Constants.MARK;
        }

        return this.getStorageType() + StrUtil.UNDERLINE + System.currentTimeMillis() + Constants.MARK;
    }

    public String getExportDirName() {

        return this.getExportFileDir().getDirName();
    }

    public FileDirEnum getExportFileDir() {
        FileDirEnum[] values = FileDirEnum.values();
        for (FileDirEnum fileDirEnum : values) {
            if (fileDirEnum.getSupportTypes().contains(this)) {
                return fileDirEnum;
            }
        }
        throw new IllegalArgumentException("未知的文件类型，无法获取导出目录名称");
    }

    public boolean isSourceFile() {
        return SOURCE_FILE.contains(this);
    }

    /**
     * 攻击窃密告警
     */
    public static final Set<ExportFile> ALARM = CollUtil.newHashSet(TROJAN, ATTACK, MALWARE, ABNORMAL,
            IP, URL, ACCOUNT, DOMAIN, EXTENDED, ALARM_SOURCE_FILE);

    /**
     * 文件筛选
     *
     */
    public static final Set<ExportFile> FILE_FILTER = CollUtil.newHashSet(KEYWORD_FILTER, MD5_FILTER, SECURITY_CLASSIFICATION_FILTER,
            SECRET_LEVEL_FILTER, ACCOUNT_FILTER, ENCRYPT_FILTER, FILE_FILTER_SOURCE_FILE);

    /**
     * 网络行为审计数据-通联关系审计
     */
    public static final Set<ExportFile> NET_LOG_SET = CollUtil.newHashSet(NET_LOG);

    /**
     * 网络行为审计数据-检测器的应用行为
     */
    public static final Set<ExportFile> APP_BEHAVIOR_SET = CollUtil.newHashSet(APP_BEHAVIOR);

    /**
     * 终端的风险软件使用行为
     */
    public static final Set<ExportFile> NETWORK_BEHAVIOR_SET = CollUtil.newHashSet(NETWORK_BEHAVIOR);

    /**
     * 策略数据
     */
    public static final Set<ExportFile> POLICY_SET = CollUtil.newHashSet(POLICY);

    /**
     * 设备信息采集
     */
    public static final Set<ExportFile> DEVICE_INFO_SET = CollUtil.newHashSet(TERMINAL_DEVICE, MEDIUM_DEVICE, WIRELESS_DEVICE, APP_SOFTWARE, WIRELESS_HOTSPOT, INFORMATION_DEVICE);


    public static final Set<ExportFile> SOURCE_FILE = CollUtil.newHashSet(ALARM_SOURCE_FILE, FILE_FILTER_SOURCE_FILE, ALARM_DESC, FILE_FILTER_DESC);
}
