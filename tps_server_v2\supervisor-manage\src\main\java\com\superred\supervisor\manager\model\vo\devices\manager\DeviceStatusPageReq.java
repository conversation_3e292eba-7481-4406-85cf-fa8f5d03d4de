package com.superred.supervisor.manager.model.vo.devices.manager;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 设备状态页面请求
 *
 * <AUTHOR>
 * @since 2025/03/12
 */
@EqualsAndHashCode(callSuper = true)
@Data

public class DeviceStatusPageReq extends PageReqDTO {


    @Schema(description = "部署单位")
    private String organs;

    @Schema(description = "部署单位")
    private Integer regionId;


    @Schema(description = "设备状态 0（成功），1（失败），2（审核中） 3离线；4无效；5已删除 6在线")
    private Integer deviceStatus;


    @Schema(description = "注册时间")
    private Date regTimeStart;

    @Schema(description = "注册时间")
    private Date regTimeEnd;


    @Schema(description = "设备编号")
    private String deviceId;


    private LocalDateTime lastHeartbeatTimeStart;

    private LocalDateTime lastHeartbeatTimeEnd;
}
