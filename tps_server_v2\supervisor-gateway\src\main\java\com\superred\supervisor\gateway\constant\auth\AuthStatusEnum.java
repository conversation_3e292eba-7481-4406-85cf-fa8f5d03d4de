package com.superred.supervisor.gateway.constant.auth;

import lombok.Getter;

/**
 *      * auth-status：      * 1：请求头里设备ID为空      * 2：未注册      * 3：未认证      * 4：已认证
 *
 * <AUTHOR>
 * @since 2025/5/19 17:02
 */
@Getter
public enum AuthStatusEnum {
    /**
     * 请求头里设备ID为空
     */
    DEVICE_ID_EMPTY("1", "请求头里设备ID为空"),

    /**
     * 未注册
     */
    UNREGISTERED("2", "未注册"),

    /**
     * 未认证
     */
    UNAUTHENTICATED("3", "未认证"),

    /**
     * 已认证
     */
    AUTHENTICATED("4", "已认证");

    private final String code;
    private final String message;

    AuthStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static final String KEY = "auth-status";
}
