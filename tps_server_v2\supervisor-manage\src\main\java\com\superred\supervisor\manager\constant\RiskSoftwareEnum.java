package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2025-03-31 10:52
 */
@Getter
public enum RiskSoftwareEnum {
    TYPE0(0, "境外即时通信软件/服务"),
    TYPE1(1, "境外邮件客户端/服务"),
    TYPE2(2, "境外代理翻墙工具");

    private final Integer key;
    private final String value;

    RiskSoftwareEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValueByKey(String key) {
        for (RiskSoftwareEnum riskSoftwareEnum : RiskSoftwareEnum.values()) {
            if (riskSoftwareEnum.getKey().equals(Integer.parseInt(key))) {
                return riskSoftwareEnum.getValue();
            }
        }
        return null;
    }
}
