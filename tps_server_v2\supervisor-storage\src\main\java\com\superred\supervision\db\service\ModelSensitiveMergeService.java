package com.superred.supervision.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.superred.supervision.db.entity.Alarm;
import com.superred.supervision.db.entity.ModelSensitiveMerge;
import com.superred.supervision.db.entity.Sensitive;

/**
 * <p>
 * 告警融合规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
public interface ModelSensitiveMergeService extends IService<ModelSensitiveMerge> {
    /**
     * 获取merge规则
     * @return merge规则
     */
    ModelSensitiveMerge getMerge(String cacheName);

    /**
     * 生成mergeId
     * @param sensitive 告警信息
     * @return id字符串
     */
    String getMergeId(Sensitive sensitive);

    /**
     * 获取sensitive的checksum
     * @param sensitive 泄密告警
     * @return checksum
     */
    String getSensitiveChecksum(Sensitive sensitive);


    /**
     * 获取alarm的checksum
     * @param sensitive 泄密告警
     * @return checksum
     */
    String getAlarmChecksum(Alarm alarm);
}
