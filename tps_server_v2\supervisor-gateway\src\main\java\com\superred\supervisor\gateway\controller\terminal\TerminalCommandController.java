package com.superred.supervisor.gateway.controller.terminal;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.constant.cmd.CmdResultType;
import com.superred.supervisor.common.model.dto.cmd.CmdResultDTO;
import com.superred.supervisor.common.model.resp.ApiResponse;
import com.superred.supervisor.gateway.kafka.producer.CmdResultEventPublisher;
import com.superred.supervisor.gateway.service.terminal.TerminalHeartBeatService;
import com.superred.supervisor.gateway.service.terminal.TerminalRegisterService;
import com.superred.supervisor.gateway.utils.AgentAuthUtils;
import com.superred.supervisor.standard.v202505.terminal.cmd.CheckUpdateReq;
import com.superred.supervisor.standard.v202505.terminal.cmd.CheckUpdateResp;
import com.superred.supervisor.standard.v202505.terminal.cmd.TerminalCmdResp;
import com.superred.supervisor.standard.v202505.terminal.cmd.TerminalCmdResultReq;
import com.superred.supervisor.standard.v202505.terminal.cmd.TerminalPolicyResultReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 终端注册、认证接口
 *
 * <AUTHOR>
 * @since 2025/5/16 16:03
 */
@Tag(name = "1.2 指令于策略接收(2025-05)")
@RestController
@Slf4j
@RequestMapping("/C2/")
public class TerminalCommandController {

    @Resource
    private TerminalHeartBeatService terminalHeartBeatService;

    @Resource
    private TerminalRegisterService terminalRegisterService;

    @Resource
    private CmdResultEventPublisher cmdResultEventPublisher;

    /**
     *  C.3.4.1 心跳.
     *  每分钟一次
     *
     **/
    @PostMapping("/heartbeat")
    @Operation(summary = "C.3.4.1 心跳接口")
    @ApiOperationSupport(order = 1)
    public List<TerminalCmdResp> heartbeat() {
        return terminalHeartBeatService.handleHeatbeat();
    }


    /**
     * C.*******.2 内置策略更新指令响应.
     *
     * @param filename: 文件名
     **/
    @GetMapping("/sys_manager/inner_policy_update")
    @Operation(summary = "C.*******.2 内置策略更新指令响应.")
    @ApiOperationSupport(order = 2)
    public void innerPolicyUpdateFile(
            @Parameter(description = "文件名") @RequestParam @NotBlank(message = "文件名不能为空") String filename,
            HttpServletResponse response) {
        terminalHeartBeatService.innerPolicyUpdateFile(filename, response);
    }

    /**
     * C.*******.2系统软件升级指令响应.
     *
     * @param filename: 文件名
     * @param version: 版本
     **/
    @GetMapping("/sys_manager/update")
    @Operation(summary = "C.*******.2系统软件升级指令响应.")
    @ApiOperationSupport(order = 3)
    public void updateAgentFile(
            @Parameter(description = "文件名") @RequestParam @NotBlank(message = "文件名不能为空") String filename,
            @Parameter(description = "版本") @RequestParam @NotBlank(message = "版本不能为空") String version,
            HttpServletResponse response) {
        terminalHeartBeatService.agentUpgradeFile(filename, version, response);
    }

    /**
     * C.3.5.1 指令响应上报.
     *
     **/
    @PostMapping("/sys_manager/command_result")
    @Operation(summary = "C.3.5.1 指令响应上报")
    @ApiOperationSupport(order = 4)
    public ApiResponse<String> commandResult(@RequestBody TerminalCmdResultReq req) {

        CmdResultDTO<TerminalCmdResultReq> cmdResultDTO = CmdResultDTO.<TerminalCmdResultReq>builder()
                .cmdResultType(CmdResultType.AGENT_CMD)
                .requestBody(req)
                .deviceId(AgentAuthUtils.getDeviceIdFromRequest())
                .build();
        cmdResultEventPublisher.sendMessage(JsonUtil.toJson(cmdResultDTO));

        return ApiResponse.success().msg("上报成功");
    }

    /**
     * C.3.5.2 策略响应上报.
     *
     **/
    @PostMapping("/sys_manager/policy_result")
    @Operation(summary = "C.3.5.2 策略响应上报.")
    @ApiOperationSupport(order = 5)
    public ApiResponse<String> policyResult(@RequestBody TerminalPolicyResultReq req) {

        CmdResultDTO<TerminalPolicyResultReq> cmdResultDTO = CmdResultDTO.<TerminalPolicyResultReq>builder()
                .cmdResultType(CmdResultType.AGENT_POLICY)
                .deviceId(AgentAuthUtils.getDeviceIdFromRequest())
                .requestBody(req)
                .build();
        cmdResultEventPublisher.sendMessage(JsonUtil.toJson(cmdResultDTO));
        return ApiResponse.success().msg("上报成功");
    }


    /*-------------------标准不详细的接口------*/

    /**
     *  系统软件手动检测升级接口.
     **/
    @PostMapping("/sys_manager/check_update")
    @Operation(summary = "系统软件手动检测升级接口")
    @ApiOperationSupport(order = 7)
    public ApiResponse<CheckUpdateResp> checkUpdate(@RequestBody CheckUpdateReq req) {
        CheckUpdateResp resp = terminalRegisterService.checkUpdate(req);
        return ApiResponse.success(resp);
    }

    /**
     * 卸载回调接口：回收点数，删除设备信息
     *
     **/
    @PostMapping("/uninstall/callback")
    @Operation(summary = "终端卸载回调")
    @ApiOperationSupport(order = 8)
    @Deprecated
    public ApiResponse<String> agentUninstallCallback() {
        terminalRegisterService.agentUninstallCallback();
        return ApiResponse.success();
    }
}
