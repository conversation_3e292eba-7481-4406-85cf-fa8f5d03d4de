package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.superred.supervisor.common.mybatis.EncryptAndDecryptHandler;
import lombok.Data;

import java.util.Date;

/**
 * 访问控制白名单(PSysAclWhitelist) 实体
 *
 * <AUTHOR>
 * @since 2025-03-19 18:26:53
 */
@Data
@TableName(value = "p_sys_acl_whitelist", autoResultMap = true)
@Deprecated
public class AclWhitelist {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * ip地址范围
     */
    @TableField(value = "source", typeHandler = EncryptAndDecryptHandler.class)
    private String source;

    /**
     * ip地址范围值
     */
    @TableField("source_addr")
    private String sourceAddr;

    /**
     * 访问控制类型，1：系统访问控制；2：通信访问控制
     */
    @TableField("acl_type")
    private Integer aclType;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modified_time")
    private Date modifiedTime;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

}

