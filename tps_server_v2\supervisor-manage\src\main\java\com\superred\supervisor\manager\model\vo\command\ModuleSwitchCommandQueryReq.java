package com.superred.supervisor.manager.model.vo.command;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模块启停指令查询请求参数
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data
public class ModuleSwitchCommandQueryReq {
    /**
     * 设备id
     */
    @Schema(description = "设备id", example = "device123")
    private String deviceId;

    /**
     * 模块名称
     */
    @Schema(description = "模块名称", example = "moduleA")
    private String moduleName;

    /**
     * 指令类型（启动/停止）
     */
    @Schema(description = "指令类型（启动/停止）", example = "start")
    private String commandType;
}
