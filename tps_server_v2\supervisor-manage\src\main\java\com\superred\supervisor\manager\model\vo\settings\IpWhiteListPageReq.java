package com.superred.supervisor.manager.model.vo.settings;

import com.superred.common.core.model.PageReqDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <AUTHOR>
 *  访问控制分页
 * @since 2025年03月13日
 */
@Data
public class IpWhiteListPageReq extends PageReqDTO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "是否启用")
    private Boolean enabled;


    @Schema(description = "源IP/源网段")
    private String source;

    @Schema(description = "源IP/源网段值")
    private String sourceAddr;


}
