package com.superred.supervisor.manager.model.dto.system;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * 系统日志数据到
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data
public class SysLogDTO {

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 操作人员
     */
    private String username;

    /**
     * 操作模块
     */
    private String operateModule;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 行为类型(级别)
     */
    private String behaviourType;

    /**
     * 日志风险级别
     */
    private String level;

    /**
     * 角色id
     */
    private List<String> roleIds;

    /**
     * 角色
     */
    private String role;

    /**
     * 是否包含 1，不包含，0，包含
     */
    private String notIn;

    /**
     * ip地址
     */
    private String hostIp;

    /**
     * 操作行为描述
     */
    private String description;

    /**
     * log 压缩包密码
     */
    @NotBlank(message = "日志包压缩密码 不可为空")
    //    @Size(min = 8,max = 11, message = "日志包压缩密码 长度限制8-11位")
    //    @BlankOrPattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9!\"#$%&'()*+,\\-./:;<=>?@\\[\\\\\\]^_`{|}~]+$", message = "日志包压缩密码 格式错误")
    private String logZipPass;

    /**
     * 结果备注
     */
    private String remarks;

    /**
     * 操作状态
     */
    private Integer result;

    /**
     * 导出日志限制
     */
    private Integer limit;

    /**
     * 日志的ids
     */
    private List<Integer> logIds;

    /**
     * 日志类型，all 全部，auth 授权，option 操作
     */
    private String logType;

}
