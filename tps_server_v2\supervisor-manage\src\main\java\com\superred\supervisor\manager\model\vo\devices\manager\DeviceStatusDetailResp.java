package com.superred.supervisor.manager.model.vo.devices.manager;

import com.superred.supervisor.manager.model.vo.devices.DeviceBaseInfoResp;
import com.superred.supervisor.manager.model.vo.devices.DeviceContactResp;
import com.superred.supervisor.manager.model.vo.devices.DeviceCpuInfoResp;
import com.superred.supervisor.manager.model.vo.devices.DeviceDiskInfoResp;
import com.superred.supervisor.manager.model.vo.devices.DeviceInterfaceManageResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/3/12 14:10
 */
@Data

public class DeviceStatusDetailResp {

    @Schema(description = "设备基本信息")
    private DeviceBaseInfoResp deviceBaseInfoResp;


    /**
     * 单位联系人
     */
    @Schema(description = "单位联系人")
    private List<DeviceContactResp> deviceContacts;

    /**
     * cpu信息
     */
    @Schema(description = "cpu信息")
    private List<DeviceCpuInfoResp> deviceCpuInfos;

    /**
     * 磁盘信息
     */
    @Schema(description = "磁盘信息")
    private List<DeviceDiskInfoResp> deviceDiskInfos;

    /**
     * 设备配置信息
     */
    @Schema(description = "设备配置信息")
    private List<DeviceInterfaceManageResp> deviceInterfaceManages;


}
