package com.superred.supervisor.manager.model.dto.devices;

import lombok.Data;

/**
 * 计数dto
 *
 * <AUTHOR>
 * @since 2025/03/19
 */
@Data
public class CountDTO {

    private Integer countTotal;

    private Integer countOnline;

    private Double countFlow;


    public static CountDTO of() {
        CountDTO countDTO = new CountDTO();
        countDTO.setCountTotal(0);
        countDTO.setCountOnline(0);
        countDTO.setCountFlow(0.0D);
        return countDTO;
    }
}
