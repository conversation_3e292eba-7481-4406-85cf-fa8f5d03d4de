package com.superred.supervisor.manager.model.dto.command;

import lombok.Data;

import java.util.List;

/**
 * 威胁预警处置params
 */
@Data
public class WarningDispose {

    /**
     * 预警编号，数值型，不超过 20位；
     */
    private Long warnId;

    /**
     * 预警标题
     */
    private String warnTitle;

    /**
     * 预警描述
     */
    private String warnDesc;

    /**
     * 告警等级
     */
    private Integer warnLevel;

    /**
     * 处置时限，时间类型，精确到日期;
     */
    private String expireDate;
    /**
     * 附件：指令ID_文件MD5校验码
     */
    private String attachment;

    /**
     * 关联告警信息
     */
    private List<RelationEvent> relationEvents;

    /**
     * 预警下发时间，时间类型，精确到时分秒
     */
    private String time;

}
