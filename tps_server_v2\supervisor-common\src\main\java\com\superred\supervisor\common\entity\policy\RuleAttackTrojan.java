package com.superred.supervisor.common.entity.policy;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 木马活动检测策略
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_rule_attack_trojan", autoResultMap = true)
public class RuleAttackTrojan extends Model<RuleAttackTrojan> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "rule_id")
    private String ruleId;

    /**
     * 策略名称
     */
    private String ruleName;

    /**
     * 是否保存pcap，默认为1保存，2不保存
     */
    private String storePcap;

    /**
     * 攻击分类 1 窃密木马 2 远控木马 3 电脑病毒 4 僵尸网络 5 网络蠕虫 6 间谍软件 7 挖矿木马 8 黑客工具 11 后门程序 99 其它
     */
    private String attackClass;

    /**
     * 攻击组织
     */
    private String attackGroup;

    /**
     * 策略内容
     */
    //@TableField(typeHandler = EncryptAndDecryptHandler.class)
    private String rule;

    /**
     * 攻击窃密告警描述。简要描述木马性质、木马进行操作（取文件、控制…）或适用系统（windows linux unix…）
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 告警级别;告警级别型，取值为：0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）
     */
    private String risk;

    /**
     * 策略应用状态，0未应用，1已应用
     */
    private String status;

    /**
     * 是否共享状态，0是，1否
     */
    private String isShare;

    /**
     * 策略来源 1 本级 2上级
     */
    private String ruleSource;

    /**
     * 平台级别
     */
    private String level;

    /**
     * 策略更新时间
     */
    private String updateTime;

    /**
     * 策略创建时间
     */
    private String createTime;

    /**
     * 扩展字段1
     */
    private Long ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 上级共享策略ID
     */
    private Long upRuleId;

}
