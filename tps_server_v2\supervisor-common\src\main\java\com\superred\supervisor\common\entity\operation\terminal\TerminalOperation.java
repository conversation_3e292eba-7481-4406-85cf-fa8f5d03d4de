package com.superred.supervisor.common.entity.operation.terminal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.superred.supervisor.common.entity.operation.enums.OperationType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端操作主表 实体
 *
 * <AUTHOR>
 * @since 2025-07-24 14:22:59
 */
@Data
@TableName("op_terminal_operation")
public class TerminalOperation {


    /**
     * 操作ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作类型：policy-策略,command-指令
     */
    @TableField("operation_type")
    private OperationType operationType;

    /**
     * 如果是指令：inner_policy_update 内置策略更新 startm 模块启动指令 stopm 模块停止指令 startm_inner 开启模块内置策略 stopm_inner 停止模块内置策略 update 系统软件更新
     * 如果是策略: reset 全量下发，add 增量下发，del 增量删除
     */
    @TableField("cmd")
    private String cmd;

    /**
     * 关联ID：当type=policy时为policy_code，当type=command时为cmd_code
     */
    @TableField("ref_id")
    private String refId;

    /**
     * 系统命令参数（指令下发时使用）
     */
    @TableField("param")
    private String param;

    /**
     * 策略规则个数（策略下发时使用）
     */
    @TableField("num")
    private Integer num;

    /**
     * 策略配置内容（策略下发时的快照，避免策略修改影响已下发内容）
     */
    @TableField("config")
    private String config;

    /**
     * 模块类型（快照数据）
     */
    @TableField("module")
    private String module;

    /**
     * 子模块名称 [] 模块启动指令的子模块名称
     */
    @TableField("submodule")
    private String submodule;

    /**
     * 策略版本号（快照数据）
     */
    @TableField("version")
    private String version;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 更新人
     */
    @TableField("updater_id")
    private String updaterId;

}

