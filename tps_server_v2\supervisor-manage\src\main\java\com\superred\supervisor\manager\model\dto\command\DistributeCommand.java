package com.superred.supervisor.manager.model.dto.command;

import lombok.Data;

import java.util.List;

/**
 * 下发命令
 *
 * <AUTHOR>
 * @since 2025/03/13 11:01
 **/
@Data
public class DistributeCommand {

    /**
     * 指令类型
     */
    private String cmd;

    /**
     * 部分指令内容
     */
    private String params;
    /**
     * 子模块
     */
    private String submodule;

    /**
     * 模块启停选择的模块
     */
    private List<ModuleStartAndStop> moduleStartAndStop;

    /**
     * 版本一致性检查文件列表。逗号隔开
     */
    private String fileList;


}
