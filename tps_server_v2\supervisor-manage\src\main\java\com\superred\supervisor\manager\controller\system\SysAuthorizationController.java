package com.superred.supervisor.manager.controller.system;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.annotation.role.IgnoreAuth;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.system.SysAuthorizationInfoResp;
import com.superred.supervisor.manager.service.system.SysAuthorizationService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


/**
 * 系统授权控制器
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Slf4j
@Tag(name = "1.1. 系统授权信息")
@RestController
@AllArgsConstructor
@RequestMapping("/sys-authorization")
public class SysAuthorizationController {

    @Resource
    private SysAuthorizationService sysAuthorizationService;


    @Operation(summary = "1 上传授权文件")
    @ApiOperationSupport(order = 1)
    @PostMapping("/uploadLic")
    @IgnoreAuth
    @SysLogAnn(module = LogTypeConstants.SYSTEM_AUTHORIZATION, operateType = OperateTypeConstants.IMPORT, desc = "导入授权文件")
    public R<Boolean> uploadLic(@RequestParam("file") MultipartFile file) {

        boolean uploaded = sysAuthorizationService.uploadLic(file);

        return R.success(uploaded);
    }

    @Operation(summary = "2 获取授权信息")
    @ApiOperationSupport(order = 2)
    @GetMapping("/licence-info")
    public R<SysAuthorizationInfoResp> getLicenceInfo() {
        SysAuthorizationInfoResp resp = sysAuthorizationService.getLicenceInfo();

        return R.success(resp);
    }
}
