package com.superred.supervisor.manager.model.vo.policy;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 规则策略应用情况
 */
@Data

public class RulePolicyApplyResp {

    @Schema(description = "规则id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long ruleId;

    @Schema(description = "策略类型")
    private String policyType;

    @Schema(description = "策略名称")
    private String policyName;

    @Schema(description = "策略id")
    private Long policyId;
}
