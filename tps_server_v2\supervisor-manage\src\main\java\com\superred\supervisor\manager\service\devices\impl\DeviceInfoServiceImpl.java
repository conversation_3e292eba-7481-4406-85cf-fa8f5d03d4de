package com.superred.supervisor.manager.service.devices.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.entity.devices.DeviceInfo;
import com.superred.supervisor.common.entity.devices.DeviceInterface;
import com.superred.supervisor.common.entity.devices.DeviceSuspectedLog;
import com.superred.supervisor.common.entity.devices.DeviceSystemStatus;
import com.superred.supervisor.common.entity.devices.enums.RegisterStatus;
import com.superred.supervisor.common.repository.devices.DeviceInfoRepository;
import com.superred.supervisor.common.repository.devices.DeviceSuspectedLogRepository;
import com.superred.supervisor.common.repository.devices.DeviceSystemStatusRepository;
import com.superred.supervisor.manager.model.dto.devices.CountDTO;
import com.superred.supervisor.manager.model.vo.devices.DetectorListReq;
import com.superred.supervisor.manager.model.vo.devices.DetectorListResp;
import com.superred.supervisor.manager.model.vo.devices.DeviceBaseInfoResp;
import com.superred.supervisor.manager.model.vo.devices.DeviceContactResp;
import com.superred.supervisor.manager.model.vo.devices.DeviceCpuInfoResp;
import com.superred.supervisor.manager.model.vo.devices.DeviceDiskInfoResp;
import com.superred.supervisor.manager.model.vo.devices.DeviceInterfaceManageResp;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditDetailResp;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditFailedReq;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditPageReq;
import com.superred.supervisor.manager.model.vo.devices.audit.DeviceAuditPageResp;
import com.superred.supervisor.manager.model.vo.devices.manager.BusinessStatusDetectorResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceEventlogReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceInterfaceMirrorStatusResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceRemarkEditReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceStateInfoPageResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceStatusPageReq;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceSuspectedStatusPageResp;
import com.superred.supervisor.manager.model.vo.devices.manager.DeviceSystemStatusResp;
import com.superred.supervisor.manager.model.vo.devices.manager.SystemStatusCpuResp;
import com.superred.supervisor.manager.repository.devices.DeviceInterfaceExtRepository;
import com.superred.supervisor.manager.service.RegionCacheService;
import com.superred.supervisor.manager.service.devices.DeviceInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/19 9:34
 */
@Slf4j
@Service
public class DeviceInfoServiceImpl implements DeviceInfoService {

    @Resource
    private DeviceInfoRepository deviceInfoRepository;

    @Resource
    private RegionCacheService regionCacheService;

    @Resource
    private DeviceSystemStatusRepository deviceSystemStatusRepository;

    @Resource
    private DeviceInterfaceExtRepository deviceInterfaceExtRepository;

    @Resource
    private DeviceSuspectedLogRepository deviceSuspectedLogRepository;

    /**
     * 分页查询设备审核列表
     *
     * @param pageReq 分页查询请求
     * @return 分页查询结果
     */
    @Override
    public IPage<DeviceAuditPageResp> pageDeviceAudit(DeviceAuditPageReq pageReq) {


        LambdaQueryWrapper<DeviceInfo> queryWrapper = new LambdaQueryWrapper<DeviceInfo>()
                .in(pageReq.getRegStatus() == null, DeviceInfo::getRegisterStatus, RegisterStatus.AUDITING, RegisterStatus.FAIL)
                .gt(pageReq.getRegTimeStart() != null, DeviceInfo::getRegisterTime, pageReq.getRegTimeStart())
                .le(pageReq.getRegTimeEnd() != null, DeviceInfo::getRegisterTime, pageReq.getRegTimeEnd())
                .eq(pageReq.getRegStatus() != null, DeviceInfo::getRegisterStatus, pageReq.getRegStatus());
        Page<DeviceInfo> deviceInfoPage = deviceInfoRepository.page(new Page<>(pageReq.getStart(), pageReq.getLimit()), queryWrapper);

        return deviceInfoPage.convert(deviceInfo -> {
            DeviceAuditPageResp resp = DeviceAuditPageResp.from(deviceInfo);
            if (StrUtil.isNotEmpty(deviceInfo.getAddressCode())) {
                resp.setAddressCodeToStr(regionCacheService.regionIdToChinesPath(Integer.valueOf(deviceInfo.getAddressCode())));
            }
            return resp;
        });
    }

    /**
     * 获取设备审核详情
     *
     * @param deviceId 设备ID
     * @return 设备审核详情
     */
    @Override
    public DeviceAuditDetailResp getAuditDetail(String deviceId) {
        DeviceInfo deviceInfo = deviceInfoRepository.getById(deviceId);
        if (deviceInfo == null) {
            throw new BaseBusinessException("设备不存在");
        }

        DeviceBaseInfoResp baseInfoResp = DeviceBaseInfoResp.from(deviceInfo);
        DeviceAuditDetailResp detailResp = DeviceAuditDetailResp.builder()
                .deviceBaseInfoResp(baseInfoResp)
                .build();
        if (StrUtil.isNotEmpty(deviceInfo.getContact())) {
            detailResp.setDeviceContacts(JsonUtil.fromJson(deviceInfo.getContact(), new TypeReference<List<DeviceContactResp>>() {
            }));
        }
        if (StrUtil.isNotEmpty(deviceInfo.getCpuInfo())) {
            detailResp.setDeviceCpuInfos(JsonUtil.fromJson(deviceInfo.getCpuInfo(), new TypeReference<List<DeviceCpuInfoResp>>() {
            }));
        }
        if (StrUtil.isNotEmpty(deviceInfo.getDiskInfo())) {
            detailResp.setDeviceDiskInfos(JsonUtil.fromJson(deviceInfo.getDiskInfo(), new TypeReference<List<DeviceDiskInfoResp>>() {
            }));
        }
        if (StrUtil.isNotEmpty(deviceInfo.getInterfaces())) {
            detailResp.setDeviceInterfaceManages(JsonUtil.fromJson(deviceInfo.getInterfaces(), new TypeReference<List<DeviceInterfaceManageResp>>() {
            }));
        }
        return detailResp;
    }

    /**
     * 审核设备
     *
     * @param deviceId 设备ID
     */
    @Override
    public void checkSuccess(String deviceId) {

        log.info("审核通过{}", deviceId);

        DeviceInfo deviceInfo = deviceInfoRepository.getById(deviceId);
        if (deviceInfo == null) {
            throw new BaseBusinessException("设备不存在");
        }

        //        Date newDate = new Date();
        //        LambdaUpdateWrapper<DeviceInfo> update = new LambdaUpdateWrapper<>();
        //        update.set(DeviceInfo::getRegisterStatus,detectorCheckDTO.getRegisterStatus());
        //        update.set(DeviceInfo::getRegisterMessage,detectorCheckDTO.getRegisterMessage());
        //        update.eq(DeviceInfo::getDeviceId,detectorCheckDTO.getDeviceId());
        //        update.set(DeviceInfo::getVerifyTime, newDate);

        DeviceInfo update = new DeviceInfo();
        update.setDeviceId(deviceId);
        update.setRegisterStatus(RegisterStatus.SUCCESS);
        update.setRegisterMessage("审核通过");
        update.setVerifyTime(LocalDateTime.now());
        deviceInfoRepository.updateById(update);


        //        // 向上级管理系统上报设备信息
        //        JSONObject jsonObject = new JSONObject();
        //        jsonObject.set("deviceType", "01");
        //        jsonObject.set("deviceId", detectorCheckDTO.getDeviceId());
        //        kafkaProducer.sendMessage("device_report", JSONUtil.toJsonStr(jsonObject));
        // 修改缓存信息
        //        this.redisUtil.hset(Const.DEVICE_INFO_CACHE_KEY + detectorCheckDTO.getDeviceId(), "registerStatus", detectorCheckDTO.getRegisterStatus());
    }

    /**
     * 审核设备失败
     *
     * @param failedReq 审核失败请求
     */
    @Override
    public void checkFailed(DeviceAuditFailedReq failedReq) {
        DeviceInfo deviceInfo = deviceInfoRepository.getById(failedReq.getDeviceId());
        if (deviceInfo == null) {
            throw new BaseBusinessException("设备不存在");
        }

        DeviceInfo update = new DeviceInfo();
        update.setRegisterStatus(RegisterStatus.FAIL);
        update.setRegisterMessage(failedReq.getRegMessage());
        update.setVerifyTime(LocalDateTime.now());
        deviceInfoRepository.updateById(update);

    }


    /**
     * 分页查询监测器设备状态信息
     *
     * @param req 分页查询请求
     * @return 分页查询结果
     */
    @Override
    public IPage<DeviceStateInfoPageResp> stateInfoPage(DeviceStatusPageReq req) {

        RegisterStatus registerStatus = RegisterStatus.getByValue(req.getDeviceStatus());
        if (registerStatus == RegisterStatus.ONLINE) {
            req.setLastHeartbeatTimeStart(LocalDateTime.now().minusMinutes(5));
        }
        if (registerStatus == RegisterStatus.OFFLINE) {
            req.setLastHeartbeatTimeEnd(LocalDateTime.now().minusMinutes(5));
        }
        LambdaQueryWrapper<DeviceInfo> queryWrapper = new LambdaQueryWrapper<DeviceInfo>()
                .in(req.getDeviceStatus() != null, DeviceInfo::getRegisterStatus, registerStatus)
                .notIn(req.getDeviceStatus() == null, DeviceInfo::getRegisterStatus, RegisterStatus.FAIL, RegisterStatus.AUDITING)
                .gt(req.getRegTimeStart() != null, DeviceInfo::getRegisterTime, req.getRegTimeStart())
                .le(req.getRegTimeEnd() != null, DeviceInfo::getRegisterTime, req.getRegTimeEnd())
                .gt(req.getLastHeartbeatTimeStart() != null, DeviceInfo::getHeartbeatTime, req.getLastHeartbeatTimeStart())
                .le(req.getLastHeartbeatTimeEnd() != null, DeviceInfo::getHeartbeatTime, req.getLastHeartbeatTimeEnd());


        Page<DeviceInfo> deviceInfoPage = deviceInfoRepository.page(new Page<>(req.getStart(), req.getLimit()), queryWrapper);

        List<String> deviceIds = deviceInfoPage.getRecords().stream().map(DeviceInfo::getDeviceId).collect(Collectors.toList());
        List<DeviceSystemStatus> list = deviceSystemStatusRepository.listByDeviceIds(deviceIds);
        Map<String, DeviceSystemStatus> systemStatusMap = list.stream().collect(Collectors.toMap(DeviceSystemStatus::getDeviceId, Function.identity()));
        Map<String, CountDTO> countDTOMap = deviceInterfaceExtRepository.selectCountMapByDeviceIds(deviceIds);

        return deviceInfoPage.convert(deviceInfo -> {
            DeviceStateInfoPageResp resp = DeviceStateInfoPageResp.from(deviceInfo);
            if (StrUtil.isNotEmpty(deviceInfo.getAddressCode())) {
                resp.setAddressCodeName(regionCacheService.regionIdToChinesPath(Integer.valueOf(deviceInfo.getAddressCode())));
            }
            DeviceSystemStatus deviceSystemStatus = systemStatusMap.get(deviceInfo.getDeviceId());
            if (deviceSystemStatus != null) {
                resp.setMem(String.valueOf(deviceSystemStatus.getMem()));
            }
            List<DeviceDiskInfoResp> diskInfos = JsonUtil.fromJson(deviceInfo.getDiskInfo(), new TypeReference<List<DeviceDiskInfoResp>>() {
            });
            if (CollUtil.isNotEmpty(diskInfos)) {
                int totalDisk = diskInfos.stream().map(DeviceDiskInfoResp::getSize).mapToInt(Integer::intValue).sum();
                resp.setDisk(String.valueOf(totalDisk));
            }
            CountDTO countDTO = countDTOMap.getOrDefault(deviceInfo.getDeviceId(), CountDTO.of());
            resp.setInterfaceCount(countDTO.getCountTotal());
            resp.setInterfaceCountOnline(countDTO.getCountOnline());
            resp.setInterfaceFlowCount(countDTO.getCountFlow());
            return resp;
        });
    }

    /**
     * 根据设备ID获取监测器设备状态信息
     *
     * @param deviceId 设备ID
     * @return 监测器设备状态信息
     */
    @Override
    public BusinessStatusDetectorResp getDetectorByDeviceId(String deviceId) {
        DeviceInfo deviceInfo = deviceInfoRepository.getById(deviceId);
        if (deviceInfo == null) {
            throw new BaseBusinessException("设备不存在");
        }
        DeviceSystemStatus deviceSystemStatus = deviceSystemStatusRepository.getOne(Wrappers.<DeviceSystemStatus>lambdaQuery().eq(DeviceSystemStatus::getDeviceId, deviceId).last("limit 1"));
        if (deviceSystemStatus == null) {
            return BusinessStatusDetectorResp.builder()
                    .build();
        }

        return BusinessStatusDetectorResp.builder()
                .time(deviceSystemStatus.getTime())
                .uptime(Long.valueOf(deviceInfo.getUptime()))
                .build();
    }

    /**
     * 分页查询设备疑似状态日志
     *
     * @param req 分页查询请求
     * @return 分页查询结果
     */
    @Override
    public IPage<DeviceSuspectedStatusPageResp> getDeviceSuspectedLogPage(DeviceEventlogReq req) {

        Page<DeviceSuspectedLog> page = deviceSuspectedLogRepository.page(new Page<>(req.getStart(), req.getLimit()), Wrappers.<DeviceSuspectedLog>lambdaQuery()
                .eq(DeviceSuspectedLog::getDeviceId, req.getDeviceId())
                .orderByDesc(DeviceSuspectedLog::getTime));
        return page.convert(DeviceSuspectedStatusPageResp::from);
    }

    /**
     * 获取设备接口镜像状态
     *
     * @param deviceId 设备ID
     * @return 设备接口镜像状态
     */
    @Override
    public List<DeviceInterfaceMirrorStatusResp> listDeviceInterfaceMirror(String deviceId) {
        List<DeviceInterface> interfaces = deviceInterfaceExtRepository.list(Wrappers.<DeviceInterface>lambdaQuery()
                .eq(DeviceInterface::getDeviceId, deviceId)
                .orderByDesc(DeviceInterface::getTime));
        return interfaces.stream().map(DeviceInterfaceMirrorStatusResp::from).collect(Collectors.toList());
    }

    /**
     * 获取监测器设备系统状态
     *
     * @param deviceId 设备ID
     * @return 监测器设备系统状态
     */
    @Override
    public DeviceSystemStatusResp getSystemStatus(String deviceId) {
        DeviceInfo deviceInfo = deviceInfoRepository.getById(deviceId);
        if (deviceInfo == null) {
            throw new BaseBusinessException("设备不存在");
        }
        DeviceSystemStatus deviceSystemStatus = deviceSystemStatusRepository.getOne(Wrappers.<DeviceSystemStatus>lambdaQuery().eq(DeviceSystemStatus::getDeviceId, deviceId).last("limit 1"));
        if (deviceSystemStatus == null) {
            return DeviceSystemStatusResp.builder().build();
        }
        return DeviceSystemStatusResp.builder()
                .did(deviceSystemStatus.getDid())
                .mem(deviceSystemStatus.getMem())
                .disk(deviceSystemStatus.getDisk())
                .time(deviceSystemStatus.getTime())
                .build();


    }

    /**
     * 获取监测器设备CPU状态
     *
     * @param deviceId 设备ID
     * @return 监测器设备CPU状态
     */
    @Override
    public List<SystemStatusCpuResp> getCpuStatus(String deviceId) {
        DeviceInfo deviceInfo = deviceInfoRepository.getById(deviceId);
        if (deviceInfo == null) {
            throw new BaseBusinessException("设备不存在");
        }
        DeviceSystemStatus deviceSystemStatus = deviceSystemStatusRepository.getOne(Wrappers.<DeviceSystemStatus>lambdaQuery().eq(DeviceSystemStatus::getDeviceId, deviceId).last("limit 1"));
        if (deviceSystemStatus == null) {
            return Collections.emptyList();
        }
        return JsonUtil.fromJson(deviceSystemStatus.getCpu(), new TypeReference<List<SystemStatusCpuResp>>() {
        });
    }

    /**
     * 获取监测器设备备注
     *
     * @param deviceId 设备ID
     * @return 监测器设备备注
     */
    @Override
    public String getRemarksDevice(String deviceId) {
        DeviceInfo deviceInfo = deviceInfoRepository.getById(deviceId);
        if (deviceInfo == null) {
            throw new BaseBusinessException("设备不存在");
        }
        return deviceInfo.getLocalRemarks();
    }

    /**
     * 更新监测器设备备注
     *
     * @param deviceBaseInfo 设备基本信息
     */
    @Override
    public void updateRemarksDevice(DeviceRemarkEditReq deviceBaseInfo) {
        DeviceInfo deviceInfo = deviceInfoRepository.getById(deviceBaseInfo.getDeviceId());
        if (deviceInfo == null) {
            throw new BaseBusinessException("设备不存在");
        }

        DeviceInfo updateDevice = new DeviceInfo();
        updateDevice.setDeviceId(deviceBaseInfo.getDeviceId());
        updateDevice.setLocalRemarks(deviceBaseInfo.getLocalRemarks());
        deviceInfoRepository.updateById(updateDevice);

    }

    @Override
    public List<DetectorListResp> detectorList(DetectorListReq req) {
        LambdaQueryWrapper<DeviceInfo> query = new LambdaQueryWrapper<>();
        query.like(CharSequenceUtil.isNotBlank(req.getOrgans()), DeviceInfo::getOrgans, req.getOrgans());
        query.eq(CharSequenceUtil.isNotBlank(req.getAddressCode()), DeviceInfo::getAddressCode, req.getAddressCode());

        List<DeviceInfo> list = deviceInfoRepository.list(query);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(DetectorListResp::from).collect(Collectors.toList());
    }
}
