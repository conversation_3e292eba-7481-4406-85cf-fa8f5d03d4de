package com.superred.supervisor.manager.model.vo.system.user;

import lombok.Data;

/**
 * SysUkeyVO.
 * ukey信息.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-21 14:03
 **/
@Data
public class SysUkeyResp {

    /**
     * id.
     **/
    private Integer id;

    /**
     * 序列号.
     **/
    private String serialNumber;

    /**
     * key类型.
     **/
    private Integer keyType;

    /**
     * 是否绑定.
     **/
    private Integer bind;

    /**
     * 用户名.
     **/
    private String username;

    /**
     * 真实姓名.
     **/
    private String realName;

    /**
     * 用户ID.
     **/
    private Integer userId;

    /**
     * 创建时间.
     **/
    private String createTime;
}
