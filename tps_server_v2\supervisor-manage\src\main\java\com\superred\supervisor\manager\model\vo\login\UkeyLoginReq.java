package com.superred.supervisor.manager.model.vo.login;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * ukey 登录请求
 *
 * <AUTHOR>
 * @since 2025/03/11
 */
@Data

public class UkeyLoginReq {

    /**
     * uid
     */
    @Schema(description = "序列号")
    private String serialNumber;

    /**
     * 签名值
     */
    @Schema(description = "签名值")
    private String sign;

    /**
     * 签名前的值 随机数
     */
    @Schema(description = "签名前的值 随机数")
    private String randomNumber;


    @Schema(description = "用户密码")
    private String password;


    @Schema(description = "图片验证码")
    private String imageCode;
}
