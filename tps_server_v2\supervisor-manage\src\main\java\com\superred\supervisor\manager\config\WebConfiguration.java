package com.superred.supervisor.manager.config;

import com.superred.supervisor.manager.config.intercepter.AuthenticationInterceptor;
import com.superred.supervisor.manager.config.intercepter.LicenseAuthInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;


/**
 * springmvc web 配置
 *
 * <AUTHOR>
 * @since 2023/12/12
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Resource
    private AuthenticationInterceptor authenticationInterceptor;

    @Resource
    private LicenseAuthInterceptor licenseAuthInterceptor;

//    @Resource
//    private IpWhiteListInterceptor ipWhiteListInterceptor;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")  // 允许所有路径
                .allowedOrigins("*") // 允许所有来源
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS") // 允许的请求方法
                .allowedHeaders("*") // 允许所有请求头
                .maxAge(3600); // 预检请求缓存 1 小时
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        authenticationInterceptor.setSingleLogin(false);
//        registry.addInterceptor(ipWhiteListInterceptor)
//                .addPathPatterns("/**");

        registry.addInterceptor(authenticationInterceptor)
                .addPathPatterns("/**");

        registry.addInterceptor(licenseAuthInterceptor)
                .addPathPatterns("/**");
    }
}
