package com.superred.supervision.base.vo.device;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 系统工作异常状态
 * <AUTHOR>
 * @since 2022/6/17 12:33
 **/
@Data
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SuspectedVo {
    /**
     * 异常类型
     */
    private Integer eventType;
    /**
     * 异常发生时间
     */
    private String time;
    /**
     * 告警级别
     */
    private String risk;
    /**
     * 异常事件描述
     */
    private String msg;
}
