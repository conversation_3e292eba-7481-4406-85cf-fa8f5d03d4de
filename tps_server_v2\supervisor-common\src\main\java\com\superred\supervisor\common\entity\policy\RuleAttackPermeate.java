package com.superred.supervisor.common.entity.policy;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 渗透攻击检测策略
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_rule_attack_permeate", autoResultMap = true)
public class RuleAttackPermeate extends Model<RuleAttackPermeate> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "rule_id")
    private String ruleId;

    /**
     * 策略名称
     */
    private String ruleName;

    /**
     * 是否保存pcap，默认为1保存，2不保存
     */
    private String storePcap;

    /**
     * 攻击分类 1. 系统漏洞利用 2. 软件漏洞利用 3. 网络钓鱼 4. 网络扫描 5. 密码猜解 6. SQL注入 7. 溢出攻击 8. 代码执行 9. 非授权访问/权限绕过 10. 跨站脚本攻击（XSS） 11. 跨站请求伪造（CSRF） 12. 目录遍历攻击 13. 文件利用 14. Webshell利用 15. Webshell上传 99. 其它
     */
    private String attackClass;

    /**
     * 攻击组织
     */
    private String attackGroup;

    /**
     * 策略内容
     */
    //@TableField(typeHandler = EncryptAndDecryptHandler.class)
    private String rule;

    /**
     * 渗透行为描述
     */
    @TableField(value = "`desc`")
    private String desc;

    /**
     * 渗透行为利用漏洞编号
     */
    private String cve;

    /**
     * 漏洞名称
     */
    private String vulnerability;

    /**
     * 告警级别，0（无风险）、1（一般级）、2（关注级）、3（严重级）、4（紧急级）。
     */
    private String risk;

    /**
     * 策略状态，0：无效，1：有效
     */
    private String status;

    /**
     * 是否共享状态，0是，1否
     */
    private String isShare;

    /**
     * 策略来源 1 本级 2上级
     */
    private String ruleSource;

    /**
     * 平台级别
     */
    private String level;

    /**
     * 策略更新时间
     */
    private String updateTime;

    /**
     * 策略创建时间
     */
    private String createTime;

    /**
     * 扩展字段1
     */
    private Long ext1;

    /**
     * 扩展字段2
     */
    private String ext2;

    /**
     * 扩展字段3
     */
    private String ext3;

    /**
     * 上级共享策略ID
     */
    private Long upRuleId;


}
