package com.superred.supervisor.manager.mapper.command;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.supervisor.common.entity.command.IssueAgent;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalCmdRecordPageReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdRecordPageResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface IssueAgentExtMapper extends BaseMapper<IssueAgent> {
    Page<TerminalCmdRecordPageResp> statisticsPage(Page<IssueAgent> page, @Param("query") TerminalCmdRecordPageReq req);
}
