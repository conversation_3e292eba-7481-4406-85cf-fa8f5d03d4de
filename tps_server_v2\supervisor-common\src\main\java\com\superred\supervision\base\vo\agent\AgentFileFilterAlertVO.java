package com.superred.supervision.base.vo.agent;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

/**
 * AgentKeywordAlertVO.
 * 终端检测组件监测数据上报.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-29 13:54
 */
@Data
@ToString
public class AgentFileFilterAlertVO {

    @JsonProperty("src_device")
    private String srcDevice;

    /**
     * 告警ID.
     **/
    @JsonProperty("id")
    private String id;

    /**
     * 告警时间.
     **/
    @JsonProperty("time")
    private String time;

    /**
     * 命中策略 ID.
     **/
    @JsonProperty("rule_id")
    private String ruleId;

    /**
     * 命中策略 ID.
     **/
    @JsonProperty("rule_desc")
    private String ruleDesc;


    /**
     * 策略类型 B.2.3 1 关键词 2 账号 3 加密文件 4 文件md5 5 电子文件涉密标志 6 密级标志
     **/
    @JsonProperty("filter_type")
    private Integer filterType;


    /**
     * 告警文件摘要
     **/
    @JsonProperty("file_summary")
    private String fileSummary;

    /**
     * 告警类型. 0未知 1重命名 2剪切 3删除 4恢复 5Win7删除 6 彻底删除 7 复制文件 8 本地扫描 9创建文件 10文件打开 11 本地文件拷贝到移动介质 12 移动介质拷贝到本地 13 移动介质拷贝到移动介质
     * 14 本地文件剪切到移动介质 15 移动介质剪切到移动介质 17文件打印 18 文件刻录
     *
     **/
    @JsonProperty("alert_type")
    private Integer alertType;


    /**
     * 告警文件MD5.
     **/
    @JsonProperty("file_md5")
    private String fileMd5;

    /**
     * 告警文件路径.
     **/
    @JsonProperty("file_path")
    private String filePath;

    /**
     * 告警文件文件名.
     **/
    @JsonProperty("filename")
    private String filename;


    /**
     * 告警文件文件大小.
     **/
    @JsonProperty("filesize")
    private Double filesize;


    /**
     *  告警信息描述 即为命中的关键词.
     **/
    @JsonProperty("file_desc")
    private String fileDesc;


    /**
     * 单位名称. 部门的根节点
     **/
    @JsonProperty("company")
    private String company;

    /**
     * 主机名称.
     **/
    @JsonProperty("computer_name")
    private String computerName;

    /**
     * 组织机构 id.
     **/
    @JsonProperty("org_id")
    private String orgId;

    /**
     * 组织机构全路径.
     **/
    @JsonProperty("org_path")
    private String orgPath;

    /**
     * 责任人.
     **/
    @JsonProperty("user_name")
    private String userName;

    /**
     * 责任人 ID.
     **/
    @JsonProperty("user_id")
    private String userId;


    /**
     * 文件密级标志
     **/
    @JsonProperty("file_secret_level")
    private Integer fileSecretLevel;


    /**
     * 拓展字段，json 格式.
     **/
    @JsonProperty("extended_fields")
    private Map<String, Object> extendedFields;


}
