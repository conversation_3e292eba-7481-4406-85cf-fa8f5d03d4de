package com.superred.supervision.base.vo.data;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

/**
 * AgentPolicyRepVO.
 * 策略响应.
 * <AUTHOR>
 * @since 2023-08-28 20:35
 * @version 1.0
 **/
@Data
@JsonInclude(NON_NULL)
public class AgentPolicyRepVO {


    /**
     * 上报时间.
     **/
    private String time;

    /**
     * 指令类型
     **/
    private String type;

    /**
     * 指令名称
     **/
    private String cmd;

    /**
     * 策略类型.
     **/
    private String module;

    /**
     * 版本.
     **/
    private String version;

    /**
     * 成功执行的策略ID列表
     **/
    private List<Long> success;

    /**
     * 未成功执行的策略对象列表.
     **/
    private List<Object> fail;
}
