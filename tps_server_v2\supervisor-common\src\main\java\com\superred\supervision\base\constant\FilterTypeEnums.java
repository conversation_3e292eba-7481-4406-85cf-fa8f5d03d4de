package com.superred.supervision.base.constant;

public enum FilterTypeEnums {
    KEYWORD_FILTER(1, Constants.MODULE_FILE_SELECTION_KEYWORD, "关键词筛选"),
    ACCOUNT_FILTER(2, Constants.MODULE_FILE_SELECTION_ACCOUNT, "账号筛选"),
    ENCRYPTION_FILTER(3, Constants.MODULE_FILE_SELECTION_ENCRYPTION, "加密文件筛选"),
    MD5_FILTER(4, Constants.MODULE_FILE_MD5_FILTER, "文件MD5筛选"),
    SECURITY_CLASSIFICATION_LEVEL(5, Constants.MODULE_FILE_SECURITY_CLASSIFICATION_LEVEL, "电子文件涉密标志"),
    SECRET_LEVEL_FILTER(6, Constants.MODULE_FILE_SECRET_LEVEL_FILTER, "密级标志文件筛选"),
    OHTER(99, "", "其他筛管类型");

    private final int key;

    private final String code;
    private final String value;

    FilterTypeEnums(int key, String code, String value) {
        this.key = key;
        this.code = code;
        this.value = value;
    }

    // 根据code获取对应的key
    public static Integer getKeyByCode(String code) {
        for (FilterTypeEnums filterType : FilterTypeEnums.values()) {
            if (filterType.getCode().equals(code)) {
                return filterType.getKey();
            }
        }
        return OHTER.getKey(); // 如果没有找到匹配的code
    }

    // Getter方法
    public int getKey() {
        return key;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
