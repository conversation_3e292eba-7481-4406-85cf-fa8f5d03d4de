package com.superred.supervisor.manager.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.superred.common.core.model.R;
import com.superred.common.core.model.RPage;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.common.BatchIdsReq;
import com.superred.supervisor.manager.model.vo.system.staff.StaffAddReq;
import com.superred.supervisor.manager.model.vo.system.staff.StaffExportReq;
import com.superred.supervisor.manager.model.vo.system.staff.StaffInfoResp;
import com.superred.supervisor.manager.model.vo.system.staff.StaffPageReq;
import com.superred.supervisor.manager.model.vo.system.staff.StaffPageResp;
import com.superred.supervisor.manager.service.system.StaffService;
import com.superred.supervisor.manager.utils.FileDownloadUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * 终端人员管理
 *
 * <AUTHOR>
 * @since 2025/3/20 15:06
 */
@Slf4j
@Tag(name = "1.10 终端人员管理")
@RestController
@AllArgsConstructor
@RequestMapping("/staff")
public class AgentStaffController {
    @Resource
    private StaffService staffService;


    @Operation(summary = "1. 查询人员分页列表")
    @PostMapping("/page")
    public RPage<StaffPageResp> getStaffList(@Valid @RequestBody StaffPageReq req) {
        IPage<StaffPageResp> result = this.staffService.getStaffList(req);
        return new RPage<>(result);
    }


    @Operation(summary = "2. 查询人员详情")
    @GetMapping("/info/{id}")
    public R<StaffInfoResp> get(@PathVariable("id") Integer id) {
        StaffInfoResp staffVO = this.staffService.get(id);
        return R.success(staffVO);
    }

    @Operation(summary = "3. 新增人员")
    @PostMapping("/add")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_PERSONNEL_MANAGEMENT, operateType = OperateTypeConstants.ADD, desc = "新增终端人员")
    public R<Integer> add(@Validated @RequestBody StaffAddReq staff) {
        Integer id = this.staffService.add(staff);
        return R.success(id);
    }

    @Operation(summary = "4. 编辑人员")
    @PostMapping("/edit/{id}")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_PERSONNEL_MANAGEMENT, operateType = OperateTypeConstants.MODIFY, desc = "编辑终端人员")
    public R<Boolean> edit(@Validated @RequestBody StaffAddReq staff, @PathVariable("id") Integer id) {
        this.staffService.edit(staff, id);
        return R.success(true);
    }

    @Operation(summary = "5. 删除人员")
    @PostMapping("/del")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_PERSONNEL_MANAGEMENT, operateType = OperateTypeConstants.DELETE, desc = "删除终端人员")
    public R<Boolean> del(@Valid @RequestBody BatchIdsReq deleteStaffDTO) {
        this.staffService.del(deleteStaffDTO);
        return R.success(true);
    }


    @Operation(summary = "6. 人员导出")
    @PostMapping("/export")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_PERSONNEL_MANAGEMENT, operateType = OperateTypeConstants.EXPORT, desc = "导出终端人员")
    public void export(HttpServletResponse response, @RequestBody StaffExportReq queryStaffDTO) {
//        this.staffService.export(response, queryStaffDTO);
    }

    @Operation(summary = "7. 下载模板")
    @PostMapping("/download")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_PERSONNEL_MANAGEMENT, operateType = OperateTypeConstants.DOWNLOAD, desc = "终端人员下载模板")
    public void download(HttpServletResponse response) {
        FileDownloadUtils.downloadClassPathFile(response, "template/人员模板.xlsx", "终端人员导入模板.xlsx");
    }


    @Operation(summary = "8. 人员导入")
    @PostMapping("/import")
    @SysLogAnn(module = LogTypeConstants.TERMINAL_PERSONNEL_MANAGEMENT, operateType = OperateTypeConstants.IMPORT, desc = "导入终端人员")
    public R<Boolean> importStaffNew(@RequestParam("file") MultipartFile file) throws IOException {

        return R.success(true);
    }

}
