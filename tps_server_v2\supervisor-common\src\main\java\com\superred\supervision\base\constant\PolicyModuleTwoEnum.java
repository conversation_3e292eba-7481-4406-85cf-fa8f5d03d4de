package com.superred.supervision.base.constant;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2025-03-25 14:47
 */
@Getter
public enum PolicyModuleTwoEnum {
    // 攻击窃密检测 alarm
    TROJAN("trojan", "木马活动检测策略", PolicyModuleOneEnum.ALARM, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 1),
    ATTACK("attack", "渗透行为检测策略", PolicyModuleOneEnum.ALARM, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 2),
    MALWARE("malware", "恶意文件检测策略", PolicyModuleOneEnum.ALARM, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 3),
    BLACKLIST("blacklist", "黑名单检测策略", PolicyModuleOneEnum.ALARM, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey(), PolicyDeviceTypeEnum.AGENT.getKey()), 4),
    //ABNORMAL("abnormal", "异常行为检测", PolicyModuleOneEnum.ALARM, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 5),
    //ALARM_WHITELIST("alarm_whitelist", "白名单过滤策略", PolicyModuleOneEnum.ALARM, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 6),

    // 文件筛选 file_filter
    KEYWORD_FILTER("keyword_filter", "关键词筛选策略", PolicyModuleOneEnum.FILE_FILTER, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey(), PolicyDeviceTypeEnum.AGENT.getKey()), 1),
    ACCOUNT_FILTER("account_filter", "账号筛选策略", PolicyModuleOneEnum.FILE_FILTER, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 2),
    //ENCRYPTION_FILTER("encryption_filter", "加密文件筛选策略", PolicyModuleOneEnum.FILE_FILTER, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 3),
    MD5_FILTER("md5_filter", "文件MD5筛选策略", PolicyModuleOneEnum.FILE_FILTER, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey(), PolicyDeviceTypeEnum.AGENT.getKey()), 4),

    // 网络行为审计 net_audit
    //NET_LOG("net_log", "通联关系审计策略", PolicyModuleOneEnum.NET_AUDIT, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 1),
    APP_BEHAVIOR("app_behavior", "应用行为审计策略", PolicyModuleOneEnum.NET_AUDIT, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 2);
    //AUDIT_ABNORMAL_WHITELIST("audit_abnormal_whitelist", "异常行为审计策略", PolicyModuleOneEnum.NET_AUDIT, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 3),
    //AUDIT_WHITELIST("audit_whitelist", "审计白名单策略", PolicyModuleOneEnum.NET_AUDIT, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 4),

    // 目标流量审计 object_listen
    //IP_LISTEN("ip_listen", "IP审计", PolicyModuleOneEnum.OBJECT_LISTEN, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 1),
    //DOMAIN_LISTEN("domain_listen", "域名审计", PolicyModuleOneEnum.OBJECT_LISTEN, Arrays.asList(PolicyDeviceTypeEnum.JCQ.getKey()), 2);

    private final String key;
    private final String value;
    private final PolicyModuleOneEnum parent;
    private final List<String> deviceType;
    private final Integer sort;

    PolicyModuleTwoEnum(String key, String value, PolicyModuleOneEnum parent, List<String> deviceType, Integer sort) {
        this.key = key;
        this.value = value;
        this.parent = parent;
        this.deviceType = deviceType;
        this.sort = sort;
    }

    public static List<PolicyModuleTwoEnum> getTwoByParent(PolicyModuleOneEnum parent, String deviceType) {
        List<PolicyModuleTwoEnum> list = new ArrayList<>();
        for (PolicyModuleTwoEnum value : PolicyModuleTwoEnum.values()) {
            if (Objects.equals(parent, value.getParent()) && value.deviceType.contains(deviceType)) {
                list.add(value);
            }
        }
        return list;
    }

}
