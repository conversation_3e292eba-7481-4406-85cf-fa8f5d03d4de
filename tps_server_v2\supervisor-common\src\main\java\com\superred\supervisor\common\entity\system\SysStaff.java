package com.superred.supervisor.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.superred.supervisor.common.mybatis.EncryptAndDecryptHandler;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 人员表 实体
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:22
 */
@Data
@TableName(value = "p_sys_staff", autoResultMap = true)
public class SysStaff {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 人员姓名
     */
    @TableField("staff_name")
    private String staffName;

    /**
     * 身份证号
     */
    @TableField(value = "id_card_num", typeHandler = EncryptAndDecryptHandler.class)
    private String idCardNum;

    /**
     * 所属单位id
     */
    @TableField("unit_id")
    private Integer unitId;

    /**
     * 所属部门id
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 联系方式
     */
    @TableField("contact_info")
    private String contactInfo;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modified_time")
    private LocalDateTime modifiedTime;

}

