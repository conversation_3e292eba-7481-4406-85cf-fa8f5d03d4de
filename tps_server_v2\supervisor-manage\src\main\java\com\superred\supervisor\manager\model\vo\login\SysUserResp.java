package com.superred.supervisor.manager.model.vo.login;

import com.superred.supervisor.common.entity.system.SysUser;
import com.superred.supervisor.common.entity.system.enums.UserLockStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 系统用户响应
 *
 * <AUTHOR>
 * @since 2025/03/14
 */
@Data

public class SysUserResp implements Serializable {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Integer id;

    /**
     * 账号
     */
    @Schema(description = "账号")
    private String username;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String realName;


    /**
     * 组织ID
     */
    @Schema(description = "组织 部门ID")
    private Integer orgId;

    /**
     * 锁定时间
     */
    @Schema(description = "锁定时间")
    private LocalDateTime lockTime;

    /**
     * 上次登录时间
     */
    @Schema(description = "上次登录时间")
    private LocalDateTime lastLoginTime;

    /**
     * 尝试次数
     */
    @Schema(description = "尝试次数")
    private Integer tryCount;

    /**
     * 锁定状态(1-正常，2-锁定)
     */
    @Schema(description = "锁定状态(1-正常，2-锁定)")
    private UserLockStatus lockFlag;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime modifiedTime;


    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remarks;

    /**
     * 密码修改时间
     */
    @Schema(description = "密码修改时间")
    private LocalDateTime passUpdateTime;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号")
    private String card;

    /**
     * 是否显示用户信息
     */
    @Schema(description = "是否显示用户信息")
    private Integer isShow;

    /**
     * 启用状态(1-启用，2-停用)
     */
    @Schema(description = "启用状态(1-启用，2-停用)")
    private UserLockStatus enable;

    /**
     * 密级
     * 1：一般涉密
     * 2：重要涉密
     * 3：核心涉密
     */
    @Schema(description = "密级")
    private Integer secret;

    public static SysUserResp fromSysUser(SysUser sysUser) {
        SysUserResp sysUserResp = new SysUserResp();
        sysUserResp.setId(sysUser.getId());
        sysUserResp.setUsername(sysUser.getUsername());
        sysUserResp.setRealName(sysUser.getRealName());
        sysUserResp.setOrgId(sysUser.getOrgId());
        sysUserResp.setLockTime(sysUser.getLockTime());
        sysUserResp.setLastLoginTime(sysUser.getLastLoginTime());
        sysUserResp.setTryCount(sysUser.getTryCount());
        sysUserResp.setLockFlag(sysUser.getLockFlag());
        sysUserResp.setCreateTime(sysUser.getCreateTime());
        sysUserResp.setModifiedTime(sysUser.getModifiedTime());
        sysUserResp.setRemarks(sysUser.getRemarks());
        sysUserResp.setPassUpdateTime(sysUser.getPassUpdateTime());
        sysUserResp.setCard(sysUser.getCard());
        sysUserResp.setIsShow(sysUser.getIsShow());
        sysUserResp.setEnable(sysUser.getEnable());
        sysUserResp.setSecret(sysUser.getSecret());
        return sysUserResp;

    }
}
