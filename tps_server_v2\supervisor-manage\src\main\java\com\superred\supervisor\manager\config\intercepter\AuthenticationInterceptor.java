package com.superred.supervisor.manager.config.intercepter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.superred.common.core.exception.UnAuthorizedException;
import com.superred.common.core.utils.WebUtils;
import com.superred.supervisor.manager.annotation.role.IgnoreAuth;
import com.superred.supervisor.manager.model.auth.LoginUser;
import com.superred.supervisor.manager.service.CacheService;
import lombok.Setter;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Set;

import static com.superred.common.core.constant.CoreConstant.TOKEN_PERFIX;
import static com.superred.supervisor.manager.constant.CommonConstants.CHECK_TOKEN_URL;


/**
 * 默认登录权限拦截器
 *
 * <AUTHOR>
 * @since 2018/10/8
 */
@Setter
@Component
public class AuthenticationInterceptor implements HandlerInterceptor {

    /**
     * 是否需要单设备登录
     */
    private boolean isSingleLogin;


    private AntPathMatcher antPathMatcher = new AntPathMatcher();


    private Set<String> byPassUrl;


    @Resource
    private CacheService cacheService;


    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object object) {
        return doHandle(httpServletRequest, object);
    }

    /**
     * 登录校验
     *
     * @param httpServletRequest
     * @param object
     * @return
     */
    private boolean doHandle(HttpServletRequest httpServletRequest, Object object) {
        if (WebUtils.isAuthIgnoredUrl()) {
            return true;
        }
        if (!(object instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) object;
        if (ignoreAuth(handlerMethod)) {
            return true;
        }
        if (bypassUrl(httpServletRequest.getRequestURI())) {
            return true;
        }
        //
        // 开始校验
        //
        String token = httpServletRequest.getHeader(HttpHeaders.AUTHORIZATION);
        if (StrUtil.isBlank(token) || token.length() < TOKEN_PERFIX.length()) {
            token = httpServletRequest.getParameter(HttpHeaders.AUTHORIZATION);
        }
        if (StrUtil.isEmpty(token)) {
            throw new UnAuthorizedException("请登录");
        }
        token = StrUtil.sub(token, TOKEN_PERFIX.length(), token.length());

        LoginUser loginUser = cacheService.getLoginUser(token);
        if (loginUser == null) {
            throw new UnAuthorizedException("用户登录超时请重新登录");
        }
        checkSingleDevice(String.valueOf(loginUser.getUserId()), token);
        refreshToken(token, loginUser, httpServletRequest.getRequestURI());

        return true;
    }


    private boolean ignoreAuth(HandlerMethod handlerMethod) {
        Class<?> beanType = handlerMethod.getBeanType();

        IgnoreAuth ignoreAuth = AnnotationUtils.findAnnotation(beanType, IgnoreAuth.class);
        if (ignoreAuth != null) {
            return true;
        }
        Method method = handlerMethod.getMethod();
        return AnnotationUtils.findAnnotation(method, IgnoreAuth.class) != null;
    }

    private void refreshToken(String token, LoginUser loginUser, String requestURI) {
        if (antPathMatcher.match(CHECK_TOKEN_URL, requestURI)) {
            return;
        }
        cacheService.refreshToken(token, loginUser);
    }

    private boolean bypassUrl(String requestURI) {
        if (CollectionUtil.isEmpty(byPassUrl)) {
            return false;
        }
        for (String url : byPassUrl) {
            if (antPathMatcher.match(url, requestURI)) {
                return true;
            }
        }
        return false;
    }

    //    /**
    //     * 校验方法的权限
    //     *
    //     * @param userId 用户id
    //     * @param method 方法
    //     */
    //    private void checkMethodPermission(String userId, Method method) {
    //        if (!method.isAnnotationPresent(Permission.class) || !isCheckPermission) {
    //            return;
    //        }
    //        Permission permission = method.getAnnotation(Permission.class);
    //        Set<String> permissions = cacheService.getUserPermission(userId);
    //        if (permissions == null) {
    //            throw new UnAuthorizedException("用户权限失效，请重新登录");
    //        }
    //        String onePermission = permission.value();
    //        if (!permissions.contains(onePermission)) {
    //            throw new UnAuthorizedException("对不起，您没有[" + onePermission + "]的权限");
    //        }
    //    }


    /**
     * 校验该token 是否为单设备的token
     *
     * @param token 用户token
     */
    private void checkSingleDevice(String userId, String token) {
        if (!isSingleLogin) {
            return;
        }
        String loginToken = cacheService.getLoginToken(userId);
        if (StrUtil.isEmpty(loginToken)) {
            throw new UnAuthorizedException("用户登录失效请重新登录");
        }
        if (!StrUtil.equals(loginToken, token)) {
            throw new UnAuthorizedException("当前登录人已在另一客户端登录");
        }

    }


    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) {
        // do nothing
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) {
        // do nothing
    }


}
