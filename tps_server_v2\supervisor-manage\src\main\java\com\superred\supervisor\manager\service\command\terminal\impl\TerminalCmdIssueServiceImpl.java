package com.superred.supervisor.manager.service.command.terminal.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.entity.command.AgentSoftware;
import com.superred.supervisor.common.entity.command.UpdateFile;
import com.superred.supervisor.common.entity.operation.enums.OperationExecStatus;
import com.superred.supervisor.common.entity.operation.terminal.TerminalCommand;
import com.superred.supervisor.common.entity.operation.terminal.TerminalOperation;
import com.superred.supervisor.common.entity.operation.terminal.TerminalOperationExec;
import com.superred.supervisor.common.entity.terminal.TerminalAgentInfo;
import com.superred.supervisor.common.repository.command.AgentSoftwareRepository;
import com.superred.supervisor.common.repository.command.UpdateFileRepository;
import com.superred.supervisor.common.repository.operation.terminal.TerminalOperationExecRepository;
import com.superred.supervisor.common.repository.operation.terminal.TerminalOperationRepository;
import com.superred.supervisor.manager.common.enums.command.CommandType;
import com.superred.supervisor.manager.model.vo.command.IssueModuleCmd;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageReq;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdExecPageResp;
import com.superred.supervisor.manager.model.vo.command.TerminalCmdRecordPageResp;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalCmdRecordPageReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalInnerPolicyUpdateCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalModuleSwitchCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalSoftUpgradeCmdReq;
import com.superred.supervisor.manager.model.vo.command.terminal.TerminalUninstallCmdReq;
import com.superred.supervisor.manager.repository.operation.TerminalCommandExtRepository;
import com.superred.supervisor.manager.repository.terminal.TerminalAgentInfoExtRepository;
import com.superred.supervisor.manager.service.CmdIdBuilder;
import com.superred.supervisor.manager.service.command.terminal.TerminalCmdIssueService;
import com.superred.supervisor.manager.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/7/24 17:45
 */
@Service
public class TerminalCmdIssueServiceImpl implements TerminalCmdIssueService {


    @Resource
    private TerminalCommandExtRepository terminalCommandExtRepository;

    @Resource
    private TerminalOperationRepository terminalOperationRepository;

    @Resource
    private TerminalOperationExecRepository terminalOperationExecRepository;

    @Resource
    private AgentSoftwareRepository agentSoftwareRepository;
    @Resource
    private TerminalAgentInfoExtRepository terminalAgentInfoExtRepository;
    @Resource
    private CmdIdBuilder cmdIdBuilder;
    @Resource
    private UpdateFileRepository updateFileRepository;


    /**
     * 终端软升级指令下发
     *
     * @param req 终端软升级请求参数
     * @return 下发结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String softUpdate(TerminalSoftUpgradeCmdReq req) {
        AgentSoftware agentSoftware = agentSoftwareRepository.getById(req.getId());
        if (agentSoftware == null) {
            throw new BaseBusinessException("升级包不存在");
        }
        List<TerminalAgentInfo> terminalAgentInfos =
                terminalAgentInfoExtRepository.searchActiveTerminalByRangeReq(req, true);

        Set<String> unMatchDevices = terminalAgentInfos.stream().filter(one -> {
            // 检查操作系统、CPU架构与升级包是否一致
            boolean osMatch = one.getOs().equalsIgnoreCase(agentSoftware.getOs());
            boolean cpuMatch = one.getArch().equalsIgnoreCase(agentSoftware.getCpuArchitecture());
            return !(osMatch && cpuMatch);
        }).map(TerminalAgentInfo::getDeviceId).collect(Collectors.toSet());

        if (CollUtil.isNotEmpty(unMatchDevices)) {
            throw new BaseBusinessException("当前存在勾选终端的操作系统、CPU架构、与升级包不一致：" + unMatchDevices);
        }

        CommandType.UpdateCmdParam param = CommandType.UpdateCmdParam.builder()
                .filename(agentSoftware.getPacketName())
                .md5(agentSoftware.getMd5())
                .softVersion(agentSoftware.getVersion())
                .build();

        String cmdId = cmdIdBuilder.buildCmdId();
        TerminalCommand terminalCommand = new TerminalCommand();
        terminalCommand.setCmdCode(cmdId);
        terminalCommand.setCommandName(CommandType.UPDATE.getName());
        terminalCommand.setCommandType(CommandType.UPDATE.getValue());
        terminalCommand.setCommandParams(JsonUtil.toJson(param));
        terminalCommand.setCreatorId(SecurityUtils.getUserIdStr());
        terminalCommand.setCreateTime(LocalDateTime.now());

        TerminalOperation terminalOperation = terminalCommand.toOperation();

        List<TerminalOperationExec> operationExecs = this.buildOperationExecList(terminalOperation, terminalAgentInfos);

        terminalCommandExtRepository.save(terminalCommand);
        terminalOperationRepository.save(terminalOperation);
        terminalOperationExecRepository.saveBatch(operationExecs);

        return cmdId;
    }

    private List<TerminalOperationExec> buildOperationExecList(TerminalOperation terminalOperation,
            List<TerminalAgentInfo> terminalAgentInfos) {

        List<TerminalOperationExec> operationExecs = new ArrayList<>();
        for (TerminalAgentInfo terminalAgentInfo : terminalAgentInfos) {
            TerminalOperationExec operationExec = new TerminalOperationExec();
            operationExec.setOperationId(terminalOperation.getId());
            operationExec.setRefId(terminalOperation.getRefId());
            operationExec.setOperationType(terminalOperation.getOperationType());
            operationExec.setVersion(terminalOperation.getVersion());
            operationExec.setDeviceId(terminalAgentInfo.getDeviceId());
            operationExec.setExecStatus(OperationExecStatus.NOT_ISSUED);
            operationExec.setCreateTime(LocalDateTime.now());
            operationExecs.add(operationExec);
        }
        return operationExecs;
    }

    /**
     * 终端内置策略更新指令下发
     *
     * @param req 终端内置策略更新请求参数
     * @return 下发结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String innerPolicyUpdate(TerminalInnerPolicyUpdateCmdReq req) {
        UpdateFile updateFile = updateFileRepository.getById(req.getId());
        if (updateFile == null) {
            throw new BaseBusinessException("查询内置策略信息为空错误");
        }
        List<TerminalAgentInfo> terminalAgentInfos =
                terminalAgentInfoExtRepository.searchActiveTerminalByRangeReq(req, true);

        CommandType.InnerPolicyUpdateParam param = CommandType.InnerPolicyUpdateParam.builder()
                .filename(updateFile.getFilename())
                .md5(updateFile.getMd5())
                .build();


        String cmdId = cmdIdBuilder.buildCmdId();
        TerminalCommand terminalCommand = new TerminalCommand();
        terminalCommand.setCmdCode(cmdId);
        terminalCommand.setCommandName(CommandType.INNER_POLICY_UPDATE.getName());
        terminalCommand.setCommandType(CommandType.INNER_POLICY_UPDATE.getValue());
        terminalCommand.setCommandParams(JsonUtil.toJson(param));
        terminalCommand.setCreatorId(SecurityUtils.getUserIdStr());
        terminalCommand.setCreateTime(LocalDateTime.now());

        TerminalOperation terminalOperation = terminalCommand.toOperation();
        List<TerminalOperationExec> operationExecs = this.buildOperationExecList(terminalOperation, terminalAgentInfos);

        terminalCommandExtRepository.save(terminalCommand);
        terminalOperationRepository.save(terminalOperation);
        terminalOperationExecRepository.saveBatch(operationExecs);
        return cmdId;

    }

    /**
     * 终端卸载指令下发
     *
     * @param req 终端卸载请求参数
     * @return 下发结果
     */
    @Override
    public String uninstall(TerminalUninstallCmdReq req) {
        throw new UnsupportedOperationException("终端卸载指令下发功能尚未实现");
    }


    /**
     * 终端模块开关指令下发
     *
     * @param req 终端模块开关请求参数
     * @return 下发结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String moduleSwitch(TerminalModuleSwitchCmdReq req) {
        List<TerminalAgentInfo> terminalAgentInfos =
                terminalAgentInfoExtRepository.searchActiveTerminalByRangeReq(req, true);

        String cmdId = cmdIdBuilder.buildCmdId();

        String cmd = req.getModules().stream().findFirst().orElse(new IssueModuleCmd()).getCmd();
        List<String> moduleList = req.getModules().stream().map(IssueModuleCmd::getModule).collect(Collectors.toList());
        CommandType cmdTypeByValue = CommandType.getCmdTypeByValue(cmd);

        TerminalCommand terminalCommand = new TerminalCommand();
        terminalCommand.setCmdCode(cmdId);
        terminalCommand.setCommandName(cmdTypeByValue.getName());
        terminalCommand.setCommandType(cmdTypeByValue.getValue());
        terminalCommand.setCommandParams(null);
        terminalCommand.setCreatorId(SecurityUtils.getUserIdStr());
        terminalCommand.setCreateTime(LocalDateTime.now());
        terminalCommand.setModule(JsonUtil.toJson(moduleList));

        List<TerminalOperation> terminalOperations = req.getModules().stream().map(one -> {

            TerminalOperation terminalOperation = terminalCommand.toOperation();
            terminalOperation.setModule(one.getModule());
            terminalOperation.setSubmodule(JsonUtil.toJson(one.getSubmodules()));
            terminalOperation.setCmd(one.getCmd());
            return terminalOperation;

        }).collect(Collectors.toList());

        List<TerminalOperationExec> operationExecs = new ArrayList<>();
        terminalOperations.forEach(terminalOperation -> {
            List<TerminalOperationExec> execs = this.buildOperationExecList(terminalOperation, terminalAgentInfos);
            operationExecs.addAll(execs);
        });

        terminalCommandExtRepository.save(terminalCommand);
        terminalOperationRepository.saveBatch(terminalOperations);
        terminalOperationExecRepository.saveBatch(operationExecs);
        return cmdId;
    }

    /**
     * 终端指令下发记录
     *
     * @param req 查询请求参数
     * @return 分页结果
     */
    @Override
    public IPage<TerminalCmdRecordPageResp> recordPage(TerminalCmdRecordPageReq req) {
        LambdaQueryWrapper<TerminalCommand> queryWrapper = Wrappers.<TerminalCommand>lambdaQuery()
                .eq(StrUtil.isNotBlank(req.getCmd()), TerminalCommand::getCommandType, req.getCmd())
                .eq(StrUtil.isNotBlank(req.getCmdId()), TerminalCommand::getCmdCode, req.getCmdId())
                .orderByDesc(TerminalCommand::getCreateTime);

        Page<TerminalCommand> commandPage = terminalCommandExtRepository.page(new Page<>(req.getStart(), req.getLimit()), queryWrapper);

        if (commandPage.getRecords().isEmpty()) {
            return new Page<>(req.getStart(), req.getLimit(), 0);
        }
        List<String> cmdIdList = commandPage.getRecords().stream().map(TerminalCommand::getCmdCode).collect(Collectors.toList());
        List<TerminalCmdRecordPageResp> groupCountByCmdIds = terminalCommandExtRepository.getBaseMapper().groupCountByCmdIds(cmdIdList);
        Map<String, TerminalCmdRecordPageResp> countMap = groupCountByCmdIds.stream().collect(Collectors.toMap(TerminalCmdRecordPageResp::getCmdId, one -> one));


        return commandPage.convert(one -> {
            TerminalCmdRecordPageResp resp = new TerminalCmdRecordPageResp();
            resp.setCmdId(one.getCmdCode());
            resp.setCmdName(one.getCommandName());
            resp.setCreateTime(one.getCreateTime());

            TerminalCmdRecordPageResp countInfo = countMap.getOrDefault(one.getCmdCode(), new TerminalCmdRecordPageResp());
            resp.setDeviceCount(NumberUtil.nullToZero(countInfo.getDeviceCount()));
            resp.setSuccessCount(NumberUtil.nullToZero(countInfo.getSuccessCount()));
            resp.setFailCount(NumberUtil.nullToZero(countInfo.getFailCount()));
            resp.setUnIssueCount(NumberUtil.nullToZero(countInfo.getUnIssueCount()));
            resp.setIssueCount(NumberUtil.nullToZero(countInfo.getIssueCount()));

            return resp;
        });
    }

    /**
     * 终端指令执行详情
     *
     * @param req 查询请求参数
     * @return 分页结果
     */
    @Override
    public IPage<TerminalCmdExecPageResp> terminalExecPage(TerminalCmdExecPageReq req) {


        return terminalCommandExtRepository.getBaseMapper().terminalExecPage(
                new Page<>(req.getStart(), req.getLimit()), req
        );
    }
}
