package com.superred.supervisor.common.entity.policy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2025-03-26 15:52
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@TableName(value = "sys_issue_policy", autoResultMap = true)
public class IssuePolicy implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 设备编号
     */
    private String deviceId;

    /**
     * 命令类型
     */
    private String type;

    /**
     * 模块
     */
    private String module;

    /**
     * 策略版本号
     */
    private String version;

    /**
     * 命令具体类型
     */
    private String cmd;

    /**
     * 策略规则个数
     */
    private Integer num;

    /**
     * 策略Base64编码后规则内容
     */
    private String config;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 指令下发状态，0（未下发）、1（已下发）
     */
    private Integer status;

    /**
     * 下发时间
     */
    private Date issueTime;

    /**
     * 结果上报时间
     */
    private Date time;

    /**
     * 在检测器成功执行的规则ID列表
     */
    private String success;

    /**
     * 在检测器未成功执行的规则对象列表
     */
    private String fail;

    /**
     * 是否管理中心下发，0：否，1：是
     */
    private Integer isMgr;

    /**
     * 是否上报到管理中心，0：否，1：是
     */
    private Integer isReport;

    /**
     * 上报到管理中心时间
     */
    private Date reportTime;

    /**
     * 策略id
     */
    private Long policyId;

    /**
     * 平台类型 001 国家级 010 省级 011地市级 100 区县级
     */
    private String platformType;
}
