package com.superred.supervisor.common.repository.system;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.system.SysOrg;
import com.superred.supervisor.common.mapper.system.SysOrgMapper;
import org.springframework.stereotype.Repository;

/**
 * 组织机构表 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:21
 */
@Repository
public class SysOrgRepository extends ServiceImpl<SysOrgMapper, SysOrg> {

    public SysOrg getByName(String name) {
        return this.getOne(Wrappers.<SysOrg>lambdaQuery().eq(SysOrg::getName, name), false);
    }
}

