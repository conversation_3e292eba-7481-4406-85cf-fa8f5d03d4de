package com.superred.supervisor.manager.model.vo.terminal.bak;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervisor.common.entity.agent.AgentDeviceInfoBak;
import com.superred.supervisor.common.entity.devices.InterfaceVo;
import com.superred.supervisor.common.entity.system.SysOrg;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 *  设备报备导入导出信息
 * @since 2025/6/5 10:51
 */
@Data
public class AgentDeviceInfoBakExportVo {

    @ExcelProperty(value = "终端名称", order = 1)
    @NotBlank(message = "终端名称为必填项")
    private String deviceName;

    @ExcelProperty(value = "IP地址", order = 2)
    @NotBlank(message = "ip地址为必填项")
    private String ip;

    @ExcelProperty(value = "MAC地址", order = 3)
    @NotBlank(message = "MAC地址为必填项")
    private String mac;

    @ExcelProperty(value = "操作系统", order = 4)
    @NotBlank(message = "操作系统为必填项")
    private String os;

    @ExcelProperty(value = "所属单位", order = 5)
    private String company;

    @ExcelIgnore
    private Integer companyId;

    @ExcelProperty(value = "所属部门", order = 6)
    private String orgName;

    @ExcelIgnore
    private Integer orgId;

    @ExcelProperty(value = "终端软件版本号", order = 7)
    private String softVersion;

    @ExcelProperty(value = "报备时间", order = 8)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ExcelProperty(value = "CPU架构", order = 9)
    @NotBlank(message = "CPU架构为必填项")
    private String arch;

    @ExcelProperty(value = "行政区域", order = 10)
    private String address;

    @ExcelProperty(value = "行政区域编码", order = 11)
    private String addressCode;

    @ExcelProperty(value = "使用人编号", order = 12)
    @NotBlank(message = "使用人编号为必填项")
    private String userId;

    @ExcelProperty(value = "人员姓名", order = 13)
    @NotBlank(message = "人员姓名为必填项")
    private String userName;

    @ExcelProperty(value = "子网掩码", order = 14)
    @NotBlank(message = "子网掩码为必填项")
    private String netmask;

    @ExcelProperty(value = "网关地址", order = 15)
    @NotBlank(message = "网关地址为必填项")
    private String gateway;

    @ExcelProperty(value = "CPU信息", order = 16)
    @NotBlank(message = "CPU信息为必填项")
    private String cpuInfo;

    @ExcelProperty(value = "内存总数", order = 17)
    @NotNull(message = "内存信息为必填项")
    private Long memTotal;

    @ExcelProperty(value = "磁盘信息", order = 18)
    @NotBlank(message = "磁盘信息为必填项")
    private String diskInfo;

    @ExcelProperty(value = "备注信息", order = 19)
    private String memo;

    public static AgentDeviceInfoBakExportVo from(AgentDeviceInfoBak agentDeviceInfoBak, Map<Integer, SysOrg> sysOrgMap) {
        AgentDeviceInfoBakExportVo exportVo = new AgentDeviceInfoBakExportVo();
        exportVo.setDeviceName(agentDeviceInfoBak.getDeviceName());
        exportVo.setIp(agentDeviceInfoBak.getIp());
        exportVo.setMac(agentDeviceInfoBak.getMac());
        exportVo.setOs(agentDeviceInfoBak.getOs());
        exportVo.setCompany(agentDeviceInfoBak.getCompany());
        SysOrg sysOrg = sysOrgMap.get(agentDeviceInfoBak.getOrgId());
        exportVo.setOrgName(sysOrg != null ? sysOrg.getName() : "");
        exportVo.setSoftVersion(agentDeviceInfoBak.getSoftVersion());
        exportVo.setCreateTime(agentDeviceInfoBak.getCreateTime());
        exportVo.setArch(agentDeviceInfoBak.getArch());
        exportVo.setAddress(agentDeviceInfoBak.getAddress());
        exportVo.setAddressCode(agentDeviceInfoBak.getAddressCode());
        exportVo.setUserId(agentDeviceInfoBak.getUserId());
        exportVo.setUserName(agentDeviceInfoBak.getUserName());

        if (StringUtils.isNotEmpty(agentDeviceInfoBak.getInterfaceInfo())) {
            InterfaceVo interfaceVo = JsonUtil.fromJson(agentDeviceInfoBak.getInterfaceInfo(), InterfaceVo.class);
            exportVo.setNetmask(interfaceVo.getNetmask());
            exportVo.setGateway(interfaceVo.getGateway());
        }

        exportVo.setCpuInfo(agentDeviceInfoBak.getCpuInfo());
        exportVo.setMemTotal(agentDeviceInfoBak.getMemTotal());
        exportVo.setDiskInfo(agentDeviceInfoBak.getDiskInfo());
        exportVo.setMemo(agentDeviceInfoBak.getMemo());

        return exportVo;
    }

    public AgentDeviceInfoBak toDo() {
        AgentDeviceInfoBak agentDeviceInfoBak = new AgentDeviceInfoBak();
        agentDeviceInfoBak.setDeviceName(this.getDeviceName());
        agentDeviceInfoBak.setOs(this.getOs());
        agentDeviceInfoBak.setSoftVersion(this.getSoftVersion());
        agentDeviceInfoBak.setArch(this.getArch());
        agentDeviceInfoBak.setCompany(this.getCompany());
        agentDeviceInfoBak.setCompanyId(this.getCompanyId());
        agentDeviceInfoBak.setOrgId(this.getOrgId());
        agentDeviceInfoBak.setAddress(this.getAddress());
        agentDeviceInfoBak.setAddressCode(this.getAddressCode());
        agentDeviceInfoBak.setUserId(this.getUserId());
        agentDeviceInfoBak.setUserName(this.getUserName());

        InterfaceVo interfaceVo = new InterfaceVo();
        interfaceVo.setIp(this.getIp());
        interfaceVo.setNetmask(this.getNetmask());
        interfaceVo.setGateway(this.getGateway());
        interfaceVo.setMac(this.getMac());

        agentDeviceInfoBak.setInterfaceInfo(JsonUtil.toJson(interfaceVo));
        agentDeviceInfoBak.setMemTotal(this.getMemTotal());
        agentDeviceInfoBak.setCpuInfo(this.getCpuInfo());
        agentDeviceInfoBak.setDiskInfo(this.getDiskInfo());
        agentDeviceInfoBak.setMemo(this.getMemo());
        agentDeviceInfoBak.setIp(this.getIp());
        agentDeviceInfoBak.setMac(this.getMac());
        agentDeviceInfoBak.setCreateTime(this.getCreateTime());

        return agentDeviceInfoBak;
    }
}
