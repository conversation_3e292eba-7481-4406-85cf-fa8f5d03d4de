package com.superred.supervisor.manager.model.dto.policy.config;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.superred.supervisor.common.entity.policy.RuleFilterKeyword;
import com.superred.supervisor.manager.utils.PolicyUtils;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-27 21:14
 */
@Data
@Builder
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FilterKeywordPolicyConfigDTO {


    /**
     * 策略ID
     */
    private Long ruleId;

    /**
     * 策略内容
     */
    private String ruleContent;

    /**
     * 策略描述
     */
    private String ruleDesc;

    /**
     * 最少命中次数 不填时默认为1
     */
    private Integer minMatchCount;
    /**
     * 过滤文件类型 文档 1 图片 2 文本/网页 3 压缩包 4 邮件 5
     */
    private List<Integer> filterFileType;


    private FileFilterSizeDTO filterFileSize;


    public static FilterKeywordPolicyConfigDTO getPolicyConfig(RuleFilterKeyword filterKeyword) {
        if (filterKeyword == null) {
            return null;
        }
//        String[] arr = filterKeyword.getFileFilterType().split(",");
//        List<Integer> filterFileType = new ArrayList<>(arr.length);
//        for (String s : arr) {
//            filterFileType.add(PolicyUtils.strToInt(s));
//        }
        return FilterKeywordPolicyConfigDTO.builder()
                .ruleId(filterKeyword.getRuleId())
                .ruleContent(PolicyUtils.handleStrNull(filterKeyword.getRuleContent()))
                .ruleDesc(PolicyUtils.handleStrNull(filterKeyword.getRuleDesc()))
                .minMatchCount(filterKeyword.getMinMatchCount())
                .filterFileType(PolicyUtils.strToIntList(filterKeyword.getFileFilterType()))
                .filterFileSize(FileFilterSizeDTO.builder()
                        .minSize(filterKeyword.getFileFilterMinSize())
                        .maxSize(filterKeyword.getFileFilterMaxSize())
                        .build())
                .build();
    }

}
