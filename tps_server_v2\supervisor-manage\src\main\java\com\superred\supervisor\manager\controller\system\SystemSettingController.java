package com.superred.supervisor.manager.controller.system;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.superred.common.core.model.R;
import com.superred.supervisor.common.constant.log.LogTypeConstants;
import com.superred.supervisor.common.constant.log.OperateTypeConstants;
import com.superred.supervisor.common.entity.system.SysConfig;
import com.superred.supervisor.manager.aop.SysLogAnn;
import com.superred.supervisor.manager.model.vo.system.SecurityConfigResp;
import com.superred.supervisor.manager.model.vo.system.SecurityConfigUpdateReq;
import com.superred.supervisor.manager.service.system.SystemSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统配置接口
 *
 * <AUTHOR>
 * @since 2025/3/11 14:28
 */
@Slf4j
@Tag(name = "1.4. 系统配置")
@RestController
@AllArgsConstructor
@RequestMapping("/sys_setting")
public class SystemSettingController {


    @Resource
    private SystemSettingService systemSettingService;

    /**
     * 获取安全策略字段
     *
     * @return
     */
    @ApiOperationSupport(order = 1)
    @Operation(summary = "1 获取安全策略")
    @GetMapping("/get_safe_policy")
    public R<SecurityConfigResp> getSafePolicy() {

        SecurityConfigResp resp = systemSettingService.getSafePolicy();
        return R.success(resp);
    }

    /**
     * 获取安全策略字段
     *
     * @return
     */
    @ApiOperationSupport(order = 1)
    @Operation(summary = "2 更新安全策略")
    @PostMapping("/update_safe_policy")
    @SysLogAnn(module = LogTypeConstants.SYSTEM_CONFIGURATION, operateType = OperateTypeConstants.MODIFY, desc = "更新安全策略")
    public R<SecurityConfigResp> updateSafePolicy(@RequestBody SecurityConfigUpdateReq req) {

        SecurityConfigResp resp = systemSettingService.updateSafePolicy(req);
        return R.success(resp);
    }

    /**
     * 获取厂商编号数字
     *
     * @return
     */
    @ApiOperationSupport(order = 3)
    @Operation(summary = "3 获取厂商编号数字")
    @GetMapping("/vendor_id/list")
    public R<List<SysConfig>> vendorIdList() {

        List<SysConfig> list = systemSettingService.vendorIdList();
        return R.success(list);
    }
}
