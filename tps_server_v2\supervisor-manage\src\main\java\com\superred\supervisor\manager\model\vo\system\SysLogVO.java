package com.superred.supervisor.manager.model.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.common.entity.system.SysRole;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Classname SysLogVO
 * @Description 日志实体类
 * @since 2020-06-22 10:31
 */
@Data
public class SysLogVO {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 日志 uuid
     */
    private String uuid;

    /**
     * 管理员角色
     */
    private String role;

    /**
     * 产品类别
     */
    private String product;

    /**
     * 操作模块
     */
    private String operateModule;

    /**
     * 操作动作
     */
    private String operateType;


    /**
     * 描述
     */
    private String description;

    /**
     * 行为类型：1.违规行为 ； 2.异常行为;  3 一般行为
     */
    private String behaviourType;

    /**
     * 日志风险级别：1紧急、2重要、3一般、4信息
     */
    private String level;

    /**
     * 操作ip
     */
    private String hostIp;

    /**
     * 操作设备id
     */
    private String hostId;

    /**
     * 日志模板表type
     */
    private String moduleType;

    /**
     * 操作人员名字
     */
    private Integer userId;

    /**
     * 操作人员名字
     */
    private String username;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-dd-MM HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-dd-MM HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * PARA1
     */
    private String para1;

    /**
     * PARA2
     */
    private String para2;

    /**
     * PARA3
     */
    private String para3;

    /**
     * PARA4
     */
    private String para4;

    /**
     * PARA5
     */
    private String para5;

    /**
     * 角色列表
     */
    private List<SysRole> roleList;

    /**
     * 操作时间
     */
    private String operateDateStr;

    /**
     * 结果
     */
    private Integer result;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 角色名称
     */
    private String roleName;

}
