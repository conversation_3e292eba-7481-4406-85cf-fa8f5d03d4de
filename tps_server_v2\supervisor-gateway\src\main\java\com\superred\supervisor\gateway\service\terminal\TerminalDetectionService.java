package com.superred.supervisor.gateway.service.terminal;

import com.superred.supervisor.standard.v202505.terminal.detection.TerminalAlarmReq;
import com.superred.supervisor.standard.v202505.terminal.detection.TerminalFileFilterAlertReq;
import org.springframework.web.multipart.MultipartFile;

/**
 * 检测数据上报
 *
 * <AUTHOR>
 * @since 2025/5/29 14:03
 */
public interface TerminalDetectionService {

    /**
     * 上报终端文件
     * @param req
     * @param type
     */
    void reportFileFilterAlter(TerminalFileFilterAlertReq req, String type);

    /**
     * 上报终端检测组件异常通信检测数据
     *
     * @param req
     * @param type 告警类型
     */
    void agentAlarmAlert(TerminalAlarmReq req, String type);

    /**
     * 上传攻击文件
     *
     * @param fileDesc 文件描述
     * @param type     文件类型
     * @param singleMultipartFile 单个上传的文件
     */
    void uploadAttackFile(String fileDesc, String type, MultipartFile singleMultipartFile);
}
