package com.superred.supervisor.common.entity.agent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 *  终端报备
 * @since 2025/6/3 17:28
 */
@Data
@TableName("agent_device_info_bak")
public class AgentDeviceInfoBak {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 终端计算机名
     */
    @TableField("device_name")
    private String deviceName;

    /**
     * 主机操作系统
     */
    @TableField("os")
    private String os;

    /**
     * 终端软件版本号
     */
    @TableField("soft_version")
    private String softVersion;

    /**
     * 主机CPU架构
     */
    @TableField("arch")
    private String arch;

    /**
     * 单位名称
     */
    @TableField("company")
    private String company;

    @TableField("company_id")
    private Integer companyId;

    /**
     * 部门id
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 区域区域
     */
    @TableField("address")
    private String address;

    /**
     * 区域code对应路径
     */
    @TableField("address_code")
    private String addressCode;

    /**
     * 使用人编号
     */
    @TableField("user_id")
    private String userId;

    /**
     * 使用人姓名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 终端网络信息
     */
    @TableField("interface")
    private String interfaceInfo;

    /**
     * 内存总数，表示整个设备的内存大小，单位MB。
     */
    @TableField("mem_total")
    private Long memTotal;

    /**
     * CPU信息
     */
    @TableField("cpu_info")
    private String cpuInfo;

    /**
     * 磁盘信息
     */
    @TableField("disk_info")
    private String diskInfo;

    /**
     * 备注信息
     */
    @TableField("memo")
    private String memo;

    @TableField("ip")
    private String ip;

    @TableField("mac")
    private String mac;

    /**
     * 拓展字段
     */
    @TableField("extended_fields")
    private String extendedFields;

    /**
     * 入库时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}
