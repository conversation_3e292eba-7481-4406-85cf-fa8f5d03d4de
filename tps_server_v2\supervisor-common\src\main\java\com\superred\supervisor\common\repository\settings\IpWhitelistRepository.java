package com.superred.supervisor.common.repository.settings;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.superred.supervisor.common.entity.settings.enums.IpWhiteListType;
import com.superred.supervisor.common.mapper.settings.IpWhitelistMapper;
import com.superred.supervisor.common.entity.settings.IpWhitelist;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 访问控制白名单 Repository(CRUD写这里)
 *
 * <AUTHOR>
 * @since 2025-07-10 16:30:20
 */
@Repository
public class IpWhitelistRepository extends ServiceImpl<IpWhitelistMapper, IpWhitelist> {

    public List<IpWhitelist> getIpWhitelistByType(IpWhiteListType type) {
        return this.list(lambdaQuery().eq(IpWhitelist::getType, type)
                .eq(IpWhitelist::getEnabled, true));
    }
}

