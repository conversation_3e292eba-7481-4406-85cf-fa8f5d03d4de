package com.superred.supervisor.manager.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2025-04-07 14:04
 */
@Getter
public enum AuditBehaviorTypeEnum {
    TYPE0(0, "不指定任何行为类型"),
    TYPE1(1, "WEB行为"),
    TYPE2(2, "DNS行为"),
    TYPE3(3, "SSL/TLS行为"),
    TYPE4(4, "数据库操作行为"),
    TYPE5(5, "文件传输行为"),
    TYPE6(6, "控制行为"),
    TYPE7(7, "登录行为"),
    TYPE8(8, "邮件行为"),
    TYPE9(9, "风险服务和软件行为"),
    TYPE99(99, "其它");

    private final Integer key;
    private final String value;

    AuditBehaviorTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

}
