package com.superred.supervisor.manager.model.vo.policy;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-03-27 16:32
 */
@Data

public class PolicyDetailResp {

    @Schema(description = "攻击窃密检测 - 木马活动检测策略")
    private List<RuleAttackTrojanResp> trojanList;

    @Schema(description = "攻击窃密检测 - 渗透行为检测策略")
    private List<RuleAttackPermeateResp> permeateList;

    @Schema(description = "攻击窃密检测 - 恶意文件检测策略")
    private List<RuleAttackMalwareResp> malwareList;

    @Schema(description = "攻击窃密检测 - 黑名单检测策略 - IP黑名单检测策略")
    private List<RuleAttackBlacklistIpResp> blacklistIpList;

    @Schema(description = "攻击窃密检测 - 黑名单检测策略 - 域名黑名单检测策略")
    private List<RuleAttackBlacklistDnsResp> blacklistDnsList;

    @Schema(description = "攻击窃密检测 - 黑名单检测策略 - URL黑名单检测策略")
    private List<RuleAttackBlacklistUrlResp> blacklistUrlList;

    @Schema(description = "文件筛选 - 关键词筛选策略")
    private List<RuleFilterKeywordResp> filterKeywordList;

    @Schema(description = "文件筛选 - 账号筛选策略")
    private List<RuleFilterAccountResp> filterAccountList;

    @Schema(description = "文件筛选 - 文件MD5筛选策略")
    private List<RuleFilterMd5Resp> md5List;

    @Schema(description = "风险软件使用行为策略")
    private List<RuleRiskSoftwareResp> riskSoftwareList;

    @Schema(description = "网络行为审计策略 - 应用行为审计策略")
    private List<RuleAuditAppBehaviorResp> auditAppBehaviorList;

    @Schema(description = "设备信息采集策略")
    private List<RuleDeviceInfoCollectionResp> deviceInfoCollectionRespList;
}
