package com.superred.supervisor.common.entity.terminal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.superred.supervisor.common.constant.devices.DevicesAuditType;
import com.superred.supervisor.common.constant.devices.DevicesConnectStatus;
import com.superred.supervisor.common.constant.devices.DevicesRegStatus;
import com.superred.supervisor.common.entity.terminal.enums.ActivateStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 终端设备信息 实体
 *
 * <AUTHOR>
 * @since 2025-07-17 17:55:18
 */
@Data
@TableName("d_terminal_agent_info")
public class TerminalAgentInfo {


    /**
     * 设备编号
     */
    @TableId(value = "device_id", type = IdType.AUTO)
    private String deviceId;

    /**
     * 前八位为年月日，下划线后自定义
     */
    @TableField("soft_version")
    private String softVersion;

    /**
     * 设备配置信息，表示包括配置的IP地址、子网掩码、MAC地址、网关地址、是否为管理口。ip为单一IP地址类型，netmask为IP子网类型，gateway 为单一IP地址类型，mac为MAC地址类型，manage为布尔值
     */
    @TableField("interface")
    private String interfaces;

    /**
     * 内存总数，表示整个设备的内存大小，单位MB。
     */
    @TableField("mem_total")
    private Long memTotal;

    /**
     * CPU信息，包括物理CPU ID、CPU核心数，cpu主频（单位GHz），使用数组形式表示，多个物理CPU则数组内有多条信息。
     physical_id：CPU ID，数值类型
     core：CPU核心数，数值类型；
     clock：CPU主频，数值类型精确到小数点后1位

     */
    @TableField("cpu_info")
    private String cpuInfo;

    /**
     * 磁盘信息，包括磁盘大小（单位GB）和序列号，使用数组形式表示。
     size为数值类型，表示磁盘大小，单位GB；
     serial为字符串类型，最长64个字节，表示磁盘序列号
     */
    @TableField("disk_info")
    private String diskInfo;

    /**
     * 部门id
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * ip地址
     */
    @TableField("ip")
    private String ip;

    /**
     * ip地址
     */
    @TableField("ip_long")
    private Long ipLong;

    /**
     * mac地址
     */
    @TableField("mac")
    private String mac;

    /**
     * 1 自动审核 2人工审核
     */
    @TableField("audit_type")
    private DevicesAuditType auditType;

    /**
     * 区域code对应路径
     */
    @TableField("region_path")
    private String regionPath;

    /**
     * 审核时间
     */
    @TableField("verify_time")
    private LocalDateTime verifyTime;

    /**
     * 心跳时间
     */
    @TableField("heartbeat_time")
    private LocalDateTime heartbeatTime;

    /**
     * 最后升级时间
     */
    @TableField("last_upgrade_time")
    private LocalDateTime lastUpgradeTime;

    /**
     * 使用人编号
     */
    @TableField("user_id")
    private String userId;

    /**
     * 使用人姓名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 主机名称
     */
    @TableField("host_name")
    private String hostName;

    /**
     * 主机操作系统
     */
    @TableField("os")
    private String os;

    /**
     * 主机CPU架构
     */
    @TableField("arch")
    private String arch;

    /**
     * 注册时间
     */
    @TableField("register_time")
    private LocalDateTime registerTime;

    /**
     * 终端状态 0 审核通过 1  审核不通过 2 待审核 3 禁用
     */
    @TableField("register_status")
    private DevicesRegStatus registerStatus;

    /**
     * 注册状态描述
     */
    @TableField("register_message")
    private String registerMessage;

    /**
     * 备注信息
     */
    @TableField("memo")
    private String memo;

    /**
     * 在线状态， 1 在线 2 离线 3 已卸载
     */
    @TableField("connection_status")
    private DevicesConnectStatus connectionStatus;

    /**
     * 激活状态， 1 未激活 2 已激活
     */
    @TableField("activate_status")
    private ActivateStatus activateStatus;

}

