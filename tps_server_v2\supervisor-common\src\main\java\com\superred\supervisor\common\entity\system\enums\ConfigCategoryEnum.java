package com.superred.supervisor.common.entity.system.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/3/13 16:53
 */

@Getter
@AllArgsConstructor
public enum ConfigCategoryEnum implements IEnum<String> {

    SECURITY_POLICY("sys_security_policy", "系统安全策略"),

    SYS_LOG("sys_log", "系统日志"),

    SYS_EXCEPTION_THRESHOLD("sys_exception_threshold", "系统异常阈值"),

    FLOW_EXCEPTION_THRESHOLD("flow_exception_threshold", "流量异常阈值"),

    CACHE_DATA_THRESHOLD("cache_data_threshold", "缓存数据阈值"),

    DATA_STORAGE_TIME("data_storage_time", "数据存储时长配置"),

    DETECTOR_OFFLINE_TIME("detector_offline_time", "检测器离线时长配置"),

    VERIFY_JAR_SIGNER("verify_jar_signer", "签名校验周期设置"),

    VENDOR_ID("venderId", "厂商数字编号"),

    AGENT_DEVICE_ID("agent_device_id", "终端组件设备ID"),

    DETECTOR_DEVICE_ID("detector_device_id", "终端组件设备ID"),


    AGENT_INSTALL("agent_install", "终端下载设置"),

    AGENT_CHECKCODE("agent_checkcode", "终端下载校验码设置");


    private final String value;

    private final String desc;

}
