package com.superred.supervision.base.constant;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/2/13 10:39
 * 数据上报的其他的topic
 */
@Data
@Component
public class OtherTopic {

    //C.3.8.2.1  文件采集筛选数据-筛选结果信息
    private String msgFileSelection = "msg_file_selection";

    //C.3.8.2.1  文件采集筛选数据-筛选文件
    private String filedescFileSelection = "filedesc_file_selection";

    //C.3.8.4.1 应用活动对象审计
    private String msgTargetIdentApps = "msg_target_ident_apps";

    //C.3.8.4.2 设备活动对象审计
    private String msgTargetIdentDevices = "msg_target_ident_devices";

    //C.3.8.4.3 账号活动对象审计
    private String msgTargetIdentAccounts = "msg_target_ident_accounts";

    //C.3.8.5.1 网络通联关系
    private String msgNetLog = "msg_net_log";

    //C.3.8.5.2 应用行为审计
    private String msgAppBehavior = "msg_app_behavior";

    //C.3.8.5.3 应用行为扩展数据
    private String msgAppBehaviorExtended = "msg_app_behavior_extended";

    //C.3.8.6.1 目标流量审计
    private String msgObjectListen = "msg_object_listen";

    //C.3.8.6.2 目标流量审计文件
    private String filedescObjectListen = "filedesc_object_listen";

    private String msgUnknownProtocol = "msg_unknown_protocol";

    private String filedescUnknownProtocol = "filedesc_unknown_protocol";

    private String msgEncryptProtocol = "msg_encrypt_protocol";

    private String filedescEncryptProtocol = "filedesc_encrypt_protocol";

    private String msgSyncTime = "msg_sync_time";

    private String msgExtendedData = "msg_extended_data";

    private String deviceReport = "device_report";

    private String deleteDeviceReport = "delete_device_report";

    private String msgStatistics = "msg_statistics";

    private String msgStatisticsError = "msg_statistics_error";
    /**
     * 数量 上报topic
     */
    private String msgStatisticsReport = "msg_statistics_report";
    /**
     * 数量 上报topic error
     */
    private String msgStatisticsReportError = "msg_statistics_report_error";

    private String filedescStatistics = "filedesc_statistics";
    /**
     * 文件上报topic
     */
    private String filedescStatisticsReport = "filedesc_statistics_report";
    /**
     * 文件上报topic error
     */
    private String filedescStatisticsReportError = "filedesc_statistics_report_error";
    /**
     * 统计合并的topic
     */
    private String msgStatisticsMerge = "msg_statistics_merge";

    private String msgSysLog = "msg_sys_log";


    /**
     * 泄密事件处置上报
     */
    private String disposeSensitive = "dispose_sensitive";

    /**
     * 泄密事件处置上报 文件上报
     */
    private String disposeSensitiveFile = "dispose_sensitive_file";

    /**
     * 威胁预警事件处置上报
     */
    private String disposeAttack = "dispose_attack";

    /**
     * 威胁预警事件处置文件上报
     */
    private String disposeAttackFile = "dispose_attack_file";
}

