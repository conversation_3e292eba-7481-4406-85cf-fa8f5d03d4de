package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.superred.supervisor.common.entity.settings.enums.IpWhiteListType;
import com.superred.supervisor.common.mybatis.EncryptAndDecryptHandler;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 访问控制白名单 实体
 *
 * <AUTHOR>
 * @since 2025-07-10 16:30:20
 */
@Data
@TableName(value = "sys_ip_whitelist", autoResultMap = true)
public class IpWhitelist {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * ip地址范围
     */
    @TableField(value = "ip", typeHandler = EncryptAndDecryptHandler.class)
    private String ip;

    /**
     * 访问控制类型，1：系统访问控制；2：通信访问控制 3 下载服务控制
     */
    @TableField("type")
    private IpWhiteListType type;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * ip2long 开始
     */
    @TableField("start_ip_long")
    private Long startIpLong;

    /**
     * ip2long 终止
     */
    @TableField("end_ip_long")
    private Long endIpLong;

}

