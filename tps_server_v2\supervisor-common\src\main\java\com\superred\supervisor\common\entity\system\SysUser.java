package com.superred.supervisor.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.superred.supervisor.common.entity.system.enums.UserLockStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户表 实体
 *
 * <AUTHOR>
 * @since 2025-03-13 11:42:22
 */
@Data
@TableName("p_sys_user")
public class SysUser {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 组织ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 锁定时间
     */
    @TableField("lock_time")
    private LocalDateTime lockTime;

    /**
     * 上次登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 尝试次数
     */
    @TableField("try_count")
    private Integer tryCount;

    /**
     * 锁定状态(1-正常，2-锁定)
     */
    @TableField("lock_flag")
    private UserLockStatus lockFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("modified_time")
    private LocalDateTime modifiedTime;

    /**
     * 1 表示删除，0 表示未删除
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 密码更新时间
     */
    @TableField("pass_update_time")
    private LocalDateTime passUpdateTime;

    /**
     * 身份证号
     */
    @TableField("card")
    private String card;

    /**
     * 1 表示不显示，0 表示显示
     */
    @TableField("is_show")
    private Integer isShow;

    /**
     * 密级（5:绝密:,4:机密3:秘密,2:内部,1:公开）
     */
    @TableField("secret")
    private Integer secret;

    /**
     * 启用状态：1 启用，2：停用
     */
    @TableField("enable")
    private UserLockStatus enable;

    /**
     * 首次登录计数
     */
    @TableField("first_login")
    private Integer firstLogin;

    /**
     * 是否是主账号0 否，1是
     */
    @TableField("is_master")
    private Integer isMaster;


}

