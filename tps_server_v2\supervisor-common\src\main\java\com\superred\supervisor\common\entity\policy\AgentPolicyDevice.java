package com.superred.supervisor.common.entity.policy;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 终端策略关系表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "policy_agent_policy_device", autoResultMap = true)
public class AgentPolicyDevice extends Model<AgentPolicyDevice> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 策略ID
     */
    private Long policyId;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 下发状态
     */
    private Integer status;

    /**
     * 模块所属模块：file_keyword 关键词，ip_blacklist ip黑名单，domain_blacklist 域名黑名单
     */
    private String module;

}
