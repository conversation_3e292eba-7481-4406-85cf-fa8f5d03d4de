package com.superred.supervisor.manager.model.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.superred.supervisor.common.entity.system.SysRegion;
import com.superred.supervisor.common.entity.system.enums.RegionLevel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 区域响应
 *
 * <AUTHOR>
 * @since 2025/3/17 16:03
 */
@Data
public class RegionResp {

    /**
     * 区域编码
     */
    @Schema(description = "区域编码")
    private Integer id;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 父ID
     */
    @Schema(description = "父ID")
    private Integer parentId;

    /**
     * 简称
     */
    @Schema(description = "简称")
    private String shortName;

    /**
     * 等级
     */
    @Schema(description = "等级")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private RegionLevel levelType;

    /**
     * 省级编码
     */
    @Schema(description = "省级编码")
    private String cityCode;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码")
    private String zipcode;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String lng;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private String lat;

    /**
     * 拼音
     */
    @Schema(description = "拼音")
    private String pinyin;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;


    @Schema(description = "区域路径")
    private String regionPath;


    private List<RegionResp> children;

    public static RegionResp fromRegion(SysRegion sysRegion) {

        RegionResp regionResp = new RegionResp();
        regionResp.setId(sysRegion.getId());
        regionResp.setName(sysRegion.getName());
        regionResp.setParentId(sysRegion.getParentId());
        regionResp.setShortName(sysRegion.getShortName());
        regionResp.setLevelType(sysRegion.getLevelType());
        regionResp.setCityCode(sysRegion.getCityCode());
        regionResp.setZipcode(sysRegion.getZipcode());
        regionResp.setLng(sysRegion.getLng());
        regionResp.setLat(sysRegion.getLat());
        regionResp.setPinyin(sysRegion.getPinyin());
        regionResp.setStatus(sysRegion.getStatus());
        regionResp.setRegionPath(sysRegion.getRegionPath());
        return regionResp;
    }
}
