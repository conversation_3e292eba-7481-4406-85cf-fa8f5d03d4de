安装部署脚本使用说明

### 1. 环境准备

- 预留16G以上内存/100G以上硬盘空间
- 预装组件服务，[wlhplatformserver-07.2.01.02.040006-1.x86_x64.rpm](http://nx.superred.com.cn:8081/repository/jenkinsBuild/zdby/productbuild/PlatformMINIServer/6/rpm/x86_x64/wlhplatformserver-07.2.01.02.040006-1.x86_x64.rpm)
### 2. 自监管检测平台服务包安装

####  2.1 使用```rpm```命令安装软件包

```bash
# 安装自监管检测平台，wlhsupervisorserver-*********.00.00.002-1.noarch.rpm为软件包文件名称，执行命令时需要根据实际文件名调整命令
rpm -ivh wlhsupervisorserver-*********.00.00.002-1.noarch.rpm
```

#### 2.2 使用```deb```命令安装软件包

```bash
# 安装自监管检测平台，wlhchecktoolsdbcheck-1.0.0.3_all.deb为软件包文件名称，执行命令时需要根据实际文件名调整命令
dpkg -i wlhsupervisorserver-*********.00.00.002-1.noarch.deb
```

### 3. 部署&初始化组件服务

**若已经初始化部署跳过本步骤**

安装完RPM软件包后，执行以下命令

```bash
sh /opt/wlh/supervisor/bin/simple-startup.sh
```

安装完DEB软件包后，执行以下命令

```bash
bash /opt/wlh/supervisor/bin/simple-startup.sh
```

执行后根据提示需要手动输入参数

- 部署服务器的IP
- 数据库superred账户密码，默认123456
- 设备ID

参数输入完成之后等待脚本执行完成即可，执行成功会给出提示如下

```bash
启动完成，耗时:xxx秒
互联网BM自监管检测平台-访问地址：https:xxxx:12300
```
#### 3.1 验证服务

执行以下命令脚本,检验服务进程是否启动：

```bash
bash /opt/wlh/supervisor/bin/check.sh
```

执行后得到如下结果：

<img src=".\img\20250401184738.png" style="zoom:50%;" />



***检测平台依耐 minio、zookeeper、kafka、redis、nginx，请确保以上服务是启动成功的，其他组件可以不启动***

**！！！！**

### 4. 启动/暂停应用服务（非必须执行）

**(若步骤4检测失败，跟上图显示不一致，才执行步骤5，重启服务)**


```shell

#  启动检测平台管理系统
systemctl start supervisor-web
# 启动接口服务
systemctl start supervisor-gateway
# 启动数据存储服务
systemctl start supervisor-storage
# 启动数据上报服务
systemctl start supervisor-report
# 启动监控服务
systemctl start supervisor-monitor
# 启动前端web页面
systemctl start wlhplatformnginx

```

### 6. 运维配置

#### 6.1  组件对应服务/日志信息

install_dir：/opt/wlh/supervisor/lib/

| 组件               | 端口                    | 服务目录（包含日志、服务配置文件） | 主要日志                 |
| ------------------ | ----------------------- | ---------------------------------- | ------------------------ |
| supervisor-web         | 8083                    | $install_dir/supervisor-web            | logs/min.log             |
| supervisor-gateway | 50070                   | $install_dir/supervisor-gateway    | logs/min.log             |
| supervisor-report  | 50071                   | $install_dir/supervisor-report     | logs/min.log             |
| supervisor-storage | -                       | $install_dir/supervisor-storage    | logs/min.log             |
| supervisor-monitor | -                       | $install_dir/supervisor-monitor    | logs/min.log             |
| nginx              | 12300/12400/10444/10443 | /opt/wlh/wlhplatform/nginx/logs    | error.log 、access_*.log |

