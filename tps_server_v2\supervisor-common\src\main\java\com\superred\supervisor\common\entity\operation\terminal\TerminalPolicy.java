package com.superred.supervisor.common.entity.operation.terminal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 终端策略表 实体
 *
 * <AUTHOR>
 * @since 2025-07-24 14:22:59
 */
@Data
@TableName("op_terminal_policy")
public class TerminalPolicy {


    /**
     * 策略ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 策略编码
     */
    @TableField("policy_code")
    private String policyCode;

    /**
     * 策略名称
     */
    @TableField("policy_name")
    private String policyName;

    /**
     * 模块类型：keyword_detect,ip_blacklist,domain_blacklist,file_md5
     */
    @TableField("module")
    private String module;

    /**
     * 策略类型：add-增加,del-删除,reset-全量,inc_del-增量删除
     */
    @TableField("policy_type")
    private String policyType;

    /**
     * 关联的规则ID列表，如[1001,1002,1003]
     */
    @TableField("rule_ids")
    private String ruleIds;

    /**
     * 规则数量（通过触发器或应用层维护）
     */
    @TableField("rule_count")
    private Integer ruleCount;

    /**
     * 策略版本
     */
    @TableField("policy_version")
    private String policyVersion;

    /**
     * 策略配置内容（JSON Base64编码）
     */
    @TableField("policy_config")
    private String policyConfig;

    /**
     * 策略描述
     */
    @TableField("description")
    private String description;

    /**
     * 策略状态：0-禁用，1-启用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private String creatorId;

    /**
     * 更新人
     */
    @TableField("updater_id")
    private String updaterId;

}

