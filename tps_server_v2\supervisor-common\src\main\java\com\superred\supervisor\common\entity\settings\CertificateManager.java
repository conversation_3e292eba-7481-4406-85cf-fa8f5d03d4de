package com.superred.supervisor.common.entity.settings;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 证书管理(LocalCertificateManager) 实体
 *
 * <AUTHOR>
 * @since 2025-03-20 14:07:06
 */
@Data
@TableName("local_certificate_manager")
public class CertificateManager {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 应用系统:
     */
    @TableField("type")
    private Integer type;

    /**
     * 证书名称
     */
    @TableField("cert_name")
    private String certName;

    /**
     * 证书有效期
     */
    @TableField("valid_period")
    private LocalDateTime validPeriod;

    /**
     * 证书状态
     */
    @TableField("status")
    private Integer status;

    /**
     * CA证书内容
     */
    @TableField("ca_pem")
    private String caPem;

    /**
     * 签名证书内容
     */
    @TableField("sign_pem")
    private String signPem;

    /**
     * 签名私钥内容
     */
    @TableField("sign_key")
    private String signKey;

    /**
     * 加密证书内容
     */
    @TableField("enc_pem")
    private String encPem;

    /**
     * 加密私钥内容
     */
    @TableField("enc_key")
    private String encKey;

}

