package com.superred.supervisor.common.entity.command;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 终端软件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_agent_software")
public class AgentSoftware {


    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 软件包名
     */
    private String packetName;

    /**
     * 软件包版本
     */
    private String version;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 适用的cpu架构
     */
    private String cpuArchitecture;

    /**
     * 添加时间
     */
    private LocalDateTime createTime;

    /**
     * 安装包地址
     */
    private String filePath;

    /**
     * 文件md5
     */
    private String md5;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * 是否发布：0未发布，1发布
     */
    private Integer isPublish;

    /**
     * 软件名称
     */
    private String softwareName;

    /**
     * 安装说明
     */
    private String remark;

    /**
     * 软件名称
     */
    private String packageName;

    /**
     * 软件版本
     */
    private String softVersion;

    /**
     * 软件类型：1：安装包；2：升级包
     */
    private Short softType;
}
