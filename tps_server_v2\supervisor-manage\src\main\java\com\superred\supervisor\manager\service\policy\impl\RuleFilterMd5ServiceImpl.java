package com.superred.supervisor.manager.service.policy.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.superred.common.core.exception.BaseBusinessException;
import com.superred.common.core.utils.JsonUtil;
import com.superred.supervision.base.constant.PolicyDeviceTypeEnum;
import com.superred.supervision.base.constant.PolicyModuleOneEnum;
import com.superred.supervision.base.constant.PolicyModuleTwoEnum;
import com.superred.supervisor.common.entity.policy.AgentPolicyRule;
import com.superred.supervisor.common.entity.policy.DetectorPolicyRule;
import com.superred.supervisor.common.entity.policy.RuleFilterMd5;
import com.superred.supervisor.common.repository.policy.RuleFilterMd5Repository;
import com.superred.supervisor.manager.constant.PolicyApplyStatusEnum;
import com.superred.supervisor.manager.model.dto.policy.config.FilterMd5PolicyConfigDTO;
import com.superred.supervisor.manager.model.vo.policy.PolicyBatchIdsReq;
import com.superred.supervisor.manager.model.vo.policy.PolicyDetailResp;
import com.superred.supervisor.manager.model.vo.policy.PolicyModuleResp;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterMd5PageReq;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterMd5Req;
import com.superred.supervisor.manager.model.vo.policy.RuleFilterMd5Resp;
import com.superred.supervisor.manager.model.vo.policy.RulePolicyApplyResp;
import com.superred.supervisor.manager.repository.policy.AgentPolicyRuleExtRepository;
import com.superred.supervisor.manager.repository.policy.DetectorPolicyRuleExtRepository;
import com.superred.supervisor.manager.service.RuleIdBuilder;
import com.superred.supervisor.manager.service.policy.RuleFilterMd5Service;
import com.superred.supervisor.manager.service.policy.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 规则过滤器MD5服务实施
 *
 * <AUTHOR>
 * @since 2025/03/14
 */
@Slf4j
@Service("ruleFilterMd5Service")
public class RuleFilterMd5ServiceImpl implements RuleFilterMd5Service, RuleService {

    @Resource
    private RuleFilterMd5Repository ruleFilterMd5Repository;
    @Resource
    private DetectorPolicyRuleExtRepository detectorPolicyRuleExtRepository;
    @Resource
    private AgentPolicyRuleExtRepository agentPolicyRuleExtRepository;
    @Resource
    private RuleIdBuilder ruleIdBuilder;


    @Override
    public IPage<RuleFilterMd5Resp> page(RuleFilterMd5PageReq ruleFilterMd5PageReq) {
        // 验证
        this.validateQureyParam(ruleFilterMd5PageReq);
        // 查询
        List<String> ruleIdList = this.getRuleIdList(ruleFilterMd5PageReq);
        LambdaQueryWrapper<RuleFilterMd5> queryWrapper = new LambdaQueryWrapper<RuleFilterMd5>()
                .like(StrUtil.isNotBlank(ruleFilterMd5PageReq.getRuleIdStr()), RuleFilterMd5::getRuleId, ruleFilterMd5PageReq.getRuleIdStr())
                .eq(ruleFilterMd5PageReq.getStatus() != null, RuleFilterMd5::getStatus, ruleFilterMd5PageReq.getStatus())
                .like(StrUtil.isNotBlank(ruleFilterMd5PageReq.getRuleContent()), RuleFilterMd5::getRuleContent, ruleFilterMd5PageReq.getRuleContent())
                .in(CollectionUtil.isNotEmpty(ruleIdList), RuleFilterMd5::getRuleId, ruleIdList)
                .orderByDesc(RuleFilterMd5::getUpdateTime);
        Page<RuleFilterMd5> page = new Page<>(ruleFilterMd5PageReq.getStart(), ruleFilterMd5PageReq.getLimit());
        IPage<RuleFilterMd5> page1 = this.ruleFilterMd5Repository.page(page, queryWrapper);
        return page1.convert(item -> {
            RuleFilterMd5Resp resp = RuleFilterMd5Resp.fromRuleFilterMd5(item);
            long detectorCount = this.detectorPolicyRuleExtRepository.count(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getRuleId, item.getRuleId()));
            resp.setDetectorPolicyCount((int) detectorCount);
            return resp;
        });
    }

    @Override
    public RuleFilterMd5Resp getById(Integer id) {
        RuleFilterMd5 ruleFilterMd5 = this.ruleFilterMd5Repository.getById(id);
        return RuleFilterMd5Resp.fromRuleFilterMd5(ruleFilterMd5);
    }

    @Override
    public void save(RuleFilterMd5Req ruleMd5) {
        // 验证
        this.validateParam(ruleMd5);
        // 验重复
        this.validateSaveRepeat(ruleMd5);
        RuleFilterMd5 ruleFilterMd5 = fromRuleFilterMd5Req(ruleMd5);
        // 赋值ruleId
        ruleFilterMd5.setRuleId(this.ruleIdBuilder.buildRuleId());
        this.ruleFilterMd5Repository.save(ruleFilterMd5);
    }

    @Override
    public void edit(RuleFilterMd5Req ruleMd5) {
        // 验证ID
        this.validateId(ruleMd5);
        // 验证参数
        this.validateParam(ruleMd5);
        // 验重复
        this.validateEditRepeat(ruleMd5);
        RuleFilterMd5 ruleFilterMd5 = fromRuleFilterMd5Req(ruleMd5);
        this.ruleFilterMd5Repository.update(ruleFilterMd5, Wrappers.<RuleFilterMd5>lambdaUpdate().eq(RuleFilterMd5::getRuleId, ruleMd5.getRuleId()));
    }

    public static RuleFilterMd5 fromRuleFilterMd5Req(RuleFilterMd5Req ruleFilterMd5Req) {
        return RuleFilterMd5.builder()
                .ruleId(ruleFilterMd5Req.getRuleId())
                .ruleContent(ruleFilterMd5Req.getRuleContent())
                .ruleDesc(ruleFilterMd5Req.getRuleDesc())
                .risk(ruleFilterMd5Req.getRisk())
                .status(ruleFilterMd5Req.getStatus())
                .updateTime(ruleFilterMd5Req.getUpdateTime())
                .build();
    }

    @Override
    public void del(PolicyBatchIdsReq batchIdsReq) {
        // 验证是否在使用
        this.validateUsed(batchIdsReq);
        // 删除
        this.ruleFilterMd5Repository.removeByIds(batchIdsReq.getIds());
    }

    @Override
    public List<RulePolicyApplyResp> policyApply(Long ruleId) {
        List<RulePolicyApplyResp> list = new ArrayList<>();
        List<RulePolicyApplyResp> agentList = this.agentPolicyRuleExtRepository.selectPolicyApply(ruleId);
        List<RulePolicyApplyResp> detectorList = this.detectorPolicyRuleExtRepository.selectPolicyApply(ruleId);
        if (CollectionUtil.isNotEmpty(agentList)) {
            list.addAll(agentList);
        }
        if (CollectionUtil.isNotEmpty(detectorList)) {
            list.addAll(detectorList);
        }
        return list;
    }

    /**
     * 验证查询参数
     *
     * @param ruleFilterMd5PageReq
     */
    private void validateQureyParam(RuleFilterMd5PageReq ruleFilterMd5PageReq) {
        if (StrUtil.isNotBlank(ruleFilterMd5PageReq.getRuleIdStr())) {
            String ruleIdStr = ruleFilterMd5PageReq.getRuleIdStr();
            if (!NumberUtil.isLong(ruleIdStr)) {
                throw new BaseBusinessException("输入的策略id有非数字，请重新输入");
            }
            if (Double.parseDouble(ruleIdStr) > Long.MAX_VALUE) {
                throw new BaseBusinessException("输入的策略id超过最大范围，请检查格式后重新输入");
            }
            ruleFilterMd5PageReq.setRuleId(Long.parseLong(ruleIdStr));
        }
    }

    /**
     * 验证是否在使用
     *
     * @param batchIdsReq
     */
    private void validateUsed(PolicyBatchIdsReq batchIdsReq) {
        List<RuleFilterMd5> list = this.ruleFilterMd5Repository.list(Wrappers.<RuleFilterMd5>lambdaQuery()
                .in(RuleFilterMd5::getId, batchIdsReq.getIds()));
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (item.getStatus().equals(PolicyApplyStatusEnum.APPLY.getKey())) {
                    throw new BaseBusinessException("存在正在应用的策略，请检查后删除");
                }
            });
        }
    }

    /**
     * 验证重复
     *
     * @param ruleMd5
     */
    private void validateSaveRepeat(RuleFilterMd5Req ruleMd5) {
        List<RuleFilterMd5> list = this.ruleFilterMd5Repository.list(Wrappers.<RuleFilterMd5>lambdaQuery()
                .eq(RuleFilterMd5::getRuleContent, ruleMd5.getRuleContent().trim()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("策略内容 存在重复数据，请检查");
        }
    }

    /**
     * 验证重复
     *
     * @param ruleMd5
     */
    private void validateEditRepeat(RuleFilterMd5Req ruleMd5) {
        List<RuleFilterMd5> list = this.ruleFilterMd5Repository.list(Wrappers.<RuleFilterMd5>lambdaQuery()
                .eq(RuleFilterMd5::getRuleContent, ruleMd5.getRuleContent().trim())
                .ne(RuleFilterMd5::getRuleId, ruleMd5.getRuleId()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new BaseBusinessException("策略内容 存在重复数据，请检查");
        }
    }

    /**
     * 验证参数
     *
     * @param ruleMd5
     */
    private void validateParam(RuleFilterMd5Req ruleMd5) {
        if (ruleMd5.getRuleContent().length() != 32) {
            throw new BaseBusinessException("策略内容 长度需要为32位");
        }
    }

    /**
     * 验证ID
     *
     * @param ruleMd5
     */
    private void validateId(RuleFilterMd5Req ruleMd5) {
        if (ruleMd5.getRuleId() == null) {
            throw new BaseBusinessException("策略ID为空错误");
        }
    }

    /**
     * 获取规则ID列表
     * @param ruleFilterMd5PageReq
     * @return
     */
    private List<String> getRuleIdList(RuleFilterMd5PageReq ruleFilterMd5PageReq) {
        List<String> ruleIdList = new ArrayList<>();
        if (StrUtil.isBlank(ruleFilterMd5PageReq.getIssueDeviceType())
                || StrUtil.isBlank(ruleFilterMd5PageReq.getPolicyId())
                || StrUtil.isBlank(ruleFilterMd5PageReq.getVersion())) {
            return ruleIdList;
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.JCQ.getKey(), ruleFilterMd5PageReq.getIssueDeviceType())) {
            // 检测器
            List<DetectorPolicyRule> detectorPolicyRuleList = this.detectorPolicyRuleExtRepository.list(Wrappers.<DetectorPolicyRule>lambdaQuery()
                    .eq(DetectorPolicyRule::getPolicyId, ruleFilterMd5PageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(detectorPolicyRuleList)) {
                detectorPolicyRuleList.forEach(detectorPolicyRule -> {
                    ruleIdList.add(detectorPolicyRule.getRuleId().toString());
                });
            }
        }
        if (StrUtil.equals(PolicyDeviceTypeEnum.AGENT.getKey(), ruleFilterMd5PageReq.getIssueDeviceType())) {
            // 终端
            List<AgentPolicyRule> agentPolicyRuleList = this.agentPolicyRuleExtRepository.list(Wrappers.<AgentPolicyRule>lambdaQuery()
                    .eq(AgentPolicyRule::getPolicyId, ruleFilterMd5PageReq.getPolicyId()));
            if (CollectionUtil.isNotEmpty(agentPolicyRuleList)) {
                agentPolicyRuleList.forEach(agentPolicyRule -> {
                    ruleIdList.add(agentPolicyRule.getRuleId().toString());
                });
            }
        }
        return ruleIdList;
    }

    @Override
    public boolean isSupported(String module) {
        // 文件筛选 - 文件MD5筛选策略
        return StrUtil.equals(module, PolicyModuleTwoEnum.MD5_FILTER.getKey());
    }

    @Override
    public PolicyModuleResp getModule() {
        // 文件筛选 - 文件MD5筛选策略
        return PolicyModuleResp.builder()
                .module(PolicyModuleTwoEnum.MD5_FILTER.getKey())
                .moduleStr(PolicyModuleTwoEnum.MD5_FILTER.getValue())
                .moduleParentStr(PolicyModuleOneEnum.FILE_FILTER.getValue())
                .build();
    }

    @Override
    public String getRuleConfig(List<Long> ruleIds) {
        // 文件筛选 - 关键词筛选策略
        if (CollectionUtil.isEmpty(ruleIds)) {
            return "";
        }
        List<RuleFilterMd5> list = this.ruleFilterMd5Repository.list(Wrappers.<RuleFilterMd5>lambdaQuery()
                .in(RuleFilterMd5::getRuleId, ruleIds));
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        List<FilterMd5PolicyConfigDTO> configDTOS = list.stream().map(item -> {
            return FilterMd5PolicyConfigDTO.getPolicyConfig(item);
        }).collect(Collectors.toList());
        return JsonUtil.toJsonIncludeDefault(configDTOS);
    }

    @Override
    public void updateStatus(List<Long> ruleIds) {
        // 文件筛选 - 文件MD5筛选策略
        this.ruleFilterMd5Repository.update(Wrappers.<RuleFilterMd5>lambdaUpdate()
                .in(RuleFilterMd5::getRuleId, ruleIds)
                .set(RuleFilterMd5::getStatus, PolicyApplyStatusEnum.APPLY.getKey()));
    }

    @Override
    public PolicyDetailResp getDetailByRuleId(List<Long> ruleIdList) {
        // 文件筛选 - 文件MD5筛选策略
        PolicyDetailResp policyDetail = new PolicyDetailResp();
        List<RuleFilterMd5> list = this.ruleFilterMd5Repository.list(Wrappers.<RuleFilterMd5>lambdaQuery()
                .in(RuleFilterMd5::getRuleId, ruleIdList));
        List<RuleFilterMd5Resp> respList = new ArrayList<>();
        list.forEach(item -> {
            RuleFilterMd5Resp resp = RuleFilterMd5Resp.fromRuleFilterMd5(item);
            respList.add(resp);
        });
        policyDetail.setMd5List(respList);
        return policyDetail;
    }
}
